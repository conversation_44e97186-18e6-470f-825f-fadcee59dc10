

mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.itco.dtrecovery.entity

#spring:
#  datasource:
#    url: *****************************************************************************************************************************************************************
#    username: lbsc
#    password: 2020#Rating
#    driver-class-name: org.postgresql.Driver

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **************************************************************************************************************************************************************
    username: yaoke_bqjtest
    driver-class-name: org.postgresql.Driver
    password: XMsbzVZ1Cjapc5Gm36wl0A3sEBl3R9xR2p6S2AJobr5t8zMpJdMyfHug2tNwu3ZicxJ4cWdqKZUAUJ0W0aUCCw==
    filters: stat,config
    maxActive: 20
    initialSize: 5
    maxWait: 60000
    minIdle: 2
    socketTimeout: 300000
    connectTimeout: 10000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: select 'x'
    testWhileIdle: true
    testOnBorrow: true
    testOnReturn: false
    poolPreparedStatements: true
    keepAlive: true
    maxOpenPreparedStatements: 50
    maxPoolPreparedStatementPerConnectionSize: 20
    druid.connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI2GlyNljp5trjUMXp5tRgxCLjcRmR0Nv0Pyg0+Nc8o/hnqz1QPC45osBw/ucopNg4S9/J35Aw8Ep+7NVwiG3D8CAwEAAQ==
    druid:
      filter:
        config:
          enabled: true

logging:
  level:
    root: INFO

component:
  dynamicdatasource:
    enabled: false   # 是否启用动态数据源(动态数据源和jdbc数据源只能启用一个，也可以同时关闭，使用application.yml中的spring.datasource配置)
  jdbcdatasource:
    enabled: true   # 是否启用jdbc数据源(jdbc数据源和动态数据源只能启用一个，也可以同时关闭，使用application.yml中的spring.datasource配置)


