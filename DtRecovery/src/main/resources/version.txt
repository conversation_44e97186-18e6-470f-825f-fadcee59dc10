--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-05-10 11:30
【修订日期】：2024-05-10
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：62097
【实现功能】：
1、新增命令行入参 -s 保持全部规则的备份，恢复。
2、正常通过dcoos_tp_process表的storageAvailableZone字段决定当前启动的DtRecovery实例只备份恢复自己负责的可用区。
3、每次扫描到任务时，都重新全量加载一次规则，以确保规则的实时性。
4、targethostId字段弃用。

【变更文件】：
修改：pom.xml
修改：src/main/java/com/itco/DtRecovery.java
修改：src/main/java/com/itco/dtrecovery/dao/DisasterRecoveryTaskDao.java
修改：src/main/java/com/itco/dtrecovery/entity/BackupConfig.java
修改：src/main/java/com/itco/dtrecovery/entity/BackupHostInfo.java
修改：src/main/java/com/itco/dtrecovery/entity/BackupResult.java
修改：src/main/java/com/itco/dtrecovery/entity/RecoveryResult.java
修改：src/main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：src/main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：src/main/java/com/itco/dtrecovery/module/impl/BackupFile.java
修改：src/main/java/com/itco/dtrecovery/module/impl/BackupPostgres.java
修改：src/main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：src/main/java/com/itco/dtrecovery/module/impl/RecoveryPostgres.java
修改：src/main/java/com/itco/dtrecovery/Thread/CreateAnyBean.java
修改：src/main/java/com/itco/dtrecovery/Thread/CreateTaskManager.java
修改：src/main/java/com/itco/dtrecovery/Thread/DealTaskManager.java
修改：src/main/java/com/itco/dtrecovery/Thread/EntityManager.java
修改：src/main/resources/mapper/BackupConfigDao.xml
修改：src/main/resources/mapper/BackupHostInfoDao.xml
修改：src/main/resources/mapper/DisasterRecoveryTaskDao.xml
修改：src/main/resources/version.txt

【修订要点】：
 1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-30 11:30
【修订日期】：2024-04-30
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61990
【实现功能】：
1、组件监控代码基本实现
2、规则支持动态加载

【变更文件】：
修改：src/main/java/com/itco/DtRecovery.java
修改：src/main/java/com/itco/dtrecovery/entity/BackupResult.java
修改：src/main/java/com/itco/dtrecovery/entity/RecoveryResult.java
修改：src/main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：src/main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：src/main/java/com/itco/dtrecovery/module/impl/BackupFile.java
修改：src/main/java/com/itco/dtrecovery/module/impl/BackupPostgres.java
修改：src/main/java/com/itco/dtrecovery/module/impl/MonitorNginx.java
修改：src/main/java/com/itco/dtrecovery/module/impl/MonitorPostgres.java
修改：src/main/java/com/itco/dtrecovery/module/impl/MonitorZookeeper.java
修改：src/main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：src/main/java/com/itco/dtrecovery/module/impl/RecoveryPostgres.java
修改：src/main/java/com/itco/dtrecovery/Thread/CreateTaskManager.java
修改：src/main/java/com/itco/dtrecovery/Thread/DealTaskManager.java
修改：src/main/java/com/itco/dtrecovery/Thread/EntityManager.java
修改：src/main/resources/version.txt

【修订要点】：
 1、无

【注意事项】：
  1、无
 --------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-22 11:30
【修订日期】：2024-04-22
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61970
【实现功能】：
1、组件监控代码基本实现

【变更文件】：
修改：main/java/com/itco/dtrecovery/module/BaseMonitorThread.java
修改：main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：main/java/com/itco/dtrecovery/module/impl/MonitorNginx.java
修改：main/java/com/itco/dtrecovery/module/impl/MonitorPostgres.java
修改：main/java/com/itco/dtrecovery/module/impl/MonitorZookeeper.java
修改：main/java/com/itco/dtrecovery/Thread/EntityManager.java
修改：main/resources/mapper/DisasterRecoveryTaskDao.xml
修改：main/resources/version.txt
新增：main/java/com/itco/dtrecovery/dao/ComponentInfoDao.java
新增：main/java/com/itco/dtrecovery/entity/ComponentInfo.java
新增：main/java/com/itco/dtrecovery/service/ComponentInfoService.java
新增：main/java/com/itco/dtrecovery/service/impl/ComponentInfoServiceImpl.java
新增：main/resources/mapper/ComponentInfoDao.xml

【修订要点】：
 1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-18 11:30
【修订日期】：2024-04-18
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61949
【实现功能】：
1、代码框架微调，新增组件监控，优化代码结构

【变更文件】：
修改：main/java/com/itco/dtrecovery/dao/ComponentManagerDao.java
修改：main/java/com/itco/dtrecovery/entity/ComponentManager.java
修改：main/java/com/itco/dtrecovery/entity/DisasterRecoveryTask.java
修改：main/java/com/itco/dtrecovery/entity/EnumValue.java
修改：main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupFile.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupPostgres.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryPostgres.java
修改：main/java/com/itco/dtrecovery/service/ComponentManagerService.java
修改：main/java/com/itco/dtrecovery/service/impl/ComponentManagerServiceImpl.java
修改：main/java/com/itco/dtrecovery/Thread/CreateTaskManager.java
修改：main/java/com/itco/dtrecovery/Thread/DealTaskManager.java
修改：main/java/com/itco/dtrecovery/Thread/EntityManager.java
修改：main/resources/mapper/ComponentManagerDao.xml
修改：main/resources/version.txt
新增：main/java/com/itco/dtrecovery/module/BaseMonitorThread.java
新增：main/java/com/itco/dtrecovery/module/BaseThread.java
新增：main/java/com/itco/dtrecovery/module/impl/MonitorComponent.java
新增：main/java/com/itco/dtrecovery/module/impl/MonitorNginx.java
新增：main/java/com/itco/dtrecovery/module/impl/MonitorPostgres.java
新增：main/java/com/itco/dtrecovery/module/impl/MonitorZookeeper.java

【修订要点】：
 1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-10 11:30
【修订日期】：2024-04-10
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61946
【实现功能】：
1、文件恢复完成，数据库恢复完成，自测通过

【变更文件】：
修改：main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupFile.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupPostgres.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryPostgres.java
修改：main/resources/version.txt

【修订要点】：
 1、文件恢复完成，数据库恢复完成，自测通过

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-09 20:30
【修订日期】：2024-04-09
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61941
【实现功能】：
1、文件恢复完成，数据库恢复完成一部分

【变更文件】：
修改：main/java/com/itco/dtrecovery/dao/DisasterRecoveryTaskDao.java
修改：main/java/com/itco/dtrecovery/dao/RecoveryResultDao.java
修改：main/java/com/itco/dtrecovery/entity/CommonModule.java
修改：main/java/com/itco/dtrecovery/entity/DisasterRecoveryTask.java
修改：main/java/com/itco/dtrecovery/entity/RecoveryResult.java
修改：main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryPostgres.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryThread.java
修改：main/java/com/itco/dtrecovery/service/DisasterRecoveryTaskService.java
修改：main/java/com/itco/dtrecovery/service/impl/DisasterRecoveryTaskServiceImpl.java
修改：main/java/com/itco/dtrecovery/service/impl/RecoveryResultServiceImpl.java
修改：main/java/com/itco/dtrecovery/service/RecoveryResultService.java
修改：main/java/com/itco/dtrecovery/Thread/CreateAnyBean.java
修改：main/java/com/itco/dtrecovery/Thread/CreateTaskManager.java
修改：main/java/com/itco/dtrecovery/Thread/DealTaskManager.java
修改：main/resources/mapper/DisasterRecoveryTaskDao.xml
修改：main/resources/mapper/RecoveryResultDao.xml
修改：main/resources/version.txt

【修订要点】：
  1、文件恢复完成，数据库恢复完成一部分。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-07 11:00
【修订日期】：2024-04-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61926
【实现功能】：
1、恢复功能未实现基础版本提交

【变更文件】：
修改：main/java/com/itco/dtrecovery/dao/DisasterRecoveryTaskDao.java
修改：main/java/com/itco/dtrecovery/entity/BackupResult.java
修改：main/java/com/itco/dtrecovery/entity/DisasterRecoveryTask.java
修改：main/java/com/itco/dtrecovery/module/BaseResasterThread.java
修改：main/java/com/itco/dtrecovery/module/BaseThreadFactory.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupFile.java
修改：main/java/com/itco/dtrecovery/module/impl/BackupPostgres.java
修改：main/java/com/itco/dtrecovery/module/impl/RecoveryFile.java
修改：main/java/com/itco/dtrecovery/service/DisasterRecoveryTaskService.java
修改：main/java/com/itco/dtrecovery/service/impl/DisasterRecoveryTaskServiceImpl.java
修改：main/java/com/itco/dtrecovery/Thread/DealTaskManager.java
修改：main/java/com/itco/dtrecovery/Thread/EntityManager.java
修改：main/resources/DtRecovery.sh
修改：main/resources/mapper/DisasterRecoveryTaskDao.xml
修改：main/resources/version.txt
修改：main/resources/zk.properties

【修订要点】：
  1、恢复功能未实现基础版本提交。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DtRecovery_code_2024-04-02 11:00
【修订日期】：2024-04-02
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：null
【实现功能】：
1、备份数据库和文件功能实现

【变更文件】：
新增:src/main/java/com/itco/DtRecovery

【修订要点】：
  1、备份数据库和文件功能实现。

【注意事项】：
  1、无
  --------------------------------------------------------------------------------------------------