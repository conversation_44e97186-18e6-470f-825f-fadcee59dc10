<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.DisasterRecoveryTaskDao">

    <resultMap type="com.itco.dtrecovery.entity.DisasterRecoveryTask" id="DisasterRecoveryTaskMap">
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="backupConfigId" column="backup_config_id" jdbcType="INTEGER"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="backupVersion" column="backup_version" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/>
        <result property="operateUser" column="operate_user" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="backupResultId" column="backup_result_id" jdbcType="INTEGER"/>
        <result property="recoveryHostId" column="recovery_host_id" jdbcType="INTEGER"/>
        <result property="recoveryDirectory" column="recovery_directory" jdbcType="VARCHAR"/>
        <result property="recoverySchame" column="recovery_schame" jdbcType="VARCHAR"/>
        <result property="recoveryTable" column="recovery_table" jdbcType="VARCHAR"/>
        <result property="elementStr" column="element_str" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="DisasterRecoveryTaskMap">
        select version,
               backup_config_id,
               task_type,
               status,
               error_message,
               backup_version,
               create_time,
               update_time,
               operate_user,
               remark,
               backup_result_id,
               recovery_host_id,
               recovery_directory,
               recovery_schame,
               recovery_table,
               element_str
        from disaster.disaster_recovery_task
        where version = #{version}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="DisasterRecoveryTaskMap">
        select
        version, backup_config_id, task_type, status, error_message, backup_version, create_time, update_time,
        operate_user, remark, backup_result_id, recovery_host_id, recovery_directory, recovery_schame, recovery_table,
        element_str
        from disaster.disaster_recovery_task
        <where>
            <if test="disasterRecoveryTask.version != null and disasterRecoveryTask.version != ''">
                and version = #{disasterRecoveryTask.version}
            </if>
            <if test="disasterRecoveryTask.taskType != null">
                and task_type = #{disasterRecoveryTask.taskType}
            </if>
            <if test="disasterRecoveryTask.backupConfigId != null">
                and backup_config_id = #{disasterRecoveryTask.backupConfigId}
            </if>
            <if test="disasterRecoveryTask.status != null and disasterRecoveryTask.status != ''">
                and status = #{disasterRecoveryTask.status}
            </if>
            <if test="disasterRecoveryTask.errorMessage != null and disasterRecoveryTask.errorMessage != ''">
                and error_message = #{disasterRecoveryTask.errorMessage}
            </if>
            <if test="disasterRecoveryTask.backupVersion != null and disasterRecoveryTask.backupVersion != ''">
                and backup_version = #{disasterRecoveryTask.backupVersion}
            </if>
            <if test="disasterRecoveryTask.createTime != null">
                and create_time = #{disasterRecoveryTask.createTime}
            </if>
            <if test="disasterRecoveryTask.updateTime != null">
                and update_time = #{disasterRecoveryTask.updateTime}
            </if>
            <if test="disasterRecoveryTask.operateUser != null and disasterRecoveryTask.operateUser != ''">
                and operate_user = #{disasterRecoveryTask.operateUser}
            </if>
            <if test="disasterRecoveryTask.remark != null and disasterRecoveryTask.remark != ''">
                and remark = #{disasterRecoveryTask.remark}
            </if>
            <if test="disasterRecoveryTask.backupResultId != null">
                and backup_result_id = #{disasterRecoveryTask.backupResultId}
            </if>
            <if test="disasterRecoveryTask.recoveryHostId != null">
                and recovery_host_id = #{disasterRecoveryTask.recoveryHostId}
            </if>
            <if test="disasterRecoveryTask.recoveryDirectory != null and disasterRecoveryTask.recoveryDirectory != ''">
                and recovery_directory = #{disasterRecoveryTask.recoveryDirectory}
            </if>
            <if test="disasterRecoveryTask.recoverySchame != null and disasterRecoveryTask.recoverySchame != ''">
                and recovery_schame = #{disasterRecoveryTask.recoverySchame}
            </if>
            <if test="disasterRecoveryTask.recoveryTable != null and disasterRecoveryTask.recoveryTable != ''">
                and recovery_table = #{disasterRecoveryTask.recoveryTable}
            </if>
            <if test="disasterRecoveryTask.elementStr != null and disasterRecoveryTask.elementStr != ''">
                and element_str = #{disasterRecoveryTask.elementStr}
            </if>
        </where>
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByMap" resultMap="DisasterRecoveryTaskMap" parameterType="java.util.Map">
        select
        a.version, a.backup_config_id, a.task_type, a.status, a.error_message, a.backup_version, a.create_time, a.update_time,
        a.operate_user, a.remark, a.backup_result_id, a.recovery_host_id, a.recovery_directory, a.recovery_schame, a.recovery_table,
        a.element_str
        from disaster.disaster_recovery_task a,disaster.backup_config b
        where a.backup_config_id = b.backup_config_id
        <if test="status != null and status != ''">
            and a.status = #{status}
        </if>
        <if test="storageAvailableZone != null and storageAvailableZone != ''">
            and #{storageAvailableZone} = ANY (SELECT * FROM regexp_split_to_table(b.storage_available_zone, ',') AS
            split_values)
        </if>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.disaster_recovery_task
        <where>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="taskType != null and taskType != ''">
                and task_type = #{taskType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                and error_message = #{errorMessage}
            </if>
            <if test="backupVersion != null and backupVersion != ''">
                and backup_version = #{backupVersion}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="operateUser != null and operateUser != ''">
                and operate_user = #{operateUser}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="backupResultId != null">
                and backup_result_id = #{backupResultId}
            </if>
            <if test="recoveryHostId != null">
                and recovery_host_id = #{recoveryHostId}
            </if>
            <if test="recoveryDirectory != null and recoveryDirectory != ''">
                and recovery_directory = #{recoveryDirectory}
            </if>
            <if test="recoverySchame != null and recoverySchame != ''">
                and recovery_schame = #{recoverySchame}
            </if>
            <if test="recoveryTable != null and recoveryTable != ''">
                and recovery_table = #{recoveryTable}
            </if>
            <if test="elementStr != null and elementStr != ''">
                and element_str = #{elementStr}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="version" useGeneratedKeys="true">
        insert into disaster.disaster_recovery_task(version,backup_config_id, task_type, status, error_message, backup_version,
                                           create_time, update_time, operate_user, remark, backup_result_id,
                                           recovery_host_id, recovery_directory, recovery_schame, recovery_table,
                                           element_str)
        values (#{version},#{backupConfigId}, #{taskType}, #{status}, #{errorMessage}, #{backupVersion}, #{createTime},
                #{updateTime}, #{operateUser}, #{remark}, #{backupResultId}, #{recoveryHostId}, #{recoveryDirectory},
                #{recoverySchame}, #{recoveryTable}, #{elementStr})
    </insert>

    <insert id="insertBatch" keyProperty="version" useGeneratedKeys="true">
        insert into disaster.disaster_recovery_task(backup_config_id, task_type, status, error_message, backup_version,
        create_time, update_time, operate_user, remark, backup_result_id, recovery_host_id, recovery_directory,
        recovery_schame, recovery_table, element_str)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.taskType}, #{entity.status}, #{entity.errorMessage},
            #{entity.backupVersion}, #{entity.createTime}, #{entity.updateTime}, #{entity.operateUser},
            #{entity.remark}, #{entity.backupResultId}, #{entity.recoveryHostId}, #{entity.recoveryDirectory},
            #{entity.recoverySchame}, #{entity.recoveryTable}, #{entity.elementStr})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="version" useGeneratedKeys="true">
        insert into disaster.disaster_recovery_task(version, backup_config_id, task_type, status, error_message, backup_version,
        create_time, update_time, operate_user, remark, backup_result_id, recovery_host_id, recovery_directory,
        recovery_schame, recovery_table, element_str)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.version}, #{entity.backupConfigId}, #{entity.taskType}, #{entity.status}, #{entity.errorMessage},
            #{entity.backupVersion}, #{entity.createTime}, #{entity.updateTime}, #{entity.operateUser},
            #{entity.remark}, #{entity.backupResultId}, #{entity.recoveryHostId}, #{entity.recoveryDirectory},
            #{entity.recoverySchame}, #{entity.recoveryTable}, #{entity.elementStr})
        </foreach>
        on conflict (version)
        do update set
        backup_config_id = excluded.backup_config_id,
        task_type = excluded.task_type,
        status = excluded.status,
        error_message = excluded.error_message,
        backup_version = excluded.backup_version,
        create_time = excluded.create_time,
        update_time = excluded.update_time,
        operate_user = excluded.operate_user,
        remark = excluded.remark,
        backup_result_id = excluded.backup_result_id,
        recovery_host_id = excluded.recovery_host_id,
        recovery_directory = excluded.recovery_directory,
        recovery_schame = excluded.recovery_schame,
        recovery_table = excluded.recovery_table,
        element_str = excluded.element_str;
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.disaster_recovery_task
        <set>
            <if test="backupConfigId != null">
                backup_config_id = #{backupConfigId},
            </if>
            <if test="taskType != null and taskType != ''">
                task_type = #{taskType},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                error_message = #{errorMessage},
            </if>
            <if test="backupVersion != null and backupVersion != ''">
                backup_version = #{backupVersion},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
            <if test="operateUser != null and operateUser != ''">
                operate_user = #{operateUser},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="backupResultId != null">
                backup_result_id = #{backupResultId},
            </if>
            <if test="recoveryHostId != null">
                recovery_host_id = #{recoveryHostId},
            </if>
            <if test="recoveryDirectory != null and recoveryDirectory != ''">
                recovery_directory = #{recoveryDirectory},
            </if>
            <if test="recoverySchame != null and recoverySchame != ''">
                recovery_schame = #{recoverySchame},
            </if>
            <if test="recoveryTable != null and recoveryTable != ''">
                recovery_table = #{recoveryTable},
            </if>
            <if test="elementStr != null and elementStr != ''">
                element_str = #{elementStr},
            </if>
        </set>
        where version = #{version}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.disaster_recovery_task
        where version = #{version}
    </delete>

</mapper>

