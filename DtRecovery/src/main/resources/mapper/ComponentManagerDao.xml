<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.ComponentManagerDao">

    <resultMap type="com.itco.dtrecovery.entity.ComponentManager" id="ComponentManagerMap">
        <result property="componentId" column="component_id" jdbcType="INTEGER"/>
        <result property="clusterId" column="cluster_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="mode" column="mode" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="exitDate" column="exit_date" jdbcType="VARCHAR"/>
        <result property="storageAvailableZone" column="storage_available_zone" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="hostId" column="host_id" jdbcType="INTEGER"/>
        <result property="port" column="port" jdbcType="VARCHAR"/>
        <result property="cron" column="cron" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ComponentManagerMap">
        select component_id,
               cluster_id,
               type,
               name,
               mode,
               address,
               status,
               create_date,
               exit_date,
               storage_available_zone,
               description,
               host_id,
               port,
               cron
        from disaster.component_manager
        where component_id = #{componentId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ComponentManagerMap">
        select
        component_id, cluster_id, type, name, mode, address, status, create_date, exit_date, storage_available_zone,
        description, host_id, port, cron
        from disaster.component_manager
    </select>

    <!--增量查询数据-->
    <select id="queryAllByIncrement" resultMap="ComponentManagerMap">
        select
        component_id, cluster_id, type, name, mode, address, status, create_date, exit_date, storage_available_zone,
        description, host_id, port, cron
        from disaster.component_manager
        where component_id > #{componentId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.component_manager
        <where>
            <if test="componentId != null">
                and component_id = #{componentId}
            </if>
            <if test="clusterId != null">
                and cluster_id = #{clusterId}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="mode != null and mode != ''">
                and mode = #{mode}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="exitDate != null and exitDate != ''">
                and exit_date = #{exitDate}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and storage_available_zone = #{storageAvailableZone}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="hostId != null">
                and host_id = #{hostId}
            </if>
            <if test="port != null and port != ''">
                and port = #{port}
            </if>
            <if test="cron != null and cron != ''">
                and cron = #{cron}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_manager(cluster_id, type, name, mode, address, status, create_date, exit_date,
                                      storage_available_zone, description,  host_id, port, cron)
        values (#{clusterId}, #{type}, #{name}, #{mode}, #{address}, #{status}, #{createDate}, #{exitDate},
                #{storageAvailableZone}, #{description},  #{hostId}, #{port}, #{cron})
    </insert>

    <insert id="insertBatch" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_manager(cluster_id, type, name, mode, address, status, create_date, exit_date,
        storage_available_zone, description,  host_id, port, cron)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.type}, #{entity.name}, #{entity.mode}, #{entity.address}, #{entity.status},
            #{entity.createDate}, #{entity.exitDate}, #{entity.storageAvailableZone}, #{entity.description},
            #{entity.hostId}, #{entity.port}, #{entity.cron})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_manager(cluster_id, type, name, mode, address, status, create_date, exit_date,
        storage_available_zone, description,  host_id, port, cron)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.type}, #{entity.name}, #{entity.mode}, #{entity.address}, #{entity.status},
            #{entity.createDate}, #{entity.exitDate}, #{entity.storageAvailableZone}, #{entity.description},
            #{entity.hostId}, #{entity.port}, #{entity.cron})
        </foreach>
        on duplicate key update
        cluster_id = values(cluster_id),
        type = values(type),
        name = values(name),
        mode = values(mode),
        address = values(address),
        status = values(status),
        create_date = values(create_date),
        exit_date = values(exit_date),
        storage_available_zone = values(storage_available_zone),
        description = values(description),
        host_id = values(host_id),
        port = values(port),
        cron = values(cron)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.component_manager
        <set>
            <if test="clusterId != null">
                cluster_id = #{clusterId},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="mode != null and mode != ''">
                mode = #{mode},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="exitDate != null and exitDate != ''">
                exit_date = #{exitDate},
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                storage_available_zone = #{storageAvailableZone},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="hostId != null">
                host_id = #{hostId},
            </if>
            <if test="port != null and port != ''">
                port = #{port},
            </if>
            <if test="cron != null and cron != ''">
                cron = #{cron},
            </if>
        </set>
        where component_id = #{componentId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.component_manager
        where component_id = #{componentId}
    </delete>

</mapper>

