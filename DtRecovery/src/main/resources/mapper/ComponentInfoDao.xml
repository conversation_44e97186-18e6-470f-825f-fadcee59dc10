<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.ComponentInfoDao">

    <resultMap type="com.itco.dtrecovery.entity.ComponentInfo" id="ComponentInfoMap">
        <result property="componentId" column="component_id" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
        <result property="processId" column="process_id" jdbcType="VARCHAR"/>
        <result property="uptime" column="uptime" jdbcType="VARCHAR"/>
        <result property="totalDiskSpace" column="total_disk_space" jdbcType="VARCHAR"/>
        <result property="usedDiskSpace" column="used_disk_space" jdbcType="VARCHAR"/>
        <result property="availableDiskSpace" column="available_disk_space" jdbcType="VARCHAR"/>
        <result property="diskUsage" column="disk_usage" jdbcType="VARCHAR"/>
        <result property="totalMemory" column="total_memory" jdbcType="VARCHAR"/>
        <result property="memoryUsage" column="memory_usage" jdbcType="VARCHAR"/>
        <result property="cpuCores" column="cpu_cores" jdbcType="VARCHAR"/>
        <result property="cpuUsage" column="cpu_usage" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ComponentInfoMap">
        select component_id,
               start_time,
               process_id,
               uptime,
               total_disk_space,
               used_disk_space,
               available_disk_space,
               disk_usage,
               total_memory,
               memory_usage,
               cpu_cores,
               cpu_usage,
               create_date,
               update_date
        from disaster.component_info
        where component_id = #{componentId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByIncrement" resultMap="ComponentInfoMap">
        select
        component_id, start_time, process_id, uptime, total_disk_space, used_disk_space, available_disk_space,
        disk_usage, total_memory, memory_usage, cpu_cores, cpu_usage, create_date, update_date
        from disaster.component_info
        <where>
            <if test="componentId != null">
                and component_id = #{componentId}
            </if>
        </where>
        order by component_id
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.component_info
        <where>
            <if test="componentId != null">
                and component_id = #{componentId}
            </if>
            <if test="startTime != null and startTime != ''">
                and start_time = #{startTime}
            </if>
            <if test="processId != null and processId != ''">
                and process_id = #{processId}
            </if>
            <if test="uptime != null and uptime != ''">
                and uptime = #{uptime}
            </if>
            <if test="totalDiskSpace != null and totalDiskSpace != ''">
                and total_disk_space = #{totalDiskSpace}
            </if>
            <if test="usedDiskSpace != null and usedDiskSpace != ''">
                and used_disk_space = #{usedDiskSpace}
            </if>
            <if test="availableDiskSpace != null and availableDiskSpace != ''">
                and available_disk_space = #{availableDiskSpace}
            </if>
            <if test="diskUsage != null and diskUsage != ''">
                and disk_usage = #{diskUsage}
            </if>
            <if test="totalMemory != null and totalMemory != ''">
                and total_memory = #{totalMemory}
            </if>
            <if test="memoryUsage != null and memoryUsage != ''">
                and memory_usage = #{memoryUsage}
            </if>
            <if test="cpuCores != null and cpuCores != ''">
                and cpu_cores = #{cpuCores}
            </if>
            <if test="cpuUsage != null and cpuUsage != ''">
                and cpu_usage = #{cpuUsage}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="updateDate != null and updateDate != ''">
                and update_date = #{updateDate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_info(start_time, process_id, uptime, total_disk_space, used_disk_space,
                                   available_disk_space, disk_usage, total_memory, memory_usage, cpu_cores, cpu_usage,
                                   create_date, update_date)
        values (#{startTime}, #{processId}, #{uptime}, #{totalDiskSpace}, #{usedDiskSpace}, #{availableDiskSpace},
                #{diskUsage}, #{totalMemory}, #{memoryUsage}, #{cpuCores}, #{cpuUsage}, #{createDate}, #{updateDate})
    </insert>

    <insert id="insertBatch" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_info(start_time, process_id, uptime, total_disk_space, used_disk_space,
        available_disk_space, disk_usage, total_memory, memory_usage, cpu_cores, cpu_usage, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.startTime}, #{entity.processId}, #{entity.uptime}, #{entity.totalDiskSpace},
            #{entity.usedDiskSpace}, #{entity.availableDiskSpace}, #{entity.diskUsage}, #{entity.totalMemory},
            #{entity.memoryUsage}, #{entity.cpuCores}, #{entity.cpuUsage}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="componentId" useGeneratedKeys="true">
        insert into disaster.component_info(component_id,start_time, process_id, uptime, total_disk_space, used_disk_space,
        available_disk_space, disk_usage, total_memory, memory_usage, cpu_cores, cpu_usage, create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.componentId},#{entity.startTime}, #{entity.processId}, #{entity.uptime}, #{entity.totalDiskSpace},
            #{entity.usedDiskSpace}, #{entity.availableDiskSpace}, #{entity.diskUsage}, #{entity.totalMemory},
            #{entity.memoryUsage}, #{entity.cpuCores}, #{entity.cpuUsage}, #{entity.createDate}, #{entity.updateDate})
        </foreach>
        on conflict (component_id)
        do update set
        start_time = excluded.start_time,
        process_id = excluded.process_id,
        uptime = excluded.uptime,
        total_disk_space = excluded.total_disk_space,
        used_disk_space = excluded.used_disk_space,
        available_disk_space = excluded.available_disk_space,
        disk_usage = excluded.disk_usage,
        total_memory = excluded.total_memory,
        memory_usage = excluded.memory_usage,
        cpu_cores = excluded.cpu_cores,
        cpu_usage = excluded.cpu_usage,
        create_date = excluded.create_date,
        update_date = excluded.update_date
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.component_info
        <set>
            <if test="startTime != null and startTime != ''">
                start_time = #{startTime},
            </if>
            <if test="processId != null and processId != ''">
                process_id = #{processId},
            </if>
            <if test="uptime != null and uptime != ''">
                uptime = #{uptime},
            </if>
            <if test="totalDiskSpace != null and totalDiskSpace != ''">
                total_disk_space = #{totalDiskSpace},
            </if>
            <if test="usedDiskSpace != null and usedDiskSpace != ''">
                used_disk_space = #{usedDiskSpace},
            </if>
            <if test="availableDiskSpace != null and availableDiskSpace != ''">
                available_disk_space = #{availableDiskSpace},
            </if>
            <if test="diskUsage != null and diskUsage != ''">
                disk_usage = #{diskUsage},
            </if>
            <if test="totalMemory != null and totalMemory != ''">
                total_memory = #{totalMemory},
            </if>
            <if test="memoryUsage != null and memoryUsage != ''">
                memory_usage = #{memoryUsage},
            </if>
            <if test="cpuCores != null and cpuCores != ''">
                cpu_cores = #{cpuCores},
            </if>
            <if test="cpuUsage != null and cpuUsage != ''">
                cpu_usage = #{cpuUsage},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="updateDate != null and updateDate != ''">
                update_date = #{updateDate},
            </if>
        </set>
        where component_id = #{componentId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.component_info
        where component_id = #{componentId}
    </delete>

</mapper>

