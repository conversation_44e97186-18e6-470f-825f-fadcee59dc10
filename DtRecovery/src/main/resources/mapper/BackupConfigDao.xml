<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.BackupConfigDao">

    <resultMap type="com.itco.dtrecovery.entity.BackupConfig" id="BackupConfigMap">
        <result property="backupConfigId" column="backup_config_id" jdbcType="INTEGER"/>
        <result property="backupType" column="backup_type" jdbcType="VARCHAR"/>
        <result property="dataType" column="data_type" jdbcType="VARCHAR"/>
        <result property="dataLevel" column="data_level" jdbcType="VARCHAR"/>
        <result property="cron" column="cron" jdbcType="INTEGER"/>
        <result property="compressionEnabled" column="compression_enabled" jdbcType="VARCHAR"/>
        <result property="encryptionEnabled" column="encryption_enabled" jdbcType="VARCHAR"/>
        <result property="encryptionPassword" column="encryption_password" jdbcType="VARCHAR"/>
        <result property="verifyEnabled" column="verify_enabled" jdbcType="VARCHAR"/>
        <result property="backupStatus" column="backup_status" jdbcType="VARCHAR"/>
        <result property="backupScope" column="backup_scope" jdbcType="VARCHAR"/>
        <result property="sourceHostId" column="source_host_id" jdbcType="VARCHAR"/>
        <result property="sourceDirectory" column="source_directory" jdbcType="VARCHAR"/>
        <result property="targetHostId" column="target_host_id" jdbcType="VARCHAR"/>
        <result property="targetDirectory" column="target_directory" jdbcType="VARCHAR"/>
        <result property="backupFileName" column="backup_file_name" jdbcType="VARCHAR"/>
        <result property="storageAvailableZone" column="storage_available_zone" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BackupConfigMap">
        select backup_config_id,
               backup_type,
               data_type,
               data_level,
               cron,
               compression_enabled,
               encryption_enabled,
               encryption_password,
               verify_enabled,
               backup_status,
               backup_scope,
               source_host_id,
               source_directory,
               target_host_id,
               target_directory,
               backup_file_name,
               storage_available_zone,
               create_time,
               last_trigger_date,
               remark
        from disaster.backup_config
        where backup_config_id = #{backupConfigId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BackupConfigMap">
        select
        backup_config_id, backup_type, data_type, data_level, cron, compression_enabled,
        encryption_enabled, encryption_password, verify_enabled, backup_status, backup_scope, source_host_id, source_directory,
        target_host_id, target_directory, backup_file_name, storage_available_zone, create_time,last_trigger_date,
        remark
        from disaster.backup_config
        <where>
            <if test="backupStatus != null and backupStatus != ''">
                and backup_status = #{backupStatus}
            </if>
        </where>
    </select>

    <!--增量查询数据-->
    <select id="queryAllByIncrement" resultMap="BackupConfigMap">
        select
        backup_config_id, backup_type, data_type, data_level, cron, compression_enabled,
        encryption_enabled, encryption_password, verify_enabled, backup_status, backup_scope, source_host_id, source_directory,
        target_host_id, target_directory, backup_file_name, storage_available_zone, create_time,last_trigger_date,
        remark
        from disaster.backup_config
        <where>
            <if test="backupConfigId != null">
                and backup_config_id > #{backupConfigId}
            </if>
            <if test="backupStatus != null and backupStatus != ''">
                and backup_status = #{backupStatus}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and #{storageAvailableZone} = ANY (SELECT * FROM regexp_split_to_table(storage_available_zone, ',') AS split_values)
            </if>
        </where>
        order by backup_config_id;
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.backup_config
        <where>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="backupType != null and backupType != ''">
                and backup_type = #{backupType}
            </if>
            <if test="dataType != null and dataType != ''">
                and data_type = #{dataType}
            </if>
            <if test="dataLevel != null and dataLevel != ''">
                and data_level = #{dataLevel}
            </if>
            <if test="backupFrequencyCron != null">
                and backup_frequency_cron = #{backupFrequencyCron}
            </if>
            <if test="compressionEnabled != null and compressionEnabled != ''">
                and compression_enabled = #{compressionEnabled}
            </if>
            <if test="encryptionEnabled != null and encryptionEnabled != ''">
                and encryption_enabled = #{encryptionEnabled}
            </if>
            <if test="verifyEnabled != null and verifyEnabled != ''">
                and verify_enabled = #{verifyEnabled}
            </if>
            <if test="backupStatus != null and backupStatus != ''">
                and backup_status = #{backupStatus}
            </if>
            <if test="backupScope != null and backupScope != ''">
                and backup_scope = #{backupScope}
            </if>
            <if test="sourceHostId != null and sourceHostId != ''">
                and source_host_id = #{sourceHostId}
            </if>
            <if test="sourceDirectory != null and sourceDirectory != ''">
                and source_directory = #{sourceDirectory}
            </if>
            <if test="targetHostId != null and targetHostId != ''">
                and target_host_id = #{targetHostId}
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                and target_directory = #{targetDirectory}
            </if>
            <if test="backupFileName != null and backupFileName != ''">
                and backup_file_name = #{backupFileName}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and storage_available_zone = #{storageAvailableZone}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="backupConfigId" useGeneratedKeys="true">
        insert into disaster.backup_config(backup_type, data_type, data_level, backup_frequency_cron, compression_enabled,
                                  encryption_enabled, verify_enabled, backup_status, backup_scope, source_host_id,
                                  source_directory, target_host_id, target_directory, backup_file_name,
                                  storage_available_zone, create_time, remark)
        values (#{backupType}, #{dataType}, #{dataLevel}, #{backupFrequencyCron}, #{compressionEnabled},
                #{encryptionEnabled}, #{verifyEnabled}, #{backupStatus}, #{backupScope}, #{sourceHostId},
                #{sourceDirectory}, #{targetHostId}, #{targetDirectory}, #{backupFileName}, #{storageAvailableZone},
                #{createTime}, #{remark})
    </insert>

    <insert id="insertBatch" keyProperty="backupConfigId" useGeneratedKeys="true">
        insert into disaster.backup_config(backup_type, data_type, data_level, backup_frequency_cron, compression_enabled,
        encryption_enabled, verify_enabled, backup_status, backup_scope, source_host_id, source_directory,
        target_host_id, target_directory, backup_file_name, storage_available_zone, create_time, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupType}, #{entity.dataType}, #{entity.dataLevel}, #{entity.backupFrequencyCron},
            #{entity.compressionEnabled}, #{entity.encryptionEnabled}, #{entity.verifyEnabled}, #{entity.backupStatus},
            #{entity.backupScope}, #{entity.sourceHostId}, #{entity.sourceDirectory}, #{entity.targetHostId},
            #{entity.targetDirectory}, #{entity.backupFileName}, #{entity.storageAvailableZone}, #{entity.createTime},
            #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="backupConfigId" useGeneratedKeys="true">
        insert into disaster.backup_config(backup_type, data_type, data_level, backup_frequency_cron, compression_enabled,
        encryption_enabled, verify_enabled, backup_status, backup_scope, source_host_id, source_directory,
        target_host_id, target_directory, backup_file_name, storage_available_zone, create_time, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupType}, #{entity.dataType}, #{entity.dataLevel}, #{entity.backupFrequencyCron},
            #{entity.compressionEnabled}, #{entity.encryptionEnabled}, #{entity.verifyEnabled}, #{entity.backupStatus},
            #{entity.backupScope}, #{entity.sourceHostId}, #{entity.sourceDirectory}, #{entity.targetHostId},
            #{entity.targetDirectory}, #{entity.backupFileName}, #{entity.storageAvailableZone}, #{entity.createTime},
            #{entity.remark})
        </foreach>
        on duplicate key update
        backup_type = values(backup_type),
        data_type = values(data_type),
        data_level = values(data_level),
        backup_frequency_cron = values(backup_frequency_cron),
        compression_enabled = values(compression_enabled),
        encryption_enabled = values(encryption_enabled),
        verify_enabled = values(verify_enabled),
        backup_status = values(backup_status),
        backup_scope = values(backup_scope),
        source_host_id = values(source_host_id),
        source_directory = values(source_directory),
        target_host_id = values(target_host_id),
        target_directory = values(target_directory),
        backup_file_name = values(backup_file_name),
        storage_available_zone = values(storage_available_zone),
        create_time = values(create_time),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.backup_config
        <set>
            <if test="backupType != null and backupType != ''">
                backup_type = #{backupType},
            </if>
            <if test="dataType != null and dataType != ''">
                data_type = #{dataType},
            </if>
            <if test="dataLevel != null and dataLevel != ''">
                data_level = #{dataLevel},
            </if>
            <if test="backupFrequencyCron != null">
                backup_frequency_cron = #{backupFrequencyCron},
            </if>
            <if test="compressionEnabled != null and compressionEnabled != ''">
                compression_enabled = #{compressionEnabled},
            </if>
            <if test="encryptionEnabled != null and encryptionEnabled != ''">
                encryption_enabled = #{encryptionEnabled},
            </if>
            <if test="verifyEnabled != null and verifyEnabled != ''">
                verify_enabled = #{verifyEnabled},
            </if>
            <if test="backupStatus != null and backupStatus != ''">
                backup_status = #{backupStatus},
            </if>
            <if test="backupScope != null and backupScope != ''">
                backup_scope = #{backupScope},
            </if>
            <if test="sourceHostId != null and sourceHostId != ''">
                source_host_id = #{sourceHostId},
            </if>
            <if test="sourceDirectory != null and sourceDirectory != ''">
                source_directory = #{sourceDirectory},
            </if>
            <if test="targetHostId != null and targetHostId != ''">
                target_host_id = #{targetHostId},
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                target_directory = #{targetDirectory},
            </if>
            <if test="backupFileName != null and backupFileName != ''">
                backup_file_name = #{backupFileName},
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                storage_available_zone = #{storageAvailableZone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where backup_config_id = #{backupConfigId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.backup_config
        where backup_config_id = #{backupConfigId}
    </delete>

</mapper>

