<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.BackupHostInfoDao">

    <resultMap type="com.itco.dtrecovery.entity.BackupHostInfo" id="BackupHostInfoMap">
        <result property="hostId" column="host_id" jdbcType="INTEGER"/>
        <result property="clusterId" column="cluster_id" jdbcType="INTEGER"/>
        <result property="sshIp" column="ssh_ip" jdbcType="VARCHAR"/>
        <result property="sshPort" column="ssh_port" jdbcType="VARCHAR"/>
        <result property="sshUsername" column="ssh_username" jdbcType="VARCHAR"/>
        <result property="sshPublicPassword" column="ssh_public_password" jdbcType="VARCHAR"/>
        <result property="sshPassword" column="ssh_password" jdbcType="VARCHAR"/>
        <result property="storageAvailableZone" column="storage_available_zone" jdbcType="VARCHAR"/>
        <result property="hostName" column="host_name" jdbcType="VARCHAR"/>
        <result property="home" column="home" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BackupHostInfoMap">
        select host_id,
               cluster_id,
               ssh_ip,
               ssh_port,
               ssh_username,
               ssh_public_password,
               ssh_password,
               storage_available_zone,
               host_name,
               home
        from disaster.backup_host_info
        where host_id = #{hostId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BackupHostInfoMap">
        select
        host_id, cluster_id, ssh_ip, ssh_port, ssh_username, ssh_public_password, ssh_password, storage_available_zone,host_name,home
        from disaster.backup_host_info
    </select>

    <!--增量查询数据-->
    <select id="queryAllByIncrement" resultMap="BackupHostInfoMap">
        select
        host_id, cluster_id, ssh_ip, ssh_port, ssh_username, ssh_public_password, ssh_password, storage_available_zone,host_name,home
        from disaster.backup_host_info
        <where>
            <if test="hostId != null">
                and host_id > #{hostId}
            </if>
        </where>
        order by host_id
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.backup_host_info
        <where>
            <if test="hostId != null">
                and host_id = #{hostId}
            </if>
            <if test="clusterId != null">
                and cluster_id = #{clusterId}
            </if>
            <if test="sshIp != null and sshIp != ''">
                and ssh_ip = #{sshIp}
            </if>
            <if test="sshPort != null and sshPort != ''">
                and ssh_port = #{sshPort}
            </if>
            <if test="sshUsername != null and sshUsername != ''">
                and ssh_username = #{sshUsername}
            </if>
            <if test="sshPublicPassword != null and sshPublicPassword != ''">
                and ssh_public_password = #{sshPublicPassword}
            </if>
            <if test="sshPassword != null and sshPassword != ''">
                and ssh_password = #{sshPassword}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and storage_available_zone = #{storageAvailableZone}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="hostId" useGeneratedKeys="true">
        insert into disaster.backup_host_info(cluster_id, ssh_ip, ssh_port, ssh_username, ssh_public_password, ssh_password,
                                     storage_available_zone)
        values (#{clusterId}, #{sshIp}, #{sshPort}, #{sshUsername}, #{sshPublicPassword}, #{sshPassword},
                #{storageAvailableZone})
    </insert>

    <insert id="insertBatch" keyProperty="hostId" useGeneratedKeys="true">
        insert into disaster.backup_host_info(cluster_id, ssh_ip, ssh_port, ssh_username, ssh_public_password, ssh_password,
        storage_available_zone)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.sshIp}, #{entity.sshPort}, #{entity.sshUsername},
            #{entity.sshPublicPassword}, #{entity.sshPassword}, #{entity.storageAvailableZone})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="hostId" useGeneratedKeys="true">
        insert into disaster.backup_host_info(cluster_id, ssh_ip, ssh_port, ssh_username, ssh_public_password, ssh_password,
        storage_available_zone)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.sshIp}, #{entity.sshPort}, #{entity.sshUsername},
            #{entity.sshPublicPassword}, #{entity.sshPassword}, #{entity.storageAvailableZone})
        </foreach>
        on duplicate key update
        cluster_id = values(cluster_id),
        ssh_ip = values(ssh_ip),
        ssh_port = values(ssh_port),
        ssh_username = values(ssh_username),
        ssh_public_password = values(ssh_public_password),
        ssh_password = values(ssh_password),
        storage_available_zone = values(storage_available_zone)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.backup_host_info
        <set>
            <if test="clusterId != null">
                cluster_id = #{clusterId},
            </if>
            <if test="sshIp != null and sshIp != ''">
                ssh_ip = #{sshIp},
            </if>
            <if test="sshPort != null and sshPort != ''">
                ssh_port = #{sshPort},
            </if>
            <if test="sshUsername != null and sshUsername != ''">
                ssh_username = #{sshUsername},
            </if>
            <if test="sshPublicPassword != null and sshPublicPassword != ''">
                ssh_public_password = #{sshPublicPassword},
            </if>
            <if test="sshPassword != null and sshPassword != ''">
                ssh_password = #{sshPassword},
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                storage_available_zone = #{storageAvailableZone},
            </if>
        </set>
        where host_id = #{hostId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.backup_host_info
        where host_id = #{hostId}
    </delete>

</mapper>

