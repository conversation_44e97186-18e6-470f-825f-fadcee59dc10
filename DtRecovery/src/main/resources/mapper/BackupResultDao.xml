<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.BackupResultDao">

    <resultMap type="com.itco.dtrecovery.entity.BackupResult" id="BackupResultMap">
        <result property="backupResultId" column="backup_result_id" jdbcType="INTEGER"/>
        <result property="backupConfigId" column="backup_config_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="success" column="success" jdbcType="BOOLEAN"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="backupStartTime" column="backup_start_time" jdbcType="VARCHAR"/>
        <result property="backupEndTime" column="backup_end_time" jdbcType="VARCHAR"/>
        <result property="compressionEnabled" column="compression_enabled" jdbcType="VARCHAR"/>
        <result property="encryptionEnabled" column="encryption_enabled" jdbcType="VARCHAR"/>
        <result property="encryptionPassword" column="encryption_password" jdbcType="VARCHAR"/>
        <result property="targetDirectory" column="target_directory" jdbcType="VARCHAR"/>
        <result property="operateUser" column="operate_user" jdbcType="VARCHAR"/>
        <result property="backupCenterStatus" column="backup_center_status" jdbcType="VARCHAR"/>
        <result property="verificationResults" column="verification_results" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BackupResultMap">
        select backup_result_id,
               backup_config_id,
               version,
               success,
               error_message,
               backup_start_time,
               backup_end_time,
               compression_enabled,
               encryption_enabled,
               encryption_password,
               target_directory,
               operate_user,
               backup_center_status,
               verification_results,
               remark
        from disaster.backup_result
        where backup_result_id = #{backupResultId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BackupResultMap">
        select
        backup_result_id, backup_config_id, version, success, error_message, backup_start_time, backup_end_time,
        compression_enabled, encryption_enabled, encryption_password, target_directory, operate_user,
        backup_center_status, verification_results, remark
        from disaster.backup_result order by backup_config_id
    </select>

    <!--增量查询数据-->
    <select id="queryAllByIncrement" resultMap="BackupResultMap">
        select
            backup_result_id, backup_config_id, version, success, error_message, backup_start_time, backup_end_time,
            compression_enabled, encryption_enabled, encryption_password, target_directory, operate_user,
            backup_center_status, verification_results, remark
        from disaster.backup_result
        <where>
            <if test="backupResultId != null">
                and backup_result_id > #{backupResultId}
            </if>
        </where>
        order by backup_config_id
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.backup_result
        <where>
            <if test="backupResultId != null">
                and backup_result_id = #{backupResultId}
            </if>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="success != null">
                and success = #{success}
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                and error_message = #{errorMessage}
            </if>
            <if test="backupStartTime != null and backupStartTime != ''">
                and backup_start_time = #{backupStartTime}
            </if>
            <if test="backupEndTime != null and backupEndTime != ''">
                and backup_end_time = #{backupEndTime}
            </if>
            <if test="compressionEnabled != null and compressionEnabled != ''">
                and compression_enabled = #{compressionEnabled}
            </if>
            <if test="encryptionEnabled != null and encryptionEnabled != ''">
                and encryption_enabled = #{encryptionEnabled}
            </if>
            <if test="encryptionPassword != null and encryptionPassword != ''">
                and encryption_password = #{encryptionPassword}
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                and target_directory = #{targetDirectory}
            </if>
            <if test="operateUser != null and operateUser != ''">
                and operate_user = #{operateUser}
            </if>
            <if test="backupCenterStatus != null and backupCenterStatus != ''">
                and backup_center_status = #{backupCenterStatus}
            </if>
            <if test="verificationResults != null and verificationResults != ''">
                and verification_results = #{verificationResults}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="backupResultId" useGeneratedKeys="true">
        insert into disaster.backup_result(backup_config_id, version, success, error_message, backup_start_time, backup_end_time,
                                  compression_enabled, encryption_enabled, encryption_password, target_directory,
                                  operate_user, backup_center_status, verification_results, remark)
        values (#{backupConfigId}, #{version}, #{success}, #{errorMessage}, #{backupStartTime}, #{backupEndTime},
                #{compressionEnabled}, #{encryptionEnabled}, #{encryptionPassword}, #{targetDirectory}, #{operateUser},
                #{backupCenterStatus}, #{verificationResults}, #{remark})
    </insert>

    <insert id="insertBatch" keyProperty="backupResultId" useGeneratedKeys="true">
        insert into disaster.backup_result(backup_config_id, version, success, error_message, backup_start_time, backup_end_time,
        compression_enabled, encryption_enabled, encryption_password, target_directory, operate_user,
        backup_center_status, verification_results, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.version}, #{entity.success}, #{entity.errorMessage},
            #{entity.backupStartTime}, #{entity.backupEndTime}, #{entity.compressionEnabled},
            #{entity.encryptionEnabled}, #{entity.encryptionPassword}, #{entity.targetDirectory}, #{entity.operateUser},
            #{entity.backupCenterStatus}, #{entity.verificationResults}, #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="backupResultId" useGeneratedKeys="true">
        insert into disaster.backup_result(backup_config_id, version, success, error_message, backup_start_time, backup_end_time,
        compression_enabled, encryption_enabled, encryption_password, target_directory, operate_user,
        backup_center_status, verification_results, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.version}, #{entity.success}, #{entity.errorMessage},
            #{entity.backupStartTime}, #{entity.backupEndTime}, #{entity.compressionEnabled},
            #{entity.encryptionEnabled}, #{entity.encryptionPassword}, #{entity.targetDirectory}, #{entity.operateUser},
            #{entity.backupCenterStatus}, #{entity.verificationResults}, #{entity.remark})
        </foreach>
        on duplicate key update
        backup_config_id = values(backup_config_id),
        version = values(version),
        success = values(success),
        error_message = values(error_message),
        backup_start_time = values(backup_start_time),
        backup_end_time = values(backup_end_time),
        compression_enabled = values(compression_enabled),
        encryption_enabled = values(encryption_enabled),
        encryption_password = values(encryption_password),
        target_directory = values(target_directory),
        operate_user = values(operate_user),
        backup_center_status = values(backup_center_status),
        verification_results = values(verification_results),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.backup_result
        <set>
            <if test="backupConfigId != null">
                backup_config_id = #{backupConfigId},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="success != null">
                success = #{success},
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                error_message = #{errorMessage},
            </if>
            <if test="backupStartTime != null and backupStartTime != ''">
                backup_start_time = #{backupStartTime},
            </if>
            <if test="backupEndTime != null and backupEndTime != ''">
                backup_end_time = #{backupEndTime},
            </if>
            <if test="compressionEnabled != null and compressionEnabled != ''">
                compression_enabled = #{compressionEnabled},
            </if>
            <if test="encryptionEnabled != null and encryptionEnabled != ''">
                encryption_enabled = #{encryptionEnabled},
            </if>
            <if test="encryptionPassword != null and encryptionPassword != ''">
                encryption_password = #{encryptionPassword},
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                target_directory = #{targetDirectory},
            </if>
            <if test="operateUser != null and operateUser != ''">
                operate_user = #{operateUser},
            </if>
            <if test="backupCenterStatus != null and backupCenterStatus != ''">
                backup_center_status = #{backupCenterStatus},
            </if>
            <if test="verificationResults != null and verificationResults != ''">
                verification_results = #{verificationResults},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where backup_result_id = #{backupResultId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.backup_result
        where backup_result_id = #{backupResultId}
    </delete>

</mapper>

