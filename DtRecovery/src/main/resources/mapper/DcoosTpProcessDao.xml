<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.DcoosTpProcessDao">

    <resultMap type="com.itco.dtrecovery.entity.DcoosTpProcess" id="DcoosTpProcessMap">
        <result property="processId" column="process_id" jdbcType="INTEGER"/>
        <result property="clusterId" column="cluster_id" jdbcType="INTEGER"/>
        <result property="moduleId" column="module_id" jdbcType="INTEGER"/>
        <result property="hostId" column="host_id" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="billingLineId" column="billing_line_id" jdbcType="INTEGER"/>
        <result property="perl" column="perl" jdbcType="INTEGER"/>
        <result property="moduleCode" column="module_code" jdbcType="VARCHAR"/>
        <result property="systemProcessId" column="system_process_id" jdbcType="INTEGER"/>
        <result property="processName" column="process_name" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="exitDate" column="exit_date" jdbcType="VARCHAR"/>
        <result property="hostName" column="host_name" jdbcType="VARCHAR"/>
        <result property="isPause" column="is_pause" jdbcType="VARCHAR"/>
        <result property="storageAvailableZone" column="storage_available_zone" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="DcoosTpProcessMap">
        select process_id,
               cluster_id,
               module_id,
               host_id,
               status,
               type,
               billing_line_id,
               perl,
               module_code,
               system_process_id,
               process_name,
               update_date,
               create_date,
               exit_date,
               host_name,
               is_pause,
               storage_available_zone,
               version
        from config.dcoos_tp_process
        where process_id = #{processId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="DcoosTpProcessMap">
        select
        process_id, cluster_id, module_id, host_id, status, type, billing_line_id, perl, module_code, system_process_id,
        process_name, update_date, create_date, exit_date, host_name, is_pause, storage_available_zone, version
        from config.dcoos_tp_process
<!--        <where>-->
<!--            <if test="dcoosTpProcess.processId != null">-->
<!--                and process_id = #{dcoosTpProcess.processId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.clusterId != null">-->
<!--                and cluster_id = #{dcoosTpProcess.clusterId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.moduleId != null">-->
<!--                and module_id = #{dcoosTpProcess.moduleId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.hostId != null">-->
<!--                and host_id = #{dcoosTpProcess.hostId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.status != null and dcoosTpProcess.status != ''">-->
<!--                and status = #{dcoosTpProcess.status}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.type != null and dcoosTpProcess.type != ''">-->
<!--                and type = #{dcoosTpProcess.type}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.billingLineId != null">-->
<!--                and billing_line_id = #{dcoosTpProcess.billingLineId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.perl != null">-->
<!--                and perl = #{dcoosTpProcess.perl}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.moduleCode != null and dcoosTpProcess.moduleCode != ''">-->
<!--                and module_code = #{dcoosTpProcess.moduleCode}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.systemProcessId != null">-->
<!--                and system_process_id = #{dcoosTpProcess.systemProcessId}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.processName != null and dcoosTpProcess.processName != ''">-->
<!--                and process_name = #{dcoosTpProcess.processName}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.updateDate != null and dcoosTpProcess.updateDate != ''">-->
<!--                and update_date = #{dcoosTpProcess.updateDate}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.createDate != null and dcoosTpProcess.createDate != ''">-->
<!--                and create_date = #{dcoosTpProcess.createDate}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.exitDate != null and dcoosTpProcess.exitDate != ''">-->
<!--                and exit_date = #{dcoosTpProcess.exitDate}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.hostName != null and dcoosTpProcess.hostName != ''">-->
<!--                and host_name = #{dcoosTpProcess.hostName}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.isPause != null and dcoosTpProcess.isPause != ''">-->
<!--                and is_pause = #{dcoosTpProcess.isPause}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.storageAvailableZone != null and dcoosTpProcess.storageAvailableZone != ''">-->
<!--                and storage_available_zone = #{dcoosTpProcess.storageAvailableZone}-->
<!--            </if>-->
<!--            <if test="dcoosTpProcess.version != null and dcoosTpProcess.version != ''">-->
<!--                and version = #{dcoosTpProcess.version}-->
<!--            </if>-->
<!--        </where>-->
<!--        limit #{pageable.offset} offset #{pageable.pageSize}-->
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from config.dcoos_tp_process
        <where>
            <if test="processId != null">
                and process_id = #{processId}
            </if>
            <if test="clusterId != null">
                and cluster_id = #{clusterId}
            </if>
            <if test="moduleId != null">
                and module_id = #{moduleId}
            </if>
            <if test="hostId != null">
                and host_id = #{hostId}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="billingLineId != null">
                and billing_line_id = #{billingLineId}
            </if>
            <if test="perl != null">
                and perl = #{perl}
            </if>
            <if test="moduleCode != null and moduleCode != ''">
                and module_code = #{moduleCode}
            </if>
            <if test="systemProcessId != null">
                and system_process_id = #{systemProcessId}
            </if>
            <if test="processName != null and processName != ''">
                and process_name = #{processName}
            </if>
            <if test="updateDate != null and updateDate != ''">
                and update_date = #{updateDate}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="exitDate != null and exitDate != ''">
                and exit_date = #{exitDate}
            </if>
            <if test="hostName != null and hostName != ''">
                and host_name = #{hostName}
            </if>
            <if test="isPause != null and isPause != ''">
                and is_pause = #{isPause}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and storage_available_zone = #{storageAvailableZone}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="processId" useGeneratedKeys="true">
        insert into config.dcoos_tp_process(cluster_id, module_id, host_id, status, type, billing_line_id, perl, module_code,
                                     system_process_id, process_name, update_date, create_date, exit_date, host_name,
                                     is_pause, storage_available_zone, version)
        values (#{clusterId}, #{moduleId}, #{hostId}, #{status}, #{type}, #{billingLineId}, #{perl}, #{moduleCode},
                #{systemProcessId}, #{processName}, #{updateDate}, #{createDate}, #{exitDate}, #{hostName}, #{isPause},
                #{storageAvailableZone}, #{version})
    </insert>

    <insert id="insertBatch" keyProperty="processId" useGeneratedKeys="true">
        insert into config.dcoos_tp_process(cluster_id, module_id, host_id, status, type, billing_line_id, perl, module_code,
        system_process_id, process_name, update_date, create_date, exit_date, host_name, is_pause,
        storage_available_zone, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.moduleId}, #{entity.hostId}, #{entity.status}, #{entity.type},
            #{entity.billingLineId}, #{entity.perl}, #{entity.moduleCode}, #{entity.systemProcessId},
            #{entity.processName}, #{entity.updateDate}, #{entity.createDate}, #{entity.exitDate}, #{entity.hostName},
            #{entity.isPause}, #{entity.storageAvailableZone}, #{entity.version})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="processId" useGeneratedKeys="true">
        insert into config.dcoos_tp_process(cluster_id, module_id, host_id, status, type, billing_line_id, perl, module_code,
        system_process_id, process_name, update_date, create_date, exit_date, host_name, is_pause,
        storage_available_zone, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.clusterId}, #{entity.moduleId}, #{entity.hostId}, #{entity.status}, #{entity.type},
            #{entity.billingLineId}, #{entity.perl}, #{entity.moduleCode}, #{entity.systemProcessId},
            #{entity.processName}, #{entity.updateDate}, #{entity.createDate}, #{entity.exitDate}, #{entity.hostName},
            #{entity.isPause}, #{entity.storageAvailableZone}, #{entity.version})
        </foreach>
        on duplicate key update
        cluster_id = values(cluster_id),
        module_id = values(module_id),
        host_id = values(host_id),
        status = values(status),
        type = values(type),
        billing_line_id = values(billing_line_id),
        perl = values(perl),
        module_code = values(module_code),
        system_process_id = values(system_process_id),
        process_name = values(process_name),
        update_date = values(update_date),
        create_date = values(create_date),
        exit_date = values(exit_date),
        host_name = values(host_name),
        is_pause = values(is_pause),
        storage_available_zone = values(storage_available_zone),
        version = values(version)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update config.dcoos_tp_process
        <set>
            <if test="clusterId != null">
                cluster_id = #{clusterId},
            </if>
            <if test="moduleId != null">
                module_id = #{moduleId},
            </if>
            <if test="hostId != null">
                host_id = #{hostId},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="billingLineId != null">
                billing_line_id = #{billingLineId},
            </if>
            <if test="perl != null">
                perl = #{perl},
            </if>
            <if test="moduleCode != null and moduleCode != ''">
                module_code = #{moduleCode},
            </if>
            <if test="systemProcessId != null">
                system_process_id = #{systemProcessId},
            </if>
            <if test="processName != null and processName != ''">
                process_name = #{processName},
            </if>
            <if test="updateDate != null and updateDate != ''">
                update_date = #{updateDate},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="exitDate != null and exitDate != ''">
                exit_date = #{exitDate},
            </if>
            <if test="hostName != null and hostName != ''">
                host_name = #{hostName},
            </if>
            <if test="isPause != null and isPause != ''">
                is_pause = #{isPause},
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                storage_available_zone = #{storageAvailableZone},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
        </set>
        where process_id = #{processId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from config.dcoos_tp_process
        where process_id = #{processId}
    </delete>

</mapper>

