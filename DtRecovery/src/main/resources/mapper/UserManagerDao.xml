<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.UserManagerDao">

    <resultMap type="com.itco.dtrecovery.entity.UserManager" id="UserManagerMap">
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="passwordText" column="password_text" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="hostIp" column="host_ip" jdbcType="VARCHAR"/>
        <result property="urlAddr" column="url_addr" jdbcType="VARCHAR"/>
        <result property="oldPasswordText" column="old_password_text" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="effDate" column="eff_date" jdbcType="TIMESTAMP"/>
        <result property="expDate" column="exp_date" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="UserManagerMap">
        select user_id,
               user_name,
               password_text,
               user_type,
               host_ip,
               url_addr,
               old_password_text,
               create_date,
               eff_date,
               exp_date,
               remark
        from disaster.user_manager
        where user_id = #{userId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="UserManagerMap">
        select
        user_id, user_name, password_text, user_type, host_ip, url_addr, old_password_text, create_date, eff_date,
        exp_date, remark
        from disaster.user_manager
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="passwordText != null and passwordText != ''">
                and password_text = #{passwordText}
            </if>
            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>
            <if test="hostIp != null and hostIp != ''">
                and host_ip = #{hostIp}
            </if>
            <if test="urlAddr != null and urlAddr != ''">
                and url_addr = #{urlAddr}
            </if>
            <if test="oldPasswordText != null and oldPasswordText != ''">
                and old_password_text = #{oldPasswordText}
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="effDate != null">
                and eff_date = #{effDate}
            </if>
            <if test="expDate != null">
                and exp_date = #{expDate}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.user_manager
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="passwordText != null and passwordText != ''">
                and password_text = #{passwordText}
            </if>
            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>
            <if test="hostIp != null and hostIp != ''">
                and host_ip = #{hostIp}
            </if>
            <if test="urlAddr != null and urlAddr != ''">
                and url_addr = #{urlAddr}
            </if>
            <if test="oldPasswordText != null and oldPasswordText != ''">
                and old_password_text = #{oldPasswordText}
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="effDate != null">
                and eff_date = #{effDate}
            </if>
            <if test="expDate != null">
                and exp_date = #{expDate}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="userId" useGeneratedKeys="true">
        insert into disaster.user_manager(user_name, password_text, user_type, host_ip, url_addr, old_password_text, create_date,
                                 eff_date, exp_date, remark)
        values (#{userName}, #{passwordText}, #{userType}, #{hostIp}, #{urlAddr}, #{oldPasswordText}, #{createDate},
                #{effDate}, #{expDate}, #{remark})
    </insert>

    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into disaster.user_manager(user_name, password_text, user_type, host_ip, url_addr, old_password_text, create_date,
        eff_date, exp_date, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.passwordText}, #{entity.userType}, #{entity.hostIp}, #{entity.urlAddr},
            #{entity.oldPasswordText}, #{entity.createDate}, #{entity.effDate}, #{entity.expDate}, #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into disaster.user_manager(user_name, password_text, user_type, host_ip, url_addr, old_password_text, create_date,
        eff_date, exp_date, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.passwordText}, #{entity.userType}, #{entity.hostIp}, #{entity.urlAddr},
            #{entity.oldPasswordText}, #{entity.createDate}, #{entity.effDate}, #{entity.expDate}, #{entity.remark})
        </foreach>
        on duplicate key update
        user_name = values(user_name),
        password_text = values(password_text),
        user_type = values(user_type),
        host_ip = values(host_ip),
        url_addr = values(url_addr),
        old_password_text = values(old_password_text),
        create_date = values(create_date),
        eff_date = values(eff_date),
        exp_date = values(exp_date),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.user_manager
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="passwordText != null and passwordText != ''">
                password_text = #{passwordText},
            </if>
            <if test="userType != null and userType != ''">
                user_type = #{userType},
            </if>
            <if test="hostIp != null and hostIp != ''">
                host_ip = #{hostIp},
            </if>
            <if test="urlAddr != null and urlAddr != ''">
                url_addr = #{urlAddr},
            </if>
            <if test="oldPasswordText != null and oldPasswordText != ''">
                old_password_text = #{oldPasswordText},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="effDate != null">
                eff_date = #{effDate},
            </if>
            <if test="expDate != null">
                exp_date = #{expDate},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where user_id = #{userId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.user_manager
        where user_id = #{userId}
    </delete>

</mapper>

