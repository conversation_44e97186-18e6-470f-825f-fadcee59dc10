<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.BackupConfigScopeDao">

    <resultMap type="com.itco.dtrecovery.entity.BackupConfigScope" id="BackupConfigScopeMap">
        <result property="scopeId" column="scope_id" jdbcType="INTEGER"/>
        <result property="backupConfigId" column="backup_config_id" jdbcType="INTEGER"/>
        <result property="enName" column="en_name" jdbcType="VARCHAR"/>
        <result property="chName" column="ch_name" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BackupConfigScopeMap">
        select scope_id,
               backup_config_id,
               en_name,
               ch_name,
               order_id
        from disaster.backup_config_scope
        where scope_id = #{scopeId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BackupConfigScopeMap">
        select
        scope_id, backup_config_id, en_name, ch_name, order_id
        from disaster.backup_config_scope order by backup_config_id,order_id
    </select>

    <select id="queryAllByIncrement" resultMap="BackupConfigScopeMap">
        select
        scope_id, backup_config_id, en_name, ch_name, order_id
        from disaster.backup_config_scope
        <where>
            <if test="scopeId != null">
                and scope_id > #{scopeId}
            </if>
        </where>
        order by backup_config_id,order_id
    </select>


    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.backup_config_scope
        <where>
            <if test="scopeId != null">
                and scope_id = #{scopeId}
            </if>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="enName != null and enName != ''">
                and en_name = #{enName}
            </if>
            <if test="chName != null and chName != ''">
                and ch_name = #{chName}
            </if>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="scopeId" useGeneratedKeys="true">
        insert into disaster.backup_config_scope(backup_config_id, en_name, ch_name, order_id)
        values (#{backupConfigId}, #{enName}, #{chName}, #{orderId})
    </insert>

    <insert id="insertBatch" keyProperty="scopeId" useGeneratedKeys="true">
        insert into disaster.backup_config_scope(backup_config_id, en_name, ch_name, order_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.enName}, #{entity.chName}, #{entity.orderId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="scopeId" useGeneratedKeys="true">
        insert into disaster.backup_config_scope(backup_config_id, en_name, ch_name, order_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.enName}, #{entity.chName}, #{entity.orderId})
        </foreach>
        on duplicate key update
        backup_config_id = values(backup_config_id),
        en_name = values(en_name),
        ch_name = values(ch_name),
        order_id = values(order_id)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.backup_config_scope
        <set>
            <if test="backupConfigId != null">
                backup_config_id = #{backupConfigId},
            </if>
            <if test="enName != null and enName != ''">
                en_name = #{enName},
            </if>
            <if test="chName != null and chName != ''">
                ch_name = #{chName},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
        </set>
        where scope_id = #{scopeId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.backup_config_scope
        where scope_id = #{scopeId}
    </delete>

</mapper>

