<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.RecoveryResultDao">

    <resultMap type="com.itco.dtrecovery.entity.RecoveryResult" id="RecoveryResultMap">
        <result property="recoveryResultId" column="recovery_result_id" jdbcType="INTEGER"/>
        <result property="backupConfigId" column="backup_config_id" jdbcType="INTEGER"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="success" column="success" jdbcType="BOOLEAN"/>
        <result property="targetDirectory" column="target_directory" jdbcType="VARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="recoveryStartTime" column="recovery_start_time" jdbcType="VARCHAR"/>
        <result property="recoveryEndTime" column="recovery_end_time" jdbcType="VARCHAR"/>
        <result property="operateUser" column="operate_user" jdbcType="VARCHAR"/>
        <result property="verificationResults" column="verification_results" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="RecoveryResultMap">
        select recovery_result_id,
               backup_config_id,
               version,
               success,
               target_directory,
               error_message,
               recovery_start_time,
               recovery_end_time,
               operate_user,
               verification_results,
               remark
        from disaster.recovery_result
        where recovery_result_id = #{recoveryResultId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="RecoveryResultMap">
        select
        recovery_result_id, backup_config_id, version, success, target_directory, error_message, recovery_start_time,
        recovery_end_time, operate_user, verification_results, remark
        from disaster.recovery_result
        <!--<where>
            <if test="recoveryResultId != null">
                and recovery_result_id = #{recoveryResultId}
            </if>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="success != null">
                and success = #{success}
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                and target_directory = #{targetDirectory}
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                and error_message = #{errorMessage}
            </if>
            <if test="recoveryStartTime != null and recoveryStartTime != ''">
                and recovery_start_time = #{recoveryStartTime}
            </if>
            <if test="recoveryEndTime != null and recoveryEndTime != ''">
                and recovery_end_time = #{recoveryEndTime}
            </if>
            <if test="operateUser != null and operateUser != ''">
                and operate_user = #{operateUser}
            </if>
            <if test="verificationResults != null and verificationResults != ''">
                and verification_results = #{verificationResults}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>-->
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.recovery_result
        <where>
            <if test="recoveryResultId != null">
                and recovery_result_id = #{recoveryResultId}
            </if>
            <if test="backupConfigId != null">
                and backup_config_id = #{backupConfigId}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="success != null">
                and success = #{success}
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                and target_directory = #{targetDirectory}
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                and error_message = #{errorMessage}
            </if>
            <if test="recoveryStartTime != null and recoveryStartTime != ''">
                and recovery_start_time = #{recoveryStartTime}
            </if>
            <if test="recoveryEndTime != null and recoveryEndTime != ''">
                and recovery_end_time = #{recoveryEndTime}
            </if>
            <if test="operateUser != null and operateUser != ''">
                and operate_user = #{operateUser}
            </if>
            <if test="verificationResults != null and verificationResults != ''">
                and verification_results = #{verificationResults}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="recoveryResultId" useGeneratedKeys="true">
        insert into disaster.recovery_result(backup_config_id, version, success, target_directory, error_message,
                                    recovery_start_time, recovery_end_time, operate_user, verification_results, remark)
        values (#{backupConfigId}, #{version}, #{success}, #{targetDirectory}, #{errorMessage}, #{recoveryStartTime},
                #{recoveryEndTime}, #{operateUser}, #{verificationResults}, #{remark})
    </insert>

    <insert id="insertBatch" keyProperty="recoveryResultId" useGeneratedKeys="true">
        insert into disaster.recovery_result(backup_config_id, version, success, target_directory, error_message,
        recovery_start_time, recovery_end_time, operate_user, verification_results, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.version}, #{entity.success}, #{entity.targetDirectory},
            #{entity.errorMessage}, #{entity.recoveryStartTime}, #{entity.recoveryEndTime}, #{entity.operateUser},
            #{entity.verificationResults}, #{entity.remark})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="recoveryResultId" useGeneratedKeys="true">
        insert into disaster.recovery_result(backup_config_id, version, success, target_directory, error_message,
        recovery_start_time, recovery_end_time, operate_user, verification_results, remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.backupConfigId}, #{entity.version}, #{entity.success}, #{entity.targetDirectory},
            #{entity.errorMessage}, #{entity.recoveryStartTime}, #{entity.recoveryEndTime}, #{entity.operateUser},
            #{entity.verificationResults}, #{entity.remark})
        </foreach>
        on duplicate key update
        backup_config_id = values(backup_config_id),
        version = values(version),
        success = values(success),
        target_directory = values(target_directory),
        error_message = values(error_message),
        recovery_start_time = values(recovery_start_time),
        recovery_end_time = values(recovery_end_time),
        operate_user = values(operate_user),
        verification_results = values(verification_results),
        remark = values(remark)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.recovery_result
        <set>
            <if test="backupConfigId != null">
                backup_config_id = #{backupConfigId},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="success != null">
                success = #{success},
            </if>
            <if test="targetDirectory != null and targetDirectory != ''">
                target_directory = #{targetDirectory},
            </if>
            <if test="errorMessage != null and errorMessage != ''">
                error_message = #{errorMessage},
            </if>
            <if test="recoveryStartTime != null and recoveryStartTime != ''">
                recovery_start_time = #{recoveryStartTime},
            </if>
            <if test="recoveryEndTime != null and recoveryEndTime != ''">
                recovery_end_time = #{recoveryEndTime},
            </if>
            <if test="operateUser != null and operateUser != ''">
                operate_user = #{operateUser},
            </if>
            <if test="verificationResults != null and verificationResults != ''">
                verification_results = #{verificationResults},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where recovery_result_id = #{recoveryResultId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.recovery_result
        where recovery_result_id = #{recoveryResultId}
    </delete>

</mapper>

