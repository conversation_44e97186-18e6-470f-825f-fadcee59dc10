<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.itco.dtrecovery.dao.ClusterManagerDao">

    <resultMap type="com.itco.dtrecovery.entity.ClusterManager" id="ClusterManagerMap">
        <result property="clusterId" column="cluster_id" jdbcType="INTEGER"/>
        <result property="enName" column="en_name" jdbcType="VARCHAR"/>
        <result property="chName" column="ch_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="storageAvailableZone" column="storage_available_zone" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="VARCHAR"/>
        <result property="exitDate" column="exit_date" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClusterManagerMap">
        select cluster_id,
               en_name,
               ch_name,
               status,
               storage_available_zone,
               create_date,
               exit_date,
               version
        from disaster.cluster_manager
        where cluster_id = #{clusterId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ClusterManagerMap">
        select
        cluster_id, en_name, ch_name, status, storage_available_zone, create_date, exit_date, version
        from disaster.cluster_manager
    </select>

    <!--增量查询数据-->
    <select id="queryAllByIncrement" resultMap="ClusterManagerMap">
        select
        cluster_id, en_name, ch_name, status, storage_available_zone, create_date, exit_date, version
        from disaster.cluster_manager
        where cluster_id > #{clusterId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from disaster.cluster_manager
        <where>
            <if test="clusterId != null">
                and cluster_id = #{clusterId}
            </if>
            <if test="enName != null and enName != ''">
                and en_name = #{enName}
            </if>
            <if test="chName != null and chName != ''">
                and ch_name = #{chName}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                and storage_available_zone = #{storageAvailableZone}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="exitDate != null and exitDate != ''">
                and exit_date = #{exitDate}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="clusterId" useGeneratedKeys="true">
        insert into disaster.cluster_manager(en_name, ch_name, status, storage_available_zone, create_date, exit_date, version)
        values (#{enName}, #{chName}, #{status}, #{storageAvailableZone}, #{createDate}, #{exitDate}, #{version})
    </insert>

    <insert id="insertBatch" keyProperty="clusterId" useGeneratedKeys="true">
        insert into disaster.cluster_manager(en_name, ch_name, status, storage_available_zone, create_date, exit_date, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.enName}, #{entity.chName}, #{entity.status}, #{entity.storageAvailableZone}, #{entity.createDate},
            #{entity.exitDate}, #{entity.version})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="clusterId" useGeneratedKeys="true">
        insert into disaster.cluster_manager(en_name, ch_name, status, storage_available_zone, create_date, exit_date, version)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.enName}, #{entity.chName}, #{entity.status}, #{entity.storageAvailableZone}, #{entity.createDate},
            #{entity.exitDate}, #{entity.version})
        </foreach>
        on duplicate key update
        en_name = values(en_name),
        ch_name = values(ch_name),
        status = values(status),
        storage_available_zone = values(storage_available_zone),
        create_date = values(create_date),
        exit_date = values(exit_date),
        version = values(version)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update disaster.cluster_manager
        <set>
            <if test="enName != null and enName != ''">
                en_name = #{enName},
            </if>
            <if test="chName != null and chName != ''">
                ch_name = #{chName},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="storageAvailableZone != null and storageAvailableZone != ''">
                storage_available_zone = #{storageAvailableZone},
            </if>
            <if test="createDate != null and createDate != ''">
                create_date = #{createDate},
            </if>
            <if test="exitDate != null and exitDate != ''">
                exit_date = #{exitDate},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
        </set>
        where cluster_id = #{clusterId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from disaster.cluster_manager
        where cluster_id = #{clusterId}
    </delete>

</mapper>

