package com.itco;


import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dtrecovery.Thread.CreateAnyBean;
import com.itco.dtrecovery.Thread.DealTaskManager;
import com.itco.dtrecovery.module.impl.BackupFile;
import com.itco.framework.Version;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.sql.DataSource;


@Slf4j
@EnableScheduling
@SpringBootApplication
public class DtRecovery {

    @Autowired
    DealTaskManager dealTaskManager;

    @Autowired
    ZkClientApi zkClientApi;

    // 注册ApplicationListener来
    @Bean
    public ApplicationListener<ApplicationReadyEvent> readyEventApplicationListener() {
        return event -> {
            // 在应用程序启动完成后执行自定义逻辑
            //System.out.println("Application is ready, do something...");
            dealTaskManager.dealTaskStart();
        };
    }

    static void test() {
        String a = "/app/tpss/data/backup/database";
        String result = a.substring(a.indexOf("/", a.indexOf("/", a.indexOf("/") + 1) + 1));
        System.out.println("result: " + result);
    }

    public static void main(String[] args) {
        //test();
        //Java -jar 包名 version
        if (Version.print(args, "DtRecovery_code_2024-04-30 11:00")) {
            return;
        }

        CreateAnyBean.initArgs(args);
        SpringApplication.run(DtRecovery.class, args);

        //testBackup();
        System.out.println("DtRecovery.class end!");
    }

}