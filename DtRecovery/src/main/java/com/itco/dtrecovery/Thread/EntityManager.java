package com.itco.dtrecovery.Thread;

import com.itco.component.jdbc.DecodePassword;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dtrecovery.dao.*;
import com.itco.dtrecovery.entity.*;
import com.itco.framework.Factory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Data
@Component
public class EntityManager {

    @Autowired
    private BackupConfigDao backupConfigDao;
    @Autowired
    private BackupConfigScopeDao backupConfigScopeDao;
    @Autowired
    private BackupHostInfoDao backingHostInfoDao;
    @Autowired
    private BackupResultDao backupResultDao;
    @Autowired
    private ClusterManagerDao clusterManagerDao;
    @Autowired
    private ComponentManagerDao componentManagerDao;
    @Autowired
    private DcoosTpProcessDao DcoosTpProcessDao;
    @Autowired
    private DisasterRecoveryTaskDao DisasterRecoveryTaskDao;
    @Autowired
    private RecoveryResultDao RecoveryResultDao;
    @Autowired
    private UserHistoryPasswordDao UserHistoryPasswordDao;
    @Autowired
    private UserManagerDao userManagerDao;
    @Autowired
    private ComponentInfoDao componentInfoDao;
    @Autowired
    private ZkClientApi zkClientApi;

    Map<String, BackupConfig> configMap = new TreeMap<>();
    Map<String, BackupHostInfo> hostMap = new TreeMap<>();
    Map<String, BackupResult> resultMap = new TreeMap<>();
    Map<String, ClusterManager> clusterMap = new TreeMap<>();
    Map<String, ComponentManager> componentMap = new TreeMap<>();
    Map<String, DcoosTpProcess> procMap = new TreeMap<>();
    Map<String, DisasterRecoveryTask> taskMap = new ConcurrentHashMap<>();
    Map<String, RecoveryResult> recoveryResultMap = new TreeMap<>();
    Map<String, UserManager> usersMap = new TreeMap<>();
    Map<String, UserHistoryPassword> userHistoryPasswordMap = new TreeMap<>();
    Map<String, List<BackupConfigScope>> scopeMap = new TreeMap<>();
    int maxScopeId = 0;
    int maxBackupConfigId = 0;
    int maxBackupHostId = 0;
    int maxClusterId = 0;
    int maxComponentId = 0;

    String storageAvailableZone = null;

    public boolean loadCofig() {
        System.out.println("\n---------------------------------------------------进程表加载中----------------------------------------------------");
        loadDcoosTpProcess();

        System.out.println("\n------------------------------------------------备份规则配置范围表加载中---------------------------------------------");
        loadBackupScope();

        System.out.println("\n------------------------------------------------备份规则配置表加载中------------------------------------------------");
        loadBackupConfig(0);

        System.out.println("\n--------------------------------------------------主机信息加载中---------------------------------------------------");
        loadBackupHostInfo(0);

        System.out.println("\n--------------------------------------------------集群配置加载中---------------------------------------------------");
        loadClusterManager(0);

        System.out.println("\n--------------------------------------------------组件信息加载中---------------------------------------------------");
        loadComponentManager(0);

        System.out.println("\n----------------------------------------------备份、恢复任务加载中---------------------------------------------------");
        loadDisasterRecoveryTask();

        System.out.println("\n-------------------------------------------------备份结果表加载中--------------------------------------------------");
        loadBackupResult();

        System.out.println("\n--------------------------------------------------恢复结果加载中---------------------------------------------------");
        loadRecoveryResult();

        System.out.println("\n--------------------------------------------------配置加载完成-----------------------------------------------------");

        return true;
    }

    // 重新加载配置
    public boolean ReloadConfig() {
        System.out.println("\n------------------重载配置中---------------------备份规则配置范围表加载中---------------------------------------------");
        scopeMap.clear();
        maxScopeId = 0;
        loadBackupScope();

        System.out.println("\n------------------重载配置中----------------------备份规则配置表加载中------------------------------------------------");
        configMap.clear();
        loadBackupConfig(0);

        System.out.println("\n------------------重载配置中-----------------------主机信息加载中---------------------------------------------------");
        hostMap.clear();
        loadBackupHostInfo(0);

        System.out.println("\n------------------重载配置中------------------------集群配置加载中---------------------------------------------------");
        clusterMap.clear();
        loadClusterManager(0);

        System.out.println("\n------------------重载配置中-----------------------组件信息加载中---------------------------------------------------");
        componentMap.clear();
        loadComponentManager(0);

        System.out.println("\n------------------重载配置中----------------------备份结果表加载中--------------------------------------------------");
        resultMap.clear();
        loadBackupResult();

        System.out.println("\n------------------重载配置中-----------------------配置加载完成-----------------------------------------------------");

        return true;
    }

    // 增量加载配置
    public boolean IncrementalLoadConfig() {
        System.out.println("\n------------------增量加载配置中- " + maxScopeId + " --------备份规则配置范围表加载中-----------------------------------");
        loadBackupScope();

        System.out.println("\n------------------增量加载配置中- " + maxBackupConfigId + " ----------备份规则配置表加载中-----------------------------");
        loadBackupConfig(maxBackupConfigId);

        System.out.println("\n------------------增量加载配置中- " + maxBackupHostId + " --------主机信息加载中-------------------------------------");
        loadBackupHostInfo(maxBackupHostId);

        System.out.println("\n------------------增量加载配置中- " + maxClusterId + " ----------集群配置加载中---------------------------------------");
        loadClusterManager(maxClusterId);

        System.out.println("\n------------------增量加载配置中- " + maxComponentId + " -------------组件信息加载中----------------------------------");
        loadComponentManager(maxComponentId);

        System.out.println("\n------------------增量加载配置中-----------------------配置加载完成----------------------------------------------");

        return true;
    }

    boolean loadBackupConfig(int backupConfigId) {
        BackupConfig incrementBackupConfig = new BackupConfig();
        incrementBackupConfig.setBackupConfigId(backupConfigId);
        incrementBackupConfig.setBackupStatus("1000");

        if (!CreateAnyBean.recoveryScopeEnabled) {
            incrementBackupConfig.setStorageAvailableZone(storageAvailableZone);
        }

        List<BackupConfig> backupConfigs = backupConfigDao.queryAllByIncrement(incrementBackupConfig);
        for (BackupConfig backupConfig : backupConfigs) {
            if (maxBackupConfigId < backupConfig.getBackupConfigId()) {
                maxBackupConfigId = backupConfig.getBackupConfigId();
            }
            if (backupConfig.getLastTriggerDate() == null) {
                backupConfig.setLastTriggerDate(new Date());
            }

            if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
                List<BackupConfigScope> backupConfigScopes = scopeMap.get(String.valueOf(backupConfig.getBackupConfigId()));
                if (backupConfigScopes == null || backupConfigScopes.size() == 0) {
                    log.error("备份规则:" + backupConfig.getBackupConfigId() + ",未配置范围请添加备份范围。");
                    continue;
                }
            }

            configMap.put(String.valueOf(backupConfig.getBackupConfigId()), backupConfig);
        }

        //打印 configMap 每个元素
        if (backupConfigs.size() > 0) {
            configMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));
        }
        return true;
    }

    boolean loadBackupScope() {
        BackupConfigScope incramentScope = new BackupConfigScope();
        incramentScope.setScopeId(maxScopeId);
        List<BackupConfigScope> backupConfigScopes = backupConfigScopeDao.queryAllByIncrement(incramentScope);
        for (BackupConfigScope backupConfigScope : backupConfigScopes) {
            log.info(backupConfigScope.toString());
            if (maxScopeId < backupConfigScope.getScopeId()) {
                maxScopeId = backupConfigScope.getScopeId();
            }
            List<BackupConfigScope> backupConfigScopesList = scopeMap.get(backupConfigScope.getBackupConfigId().toString());
            if (backupConfigScopesList == null) {
                backupConfigScopesList = new ArrayList<>();
                backupConfigScopesList.add(backupConfigScope);
                scopeMap.put(backupConfigScope.getBackupConfigId().toString(), backupConfigScopesList);
            } else {
                backupConfigScopesList.add(backupConfigScope);
            }
        }

        //分组打印 configMap 每个元素
        /*scopeMap.forEach((key, value) -> {
            System.out.println("Key: " + key + ",size:" + value.size());
            value.forEach(scope -> System.out.println("  - " + scope.getEnName() + ", " + scope.getOrderId()));
        });*/

        return true;
    }

    boolean loadBackupHostInfo(int backupHostId) {
        BackupHostInfo incrementBackupHostInfo = new BackupHostInfo();
        incrementBackupHostInfo.setHostId(backupHostId);

        List<BackupHostInfo> backupHostInfos = backingHostInfoDao.queryAllByIncrement(incrementBackupHostInfo);
        for (BackupHostInfo backupHostInfo : backupHostInfos) {
            if (maxBackupHostId < backupHostInfo.getHostId()) {
                maxBackupHostId = backupHostInfo.getHostId();
            }

            String publickey = backupHostInfo.getSshPublicPassword();
            String privatekey = backupHostInfo.getSshPassword();
            if (publickey == null && privatekey == null) {
                log.error("主机:" + backupHostInfo.getHostId() + " 未配置ssh密钥，请配置ssh密钥。");
                continue;
            }
            String passwd = DecodePassword.decryption(publickey, privatekey);
            backupHostInfo.setPassword(passwd);

            hostMap.put(backupHostInfo.getHostId().toString(), backupHostInfo);
        }
        if (backupHostInfos.size() > 0) {
            hostMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));
        }
        return true;
    }

    boolean loadBackupResult() {
        List<BackupResult> backupResults = backupResultDao.queryAllByIncrement(new BackupResult());
        for (BackupResult backupResult : backupResults) {
            resultMap.put(backupResult.getBackupResultId().toString(), backupResult);
        }
        resultMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        return true;
    }

    public BackupResult getBackupResult(int backupResultId) {
        BackupResult backupResult = backupResultDao.queryById(backupResultId);
        return backupResult;
    }

    boolean loadClusterManager(int clusterId) {
        ClusterManager incrementClusterManager = new ClusterManager();
        incrementClusterManager.setClusterId(clusterId);

        List<ClusterManager> clusterManagers = clusterManagerDao.queryAllByIncrement(incrementClusterManager);
        for (ClusterManager clusterManager : clusterManagers) {
            if (maxClusterId < clusterManager.getClusterId()) {
                maxClusterId = clusterManager.getClusterId();
            }
            clusterMap.put(clusterManager.getClusterId().toString(), clusterManager);
        }
        if (clusterManagers.size() > 0) {
            clusterMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));
        }
        return true;
    }

    boolean loadComponentManager(int componentId) {
        ComponentManager incrementComponentManager = new ComponentManager();
        incrementComponentManager.setComponentId(componentId);

        List<ComponentManager> componentManagers = componentManagerDao.queryAllByIncrement(incrementComponentManager);
        for (ComponentManager componentManager : componentManagers) {
            if (maxComponentId < componentManager.getComponentId()) {
                maxComponentId = componentManager.getComponentId();
            }
            if (componentManager.getLastTriggerDate() == null) {
                componentManager.setLastTriggerDate(new Date());
            }
            componentMap.put(componentManager.getComponentId().toString(), componentManager);
        }
        if (componentManagers.size() > 0) {
            componentMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));
        }
        return true;
    }

    boolean loadDcoosTpProcess() {
        Pageable pageable = PageRequest.of(0, 10000);
        List<DcoosTpProcess> backupConfigs = DcoosTpProcessDao.queryAllByLimit(new DcoosTpProcess(), pageable);
        //把backupConfig存入map
        for (DcoosTpProcess backupConfig : backupConfigs) {
            procMap.put(backupConfig.getProcessId().toString(), backupConfig);
        }
        procMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        if (null != procMap.get(zkClientApi.getProcessID())) {
            storageAvailableZone = procMap.get(zkClientApi.getProcessID()).getStorageAvailableZone();
        }

        log.info("进程负责可用区:" + storageAvailableZone + ",是否启用全部规则备份恢复:" + CreateAnyBean.recoveryScopeEnabled);
        return true;
    }

    // 启动的时候，加载DCL未完成的任务
    boolean loadDisasterRecoveryTask() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", EnumValue.TaskStatus.DCL.getCode());

        if (!CreateAnyBean.recoveryScopeEnabled) {
            map.put("storageAvailableZone", storageAvailableZone);
        }

        List<DisasterRecoveryTask> disasterRecoveryTasks = DisasterRecoveryTaskDao.queryAllByMap(map);
        for (DisasterRecoveryTask disasterRecoveryTask : disasterRecoveryTasks) {
            taskMap.put(disasterRecoveryTask.getVersion().toString(), disasterRecoveryTask);
        }
        taskMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        log.info("loadDisasterRecoveryTask() ,size:" + taskMap.size());
        return true;
    }


    boolean insertDisasterRecoveryTask(DisasterRecoveryTask task) {
        if (task != null) {
            DisasterRecoveryTaskDao.insert(task);
        }
        return true;
    }

    boolean createComponentMonitorTask(DisasterRecoveryTask task) {
        if (task != null) {
            task.setStatus(EnumValue.TaskStatus.DCL.getCode());
            task.setUpdateTime(Factory.getSystemDateStr());
            task.setElementStr((task.getElementStr() == null ? "" : task.getElementStr()) + task.toElement());

            taskMap.put(task.getVersion().toString(), task);
        }
        return true;
    }


    // 定时扫描的时候，扫描NCL未完成的备份恢复任务
    boolean loadDisasterRecoveryTaskBySchedule() {
        boolean b = false;

        Map<String, Object> map = new HashMap<>();
        map.put("status", EnumValue.TaskStatus.NCL.getCode());

        if (!CreateAnyBean.recoveryScopeEnabled) {
            map.put("storageAvailableZone", storageAvailableZone);
        }

        List<DisasterRecoveryTask> disasterRecoveryTasks = DisasterRecoveryTaskDao.queryAllByMap(map);
        for (DisasterRecoveryTask task : disasterRecoveryTasks) {
            if (!EnumValue.TaskType.COMPONENT_MONITOR.getCode().equals(task.getTaskType())) {
                if (!b) {
                    ReloadConfig();     // 加载到新的任务就重新加载配置
                    b = true;
                }
            }

            task.setStatus(EnumValue.TaskStatus.DCL.getCode());
            task.setUpdateTime(Factory.getSystemDateStr());
            task.setElementStr((task.getElementStr() == null ? "" : task.getElementStr()) + task.toElement());

            taskMap.put(task.getVersion().toString(), task);
        }

        if (disasterRecoveryTasks.size() > 0) {
            DisasterRecoveryTaskDao.insertOrUpdateBatch(disasterRecoveryTasks);
        }

        return true;
    }

    // 定时扫描，为完成的组件NCL任务
    boolean loadComponentMonitorTaskBySchedule() {
        Pageable pageable = PageRequest.of(0, 100);
        DisasterRecoveryTask taskTmp = new DisasterRecoveryTask();
        taskTmp.setStatus("NCL");
        taskTmp.setTaskType("COMPONENT_MONITOR");
        List<DisasterRecoveryTask> disasterRecoveryTasks = DisasterRecoveryTaskDao.queryAllByLimit(taskTmp, pageable);
        for (DisasterRecoveryTask task : disasterRecoveryTasks) {
            task.setStatus(EnumValue.TaskStatus.DCL.getCode());
            task.setUpdateTime(Factory.getSystemDateStr());
            task.setElementStr((task.getElementStr() == null ? "" : task.getElementStr()) + task.toElement());

            taskMap.put(task.getVersion().toString(), task);
        }
        //taskMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        /*if (disasterRecoveryTasks.size() > 0) {
            DisasterRecoveryTaskDao.insertOrUpdateBatch(disasterRecoveryTasks);
        }*/

        //log.info("loadDisasterRecoveryTask() ,size:" + taskMap.size());
        return true;
    }

    public boolean updateDisasterRecoveryTaskStatus(DisasterRecoveryTask task) {
        if (task != null) {
            DisasterRecoveryTaskDao.update(task);
        }

        return true;
    }

    boolean loadRecoveryResult() {
        Pageable pageable = PageRequest.of(0, 100);
        List<RecoveryResult> recoveryResults = RecoveryResultDao.queryAllByLimit(new RecoveryResult(), pageable);
        for (RecoveryResult recoveryResult : recoveryResults) {
            recoveryResultMap.put(String.valueOf(recoveryResult.getRecoveryResultId()), recoveryResult);
        }
        recoveryResultMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        return true;
    }


    // 组件监控信息更新到数据库表
    public boolean insertOrUpdateComponentInfo(List<ComponentInfo> componentInfos) {
        if (componentInfos != null && componentInfos.size() > 0) {
            componentInfoDao.insertOrUpdateBatch(componentInfos);
        }
        return true;
    }

    boolean loadUserManager() {
        Pageable pageable = PageRequest.of(0, 100);
        List<UserManager> userManagers = userManagerDao.queryAllByLimit(new UserManager(), pageable);
        for (UserManager userManager : userManagers) {
            usersMap.put(userManager.getUserId().toString(), userManager);
        }
        usersMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        return true;
    }

    boolean loadUserHistoryPassword() {
        Pageable pageable = PageRequest.of(0, 100);
        List<UserHistoryPassword> userHistoryPasswords = UserHistoryPasswordDao.queryAllByLimit(new UserHistoryPassword(), pageable);
        for (UserHistoryPassword userHistoryPassword : userHistoryPasswords) {
            userHistoryPasswordMap.put(userHistoryPassword.getUserId().toString(), userHistoryPassword);
        }
        userHistoryPasswordMap.forEach((key, value) -> System.out.println("key:" + key + " value:" + value));

        return true;
    }


    public List<BackupConfig> scanBackupConfig() {
        BackupConfig incrementBackupConfig = new BackupConfig();
        incrementBackupConfig.setBackupConfigId(maxBackupConfigId);
        incrementBackupConfig.setBackupStatus("1000");

        if (!CreateAnyBean.recoveryScopeEnabled) {
            incrementBackupConfig.setStorageAvailableZone(storageAvailableZone);
        }

        List<BackupConfig> backupConfigs = backupConfigDao.queryAllByIncrement(incrementBackupConfig);
        /*for (BackupConfig backupConfig : backupConfigs) {
            if (maxBackupConfigId < backupConfig.getBackupConfigId()) {
                maxBackupConfigId = backupConfig.getBackupConfigId();
            }
        }*/
        return backupConfigs;
    }
}
