package com.itco.dtrecovery.Thread;

import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dtrecovery.entity.CommonModule;
import com.itco.entity.common.Common;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.File;
import java.util.Map;

@Slf4j
@Configuration
public class CreateAnyBean {

    static String billingLineId = "11";
    static boolean recoveryScopeEnabled = false;//是否启用全部规则备份恢复
    static String moduleCode = "DtRecovery";
    ZkClientApi zkClientApi = new ZkClientApi();

    public static synchronized void initArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            if (args[i].startsWith("-f")) {
                CreateAnyBean.billingLineId = args[i].substring(2);
            } else if (args[i].startsWith("-s")) {
                CreateAnyBean.recoveryScopeEnabled = true;
                log.info("是否启用全部规则备份恢复:" + CreateAnyBean.recoveryScopeEnabled);
            }
        }
    }

    @Bean()
    @DependsOn("register")
    @ConditionalOnProperty(name = "component.jdbcdatasource.enabled", havingValue = "true")
    public DataSource getDataSource() {
        DbPool.setZkClientApi(zkClientApi);

        if (!DbPool.init()) {
            throw new BeanCreationException("数据库连接初始化失败");
        }
        return DbPool.getDataSource();
    }

    public boolean checkEnvironment(CommonModule commonModule) {
        Map<String, String> oSEnv = System.getenv();
        String pgHome = oSEnv.get("PGHOME");
        String pgData = oSEnv.get("PGDATA");
        if (pgHome == null || pgData == null) {
            throw new BeanCreationException("环境变量未设置,请设置PGHOME和PGDATA");
        } else {
            // 校验环境变量pgHome、pgData的配置的目录是否存在
            File pgHomeDir = new File(pgHome);
            File pgDataDir = new File(pgData);

            if (!pgHomeDir.exists() || !pgHomeDir.isDirectory()) {
                throw new BeanCreationException("PGHOME环境变量所指向的目录不存在或不是一个有效目录");
            } else {
                commonModule.setPgHome(pgHome);
            }

            if (!pgDataDir.exists() || !pgDataDir.isDirectory()) {
                throw new BeanCreationException("PGDATA环境变量所指向的目录不存在或不是一个有效目录");
            } else {
                commonModule.setPgData(pgData);
            }
        }
        log.info("环境变量设置正常，PGHOME:" + pgHome + ",PGDATA:" + pgData);
        return true;
    }

    @Bean
    public CommonModule initCommon() {
        CommonModule commonModule = new CommonModule();

        //设置线程池数量
        commonModule.setThreadNum(5);

        //循环标志位
        commonModule.setWhileFlag(true);

        String home = System.getenv().get("HOME");
        if (home == null) {
            throw new BeanCreationException("HOME环境变量未设置,请设置HOME");
        }
        commonModule.setBaseWorkDir(home + "/data/backup/work");
        log.info("工作目录设置正常,baseWorkDir:" + commonModule.getBaseWorkDir());

        //checkEnvironment(commonModule);

        return commonModule;
    }


    @Bean
    public ZkClientApi register() {
        log.info("进程注册,billing_line_id:" + billingLineId);
        Common common = new Common();


        zkClientApi.setVersion(common.getVersion());
        boolean ret = zkClientApi.init();
        if (!ret) {
            throw new BeanCreationException("zkClientApi.init()初始化失败");
        }
        zkClientApi.setBillingLineId(billingLineId);
        zkClientApi.setModuleCode(moduleCode);
        ret = zkClientApi.register(false);
        if (!ret) {
            throw new BeanCreationException("zkClientApi.register(false) 进程注册失败");
        }

        // 读取Common.properties
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return null;
        }

        log.info(common.toString());

        return zkClientApi;
    }


}
