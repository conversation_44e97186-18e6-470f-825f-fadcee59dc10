package com.itco.dtrecovery.Thread;

import com.itco.dtrecovery.entity.*;
import com.itco.dtrecovery.module.BaseThread;
import com.itco.dtrecovery.module.BaseThreadFactory;
import com.itco.framework.Factory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Slf4j
@Component
public class DealTaskManager {

    @Autowired
    EntityManager entityManager;

    @Autowired
    CommonModule commonModule;

    ExecutorService fixedThreadPool;

    @Autowired
    BaseThreadFactory baseThreadFactory;

    @Bean
    boolean initDealTaskManager() {
        log.debug("initDealTaskManager start");
        log.info("common:" + commonModule.toString());

        boolean result = entityManager.loadCofig();
        if (!result) {
            return false;
        }

        log.debug("创建线程池成功，工作线程数量:" + commonModule.getThreadNum());
        fixedThreadPool = Executors.newFixedThreadPool(commonModule.getThreadNum());  // 线程池类

        log.debug("initDealTaskManager close");
        return true;
    }


    public boolean dealTaskStart() {
        log.info("DealTaskManager start");
        try {
            while (commonModule.isWhileFlag()) {
                Iterator<DisasterRecoveryTask> iterator = entityManager.getTaskMap().values().iterator();
                while (iterator.hasNext()) {
                    DisasterRecoveryTask task = iterator.next();
                    if (EnumValue.TaskStatus.DCL.getCode().equals(task.getStatus())) {
                        log.info("----------------------  开始分配任务：{},config_id:{},task_type:{}  --------------------------------------", task.getVersion(), task.getBackupConfigId(), task.getTaskType());
                        BaseThread baseThread = baseThreadFactory.getCalcClassByTask(task);
                        if (baseThread == null) {
                            log.error("创建处理类失败，" + task.toString());
                            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                            task.setErrorMessage("创建处理类失败，" + task.getErrorMessage());
                            task.setUpdateTime(Factory.getSystemDateStr());
                            task.setElementStr(task.getElementStr() + task.toElement());
                            createTaskResult(task);
                            log.info("----------------------  任务处理完成：{},config_id:{},task_type:{}  ---------", task.getVersion(), task.getBackupConfigId(), task.getTaskType());
                            entityManager.updateDisasterRecoveryTaskStatus(task);
                            iterator.remove(); // 安全地删除当前元素
                            continue;
                        } else {
                            Future<?> submit = fixedThreadPool.submit(baseThread);
                            task.setSubmit(submit);
                            task.setStatus(EnumValue.TaskStatus.OCL.getCode());
                            task.setUpdateTime(Factory.getSystemDateStr());
                            task.setElementStr(task.getElementStr() + task.toElement());
                        }
                        entityManager.updateDisasterRecoveryTaskStatus(task);
                    } else if (EnumValue.TaskStatus.SCL.getCode().equals(task.getStatus()) || EnumValue.TaskStatus.FCL.getCode().equals(task.getStatus()) || EnumValue.TaskStatus.SSS.getCode().equals(task.getStatus())) {
                        entityManager.updateDisasterRecoveryTaskStatus(task);
                        createTaskResult(task);
                        log.info("----------------------  任务处理完成：{},config_id:{},task_type:{}  --------------------------------------", task.getVersion(), task.getBackupConfigId(), task.getTaskType());
                        iterator.remove(); // 安全地删除当前元素
                    }
                }

                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.error("DealTaskManager error:" + e.getMessage());
            e.printStackTrace();
            return false;
        }
        log.info("DealTaskManager close");
        return true;
    }


    boolean createTaskResult(DisasterRecoveryTask task) {
        if (EnumValue.TaskType.BACKUP.getCode().equals(task.getTaskType())) {
            // 备份结果入库
            BackupResult backupResult = new BackupResult();
            BackupConfig backConfig = entityManager.getConfigMap().get(String.valueOf(task.getBackupConfigId()));
            //BackupHostInfo targetHostInfo = entityManager.getHostMap().get(backConfig.getTargetHostId());
            backupResult.setBackupResult(task, backConfig);
            backupResult.setErrorMessage(task.getErrorMessage());

            entityManager.getBackupResultDao().insert(backupResult);
        } else if (EnumValue.TaskType.RECOVERY.getCode().equals(task.getTaskType())) {
            // 恢复结果入库
            RecoveryResult recoveryResult = new RecoveryResult();
            BackupConfig backConfig = entityManager.getConfigMap().get(String.valueOf(task.getBackupConfigId()));
            //BackupHostInfo targetHostInfo = entityManager.getHostMap().get(backConfig.getTargetHostId());
            RecoveryResult backResult = entityManager.getRecoveryResultDao().queryById(task.getBackupResultId());
            recoveryResult.setRecoveryResult(task, backConfig, backResult);
            recoveryResult.setErrorMessage(task.getErrorMessage());

            entityManager.getRecoveryResultDao().insert(recoveryResult);
        }

//        task.setStatus(EnumValue.TaskStatus.SSS.getCode());
//        task.setUpdateTime(Factory.getSystemDateStr());
//        entityManager.updateDisasterRecoveryTaskStatus(task);
        return true;
    }
}
