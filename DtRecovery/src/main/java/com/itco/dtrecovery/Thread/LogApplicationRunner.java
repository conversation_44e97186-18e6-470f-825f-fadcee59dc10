package com.itco.dtrecovery.Thread;

import com.itco.framework.log.LogLevelSpring;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class LogApplicationRunner implements ApplicationRunner {
    @Override
    public void run(ApplicationArguments args) throws Exception {
        List<String> argsList = Arrays.asList(args.getSourceArgs());

        if (argsList.contains("--debug") || argsList.contains("-d")) {
            log.info("设置日志级别为：DEBUG");
            LogLevelSpring.levelSwitch("com.itco", "DEBUG");
        } else if (argsList.contains("--info") || argsList.contains("-i")) {
            log.info("设置日志级别为：INFO");
            LogLevelSpring.levelSwitch("com.itco", "INFO");
        } else if (argsList.contains("--error") || argsList.contains("-e")) {
            log.info("设置日志级别为：ERROR");
            LogLevelSpring.levelSwitch("com.itco", "ERROR");
        }
    }
}
