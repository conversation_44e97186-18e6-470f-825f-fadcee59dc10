package com.itco.dtrecovery.Thread;

import com.itco.dtrecovery.dao.DisasterRecoveryTaskDao;
import com.itco.dtrecovery.entity.*;
import com.itco.framework.Factory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

@Slf4j
@Component
public class CreateTaskManager {

    @Autowired
    EntityManager entityManager;

    @Autowired
    DisasterRecoveryTaskDao disasterRecoveryTaskDao;

    @Autowired
    CommonModule commonModule;

    int iCnt = 0;


    public boolean isTimeCrontab_tyqw(BackupConfig backupConfig) {
        if (!"1000".equals(backupConfig.getBackupStatus())) {
            log.debug("失效规则，跳过。" + backupConfig.toString());
            // 数据库已经故障，进行监控
            return false;
        }

        boolean validExpression = CronSequenceGenerator.isValidExpression(backupConfig.getCron());
        if (validExpression) {
            // 创建CronSequenceGenerator实例
            CronSequenceGenerator generator = new CronSequenceGenerator(backupConfig.getCron(), TimeZone.getTimeZone("Asia/Shanghai"));

            // 获取当前时间
            Date now = new Date();

            // 上一次触发时间
            Date lastTrigger = backupConfig.getLastTriggerDate();
            // 计算下一次触发时间
            Date nextExecutionTime = generator.next(lastTrigger);
            log.debug("cron:" + backupConfig.getCron() + "-> nextExecutionTime:" + nextExecutionTime + ",lastTrigger:" + lastTrigger + ",now():" + now);
            //log.info("nextExecutionTime.before(now):" + nextExecutionTime.before(now));
            //log.info("nextExecutionTime.equals(now):" + nextExecutionTime.equals(now));
            // 如果下一次触发时间等于当前时间或者小于当前时间（考虑到毫秒级的时间差），则返回true，表示任务该触发
            return nextExecutionTime.before(now) || nextExecutionTime.equals(now);
        } else {
            log.error("crontab:" + backupConfig.getCron() + ",无效的表达式");
        }

        return false;
    }

    public boolean isTimeCrontab_tyqw(ComponentManager componentManager) {
        if (!"1000".equals(componentManager.getStatus())) {
            log.debug("组件失效，跳过。" + componentManager.toString());
            return false;
        }

        boolean validExpression = CronSequenceGenerator.isValidExpression(componentManager.getCron());
        if (validExpression) {
            // 创建CronSequenceGenerator实例
            CronSequenceGenerator generator = new CronSequenceGenerator(componentManager.getCron(), TimeZone.getTimeZone("Asia/Shanghai"));

            // 获取当前时间
            Date now = new Date();

            // 上一次触发时间
            Date lastTrigger = componentManager.getLastTriggerDate();
            // 计算下一次触发时间
            Date nextExecutionTime = generator.next(lastTrigger);
            log.debug("cron:" + componentManager.getCron() + "-> nextExecutionTime:" + nextExecutionTime + ",lastTrigger:" + lastTrigger + ",now():" + now);
            //log.info("nextExecutionTime.before(now):" + nextExecutionTime.before(now));
            //log.info("nextExecutionTime.equals(now):" + nextExecutionTime.equals(now));
            // 如果下一次触发时间等于当前时间或者小于当前时间（考虑到毫秒级的时间差），则返回true，表示任务该触发
            return nextExecutionTime.before(now) || nextExecutionTime.equals(now);
        } else {
            log.error("crontab:" + componentManager.getCron() + ",无效的表达式");
        }

        return false;
    }

    BackupConfig findValidBackupConfig() {
        for (Map.Entry<String, BackupConfig> entry : entityManager.configMap.entrySet()) {
            BackupConfig config = entry.getValue();
            if (EnumValue.BackupType.SCHEDULED.getCode().equals(config.getBackupType())) {
                if (isTimeCrontab_tyqw(config)) {
                    return config;
                }
            }
        }
        return null;
    }

    // 创建备份恢复任务，通过定时任务触发
    boolean createBackupRecoveryTaskByScheduled() {

        int iNo = 1;
        BackupConfig validBackupConfig = null;
        do {
            validBackupConfig = findValidBackupConfig();
            if (validBackupConfig != null) {
                DisasterRecoveryTask task = new DisasterRecoveryTask();
                task.setVersion("backup_" + Factory.getSystemDateStr("yyyyMMddHHmmss") + "_" + iNo++);
                task.setTaskType(EnumValue.TaskType.BACKUP.getCode());
                task.setBackupVersion(Factory.getSystemDateStr());
                task.setBackupConfigId(validBackupConfig.getBackupConfigId());
                task.setStatus(EnumValue.TaskStatus.NCL.getCode());
                task.setCreateTime(Factory.getSystemDateStr());
                task.setUpdateTime(Factory.getSystemDateStr());
                task.setElementStr(task.toElement());

                entityManager.insertDisasterRecoveryTask(task);
                log.debug("创建备份恢复任务成功," + task.toString());

                // 刷新本次触发时间
                validBackupConfig.setLastTriggerDate(new Date());
            }
        } while (validBackupConfig != null);

        return true;
    }

    ComponentManager findValidComponentManager() {
        for (Map.Entry<String, ComponentManager> entry : entityManager.componentMap.entrySet()) {
            ComponentManager config = entry.getValue();
            if (isTimeCrontab_tyqw(config)) {
                return config;
            }
        }
        return null;
    }

    // 创建组件监控任务，通过定时任务触发
    boolean createComponentMonitorTaskByScheduled() {

        int iNo = 1;
        ComponentManager validComponentManager = null;
        do {
            validComponentManager = findValidComponentManager();
            if (validComponentManager != null) {
                DisasterRecoveryTask task = new DisasterRecoveryTask();
                task.setVersion("monitor_" + Factory.getSystemDateStr("yyyyMMddHHmmss") + "_" + iNo++);
                task.setTaskType(EnumValue.TaskType.COMPONENT_MONITOR.getCode());
                task.setBackupVersion(Factory.getSystemDateStr());
                task.setBackupConfigId(validComponentManager.getComponentId());
                task.setStatus(EnumValue.TaskStatus.NCL.getCode());
                task.setCreateTime(Factory.getSystemDateStr());
                task.setUpdateTime(Factory.getSystemDateStr());
                task.setElementStr(task.toElement());

                entityManager.createComponentMonitorTask(task);
                log.debug("创建组件监控任务成功," + task.toString());

                // 刷新本次触发时间
                validComponentManager.setLastTriggerDate(new Date());
            }
        } while (validComponentManager != null);

        //log.debug("没有可触发的组件监控任务");
        return true;
    }

    // 创建任务，通过数据库加载触发
    boolean loadTaskByDatabase() {

        entityManager.loadDisasterRecoveryTaskBySchedule();

        return true;
    }

    boolean scanConfigScheduled() {
        List<BackupConfig> backupConfigs = entityManager.scanBackupConfig();
        if (backupConfigs != null && backupConfigs.size() > 0) {
            log.info("扫描到备份配置新增：" + backupConfigs.size());
            entityManager.ReloadConfig();
        }
        return true;
    }

    @Scheduled(cron = "*/5 * * * * ?")
    public boolean createTaskstart() {

        // 定时刷新备份、恢复、监控的配置表
        //scanConfigScheduled();

        // 定时扫描生成定时任务插入数据库
        createBackupRecoveryTaskByScheduled();

        // 定时扫描生成组件监控任务插入数据库
        createComponentMonitorTaskByScheduled();

        // 加载数据库触发任务
        loadTaskByDatabase();

        return true;
    }
}
