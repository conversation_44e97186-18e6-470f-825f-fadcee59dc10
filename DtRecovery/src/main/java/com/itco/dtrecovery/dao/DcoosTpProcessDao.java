package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.DcoosTpProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 应用管理表(DcoosTpProcess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:31
 */
@Mapper
public interface DcoosTpProcessDao {

    /**
     * 通过ID查询单条数据
     *
     * @param processId 主键
     * @return 实例对象
     */
    DcoosTpProcess queryById(Long processId);

    /**
     * 查询指定行数据
     *
     * @param dcoosTpProcess 查询条件
     * @param pageable       分页对象
     * @return 对象列表
     */
    List<DcoosTpProcess> queryAllByLimit(DcoosTpProcess dcoosTpProcess, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param dcoosTpProcess 查询条件
     * @return 总行数
     */
    long count(DcoosTpProcess dcoosTpProcess);

    /**
     * 新增数据
     *
     * @param dcoosTpProcess 实例对象
     * @return 影响行数
     */
    int insert(DcoosTpProcess dcoosTpProcess);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<DcoosTpProcess> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DcoosTpProcess> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<DcoosTpProcess> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<DcoosTpProcess> entities);

    /**
     * 修改数据
     *
     * @param dcoosTpProcess 实例对象
     * @return 影响行数
     */
    int update(DcoosTpProcess dcoosTpProcess);

    /**
     * 通过主键删除数据
     *
     * @param processId 主键
     * @return 影响行数
     */
    int deleteById(Long processId);

}

