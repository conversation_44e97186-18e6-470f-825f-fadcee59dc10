package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.UserHistoryPassword;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 密码历史表(UserHistoryPassword)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:41
 */
@Mapper
public interface UserHistoryPasswordDao {

    /**
     * 通过ID查询单条数据
     *
     * @param historyPasswordId 主键
     * @return 实例对象
     */
    UserHistoryPassword queryById(Integer historyPasswordId);

    /**
     * 查询指定行数据
     *
     * @param userHistoryPassword 查询条件
     * @param pageable            分页对象
     * @return 对象列表
     */
    List<UserHistoryPassword> queryAllByLimit(UserHistoryPassword userHistoryPassword, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param userHistoryPassword 查询条件
     * @return 总行数
     */
    long count(UserHistoryPassword userHistoryPassword);

    /**
     * 新增数据
     *
     * @param userHistoryPassword 实例对象
     * @return 影响行数
     */
    int insert(UserHistoryPassword userHistoryPassword);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserHistoryPassword> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserHistoryPassword> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserHistoryPassword> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserHistoryPassword> entities);

    /**
     * 修改数据
     *
     * @param userHistoryPassword 实例对象
     * @return 影响行数
     */
    int update(UserHistoryPassword userHistoryPassword);

    /**
     * 通过主键删除数据
     *
     * @param historyPasswordId 主键
     * @return 影响行数
     */
    int deleteById(Integer historyPasswordId);

}

