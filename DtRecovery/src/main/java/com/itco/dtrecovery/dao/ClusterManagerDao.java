package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.ClusterManager;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 集群表(ClusterManager)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:30
 */
@Mapper
public interface ClusterManagerDao {

    /**
     * 通过ID查询单条数据
     *
     * @param clusterId 主键
     * @return 实例对象
     */
    ClusterManager queryById(Integer clusterId);

    /**
     * 查询指定行数据
     *
     * @param clusterManager 查询条件
     * @return 对象列表
     */
    List<ClusterManager> queryAllByLimit(ClusterManager clusterManager);

    /**
     * 查询指定行数据
     *
     * @param clusterManager 查询条件
     * @return 对象列表
     */
    List<ClusterManager> queryAllByIncrement(ClusterManager clusterManager);

    /**
     * 统计总行数
     *
     * @param clusterManager 查询条件
     * @return 总行数
     */
    long count(ClusterManager clusterManager);

    /**
     * 新增数据
     *
     * @param clusterManager 实例对象
     * @return 影响行数
     */
    int insert(ClusterManager clusterManager);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClusterManager> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ClusterManager> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ClusterManager> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ClusterManager> entities);

    /**
     * 修改数据
     *
     * @param clusterManager 实例对象
     * @return 影响行数
     */
    int update(ClusterManager clusterManager);

    /**
     * 通过主键删除数据
     *
     * @param clusterId 主键
     * @return 影响行数
     */
    int deleteById(Integer clusterId);

}

