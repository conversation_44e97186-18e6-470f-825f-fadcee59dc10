package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.BackupResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 备份结果表(BackupResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-29 17:31:22
 */
@Mapper
public interface BackupResultDao {

    /**
     * 通过ID查询单条数据
     *
     * @param backupResultId 主键
     * @return 实例对象
     */
    BackupResult queryById(Integer backupResultId);

    /**
     * 查询指定行数据
     *
     * @param backupResult 查询条件
     * @return 对象列表
     */
    List<BackupResult> queryAllByLimit(BackupResult backupResult);

    /**
     * 增量查询数据
     *
     * @param backupResult 查询条件
     * @return 对象列表
     */
    List<BackupResult> queryAllByIncrement(BackupResult backupResult);

    /**
     * 统计总行数
     *
     * @param backupResult 查询条件
     * @return 总行数
     */
    long count(BackupResult backupResult);

    /**
     * 新增数据
     *
     * @param backupResult 实例对象
     * @return 影响行数
     */
    int insert(BackupResult backupResult);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupResult> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BackupResult> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupResult> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BackupResult> entities);

    /**
     * 修改数据
     *
     * @param backupResult 实例对象
     * @return 影响行数
     */
    int update(BackupResult backupResult);

    /**
     * 通过主键删除数据
     *
     * @param backupResultId 主键
     * @return 影响行数
     */
    int deleteById(Integer backupResultId);

}

