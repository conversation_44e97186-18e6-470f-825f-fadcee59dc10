package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.BackupConfigScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 备份范围表(BackupConfigScope)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-25 08:50:13
 */
@Mapper
public interface BackupConfigScopeDao {

    /**
     * 通过ID查询单条数据
     *
     * @param scopeId 主键
     * @return 实例对象
     */
    BackupConfigScope queryById(Integer scopeId);

    /**
     * 查询指定行数据
     *
     * @param backupConfigScope 查询条件
     * @return 对象列表
     */
    List<BackupConfigScope> queryAllByLimit(BackupConfigScope backupConfigScope);

    /**
     * 按增量查询
     *
     * @param backupConfigScope 查询条件
     * @return 对象列表
     */
    List<BackupConfigScope> queryAllByIncrement(BackupConfigScope backupConfigScope);

    /**
     * 统计总行数
     *
     * @param backupConfigScope 查询条件
     * @return 总行数
     */
    long count(BackupConfigScope backupConfigScope);

    /**
     * 新增数据
     *
     * @param backupConfigScope 实例对象
     * @return 影响行数
     */
    int insert(BackupConfigScope backupConfigScope);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupConfigScope> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BackupConfigScope> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupConfigScope> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BackupConfigScope> entities);

    /**
     * 修改数据
     *
     * @param backupConfigScope 实例对象
     * @return 影响行数
     */
    int update(BackupConfigScope backupConfigScope);

    /**
     * 通过主键删除数据
     *
     * @param scopeId 主键
     * @return 影响行数
     */
    int deleteById(Integer scopeId);

}

