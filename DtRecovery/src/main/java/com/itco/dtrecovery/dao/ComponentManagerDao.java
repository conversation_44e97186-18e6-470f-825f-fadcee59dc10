package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.ComponentManager;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 数据库管理表(ComponentManager)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-17 17:23:59
 */
@Mapper
public interface ComponentManagerDao {

    /**
     * 通过ID查询单条数据
     *
     * @param componentId 主键
     * @return 实例对象
     */
    ComponentManager queryById(Integer componentId);

    /**
     * 查询指定行数据
     *
     * @param componentManager 查询条件
     * @return 对象列表
     */
    List<ComponentManager> queryAllByLimit(ComponentManager componentManager);

    /**
     * 增量查询数据
     *
     * @param componentManager 查询条件
     * @return 对象列表
     */
    List<ComponentManager> queryAllByIncrement(ComponentManager componentManager);

    /**
     * 统计总行数
     *
     * @param componentManager 查询条件
     * @return 总行数
     */
    long count(ComponentManager componentManager);

    /**
     * 新增数据
     *
     * @param componentManager 实例对象
     * @return 影响行数
     */
    int insert(ComponentManager componentManager);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComponentManager> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComponentManager> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComponentManager> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ComponentManager> entities);

    /**
     * 修改数据
     *
     * @param componentManager 实例对象
     * @return 影响行数
     */
    int update(ComponentManager componentManager);

    /**
     * 通过主键删除数据
     *
     * @param componentId 主键
     * @return 影响行数
     */
    int deleteById(Integer componentId);

}

