package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.BackupHostInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 主机信息表(BackupHostInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:30
 */
@Mapper
public interface BackupHostInfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param hostId 主键
     * @return 实例对象
     */
    BackupHostInfo queryById(Integer hostId);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<BackupHostInfo> queryAllByLimit(BackupHostInfo backupHostInfo);

    /**
     * 增量查询数据
     *
     * @return 对象列表
     */
    List<BackupHostInfo> queryAllByIncrement(BackupHostInfo backupHostInfo);

    /**
     * 统计总行数
     *
     * @param backupHostInfo 查询条件
     * @return 总行数
     */
    long count(BackupHostInfo backupHostInfo);

    /**
     * 新增数据
     *
     * @param backupHostInfo 实例对象
     * @return 影响行数
     */
    int insert(BackupHostInfo backupHostInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupHostInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BackupHostInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupHostInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BackupHostInfo> entities);

    /**
     * 修改数据
     *
     * @param backupHostInfo 实例对象
     * @return 影响行数
     */
    int update(BackupHostInfo backupHostInfo);

    /**
     * 通过主键删除数据
     *
     * @param hostId 主键
     * @return 影响行数
     */
    int deleteById(Integer hostId);

}

