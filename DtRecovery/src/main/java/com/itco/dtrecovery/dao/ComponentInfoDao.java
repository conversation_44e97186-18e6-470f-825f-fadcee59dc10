package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.ComponentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 组件实时运行信息表(ComponentInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-19 00:28:52
 */
@Mapper
public interface ComponentInfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param componentId 主键
     * @return 实例对象
     */
    ComponentInfo queryById(Integer componentId);

    /**
     * 查询指定行数据
     *
     * @param componentInfo 查询条件
     * @return 对象列表
     */
    List<ComponentInfo> queryAllByLimit(ComponentInfo componentInfo);

    /**
     * 查询指定行数据
     *
     * @param componentInfo 查询条件
     * @return 对象列表
     */
    List<ComponentInfo> queryAllByIncrement(ComponentInfo componentInfo);

    /**
     * 统计总行数
     *
     * @param componentInfo 查询条件
     * @return 总行数
     */
    long count(ComponentInfo componentInfo);

    /**
     * 新增数据
     *
     * @param componentInfo 实例对象
     * @return 影响行数
     */
    int insert(ComponentInfo componentInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComponentInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComponentInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ComponentInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ComponentInfo> entities);

    /**
     * 修改数据
     *
     * @param componentInfo 实例对象
     * @return 影响行数
     */
    int update(ComponentInfo componentInfo);

    /**
     * 通过主键删除数据
     *
     * @param componentId 主键
     * @return 影响行数
     */
    int deleteById(Integer componentId);

}

