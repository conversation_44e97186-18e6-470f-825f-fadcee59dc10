package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.DisasterRecoveryTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 容灾任务表(DisasterRecoveryTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-07 17:41:27
 */
@Mapper
public interface DisasterRecoveryTaskDao {

    /**
     * 通过ID查询单条数据
     *
     * @param version 主键
     * @return 实例对象
     */
    DisasterRecoveryTask queryById(String version);

    /**
     * 查询指定行数据
     *
     * @param disasterRecoveryTask 查询条件
     * @param pageable             分页对象
     * @return 对象列表
     */
    List<DisasterRecoveryTask> queryAllByLimit(DisasterRecoveryTask disasterRecoveryTask, @Param("pageable") Pageable pageable);

    List<DisasterRecoveryTask> queryAllByMap(Map<String, Object> map);

    /**
     * 统计总行数
     *
     * @param disasterRecoveryTask 查询条件
     * @return 总行数
     */
    long count(DisasterRecoveryTask disasterRecoveryTask);

    /**
     * 新增数据
     *
     * @param disasterRecoveryTask 实例对象
     * @return 影响行数
     */
    int insert(DisasterRecoveryTask disasterRecoveryTask);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<DisasterRecoveryTask> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DisasterRecoveryTask> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<DisasterRecoveryTask> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<DisasterRecoveryTask> entities);

    /**
     * 修改数据
     *
     * @param disasterRecoveryTask 实例对象
     * @return 影响行数
     */
    int update(DisasterRecoveryTask disasterRecoveryTask);

    /**
     * 通过主键删除数据
     *
     * @param version 主键
     * @return 影响行数
     */
    int deleteById(String version);

}

