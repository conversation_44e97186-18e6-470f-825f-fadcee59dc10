package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.BackupConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 备份规则配置表(BackupConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:26
 */
@Mapper
public interface BackupConfigDao {

    /**
     * 通过ID查询单条数据
     *
     * @param backupConfigId 主键
     * @return 实例对象
     */
    BackupConfig queryById(Integer backupConfigId);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<BackupConfig> queryAllByLimit(BackupConfig backupConfig);

    /**
     * 增量查询配置表
     *
     * @return 对象列表
     */
    List<BackupConfig> queryAllByIncrement(BackupConfig backupConfig);

    /**
     * 统计总行数
     *
     * @param backupConfig 查询条件
     * @return 总行数
     */
    long count(BackupConfig backupConfig);

    /**
     * 新增数据
     *
     * @param backupConfig 实例对象
     * @return 影响行数
     */
    int insert(BackupConfig backupConfig);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BackupConfig> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BackupConfig> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BackupConfig> entities);

    /**
     * 修改数据
     *
     * @param backupConfig 实例对象
     * @return 影响行数
     */
    int update(BackupConfig backupConfig);

    /**
     * 通过主键删除数据
     *
     * @param backupConfigId 主键
     * @return 影响行数
     */
    int deleteById(Integer backupConfigId);

}

