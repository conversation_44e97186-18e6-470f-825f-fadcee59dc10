package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.UserManager;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 密码管理表(UserManager)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:42
 */
@Mapper
public interface UserManagerDao {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    UserManager queryById(Integer userId);

    /**
     * 查询指定行数据
     *
     * @param userManager 查询条件
     * @param pageable    分页对象
     * @return 对象列表
     */
    List<UserManager> queryAllByLimit(UserManager userManager, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param userManager 查询条件
     * @return 总行数
     */
    long count(UserManager userManager);

    /**
     * 新增数据
     *
     * @param userManager 实例对象
     * @return 影响行数
     */
    int insert(UserManager userManager);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserManager> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserManager> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserManager> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserManager> entities);

    /**
     * 修改数据
     *
     * @param userManager 实例对象
     * @return 影响行数
     */
    int update(UserManager userManager);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Integer userId);

}

