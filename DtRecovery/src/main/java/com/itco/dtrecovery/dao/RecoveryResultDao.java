package com.itco.dtrecovery.dao;

import com.itco.dtrecovery.entity.RecoveryResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 恢复结果表(RecoveryResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-09 15:43:26
 */
@Mapper
public interface RecoveryResultDao {

    /**
     * 通过ID查询单条数据
     *
     * @param recoveryResultId 主键
     * @return 实例对象
     */
    RecoveryResult queryById(Integer recoveryResultId);

    /**
     * 查询指定行数据
     *
     * @param recoveryResult 查询条件
     * @param pageable       分页对象
     * @return 对象列表
     */
    List<RecoveryResult> queryAllByLimit(RecoveryResult recoveryResult, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param recoveryResult 查询条件
     * @return 总行数
     */
    long count(RecoveryResult recoveryResult);

    /**
     * 新增数据
     *
     * @param recoveryResult 实例对象
     * @return 影响行数
     */
    int insert(RecoveryResult recoveryResult);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<RecoveryResult> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RecoveryResult> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<RecoveryResult> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<RecoveryResult> entities);

    /**
     * 修改数据
     *
     * @param recoveryResult 实例对象
     * @return 影响行数
     */
    int update(RecoveryResult recoveryResult);

    /**
     * 通过主键删除数据
     *
     * @param recoveryResultId 主键
     * @return 影响行数
     */
    int deleteById(Integer recoveryResultId);

}

