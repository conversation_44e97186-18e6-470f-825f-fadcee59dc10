package com.itco.dtrecovery.entity;

import com.itco.dtrecovery.module.BaseThread;
import lombok.Data;

import java.io.Serializable;
import java.util.concurrent.Future;

/**
 * 容灾任务表(DisasterRecoveryTask)实体类
 *
 * <AUTHOR>
 * @since 2024-04-07 17:41:28
 */
@Data
public class DisasterRecoveryTask implements Serializable {
    private static final long serialVersionUID = 610152921458303943L;
    /**
     * 版本号（备份、恢复）
     */
    private String version;
    /**
     * 备份规则标识
     */
    private Integer backupConfigId;
    /**
     * 备份类型:BACKUP,RECOVERY
     */
    private String taskType;
    /**
     * 任务状态：DCL:未开始，OCL:进行中，SCL:备份成功，FCL备份失败
     */
    private String status;
    /**
     * 失败信息
     */
    private String errorMessage;
    /**
     * 备份版本号
     */
    private String backupVersion;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 操作人员
     */
    private String operateUser;
    /**
     * 备份备注
     */
    private String remark;
    /**
     * 恢复结果标识
     */
    private Integer backupResultId;
    /**
     * 恢复到执行资源标识
     */
    private Integer recoveryHostId;
    /**
     * 恢复到目录
     */
    private String recoveryDirectory;
    /**
     * 恢复到模式
     */
    private String recoverySchame;
    /**
     * 恢复到表
     */
    private String recoveryTable;
    /**
     * 状态变化轨迹
     */
    private String elementStr;

    /**
     * 任务输出全路径文件名
     */
    private String targetFullFileName;

    private String result;

    Future<?> submit;

    public String toElement() {
        return "{" +
                "version='" + version + '\'' +
                ", backupConfigId=" + backupConfigId +
                ", taskType='" + taskType + '\'' +
                ", status='" + status + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", backupVersion='" + backupVersion + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", result='" + result + '\'' +
                ", backupResultId=" + backupResultId +
                ", recoveryHostId=" + recoveryHostId +
                ", recoveryDirectory='" + recoveryDirectory + '\'' +
                ", recoverySchame='" + recoverySchame + '\'' +
                ", recoveryTable='" + recoveryTable + '\'' +
                '}';
    }


}

