package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 备份范围表(BackupConfigScope)实体类
 *
 * <AUTHOR>
 * @since 2024-03-25 08:50:13
 */
@Data
public class BackupConfigScope implements Serializable {
    private static final long serialVersionUID = -30047386393698499L;
    /**
     * 备份范围主键标识
     */
    private Integer scopeId;
    /**
     * 备份范围表
     */
    private Integer backupConfigId;
    /**
     * 英文
     */
    private String enName;
    /**
     * 中文
     */
    private String chName;
    /**
     * 顺序
     */
    private Integer orderId;


}

