package com.itco.dtrecovery.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 密码历史表(UserHistoryPassword)实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:41
 */
@Data
public class UserHistoryPassword implements Serializable {
    private static final long serialVersionUID = 422741265487878197L;
    /**
     * 历史表主键
     */
    private Integer historyPasswordId;
    /**
     * 用户标识
     */
    private Integer userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码加密后
     */
    private String passwordText;
    /**
     * 用户类型：主机用户、数据库用户、sftp用户、web页面用户
     */
    private String userType;
    /**
     * 主机ip
     */
    private String hostIp;
    /**
     * web账号的登录url地址
     */
    private String urlAddr;
    /**
     * 历史密码
     */
    private String oldPasswordText;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 生效时间
     */
    private String effDate;
    /**
     * 失效时间
     */
    private String expDate;
    /**
     * 备注
     */
    private String remark;


}

