package com.itco.dtrecovery.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 备份规则配置表(BackupConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:27
 */
@Data
public class BackupConfig implements Serializable {
    private static final long serialVersionUID = -57911533958557450L;
    /**
     * 备份规则配置标识
     */
    private Integer backupConfigId;
    /**
     * 备份类型：实时备份，定时备份
     */
    private String backupType;
    /**
     * 数据分类
     */
    private String dataType;
    /**
     * 数据等级
     */
    private String dataLevel;
    /**
     * 备份频率
     */
    private String cron;
    /**
     * 是否压缩
     */
    private String compressionEnabled;
    /**
     * 是否加密
     */
    private String encryptionEnabled;

    private String encryptionPassword;
    /**
     * 是否验证
     */
    private String verifyEnabled;
    /**
     * 备份配置状态：启用，未启用
     */
    private String backupStatus;
    /**
     * 备份范围：文件，目录，表，模式，库，
     */
    private String backupScope;
    /**
     * 备份源标识
     */
    private String sourceHostId;
    /**
     * 需要备份的目录
     */
    private String sourceDirectory;
    /**
     * 备份目标标识
     */
    private String targetHostId;
    /**
     * 备份文件存放目录默认设置
     */
    private String targetDirectory;
    /**
     * 备份文件名
     */
    private String backupFileName;
    /**
     * 备份可用区
     */
    private String storageAvailableZone;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 备份备注
     */
    private String remark;

    /*
     * 规则触发时间
     */
    private Date lastTriggerDate;

}

