package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 集群表(ClusterManager)实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:30
 */
@Data
public class ClusterManager implements Serializable {
    private static final long serialVersionUID = 185058939444237277L;
    /**
     * 集群编号
     */
    private Integer clusterId;
    /**
     * 集群英文名称
     */
    private String enName;
    /**
     * 集群中文名称
     */
    private String chName;
    /**
     * 集群转态
     */
    private String status;
    /**
     * 可用区
     */
    private String storageAvailableZone;
    /**
     * 启动时间
     */
    private String createDate;
    /**
     * 退出时间
     */
    private String exitDate;
    /**
     * 版本号
     */
    private String version;




}

