package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据库管理表(ComponentManager)实体类
 *
 * <AUTHOR>
 * @since 2024-04-17 17:24:01
 */
@Data
public class ComponentManager implements Serializable {
    private static final long serialVersionUID = 596103294496310541L;
    /**
     * 组件编号
     */
    private Integer componentId;
    /**
     * 集群编号
     */
    private Integer clusterId;
    /**
     * 组件类型，pg库，zk组件，ng组件
     */
    private String type;
    /**
     * 实例名称
     */
    private String name;
    /**
     * 模式
     */
    private String mode;
    /**
     * url地址
     */
    private String address;
    /**
     * 数据库状态
     */
    private String status;
    /**
     * 启动时间
     */
    private String createDate;
    /**
     * 退出时间
     */
    private String exitDate;
    /**
     * 可用区
     */
    private String storageAvailableZone;
    /**
     * 描述
     */
    private String description;
    /**
     * 资源标识
     */
    private Integer hostId;
    /**
     * 监听端口
     */
    private String port;
    /**
     * 定时触发监控
     */
    private String cron;

    /*
     * 规则触发时间
     */
    private Date lastTriggerDate;


}

