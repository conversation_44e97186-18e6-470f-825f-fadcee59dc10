package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 组件实时运行信息表(ComponentInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-04-19 00:28:54
 */
@Data
public class ComponentInfo implements Serializable {
    private static final long serialVersionUID = 113878043689046500L;
    /**
     * 组件标识
     */
    private Integer componentId;
    /**
     * 启动时间
     */
    private String startTime;
    /**
     * 进程号
     */
    private String processId;
    /**
     * 运行时长
     */
    private String uptime;
    /**
     * 磁盘总大小
     */
    private String totalDiskSpace;
    /**
     * 磁盘已用大小
     */
    private String usedDiskSpace;
    /**
     * 磁盘可用大小
     */
    private String availableDiskSpace;
    /**
     * 磁盘使用率
     */
    private String diskUsage;
    /**
     * 内存大小
     */
    private String totalMemory;
    /**
     * 内存使用率
     */
    private String memoryUsage;
    /**
     * CPU核数
     */
    private String cpuCores;
    /**
     * CPU使用率
     */
    private String cpuUsage;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 更新时间
     */
    private String updateDate;




}

