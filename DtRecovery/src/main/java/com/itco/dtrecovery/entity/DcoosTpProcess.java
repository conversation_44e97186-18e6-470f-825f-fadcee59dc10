package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 应用管理表(DcoosTpProcess)实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:31
 */
@Data
public class DcoosTpProcess implements Serializable {
    private static final long serialVersionUID = -22191067548936327L;
    /**
     * 进程标识
     */
    private Long processId;
    /**
     * 集群标识
     */
    private Integer clusterId;
    /**
     * 模块标识
     */
    private Integer moduleId;
    /**
     * 主机标识
     */
    private Integer hostId;
    /**
     * 进程状态 TA:关闭，TB:空闲，TC:处理中，TD:异常关闭，TE:已分配任务
     */
    private String status;
    /**
     * 处理业务类型
     */
    private String type;
    /**
     * 进程实例标识
     */
    private Integer billingLineId;
    /**
     * 性能
     */
    private Integer perl;
    /**
     * 模块编码
     */
    private String moduleCode;
    /**
     * 系统进程标识
     */
    private Integer systemProcessId;
    /**
     * 进程名称
     */
    private String processName;
    /**
     * 更新时间
     */
    private String updateDate;
    /**
     * 启动时间
     */
    private String createDate;
    /**
     * 退出时间
     */
    private String exitDate;
    /**
     * 运行主机名称
     */
    private String hostName;
    /**
     * 挂起状态 0:正常，1:挂起
     */
    private String isPause;
    /**
     * 可用区
     */
    private String storageAvailableZone;
    /**
     * 当前版本
     */
    private String version;
}

