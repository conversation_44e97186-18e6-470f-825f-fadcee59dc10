package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 备份结果表(BackupResult)实体类
 *
 * <AUTHOR>
 * @since 2024-03-29 17:31:23
 */
@Data
public class BackupResult implements Serializable {
    private static final long serialVersionUID = -30821572523584256L;
    /**
     * 备份结果标识
     */
    private Integer backupResultId;
    /**
     * 备份规则编号
     */
    private Integer backupConfigId;
    /**
     * 版本号
     */
    private String version;
    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 备份开始时间
     */
    private String backupStartTime;
    /**
     * 备份结束时间
     */
    private String backupEndTime;
    /**
     * 是否压缩:Y、N
     */
    private String compressionEnabled;
    /**
     * 是否加密:Y、N
     */
    private String encryptionEnabled;
    /**
     * 加密密码
     */
    private String encryptionPassword;
    /**
     * 备份文件存放目录默认设置
     */
    private String targetDirectory;
    /**
     * 操作人员
     */
    private String operateUser;
    /**
     * 备份到中台状态
     */
    private String backupCenterStatus;
    /**
     * 验证结果
     */
    private String verificationResults;
    /**
     * 备份备注
     */
    private String remark;


    public void setBackupResult(DisasterRecoveryTask task, BackupConfig backupConfig) {
        this.backupConfigId = task.getBackupConfigId();
        this.version = task.getVersion();
        this.success = task.getStatus().equals("SCL") ? true : false;
        this.backupCenterStatus = "Y";
        this.backupStartTime = task.getCreateTime();
        this.backupEndTime = task.getUpdateTime();
        this.compressionEnabled = backupConfig.getCompressionEnabled();
        this.encryptionEnabled = backupConfig.getEncryptionEnabled();
        this.targetDirectory = task.getTargetFullFileName();
        this.operateUser = task.getOperateUser();
        this.verificationResults = "Y";
        this.encryptionPassword = backupConfig.getEncryptionPassword();
    }

}

