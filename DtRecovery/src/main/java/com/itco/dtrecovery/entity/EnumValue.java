package com.itco.dtrecovery.entity;

public class EnumValue {


    public enum TaskStatus {
        NCL("NCL", "新增任务"),
        DCL("DCL", "未开始"),
        OCL("OCL", "进行中"),
        SCL("SCL", "任务成功"),
        FCL("FCL", "任务失败"),
        SSS("SSS", "任务状态同步到表单");

        private String code;
        private String description;

        TaskStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }


    public enum TaskType {
        BACKUP("BACKUP", "备份任务"),
        RECOVERY("RECOVERY", "恢复任务"),
        COMPONENT_MONITOR("COMPONENT_MONITOR", "组件监控"),
        SWITCH("SWITCH", "流量切换");

        private String code;
        private String description;

        TaskType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }

    public enum DataType {
        DATABASE("DATABASE", "数据库备份"),
        DATABASE_SCHEMA("DATABASE_SCHEMA", "数据库模式备份"),
        DATABASE_TABLE("DATABASE_TABLE", "数据库表备份"),
        DATA_FILE("DATA_FILE", "文件目录备份");

        private String code;
        private String description;

        DataType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }

    public enum ComponentType {
        ZOOKEEPER("zookeeper", "数据库备份"),
        NGINX("nginx", "数据库模式备份"),
        POSTGRES("postgresql", "数据库表备份");

        private String code;
        private String description;

        ComponentType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }

    //备份类型，人工备份:MANUAL、REAL_TIME:实时备份，SCHEDULED：定时备份
    public enum BackupType {
        MANUAL("MANUAL", "人工备份"),
        REAL_TIME("REAL_TIME", "实时备份"),
        SCHEDULED("SCHEDULED", "定时备份");

        private String code;
        private String description;

        BackupType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public String getCode() {
            return code;
        }
    }
}
