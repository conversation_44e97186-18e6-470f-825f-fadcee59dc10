package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 主机信息表(BackupHostInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-03-20 14:41:30
 */
@Data
public class BackupHostInfo implements Serializable {
    private static final long serialVersionUID = 733843208349807976L;
    /**
     * 主机标识
     */
    private Integer hostId;
    /**
     * 集群标识
     */
    private Integer clusterId;
    /**
     * ip地址
     */
    private String sshIp;
    /**
     * 端口号
     */
    private String sshPort;
    /**
     * 用户名
     */
    private String sshUsername;
    /**
     * 密码公钥串
     */
    private String sshPublicPassword;
    /**
     * 密码私钥串
     */
    private String sshPassword;
    /**
     * 可用区
     */
    private String storageAvailableZone;

    /**
     * 解密后的密码
     */
    private String password;

    private String hostName;

    private String home;
}

