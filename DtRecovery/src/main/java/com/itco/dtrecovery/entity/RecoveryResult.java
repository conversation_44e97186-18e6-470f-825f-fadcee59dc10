package com.itco.dtrecovery.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 恢复结果表(RecoveryResult)实体类
 *
 * <AUTHOR>
 * @since 2024-04-09 15:43:31
 */
@Data
public class RecoveryResult implements Serializable {
    private static final long serialVersionUID = -81306884826376580L;
    /**
     * 恢复结果标识
     */
    private Integer recoveryResultId;
    /**
     * 备份规则编号
     */
    private Integer backupConfigId;
    /**
     * 版本号
     */
    private String version;
    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 恢复文件存放目录
     */
    private String targetDirectory;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 恢复开始时间
     */
    private String recoveryStartTime;
    /**
     * 恢复结束时间
     */
    private String recoveryEndTime;
    /**
     * 操作人员
     */
    private String operateUser;
    /**
     * 验证结果
     */
    private String verificationResults;
    /**
     * 恢复备注
     */
    private String remark;


    public boolean setRecoveryResult(DisasterRecoveryTask task, BackupConfig backupConfig, RecoveryResult recoveryResult) {
        this.backupConfigId = backupConfig == null ? null : backupConfig.getBackupConfigId();
        this.version = task.getVersion();
        this.targetDirectory = task.getTargetFullFileName();
        this.success = task.getStatus().equals("SCL") ? true : false;
        this.errorMessage = task.getErrorMessage();
        this.recoveryStartTime = task.getCreateTime();
        this.recoveryEndTime = task.getUpdateTime();
        this.operateUser = task.getOperateUser();
        this.verificationResults = "Y";

        return true;
    }

}

