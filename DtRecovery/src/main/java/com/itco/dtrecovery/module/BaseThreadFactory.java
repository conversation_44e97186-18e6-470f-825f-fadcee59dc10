package com.itco.dtrecovery.module;

import com.itco.dtrecovery.Thread.EntityManager;
import com.itco.dtrecovery.entity.*;
import com.itco.dtrecovery.module.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class BaseThreadFactory {

    @Autowired
    EntityManager entityManager;

    @Autowired
    CommonModule commonModule;

    BaseThread getClassByBackupTask(DisasterRecoveryTask task) {
        BackupConfig backupConfig = entityManager.getConfigMap().get(String.valueOf(task.getBackupConfigId()));
        BackupHostInfo sourcehostInfo = entityManager.getHostMap().get(backupConfig.getSourceHostId());
        //BackupHostInfo targethostInfo = entityManager.getHostMap().get(backupConfig.getTargetHostId());
        List<BackupConfigScope> scopeList = entityManager.getScopeMap().get(String.valueOf(backupConfig.getBackupConfigId()));

        log.info(task.toString());
        if (sourcehostInfo == null) {
            task.setErrorMessage("没有找到备份配置源资源信息：" + backupConfig.getSourceHostId());
            return null;
        }
        /*if (targethostInfo == null) {
            task.setErrorMessage("没有找到备份配置目标资源信息：" + backupConfig.getTargetHostId());
            return null;
        }*/

        BaseResasterThread baseResasterThread = null;
        if (EnumValue.DataType.DATABASE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new BackupPostgres();
        } else if (EnumValue.DataType.DATABASE_SCHEMA.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new BackupPostgres();
        } else if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new BackupPostgres();
        } else if (EnumValue.DataType.DATA_FILE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new BackupFile();
        }

        if (baseResasterThread == null) {
            task.setErrorMessage("暂不支持的备份恢复任务类型");
            log.error("暂不支持的备份恢复任务类型");
            return null;
        }
        baseResasterThread.setEntityManager(entityManager);
        baseResasterThread.setTask(task);
        baseResasterThread.setBackupConfig(backupConfig);
        baseResasterThread.setSourcehostInfo(sourcehostInfo);
        //baseResasterThread.setTargethostInfo(targethostInfo);
        baseResasterThread.setScopeList(scopeList);
        baseResasterThread.setCommonModule(commonModule);

        return baseResasterThread;
    }

    BaseThread getClassByRecoveryTask(DisasterRecoveryTask task) {
        BackupConfig backupConfig = entityManager.getConfigMap().get(String.valueOf(task.getBackupConfigId()));
        BackupHostInfo sourcehostInfo = entityManager.getHostMap().get(backupConfig.getSourceHostId());
        //BackupHostInfo targethostInfo = entityManager.getHostMap().get(backupConfig.getTargetHostId());
        List<BackupConfigScope> scopeList = entityManager.getScopeMap().get(String.valueOf(backupConfig.getBackupConfigId()));

        log.info(task.toString());
        if (backupConfig == null) {
            task.setErrorMessage("没有找到备份配置信息：" + task.getBackupConfigId());
            return null;
        }
        if (sourcehostInfo == null) {
            task.setErrorMessage("没有找到恢复配置源资源信息，source_host_id字段值为："+backupConfig.getSourceHostId());
            return null;
        }
        /*if (targethostInfo == null) {
            task.setErrorMessage("没有找到恢复配置资源信息，target_host_id字段值为："+backupConfig.getTargetHostId());
            return null;
        }*/

        BackupResult backupResult = entityManager.getBackupResult(task.getBackupResultId());
        if (backupResult == null) {
            task.setErrorMessage("没有找到可以恢复的备份结果：" + task.getBackupResultId());
            log.error("没有找到可以恢复的备份结果：" + task.getBackupResultId());
            return null;
        }
        if (task.getRecoveryHostId() == null) {
            task.setErrorMessage("恢复的主机标识不能为空：" + task.getVersion());
            log.error("恢复的资源标识不能为空：" + task.getVersion());
            return null;
        }
        BackupHostInfo recoveryhostInfo = entityManager.getHostMap().get(String.valueOf(task.getRecoveryHostId()));
        if (recoveryhostInfo == null) {
            task.setErrorMessage("没有找到可以恢复的主机信息：" + task.getRecoveryHostId());
            log.error("没有找到可以恢复的主机信息：" + task.getRecoveryHostId());
            return null;
        }

        BaseResasterThread baseResasterThread = null;
        if (EnumValue.DataType.DATABASE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new RecoveryPostgres();
        } else if (EnumValue.DataType.DATABASE_SCHEMA.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new RecoveryPostgres();
        } else if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new RecoveryPostgres();
        } else if (EnumValue.DataType.DATA_FILE.getCode().equals(backupConfig.getDataType())) {
            baseResasterThread = new RecoveryFile();
        }

        if (baseResasterThread == null) {
            task.setErrorMessage("暂不支持的备份恢复任务类型");
            log.error("暂不支持的备份恢复任务类型");
            return null;
        }

        baseResasterThread.setEntityManager(entityManager);
        baseResasterThread.setTask(task);
        baseResasterThread.setBackupConfig(backupConfig);
        baseResasterThread.setSourcehostInfo(sourcehostInfo);
        //baseResasterThread.setTargethostInfo(targethostInfo);
        baseResasterThread.setScopeList(scopeList);
        baseResasterThread.setBackupResult(backupResult);
        baseResasterThread.setCommonModule(commonModule);
        baseResasterThread.setRecoveryhostInfo(recoveryhostInfo);

        return baseResasterThread;
    }

    BaseThread getClassByMonitorTask(DisasterRecoveryTask task) {
        log.info(task.toString());
        ComponentManager componentManager = entityManager.getComponentMap().get(String.valueOf(task.getBackupConfigId()));
        BackupHostInfo hostInfo = entityManager.getHostMap().get(String.valueOf(componentManager.getHostId()));

        if (componentManager == null) {
            task.setErrorMessage("没有找到组件管理器");
            log.error("没有找到组件管理器：" + task.getBackupConfigId());
            return null;
        }

        BaseMonitorThread baseMonitorThread = null;
        if (EnumValue.ComponentType.POSTGRES.getCode().equals(componentManager.getType())) {
            baseMonitorThread = new MonitorPostgres();
        } else if (EnumValue.ComponentType.ZOOKEEPER.getCode().equals(componentManager.getType())) {
            baseMonitorThread = new MonitorZookeeper();
        } else if (EnumValue.ComponentType.NGINX.getCode().equals(componentManager.getType())) {
            baseMonitorThread = new MonitorNginx();
        }

        if (baseMonitorThread == null) {
            log.error("监控线程实例化失败：" + task.getBackupConfigId());
            return null;
        }

        baseMonitorThread.setEntityManager(entityManager);
        baseMonitorThread.setTask(task);
        baseMonitorThread.setComponentManager(componentManager);
        baseMonitorThread.setCommonModule(commonModule);
        baseMonitorThread.setMonitorHostInfo(hostInfo);

        return baseMonitorThread;
    }

    public BaseThread getCalcClassByTask(DisasterRecoveryTask task) {
        if (EnumValue.TaskType.BACKUP.getCode().equals(task.getTaskType())) {
            return getClassByBackupTask(task);
        } else if (EnumValue.TaskType.RECOVERY.getCode().equals(task.getTaskType())) {
            return getClassByRecoveryTask(task);
        } else if (EnumValue.TaskType.COMPONENT_MONITOR.getCode().equals(task.getTaskType())) {
            return getClassByMonitorTask(task);
        }
        return null;
    }

    /*
     * @decription TODO
     * @param className
     * @return 返回值
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public BaseResasterThread getCalcInstance(String className) {
        Class<?> clazz = null;
        BaseResasterThread baseResasterThread = null;
        try {
            clazz = Class.forName(className);
            baseResasterThread = (BaseResasterThread) clazz.newInstance();
            return baseResasterThread;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + className + "," + e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public BaseResasterThread getCalcBackupInstance(Class<? extends BaseResasterThread> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + BaseResasterThread.class + "," + e.getMessage());
        }
        return null;
    }

    public BaseMonitorThread getCalcMonitorInstance(Class<? extends BaseMonitorThread> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + BaseResasterThread.class + "," + e.getMessage());
        }
        return null;
    }

    public BaseResasterThread getCalcInstance(Class<? extends BaseResasterThread> clazz, String className) {
        BaseResasterThread baseResasterThread = null;
        if (null == clazz) {
            baseResasterThread = getCalcInstance(className);
            if (null == baseResasterThread) {
                log.error("备份恢复线程类实例化失败：" + className);
                return null;
            }
            log.info("备份恢复线程类名：" + className);
        } else {
            baseResasterThread = getCalcBackupInstance(clazz);
            if (null == baseResasterThread) {
                log.error("备份恢复线程实例化失败：" + clazz.getName());
                return null;
            }
            log.info("备份恢复线程名：" + clazz.getName());
        }

        return baseResasterThread;
    }


}