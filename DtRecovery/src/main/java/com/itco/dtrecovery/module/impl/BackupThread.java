package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.BackupConfig;
import com.itco.dtrecovery.entity.DisasterRecoveryTask;
import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseResasterThread;
import com.itco.framework.Factory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BackupThread extends BaseResasterThread{



    @Override
    public void run() {


        try {



            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
        } catch (Exception e) {
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
        }finally {
            OnExit();
        }
    }
}
