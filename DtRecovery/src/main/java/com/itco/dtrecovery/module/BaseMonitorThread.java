package com.itco.dtrecovery.module;

import com.itco.dtrecovery.entity.*;
import com.itco.dtrecovery.module.impl.MonitorComponent;
import com.itco.dtrecovery.module.impl.MonitorNginx;
import com.itco.dtrecovery.module.impl.MonitorPostgres;
import com.itco.dtrecovery.module.impl.MonitorZookeeper;
import com.itco.framework.Factory;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Data
@Slf4j
public abstract class BaseMonitorThread extends BaseThread {

    public ComponentManager componentManager;

    public BackupHostInfo monitorHostInfo;

    String commandResult = "";

    ComponentInfo componentInfo;

    Session session = null;

    public List<String> getPidByCommand(String port) {
        //netstat -nltp|grep -v tcp6 |grep 18921
        List<String> command = new ArrayList<>();
        command.add("netstat");
        command.add("-nltp");
        command.add("|");
        command.add("grep");
        command.add("-v");
        command.add("tcp6");
        command.add("|");
        command.add("grep");
        command.add(":" + port);

        return command;
    }

    public List<String> getProInfoByCommand(String user, String component, String pid) {
        String userTmp = user;
        if (MonitorPostgres.componentName.equals(component)) {
            userTmp = "postgres";
        }

        List<String> command = new ArrayList<>();
        command.add("ps");
        command.add("-u");
        command.add(userTmp);
        command.add("-o");
        command.add("pid,lstart,etime,cmd");
        command.add("--sort=start_time");
        command.add("|");
        command.add("grep");
        command.add("-v");
        command.add("grep");
        command.add("|");
        command.add("grep");
        command.add(component);
/*        command.add("|");
        command.add("awk '{print $1, $2, $3, $4, $5, $6, $7, $8, $9}'");*/

        return command;
    }

    boolean monitorProcess(String componentName, String componentPort) {
        // 1 执行命令，获取进程信息
        List<String> commandInfo = getProInfoByCommand(monitorHostInfo.getSshUsername(), componentName, componentPort);  // 执行的命令
        commandResult = "";
        if (!executeCommand(commandInfo)) {
            log.error("执行命令失败");
            return false;
        }
        commandResult = commandResult.trim();
        log.info("命令执行结果：" + commandResult);

        // 解析命令的输出，判断数据库是否正常运行
        //String commandResult = "24728 Wed Apr 17 14:18:46 2024  1-01:52:40 /home/<USER>/opensource/postgres_slave/bin/postgres -D /home/<USER>/opensource/postgres_slave/data";
        // commandResult 格式为：pid lstart etime cmd
        // 其中 pid 为进程号，lstart 为启动时间，etime 为运行时间，cmd 为命令
        String[] parts = commandResult.split("\\s+"); // Split by whitespace
        if (parts.length >= 7) {
            String pid = parts[0];
            String lstartTmp = parts[1] + " " + parts[2] + " " + parts[3] + " " + parts[4] + " " + parts[5]; // Combine date and time
            String lstart = convertToyyyyMMddHHmmss(lstartTmp);
            String etime = parts[6];
            String[] subParts = Arrays.copyOfRange(parts, 7, parts.length);
            String cmd = String.join(" ", subParts);

            componentInfo.setProcessId(pid);
            componentInfo.setStartTime(lstart);
            componentInfo.setUptime(etime);
            System.out.println("进程号:" + pid + ",启动时间:" + lstart + ",启动时长:" + etime + ",命令:" + cmd);
        } else {
            log.error("无法解析命令输出。commandResult:" + commandResult);
            return false;
        }

        return true;
    }

    public List<String> getDiskByCommand() {
        // df -m| grep "$(echo ~ | cut -d'/' -f2)" | awk 'NR==1 {print $2 " " $3 " " $4 " " $5}'
        List<String> command = new ArrayList<>();
        command.add("df");
        command.add("-m");
        command.add("|");
        command.add("grep");
        command.add("$(echo ~ | cut -d'/' -f2)");
        command.add("|");
        command.add("awk");
        command.add("'NR==1 {print $2 \" \" $3 \" \" $4 \" \" $5}'");

        return command;
    }

    boolean monitorDisk(String componentName) {
        // 2 执行命令，获取磁盘信息
        List<String> diskByCommand = getDiskByCommand();
        commandResult = "";
        if (!executeCommand(diskByCommand)) {
            log.error("执行命令失败");
            return false;
        }
        log.info("命令执行结果：" + commandResult);
        String[] diskParts = commandResult.split("\\s+"); // Split by whitespace
        if (diskParts.length >= 4) {
            String total = diskParts[0];
            String used = diskParts[1];
            String available = diskParts[2];
            String usePercent = diskParts[3];

            componentInfo.setTotalDiskSpace(total);
            componentInfo.setUsedDiskSpace(used);
            componentInfo.setAvailableDiskSpace(available);
            componentInfo.setDiskUsage(usePercent);

            log.info("磁盘信息，总大小：" + total + " ，已用大小：" + used + " ，可用大小：" + available + " ，使用率：" + usePercent);
        } else {
            log.error("无法解析命令输出。commandResult:" + commandResult);
            return false;
        }

        return true;
    }

    public List<String> getCpuCoresInfoCommand() {
        List<String> command = new ArrayList<>();
        command.add("nproc");
        return command;
    }

    public List<String> getCpuInfoByCommand() {
        // top -b -n 1| grep 'Cpu(s)'| awk '{print $2+$4"%"}'
        List<String> command = new ArrayList<>();
        command.add("top");
        command.add("-b");
        command.add("-n");
        command.add("1");
        command.add("|");
        command.add("grep");
        command.add("'Cpu(s)'");
        command.add("|");
        command.add("awk");
        command.add("'{print $2+$4\"%\"}'");

        return command;
    }

    public boolean monitorCpu() {
        // 3 执行命令，获取cpu信息
        // 3.1 执行命令，获取CPU核心数
        List<String> cpuCoresCommand = getCpuCoresInfoCommand();
        commandResult = "";
        if (!executeCommand(cpuCoresCommand)) {
            log.error("获取CPU核心数失败");
            return false;
        }
        log.info("CPU核心数：" + commandResult.trim());

        // Set CPU cores information
        componentInfo.setCpuCores(commandResult.trim());

        // 3.2 执行命令，获取CPU使用率
        List<String> cpuCommand = getCpuInfoByCommand();
        commandResult = "";
        if (!executeCommand(cpuCommand)) {
            log.error("执行命令失败");
            return false;
        }
        log.info("CPU命令执行结果：" + commandResult);

        // Parse command output to extract CPU usage
        // Assuming the output is in percentage format, e.g., "12.3"
        try {
            componentInfo.setCpuUsage(String.valueOf(commandResult));
            log.info("CPU使用率：" + commandResult);
        } catch (NumberFormatException e) {
            log.error("无法解析CPU命令输出。commandResult:" + commandResult);
            return false;
        }

        return true;
    }

    public List<String> getMemoryInfoByCommand() {
        // free -m | grep Mem | awk '{print $2 " " $3 " " $4 " " $3/$2*100"%"}'
        List<String> command = new ArrayList<>();
        command.add("free");
        command.add("-m");
        command.add("|");
        command.add("grep");
        command.add("Mem");
        command.add("|");
        command.add("awk");
        command.add("'{print $2 \" \" $3 \" \" $4 \" \" $3/$2*100\"%\"}'");

        return command;
    }

    public boolean monitorMemory() {
        // 4 执行命令，获取内存信息
        List<String> memoryCommand = getMemoryInfoByCommand();
        commandResult = "";
        if (!executeCommand(memoryCommand)) {
            log.error("执行命令失败");
            return false;
        }
        log.info("内存命令执行结果：" + commandResult);

        // Parse command output to extract memory information
        // Assuming the output is in format: "totalMemory usedMemory freeMemory memoryUsagePercentage"
        String[] memoryParts = commandResult.split("\\s+"); // Split by whitespace
        if (memoryParts.length == 4) {
            String totalMemory = memoryParts[0];
            String usedMemory = memoryParts[1];
            String freeMemory = memoryParts[2];
            String memoryUsage = memoryParts[3];

            componentInfo.setTotalMemory(totalMemory);
            componentInfo.setMemoryUsage(memoryUsage);

            log.info("内存信息，总大小：" + totalMemory + "MB ，已用大小：" + usedMemory + "MB ，可用大小：" + freeMemory + "MB ，使用率：" + memoryUsage);
        } else {
            log.error("无法解析内存命令输出。commandResult:" + commandResult);
            return false;
        }

        return true;
    }

    boolean sshConnection() throws JSchException {
        String sshIP = monitorHostInfo.getSshIp();
        String sshUser = monitorHostInfo.getSshUsername();
        String sshPassword = monitorHostInfo.getPassword();
        String sshPort = String.valueOf(monitorHostInfo.getSshPort());

        // 请补充完整的代码，执行命令，并返回结果
        JSch jsch = new JSch();
        session = jsch.getSession(sshUser, sshIP, Integer.parseInt(sshPort));
        session.setPassword(sshPassword);

        // Avoid asking for key confirmation.
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        return true;
    }

    // ssh 登录远程服务器，执行命令
    public boolean monitorComponent(String componentName, String componentPort) {
        // 创建组件信息对象
        componentInfo = new ComponentInfo();
        componentInfo.setComponentId(componentManager.getComponentId());
        componentInfo.setCreateDate(Factory.getSystemDateStr());
        componentInfo.setUpdateDate(Factory.getSystemDateStr());

        try {
            // 连接远程服务器
            if (!sshConnection()) {
                log.error("连接远程服务器失败");
                return false;
            }

            // 1 执行命令，获取进程信息
            if (!monitorProcess(componentName, componentPort)) {
                log.error("监控进程失败");
                return false;
            }

            // 2 执行命令，获取磁盘信息
            if (!monitorDisk(componentName)) {
                log.error("监控磁盘失败");
                return false;
            }

            // 3 执行命令，获取cpu信息
            if (!monitorCpu()) {
                log.error("监控CPU失败");
                return false;
            }

            // 4 执行命令，获取内存信息
            if (!monitorMemory()) {
                log.error("监控内存失败");
                return false;
            }

            // 5 更新数据库信息
            entityManager.insertOrUpdateComponentInfo(Arrays.asList(componentInfo));
        } catch (Exception e) {
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
            return false;
        } finally {
            if (session != null) {
                // 关闭连接
                session.disconnect();
                session = null;
            }
        }

        log.info("监控组件成功");
        return true;
    }


    public String convertToyyyyMMddHHmmss(String inputTime) {
        try {
            log.info("inputTime:" + inputTime);
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM d HH:mm:ss yyyy", Locale.ENGLISH);
            Date date = inputFormat.parse(inputTime);

            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return inputTime;
        }
    }


    boolean executeCommand(List<String> command) {
        log.info("执行命令：" + String.join(" ", command));

        try {
            // Open a channel to execute the command
            ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
            String commandStr = String.join(" ", command);
            channelExec.setCommand(commandStr);

            // Set up input and output streams for the command execution
            InputStream in = channelExec.getInputStream();
            InputStream err = channelExec.getErrStream();

            channelExec.connect();

            // Read the output of the command
            String line;
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            StringBuilder output = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                commandResult = line;
            }

            // Check for any errors
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(err));
            StringBuilder errorOutput = new StringBuilder();

            line = "";
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
                System.out.println("error line:" + line);
            }

            int exitStatus = channelExec.getExitStatus();

            // Close the channel
            channelExec.disconnect();

            // Log the output and error messages
            if (exitStatus == 0) {
                log.info("Command executed successfully. Output:\n" + output.toString());
            } else {
                log.error("Command execution failed. Error:\n" + errorOutput.toString());
            }

            return exitStatus == 0;
        } catch (Exception e) {
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
            return false;
        }
    }


    boolean createTaskResult() {
//        task.setStatus(EnumValue.TaskStatus.SSS.getCode());
//        task.setUpdateTime(Factory.getSystemDateStr());
//        entityManager.updateDisasterRecoveryTaskStatus(task);
        return true;
    }

    public void OnExit() {
        task.setUpdateTime(Factory.getSystemDateStr());
        task.setElementStr(task.getElementStr() + task.toElement());
        task.setErrorMessage(errorMsg);

        // 更新任务状态
        entityManager.updateDisasterRecoveryTaskStatus(task);
        // 插入任务结果日志表
        createTaskResult();
    }
}
