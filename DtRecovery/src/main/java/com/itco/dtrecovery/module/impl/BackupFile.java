package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.BackupConfigScope;
import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseResasterThread;
import com.itco.framework.Factory;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Slf4j
public class BackupFile extends BaseResasterThread {

    @Override
    public void run() {
        try {
            log.info("--------------------开始执行备份任务：" + task.getVersion() + ",涉及配置如下：--------------------");
            System.out.println(task.toString());
            System.out.println(backupConfig.toString());

            System.out.println("source :" + sourcehostInfo.toString());
            //System.out.println("target :" + targethostInfo.toString());
            System.out.println();

            if (!work()) {
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                log.error("BackupFile,task_type:" + task.getTaskType() + ",data_type:" + backupConfig.getDataType() + ",version:" + task.getVersion() + ",errorMsg:" + errorMsg);
                return;
            }

            Thread.sleep(10);
            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
            log.info("--------------------备份文件任务完成:" + task.getVersion() + "--------------------");
        } catch (Exception e) {
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
        } finally {
            OnExit();
        }
    }

    boolean work() throws IOException, InterruptedException {
        // 备份导出
        if (!backupFile()) {
            log.error("backupFile()失败");
            return false;
        }

        // 压缩
        if ("Y".equals(backupConfig.getCompressionEnabled())) {
            String sourcePath = targetFileFullPath;
            if (!compressionFile(sourcePath)) {
                return false;
            }
            // 压缩成功后删除源文件
            if (deletePath(sourcePath)) {
                System.out.println("源文件删除成功： " + sourcePath);
            } else {
                System.out.println("源文件删除失败： " + sourcePath);
            }
        }


        // 加密
        if ("Y".equals(backupConfig.getEncryptionEnabled())) {
            String sourcePath = targetFileFullPath;
            //添加文件加密逻辑。
            if (!encryptFile(sourcePath)) {
                return false;
            }
            // 压缩成功后删除源文件
            if (deletePath(sourcePath)) {
                System.out.println("源文件删除成功： " + sourcePath);
            } else {
                System.out.println("源文件删除失败： " + sourcePath);
            }
        }

        log.info("BackupFile work() end");
        return true;
    }

    ChannelSftp sftpChannel;

    public boolean backupFile() {
        log.error("backupFile() start");

        String remoteHost = sourcehostInfo.getSshIp();
        int remotePort = Integer.parseInt(sourcehostInfo.getSshPort());
        String username = sourcehostInfo.getSshUsername();
        String password = sourcehostInfo.getPassword();
        //String remoteFilePath = backupConfig.getSourceDirectory();
        int count = backupConfig.getTargetDirectory().split("/").length - 1;
        if (count < 3) {
            log.error("备份目标目录格式不正确，请检查");
            return false;
        }
        String homePath = System.getenv("HOME");
        String localBackupPath = homePath + backupConfig.getTargetDirectory().substring(backupConfig.getTargetDirectory().indexOf("/", backupConfig.getTargetDirectory().indexOf("/", backupConfig.getTargetDirectory().indexOf("/") + 1) + 1));

        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(username, remoteHost, remotePort);
            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();
            log.debug("登录成功");

            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            log.debug("打开SFTP通道成功");

            String timeTemp = Factory.getSystemDateStr("yyyyMMddHHmmss");
            for (BackupConfigScope scopeTmp : scopeList) {
                String remoteFilePath = scopeTmp.getEnName();

                SftpATTRS attrs = sftpChannel.lstat(remoteFilePath);
                if (attrs.isDir()) {
                    // If it's a directory, create a corresponding local directory
                    String localBackupPathTmp = localBackupPath + "/" + backupConfig.getBackupFileName() + timeTemp + "/" + remoteFilePath.substring(remoteFilePath.lastIndexOf('/') + 1);
                    File localDir = new File(localBackupPathTmp);
                    if (!localDir.exists()) {
                        localDir.mkdirs();
                    }

                    // Recursively download all files and subdirectories within the remote directory
                    downloadDirectory(remoteFilePath, localBackupPathTmp);
                    targetFileFullPath = localBackupPath + "/" + backupConfig.getBackupFileName() + timeTemp;
                } else {
                    // If it's a file, download the file directly
                    String remoteFileName = remoteFilePath.substring(remoteFilePath.lastIndexOf('/') + 1);
                    String tmpFileName = localBackupPath + "/" + "~" + remoteFileName;
                    log.debug("remoteFilePath:" + remoteFilePath + " -> tmpFileName:" + tmpFileName);
                    sftpChannel.get(remoteFilePath, tmpFileName);
                    log.debug("Downloaded file: " + remoteFilePath);

                    //tmpFileName文件重命名去掉波浪号
                    File tmpFile = new File(tmpFileName);
                    tmpFile.renameTo(new File(localBackupPath + "/" + remoteFileName));
                    targetFileFullPath = localBackupPath + "/" + remoteFileName;
                }
                log.info("拉取:" + remoteFilePath + " 到 " + localBackupPath + " 目录文件成功");
            }

            sftpChannel.disconnect();
            session.disconnect();
            log.debug("断开SFTP通道和会话成功");

        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            e.printStackTrace();
            return false;
        }

        log.info("拉取文件成功");
        log.error("backupFile() close");
        return true;
    }


    // Method to recursively download a directory and its contents
    private void downloadDirectory(String remotePath, String localPath) throws SftpException {
        Vector<ChannelSftp.LsEntry> entries = sftpChannel.ls(remotePath);
        for (ChannelSftp.LsEntry entry : entries) {
            String entryName = entry.getFilename();
            if (!entryName.equals(".") && !entryName.equals("..")) {
                String remoteFilePath = remotePath + "/" + entryName;
                String localFilePath = localPath + "/" + entryName;
                if (entry.getAttrs().isDir()) {
                    // If it's a directory, create a corresponding local directory and recursively download its contents
                    File localDir = new File(localFilePath);
                    if (!localDir.exists()) {
                        localDir.mkdirs();
                    }
                    downloadDirectory(remoteFilePath, localFilePath);
                } else {
                    // If it's a file, download the file directly
                    sftpChannel.get(remoteFilePath, localFilePath);
                    log.info("Downloaded file: " + remoteFilePath);
                }
            }
        }
    }
}
