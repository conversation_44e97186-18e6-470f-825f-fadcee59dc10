package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseResasterThread;
import com.itco.framework.Factory;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;

@Slf4j
public class RecoveryPostgres extends BaseResasterThread implements Runnable {

    @Override
    public void run() {
        try {
            log.info("--------------------开始执行恢复任务：" + task.getBackupResultId() + ",涉及配置如下：--------------------");
            System.out.println(backupResult.toString());
            System.out.println(backupConfig.toString());

            System.out.println("source :" + sourcehostInfo.toString());
            //System.out.println("target :" + targethostInfo.toString());
            System.out.println();


            if (!work()) {
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                log.error("BackupPostgres,task_type:" + task.getTaskType() + ",data_type:" + backupConfig.getDataType() + ",version:" + task.getVersion() + ",errorMsg:" + errorMsg);
                return;
            }

            Thread.sleep(10);
            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
            log.info("--------------------恢复数据库任务完成:" + task.getVersion() + "--------------------");
        } catch (Exception e) {
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
        } finally {
            OnExit();
        }
    }


    boolean work() throws IOException, InterruptedException {
        baseWorkPath = commonModule.getBaseWorkDir() + "/" + Factory.getSystemDateStr("yyyyMMddHHmmss_" + backupResult.getBackupResultId());
        targetFileFullPath = backupResult.getTargetDirectory();
        int flowNo = 0;

        if (!mkdirs(baseWorkPath)) {
            return false;
        }

        // 解密
        if ("Y".equals(backupResult.getEncryptionEnabled())) {
            String sourcePath = targetFileFullPath;

            //添加文件解密逻辑。
            if (!decryptFile(sourcePath)) {
                return false;
            }
            deletePath(flowNo++, sourcePath);
            log.info("解密文件成功,文件路径:" + targetFileFullPath);
        }

        // 解压
        if ("Y".equals(backupResult.getCompressionEnabled())) {
            String sourcePath = targetFileFullPath;

            if (!decompressFile(targetFileFullPath, task.getTaskType(), backupConfig.getDataType())) {
                return false;
            }
            deletePath(flowNo++, sourcePath);
            log.info("解压文件成功,文件路径:" + targetFileFullPath);
        }

        String sourcePath = targetFileFullPath;
        // 恢复
        if (!recoveryPostgres()) {
            log.error("RecoveryFile work()失败");
            return false;
        }
        deletePath(flowNo++, sourcePath);

        log.info("恢复文件成功,文件路径:" + targetFileFullPath);

        log.info("RecoveryFile work() end");
        return true;
    }


    List<String> getCommand() {
        String progreamPath = System.getenv().get("PGHOME") + "/bin/pg_restore";

        //bin/pg_restore -U digit -h *********** -p 18921 -d bill_db -O -x -n schema_name -t table_name < bill_db_20240219.dat
        List<String> command = new ArrayList<>(Arrays.asList(
                progreamPath,
                "-U", recoveryhostInfo.getSshUsername(),
                "-h", recoveryhostInfo.getSshIp(),
                "-p", recoveryhostInfo.getSshPort(),
                "-d", backupConfig.getSourceDirectory(),
                "-O",
                "-x"/*,
                "-c"*/
        ));

        // 添加模式
        if (EnumValue.DataType.DATABASE_SCHEMA.getCode().equals(backupConfig.getDataType()) || EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
            command.add("-n");
            command.add(backupConfig.getBackupScope());
        }

        // 添加表
        if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
            for (int i = 0; i < scopeList.size(); i++) {
                command.add("-t");
                command.add(scopeList.get(i).getEnName().substring(scopeList.get(i).getEnName().indexOf(".") + 1));
            }
        }
//        command.add(backupResult.getTargetDirectory());
        command.add(targetFileFullPath);

        return command;
    }

    private boolean recoveryPostgres() throws IOException, InterruptedException {
        ProcessBuilder processBuilder = new ProcessBuilder();

        // 构建 pg_restore 命令
        List<String> command = getCommand();

        log.info("最终恢复执行命令：" + String.join(" ", command));
        processBuilder.command(command);

        // 设置环境变量 PGPASSWORD
        Map<String, String> env = processBuilder.environment();
        env.put("PGPASSWORD", recoveryhostInfo.getPassword());

        // 设置输出流重定向，将pg_restore命令的输出直接打印到日志中
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // 读取输出流，打印 pg_restore 的执行结果
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }
        }

        // 等待进程终止并获取退出状态
        int exitCode = process.waitFor();
        log.info("recoveryPostgres() exited with code: " + exitCode);

        // 根据 pg_restore 的退出状态判断是否成功
        return exitCode == 0;
    }

}
