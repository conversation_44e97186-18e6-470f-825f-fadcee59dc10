package com.itco.dtrecovery.module;

import com.itco.dtrecovery.Thread.EntityManager;
import com.itco.dtrecovery.entity.*;
import com.itco.framework.Factory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Data
public abstract class BaseResasterThread extends BaseThread {

    public BackupConfig backupConfig;

    public BackupHostInfo sourcehostInfo;
    //public BackupHostInfo targethostInfo;

    public BackupHostInfo recoveryhostInfo; // 用于恢复的主机信息
    public List<BackupConfigScope> scopeList;

    public String targetFileFullPath;
    public String sourceFileFullPath;
    public String baseWorkPath;
    public BackupResult backupResult;

    public void OnExit() {
        task.setUpdateTime(Factory.getSystemDateStr());
        task.setElementStr(task.getElementStr() + task.toElement());
        task.setErrorMessage(errorMsg);
        task.setTargetFullFileName(targetFileFullPath);

        // 更新任务状态
        entityManager.updateDisasterRecoveryTaskStatus(task);
        // 插入任务结果日志表
        //createTaskResult();
    }

    //压缩文件
    public boolean compressionFile(String inputFile) throws IOException, InterruptedException {
        int exitCode = -1;
        String preFileName;
        if (inputFile.indexOf(".") > 0) {
            preFileName = inputFile.substring(0, inputFile.lastIndexOf("."));
        } else {
            preFileName = inputFile;
        }
        targetFileFullPath = preFileName + ".tar.gz";

        List<String> command = Arrays.asList(
                "tar",
                "-zcvf",
                targetFileFullPath,
                "-C", //选项来去除目录结构
                inputFile.substring(0, inputFile.lastIndexOf("/")),// 使用文件的父目录作为压缩时的目录
                inputFile.substring(inputFile.lastIndexOf("/") + 1) // 使用文件名作为要压缩的文件
        );

        log.info("最终压缩执行命令：" + String.join(" ", command));

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(command);

        try {
            Process process = processBuilder.start();
            // 读取输出流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }

            // 等待进程终止并获取退出状态
            exitCode = process.waitFor();
            log.info("Exited with CompressionFile() code:" + exitCode);

        } catch (IOException | InterruptedException e) {
            log.error(Factory.printStackTraceToString(e));
            return false;
        }

        return exitCode == 0;
    }

    //解压文件
    public boolean decompressFile(String inputFile, String taskType, String dataType) throws IOException, InterruptedException {
        int exitCode = -1;

        //补充解压文件代码，targetFileFullPath 赋值成解密后的包含路径的文件名
        // 设置解压后的文件路径
        String targetTempPath = baseWorkPath;

        List<String> command = Arrays.asList(
                "tar",
                "-zxvf",
                inputFile,
                "-C",
                targetTempPath
        );

        log.info("最终解压执行命令：" + String.join(" ", command));

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(command);

        List<String> decompressFileList = new ArrayList<>();

        try {
            Process process = processBuilder.start();
            // 读取输出流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                decompressFileList.add(line);
                log.info(line);
            }

            // 等待进程终止并获取退出状态
            exitCode = process.waitFor();
            log.info("Exited with DecompressionFile() code:" + exitCode);

        } catch (IOException | InterruptedException e) {
            log.error(Factory.printStackTraceToString(e));
            return false;
        }

        if (EnumValue.DataType.DATA_FILE.getCode().equals(dataType)) {
            if (decompressFileList.size() == 1) {
                targetFileFullPath = targetTempPath + "/" + decompressFileList.get(0);
            } else if (decompressFileList.size() > 1) {
                targetFileFullPath = targetTempPath;
            }
        } else if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(dataType) || EnumValue.DataType.DATABASE_SCHEMA.getCode().equals(dataType) || EnumValue.DataType.DATABASE.getCode().equals(dataType)) {
            if (decompressFileList.size() == 1) {
                targetFileFullPath = targetTempPath + "/" + decompressFileList.get(0);
            } else if (decompressFileList.size() > 1) {
                targetFileFullPath = targetTempPath;
            }
        }

        return exitCode == 0;
    }

    //加密文件
    public boolean encryptFile(String inputFile) throws IOException, InterruptedException {
        int exitCode = -1;

        targetFileFullPath = inputFile + ".enc";
        //加密: openssl enc -aes-256-cbc -salt -in /home/<USER>/data/backup/bill_db_20240328203030.tar.gz -out /home/<USER>/data/backup/bill_db_20240328203030.tar.gz.enc -k 2020#Rating
        //解密: openssl enc -aes-256-cbc -d -in bill_db_20240328203030.tar.gz.enc -out bill_db_20240328203030.tar.gz -k 2020#Rating
        List<String> command = Arrays.asList(
                "openssl",
                "enc",
                "-aes-256-cbc",
                "-salt",
                "-in",
                inputFile,
                "-out",
                targetFileFullPath,
                "-k",
                backupConfig.getEncryptionPassword()
        );

        log.info("最终加密执行命令：" + String.join(" ", command));

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(command);

        try {
            Process process = processBuilder.start();
            // 读取输出流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }

            // 等待进程终止并获取退出状态
            exitCode = process.waitFor();
            log.info("Exited with CompressionFile() code:" + exitCode);

        } catch (IOException | InterruptedException e) {
            log.error(Factory.printStackTraceToString(e));
            return false;
        }

        return exitCode == 0;
    }

    //解密文件
    public boolean decryptFile(String inputFile) throws IOException, InterruptedException {
        int exitCode = -1;

        //补充解密文件代码，targetFileFullPath赋值成解密后的包含路径的文件名
        // 设置解密后的文件路径
        String targetTempPath = baseWorkPath + "/" + inputFile.substring(inputFile.lastIndexOf("/") + 1, inputFile.lastIndexOf(".enc"));
        log.info("targetTempPath：" + targetTempPath);

        List<String> command = Arrays.asList(
                "openssl",
                "enc",
                "-aes-256-cbc",
                "-d",
                "-in",
                inputFile,
                "-out",
                targetTempPath,
                "-k",
                backupResult.getEncryptionPassword()
        );

        log.info("最终解密执行命令：" + String.join(" ", command));

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(command);

        try {
            Process process = processBuilder.start();
            // 读取输出流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                log.info(line);
            }

            // 等待进程终止并获取退出状态
            exitCode = process.waitFor();
            log.info("Exited with DecryptionFile() code:" + exitCode);

        } catch (IOException | InterruptedException e) {
            log.error(Factory.printStackTraceToString(e));
            return false;
        }

        targetFileFullPath = targetTempPath;

        return exitCode == 0;
    }

    public void deletePath(int flowNo, String path) {
        if (flowNo > 0) {
            // 压缩成功后删除源文件
            if (deletePath(path)) {
                System.out.println("源文件删除成功： " + path);
            } else {
                System.out.println("源文件删除失败： " + path);
            }
        }
        flowNo++;
    }

    //删除文件或目录
    public boolean deletePath(String fileOrDirectoryPath) {
        File fileOrDirectory = new File(fileOrDirectoryPath);
        if (!fileOrDirectory.exists()) {
            log.error("File or directory does not exist: " + fileOrDirectoryPath);
            return false;
        }

        if (fileOrDirectory.isDirectory()) {
            File[] files = fileOrDirectory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!deletePath(file.getAbsolutePath())) {
                        return false;
                    }
                }
            }
        }

        return fileOrDirectory.delete();
    }

    //创建目录
    public boolean mkdirs(String path) {
        try {
            File baseWorkDirFile = new File(path);
            if (!baseWorkDirFile.exists()) {
                baseWorkDirFile.mkdirs();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("创建目录失败：" + path + "," + e.getMessage());
            return false;

        }
        return true;
    }
}
