package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseResasterThread;
import com.itco.framework.Factory;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.Properties;

@Slf4j
public class RecoveryFile extends BaseResasterThread {

    @Override
    public void run() {
        try {
            log.info("--------------------开始执行恢复任务：" + task.getBackupResultId() + ",涉及配置如下：--------------------");
            System.out.println(backupResult.toString());
            System.out.println(backupConfig.toString());

            System.out.println("source :" + sourcehostInfo.toString());
            //System.out.println("target :" + targethostInfo.toString());
            System.out.println();

            if (!work()) {
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                log.error("BackupPostgres,task_type:" + task.getTaskType() + ",data_type:" + backupConfig.getDataType() + ",version:" + task.getVersion() + ",errorMsg:" + errorMsg);
                return;
            }

            Thread.sleep(10);
            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
            log.info("--------------------恢复文件任务完成:" + task.getVersion() + "--------------------");
        } catch (Exception e) {
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
        } finally {
            OnExit();
        }
    }


    boolean work() throws IOException, InterruptedException {
        baseWorkPath = commonModule.getBaseWorkDir() + "/" + Factory.getSystemDateStr("yyyyMMddHHmmss_" + backupResult.getBackupResultId());
        targetFileFullPath = backupResult.getTargetDirectory();
        int flowNo = 0;

        if (!mkdirs(baseWorkPath)) {
            return false;
        }

        // 解密
        if ("Y".equals(backupResult.getEncryptionEnabled())) {
            String sourcePath = targetFileFullPath;
            //添加文件解密逻辑。
            if (!decryptFile(sourcePath)) {
                return false;
            }
            deletePath(flowNo++, sourcePath);
            log.info("解密文件成功,文件路径:" + targetFileFullPath);
        }

        // 解压
        if ("Y".equals(backupResult.getCompressionEnabled())) {
            String sourcePath = targetFileFullPath;
            if (!decompressFile(sourcePath, task.getTaskType(), backupConfig.getDataType())) {
                return false;
            }
            deletePath(flowNo++, sourcePath);
            log.info("解压文件成功,文件路径:" + targetFileFullPath);
        }

        String sourcePath = targetFileFullPath;
        // 恢复
        if (!RecoveryFile()) {
            log.error("RecoveryFile work()失败");
            return false;
        }

        //deletePath(flowNo++, sourcePath);

        log.info("恢复文件成功,文件路径:" + targetFileFullPath);

        log.info("RecoveryFile work() end");
        return true;
    }

    ChannelSftp sftpChannel;

    public boolean RecoveryFile() {
        log.info("RecoveryFile() start");

        String remoteHost = sourcehostInfo.getSshIp();
        int remotePort = Integer.parseInt(sourcehostInfo.getSshPort());
        String username = sourcehostInfo.getSshUsername();
        String password = sourcehostInfo.getPassword();
        String remoteFilePath = sourcehostInfo.getHome() + task.getRecoveryDirectory() + "/" + task.getVersion();
        String sourceFullPathFileName = targetFileFullPath; //本地用户端备份目录

        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(username, remoteHost, remotePort);
            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();
            log.debug("登录成功");

            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            log.debug("打开SFTP通道成功");

            log.info("从本地上传文件到远程服务器成功: sourceFullPathFileName:" + sourceFullPathFileName + " ,remoteFilePath:" + remoteFilePath);

            mkdirsRemote(remoteFilePath);

            //判断sourceFullPathFileName是文件还是目录
            if (new File(sourceFullPathFileName).isFile()) {
                sftpChannel.put(sourceFullPathFileName, remoteFilePath);
                log.info("Uploaded file: " + sourceFullPathFileName);
            } else {
                // 把sourceFullPathFileName目录下的文件上传到远程服务器sourcehostInfo的remoteFilePath目录下
                uploadDirectory(sourceFullPathFileName, remoteFilePath);
            }

            sftpChannel.disconnect();
            session.disconnect();
            log.debug("断开SFTP通道和会话成功");

            targetFileFullPath = remoteFilePath; //更新目标文件路径
        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            return false;
        }

        log.info("恢复文件成功");
        log.info("RecoveryFile() close");
        return true;
    }

    private void uploadDirectory(String localPath, String remotePath) throws SftpException {
        File localDir = new File(localPath);
        File[] files = localDir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // If it's a directory, create a corresponding remote directory and recursively upload its contents
                    String localFilePath = file.getAbsolutePath();
                    String remoteFilePath = remotePath + "/" + file.getName();
                    try {
                        sftpChannel.mkdir(remoteFilePath);
                        log.info("uploadDirectory Created remote directory: " + remoteFilePath);
                    } catch (SftpException e) {
                        log.error("Failed to create remote directory: " + remoteFilePath);
                        throw e;
                    }
                    uploadDirectory(localFilePath, remoteFilePath);
                } else {
                    // If it's a file, upload the file directly
                    String localFilePath = file.getAbsolutePath();
                    String remoteFilePath = remotePath + "/" + file.getName();
                    try {
                        sftpChannel.put(localFilePath, remoteFilePath);
                        log.info("Uploaded file: " + localFilePath);
                    } catch (SftpException e) {
                        log.error("Failed to upload file: " + localFilePath);
                        throw e;
                    }
                }
            }
        }
    }


    boolean mkdirsRemote(String path) throws SftpException {
        if (path == null || path.isEmpty()) {
            throw new IllegalArgumentException("Path must not be null or empty.");
        }

        // 确保路径以斜杠开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }

        String[] remoteDirs = path.split("/");
        String remoteDir = "";
        boolean success = true;

        for (String dir : remoteDirs) {
            if (!dir.isEmpty()) {
                remoteDir += "/" + dir;
                try {
                    // 尝试获取目录属性，如果不存在则创建目录
                    sftpChannel.stat(remoteDir);
                } catch (SftpException e) {
                    // 根据异常类型判断是否需要创建目录
                    if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                        sftpChannel.mkdir(remoteDir);
                        log.info("mkdirsRemote() Created remote directory: " + remoteDir);
                    } else {
                        // 如果是其他类型的SftpException，则抛出异常
                        throw e;
                    }
                }
            }
        }
        return true;
    }
}
