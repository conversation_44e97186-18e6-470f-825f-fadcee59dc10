package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseMonitorThread;
import com.itco.framework.Factory;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class MonitorPostgres extends BaseMonitorThread {

    public static String componentName = "bin/postgres";
    String listenPort = "18921";

    @Override
    public void run() {
        try {
            log.info("--------------------开始执行监控数据库任务：" + task.getVersion() + ",涉及配置如下：--------------------");
            System.out.println(task.toString());
            System.out.println(componentManager.toString());
            if (componentManager.getAddress().split(":").length != 2) {
                log.error("MonitorPostgres,task_type:" + task.getTaskType() + ",data_type:" + componentManager.getType() + ",version:" + task.getVersion() + ",errorMsg:地址格式错误，请检查");
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                task.setErrorMessage("地址格式错误，请检查。componentManager.getAddress()：" + componentManager.getAddress());
                return;
            }
            listenPort = componentManager.getAddress().split(":")[1];

            System.out.println();


            if (!work()) {
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                log.error("MonitorPostgres,task_type:" + task.getTaskType() + ",data_type:" + componentManager.getType() + ",version:" + task.getVersion() + ",errorMsg:" + errorMsg);
                return;
            }

            Thread.sleep(10);
            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
            log.info("--------------------监控数据库任务完成:" + task.getVersion() + "--------------------");
        } catch (Exception e) {
            e.printStackTrace();
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
        } finally {
            OnExit();
        }
    }


    public boolean work() throws IOException, InterruptedException {
        // 备份导出
        if (!monitorComponent(componentName, listenPort)) {
            log.error("MonitorPostgres()失败");
            return false;
        }

        log.info("MonitorPostgres work() end");
        return true;
    }


}
