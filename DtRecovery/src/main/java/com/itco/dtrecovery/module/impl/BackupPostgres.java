package com.itco.dtrecovery.module.impl;

import com.itco.dtrecovery.entity.EnumValue;
import com.itco.dtrecovery.module.BaseResasterThread;
import com.itco.framework.Factory;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class BackupPostgres extends BaseResasterThread {

    @Override
    public void run() {
        log.info("--------------------开始执行备份任务：" + task.getVersion() + ",涉及配置如下：--------------------");
        System.out.println(task.toString());
        System.out.println(backupConfig.toString());

        System.out.println("source :" + sourcehostInfo.toString());
        //System.out.println("target :" + targethostInfo.toString());
        System.out.println();

        try {
            if (!work()) {
                task.setStatus(EnumValue.TaskStatus.FCL.getCode());
                log.error("BackupPostgres,task_type:" + task.getTaskType() + ",data_type:" + backupConfig.getDataType() + ",version:" + task.getVersion() + ",errorMsg:" + errorMsg);
                return;
            }

            Thread.sleep(10);
            task.setStatus(EnumValue.TaskStatus.SCL.getCode());
            log.info("--------------------备份数据库任务完成:" + task.getVersion() + "--------------------");
        } catch (Exception e) {
            e.printStackTrace();
            task.setStatus(EnumValue.TaskStatus.FCL.getCode());
            errorMsg = Factory.printStackTraceToString(e);
            log.error(Factory.printStackTraceToString(e));
        } finally {
            OnExit();
        }
    }


    public boolean work() throws IOException, InterruptedException {
        // 备份导出
        if (!backupDatabase()) {
            log.error("backupDatabase()失败");
            return false;
        }

        // 压缩
        if ("Y".equals(backupConfig.getCompressionEnabled())) {
            String sourcePath = targetFileFullPath;
            if (!compressionFile(targetFileFullPath)) {
                return false;
            }
            // 压缩成功后删除源文件
            if (deletePath(sourcePath)) {
                System.out.println("源文件删除成功： " + sourcePath);
            } else {
                System.out.println("源文件删除失败： " + sourcePath);
            }
        }

        // 加密
        if ("Y".equals(backupConfig.getEncryptionEnabled())) {
            String sourcePath = targetFileFullPath;
            //添加文件加密逻辑。
            if (!encryptFile(targetFileFullPath)) {
                return false;
            }
            // 压缩成功后删除源文件
            if (deletePath(sourcePath)) {
                System.out.println("源文件删除成功： " + sourcePath);
            } else {
                System.out.println("源文件删除失败： " + sourcePath);
            }
        }

        log.info("BackupPostgres work() end");
        return true;
    }


    List<String> getCommand() {
        String progreamPath = System.getenv().get("PGHOME") + "/bin/pg_dump";
        List<String> command = new ArrayList<>(Arrays.asList(
                progreamPath,
                "-U", sourcehostInfo.getSshUsername(),
                "-h", sourcehostInfo.getSshIp(),
                "-p", sourcehostInfo.getSshPort(),
                "-f", targetFileFullPath,
                "-F", "c",
                "-O"
        ));

        // 添加数据库
        command.add("-d");
        command.add(backupConfig.getSourceDirectory());

        // 添加模式
        if (EnumValue.DataType.DATABASE_SCHEMA.getCode().equals(backupConfig.getDataType())) {
            command.add("-n");
            command.add(backupConfig.getBackupScope());
        }

        // 添加表
        if (EnumValue.DataType.DATABASE_TABLE.getCode().equals(backupConfig.getDataType())) {
            for (int i = 0; i < scopeList.size(); i++) {
                command.add("-t");
                command.add(scopeList.get(i).getEnName());
            }
        }

        return command;
    }

    boolean backupDatabase() throws IOException, InterruptedException {
        int count = backupConfig.getTargetDirectory().split("/").length - 1;
        if (count < 3) {
            log.error("备份目标目录格式不正确，请检查");
            return false;
        }
        ProcessBuilder processBuilder = new ProcessBuilder();
        String targetDirectory = backupConfig.getTargetDirectory().substring(backupConfig.getTargetDirectory().indexOf("/", backupConfig.getTargetDirectory().indexOf("/", backupConfig.getTargetDirectory().indexOf("/") + 1) + 1));
        String homePath = System.getenv().get("HOME");
        targetFileFullPath = homePath + targetDirectory + "/" + backupConfig.getBackupFileName() + Factory.getSystemDateStr("yyyyMMddHHmmss") + ".dat";


        List<String> command = getCommand();
        log.info("最终备份执行命令：" + String.join(" ", command));
        processBuilder.command(command);

        // 设置环境变量PGPASSWORD
        Map<String, String> env = processBuilder.environment();
        env.put("PGPASSWORD", sourcehostInfo.getPassword());

        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();
        // 读取输出流
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        String line;
        while ((line = reader.readLine()) != null) {
            log.info(line);
        }

        // 等待进程终止并获取退出状态
        int exitCode = process.waitFor();
        log.info("Exited with backupDatabase() code:" + exitCode);
        return exitCode == 0;
    }


}
