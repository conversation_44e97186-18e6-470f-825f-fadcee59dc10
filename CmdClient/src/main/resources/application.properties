logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.org.springframework.boot=ERROR
logging.level.org.springframework.web=ERROR
logging.level.org.springframework.boot.web=ERROR
logging.level.com.ctg.itrdc.cache=ERROR
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n

logging.file.name=${LOG_HOME:~/javalog}/CmdClient.log
logging.file.max-size=10MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz


source_path=/data/work/normal/Collection/
target_path=/data/work/normal/Collection/
hdfs_path=/bssdata/zhjs-fj/


#db2file-normal
sc_sql=SELECT * from service_control 
#db2file-è¯åæä»¶ååç¼
sc_prefix=SC_
#db2file-è®°å½æ°
sc_cnt=2000
#db2file-è¯åæ ¼å¼
sc_format=JSON

#db2file-trial
tc_sql=SELECT * from service_control_trial
#db2file-è¯åæä»¶ååç¼
tc_prefix=TC_
#db2file-è®°å½æ°
tc_cnt=2000
#db2file-è¯åæ ¼å¼
tc_format=JSON

#db2file-rollback
rc_sql=SELECT * from service_control_rollback
#db2file-è¯åæä»¶ååç¼
rc_prefix=RC_
#db2file-è®°å½æ°
rc_cnt=2000
#db2file-è¯åæ ¼å¼
rc_format=JSON

#åå¸çæ¬
version=CmdClient_deploy_2022-01-30 11:00