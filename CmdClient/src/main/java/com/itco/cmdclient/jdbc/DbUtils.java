package com.itco.cmdclient.jdbc;

import com.itco.component.jdbc.DecodePassword;
import com.itco.component.zookeeper.ZkClientApi;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.*;
import java.util.*;

public class DbUtils {
    static Log log = LogFactory.getLog(DbUtils.class);
    String path = "dbc.properties";

    // 数据库客户端
    Connection gConn = null;
    PreparedStatement gPs = null;
    ResultSet gRs = null;
    ResultSetMetaData gRsmd = null;
    String gSql = new String();
    ZkClientApi zkClientApi = null;

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    Connection getConnection() {
        Connection conn = null;
        String driver;
        String url;
        String username;
        String publicKey;
        String password;
        boolean autoCommit = true;

        try {
            Properties p = zkClientApi.getPropertiesFromZK(path);
            driver = p.getProperty("driver");
            url = p.getProperty("url");
            username = p.getProperty("username");
            publicKey = p.getProperty("publicKey");
            password = p.getProperty("password");

            Class.forName(driver);
            String passwd = DecodePassword.decryption(publicKey, password);
            log.info("url:" + url + ",username:" + username + ",passwd:" + passwd);
            conn = DriverManager.getConnection(url, username, passwd);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return conn;
    }


    public int loadTableRecord(String sql, int cnt, List<Map<String, Object>> list) {
        if (gConn == null) {
            gConn = getConnection();
            if (gConn == null) {
                return -1;
            }
        }

        if (!gSql.equals(sql)) {
            try {
                gPs = gConn.prepareStatement(sql);
                gRs = gPs.executeQuery();
                // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
                gRsmd = gRs.getMetaData();
            } catch (SQLException e) {
                e.printStackTrace();
                return -1;
            }
            gSql = new String(sql);
        }

        if (list == null) {
            list = new ArrayList<>();
        } else {
            list.clear();
        }

        try {
            // 遍历结果集
            while (gRs.next()) {
                // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
                Map<String, Object> map = new HashMap<>();
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < gRsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = gRsmd.getColumnLabel(i + 1);
                    //获取列类型的int表示形式，以及列类型名称
                    //System.out.println("列名："+gRsmd.getColumnName(i+1)+",列类型:"+gRsmd.getColumnType(i + 1)+"----"+gRsmd.getColumnTypeName(i+1));
                    // 获取列值
                    Object value = gRs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname.toLowerCase(), value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {

                    list.add(map);
                    if (list.size() >= cnt) {
                        break;
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return -1;
        }

        return list.size();
    }
}
