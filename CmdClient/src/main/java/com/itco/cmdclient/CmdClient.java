package com.itco.cmdclient;

import com.itco.cmdclient.Thread.ManagerThread;
import com.itco.framework.Version;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.PreDestroy;


@SpringBootApplication
public class CmdClient {
    static Log log = LogFactory.getLog(CmdClient.class);
    static ConfigurableApplicationContext context;

    static String mode = ManagerThread.RUN_MODE.SIMULATOR.getMode();
    static String modeSub = null;
    static String sParam = null;
    static String sParamValue = null;
    static String sParamRbModuleId = null;
    static String sParamLoadId = null;
    static String sFileName = null;

    public static boolean start() {
        log.info("mode:" + mode + ",sParam:" + sParam + ",sParamValue:" + sParamValue);

        ManagerThread managerThread = new ManagerThread();
        managerThread.setMode(mode);
        managerThread.setModeSub(modeSub);
        managerThread.setsParam(sParam);
        managerThread.setsParamValue(sParamValue);
        managerThread.setsParamRbModuleId(sParamRbModuleId);
        managerThread.setsParamLoadId(sParamLoadId);
        managerThread.setsFileName(sFileName);

        // 初始化
        if (!managerThread.init()) {
            log.error("managerThread.init() 失败");
            return false;
        }

        // 创建线程启动
        Thread thread = new Thread(managerThread);
        thread.start();
        return true;
    }


    // 退出之前需要关闭容器
    public static synchronized void close() {
        if (context != null) {
            int exit = SpringApplication.exit(context);
            context = null;
            System.exit(exit);
        }

        log.info("main exit,SpringApplication.exit(context) ");
    }

    public static void help() {
        log.info("CmdClient.sh -simulator [normal/trial/rollback]");
        log.info("CmdClient.sh -db2file [normal/trial/rollback]");
        log.info("CmdClient.sh -sync module_id 1013 [load_id 1]");
        log.info("CmdClient.sh -sync process_id 10131111 [load_id 1]");
        log.info("CmdClient.sh -rollback [module_id 1012] task_id 100000-200000");
        log.info("CmdClient.sh -control [exit/start/stop] [module_id/process_id] 10131111");
        log.info("CmdClient.sh -debug [0/1/DEBUG/INFO/WARN/ERROR] [module_id/process_id] 10131111");
        log.info("CmdClient.sh -clear topic [主题名] [消费者组]");
        log.info("CmdClient.sh -hbase [create/delete] [yyyyMM,yyyyMM] //排重建表");
        log.info("CmdClient.sh -hbase [createTable/deleteTable] [name表名(可选)] //建表");
        log.info("CmdClient.sh -redislen [key名称] //查询某个key的记录数");
    }

    @PreDestroy
    public void destroy() {
        Version.print(0);
    }

    public static void main(String[] args) {
        if (Version.print(args, "CmdClient_code_2022-12-30 11:00", 0)) {
            return;
        }

        boolean flag = false;
        for (int i = 0; i < args.length; i++) {
            log.info("str:" + args[i]);
            if (args[i].equals("-simulator")) {
                mode = args[i].substring(1);
                if (args.length >= i + 1 + 1) {
                    sParam = args[i + 1];
                    if (args.length >= i + 2 + 1) {
                        sFileName = args[i + 2];
                    }
                } else {
                    sParam = "normal";
                }
                flag = true;
            } else if (args[i].equals("-db2file")) {
                mode = args[i].substring(1);
                if (args.length >= i + 1 + 1) {
                    sParam = args[i + 1];
                } else {
                    sParam = "normal";
                }
                flag = true;
            } else if (args[i].equals("-sync")) {
                mode = args[i].substring(1);
                if (args.length == 3 || args.length == 5) {
                    if (args[1].equals("module_id")) {
                        sParam = args[1];
                        sParamValue = args[2];
                        flag = true;
                    } else if (args[1].equals("process_id")) {
                        sParam = args[1];
                        sParamValue = args[2];
                        flag = true;
                    }
                    if (args.length == 5) {
                        if (args[3].equals("load_id")) {
                            sParamLoadId = args[4];
                        }
                    }
                }
            } else if (args[i].equals("-rollback")) {
                mode = args[i].substring(1);
                if (args.length == 3) {
                    if (args[1].equals("task_id")) {
                        sParam = args[1];
                        sParamValue = args[2];
                        flag = true;
                    }
                } else if (args.length == 5) {
                    if (args[1].equals("module_id") && args[3].equals("task_id")) {
                        sParam = args[1];
                        sParamValue = args[4];
                        sParamRbModuleId = args[2];
                        flag = true;
                    }
                }
            } else if (args[i].equals("-control")) {
                mode = args[i].substring(1);
                if ("exit".equals(args[1]) || "start".equals(args[1]) || "stop".equals(args[1])) {
                    modeSub = args[1].toUpperCase();
                    if (args.length >= 4) {
                        if (args[2].equals("module_id")) {
                            sParam = args[2];
                            sParamValue = args[3];
                            flag = true;
                        } else if (args[2].equals("process_id")) {
                            sParam = args[2];
                            sParamValue = args[3];
                            flag = true;
                        }
                    }
                }
            } else if (args[i].equals("-debug")) {
                mode = args[i].substring(1);
                modeSub = args[1].toUpperCase();
                if (args.length == 4) {
                    if (args[2].equals("module_id")) {
                        sParam = args[2];
                        sParamValue = args[3];
                        flag = true;
                    } else if (args[2].equals("process_id")) {
                        sParam = args[2];
                        sParamValue = args[3];
                        flag = true;
                    }
                }

            } else if (args[i].equals("-clear")) {
                mode = args[i].substring(1);
                if (args.length == 4) {
                    if (args[1].equals("topic")) {
                        sParam = args[2];
                        sParamValue = args[3];
                        flag = true;
                    }
                }
            } else if (args[i].equals("-hbase")) {
                mode = args[i].substring(1);
                sParam = args[i + 1];
                if (args.length == 3) {
                    sParamValue = args[i + 2];
                }
                flag = true;
            } else if (args[i].equals("-redislen")) {
                mode = args[i].substring(1);
                sParam = args[i + 1];
                flag = true;
            }
        }

        if (!flag) {
            log.error("没有匹配的工作模式");
            help();
            return;
        }

        context = SpringApplication.run(CmdClient.class, args);
        if (!CmdClient.start()) {
            log.error("start 运行失败");
        }
        log.info("初始化成功");
    }

}
