package com.itco.cmdclient.Thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ctg.mq.api.bean.MQResult;
import com.itco.cmdclient.CmdClient;
import com.itco.cmdclient.hbase.HBaseClientApi;
import com.itco.cmdclient.jdbc.DbUtils;
import com.itco.component.ctgmq.CtgMqPullApi;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.TpProcess;
import com.itco.entity.common.TpProcessMQ;
import com.itco.entity.common.TplIndbTableItem;
import com.itco.entity.process.E_RecordTransferMode;
import com.itco.entity.process.MsgBody;
import com.itco.entity.table.FilterRegionConfig;
import com.itco.framework.Factory;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.message.TaskMessageFactory;
import com.itco.framework.record.TransferFactory;
import com.itco.framework.record.impl.settle.TransferSettle;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

public class ManagerThread implements Runnable {
    static Log log = LogFactory.getLog(ManagerThread.class);
    static boolean eExit = false;

    ZkClientApi zkClientApi = new ZkClientApi();

    String sSourcePath = null;
    String sTargetPath = null;
    String sHdfsPath = "/";

    String mode = null;
    String modeSub = null;
    String sParam = null;
    String sParamValue = null;
    String sParamRbModuleId = null;
    String sParamLoadId = null;

    String sFileName = null;

    List<TpProcess> proZkList = null;

    Common common = new Common(); //公共配置类
    CtgMqPullApi ctgMqPullApi = null; //话单消息流转

    TaskMessage taskMessage = null;
    TaskMessageFactory taskMessageFactory = new TaskMessageFactory();
    TransferSettle transfer = null;
    TransferFactory transferFactory = new TransferFactory();


    // db2file-normal
    String sSql;
    int sCnt = 2000;
    String sPrefix;

    public boolean init() {
        log.info("ManagerThread init start ");
        log.info("ManagerThread init mode: " + mode);
        log.info("ManagerThread init sParam: " + sParam);
        log.info("ManagerThread init sParamValue: " + sParamValue);
        log.info("ManagerThread init sParamLoadId: " + sParamLoadId);

        String path = "/application.properties";
        Resource resource = new ClassPathResource(path);
        try {
            Properties props = PropertiesLoaderUtils.loadProperties(resource);
            String home = System.getenv("HOME");
            if (home == null || home.equals("") || home.indexOf("root") > 0) {
                sSourcePath = props.getProperty("source_path");
                sTargetPath = props.getProperty("target_path");
            } else {
                sSourcePath = home + "/" + props.getProperty("source_path");
                sTargetPath = home + "/" + props.getProperty("target_path");
            }
            if (null != props.getProperty("hdfs_path")) {
                sHdfsPath = props.getProperty("hdfs_path");
            }

            // db2file
            if (mode.equals("db2file")) {
                if (sParam.equals("normal")) {
                    sSql = props.getProperty("sc_sql");
                    sPrefix = props.getProperty("sc_prefix");
                    sCnt = Integer.valueOf(props.getProperty("sc_cnt"));
                } else if (sParam.equals("trial")) {
                    sSql = props.getProperty("tc_sql");
                    sPrefix = props.getProperty("tc_prefix");
                    sCnt = Integer.valueOf(props.getProperty("tc_cnt"));
                } else if (sParam.equals("rollback")) {
                    sSql = props.getProperty("rc_sql");
                    sPrefix = props.getProperty("rc_prefix");
                    sCnt = Integer.valueOf(props.getProperty("rc_cnt"));
                } else {
                    return false;
                }
            }

        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }

        // zookeeper 初始化
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("CmdClient");
        if (!zkClientApi.init()) {
            log.error(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        if (!zkClientApi.register(true)) {
            log.info("注册失败");
        }
        proZkList = zkClientApi.getProcessListFromZk();

        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        log.info(common.toString());

        taskMessage = taskMessageFactory.getTaskMessageInstanceByMode(common.getTaskTransferMode());
        if (taskMessage == null) {
            log.error("任务消息管理类初始化失败，配置的消息通讯模式异常task_transfer_mode：" + common.getTaskTransferMode());
            Factory.setbWhileFlag(true);
            return false;
        }

        // 模块间通讯消息初始化
        taskMessage.setMode(1); //无消费者模式
        taskMessage.setZkClientApi(zkClientApi);
        taskMessage.setCommon(common);
        if (!taskMessage.init()) {
            log.error("taskMessage.init() 失败");
            return false;
        }

        log.info("taskMessage 初始化完成");

        if (mode.equals("clear")) {
            ctgMqPullApi = new CtgMqPullApi();
            ctgMqPullApi.setZkClientApi(zkClientApi);
            ctgMqPullApi.setCommon(common);

            // 消息类初始化
            if (!ctgMqPullApi.initCmdClientClear(sParam, sParamValue)) {
                log.error("ctgMqPullApi.initCmdClientClear() 失败");
                return false;
            }
        } else if (mode.equals("simulator") || mode.equals("redislen")) {
            transfer = transferFactory.getTransferSettleInstanceByMode(common.getRecordTransferMode());
            if (transfer == null) {
                log.error("transfer 实例化失败");
                return false;
            }

            transfer.setZkClientApi(zkClientApi);
            transfer.setCommon(common);
            if (!transfer.init()) {
                log.error("transferRecord.init(p) 失败");
                return false;
            }
        }

        log.info("ManagerThread init close");
        return true;
    }

    List<File> getRecordFile(String fileName) {
        List<File> list = new ArrayList<>();
        log.info("sSourcePath:" + sSourcePath);

        File dirFile = new File(sSourcePath, ".");
        if (dirFile.listFiles() == null) {
            return null;
        }
        for (File name : dirFile.listFiles()) {
            if (name.isFile()) {
                if (fileName == null) {
                    list.add(name);
                } else {
                    if (fileName.equals(name.getName())) {
                        list.add(name);
                    }
                }
                //log.info("find:" + name.getPath());
            }
        }

        return list.size() > 0 ? list : null;
    }

    boolean db2file() {
        int iNo = 1;
        List<String> fileList = new ArrayList<>();
        DbUtils dbUtils = new DbUtils();
        dbUtils.setZkClientApi(zkClientApi);
        int allCnt = 0;
        boolean flag = false;
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
        System.out.println(df.format(new Date()));// new Date()为获取当前系统时间

        while (true) {
            List<Map<String, Object>> list = new ArrayList<>();

            int iRet = dbUtils.loadTableRecord(sSql, sCnt, list);
            if (iRet < 0) {
                log.error("连接数据库失败");
                return false;
            }

            if (iRet == 0) {
                break;
            }

            allCnt += iRet;
            log.info("读取业务控制表记录数:" + iRet);
            flag = true;
            // 写话单文件
            String writePath = sSourcePath + sPrefix + df.format(new Date()) + "_" + (iNo++) + ".dat";
            BufferedWriter recordWriter = null;
            try {
                recordWriter = new BufferedWriter(new FileWriter(writePath, false));

                for (Map<String, Object> map : list) {
                    JSONObject jsonObject = new JSONObject(map);

                    recordWriter.write(JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue));
                    recordWriter.newLine();
                }
                recordWriter.close();
                fileList.add(writePath + " , 记录数：" + iRet);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        if (flag) {
            log.info(sSql);
            log.info("从业务控制表，共接收到 " + allCnt + " 条记录 ");
            for (String file : fileList) {
                log.info(file);
            }
        } else {
            log.info(sSql);
            log.info("业务控制表没有数据");
        }

        return true;
    }

    boolean simulator() {
        String processId = new String("10111111");

        while (!eExit) {
            try {
                List<File> fileList = getRecordFile(sFileName);
                if (fileList != null) {
                    for (File file : fileList) {
                        log.info("file:" + file.getName());

                        if (sParam.equals("normal")) {
                            if (sFileName != null) {

                            } else if (file.getName().indexOf("N_REAL_") > -1 ||
                                    file.getName().indexOf("N_CYCLE_") > -1 ||
                                    file.getName().substring(0, 7).equals("NORMAL_") ||
                                    file.getName().startsWith("JS02") ||
                                    file.getName().startsWith("JS06")) {


                            } else {
                                continue;
                            }
                        } else if (sParam.equals("trial")) {
                            if (!file.getName().substring(0, 3).equals("TC_")) {
                                continue;
                            }
                        } else if (sParam.equals("rollback")) {
                            if (!file.getName().substring(0, 3).equals("RC_")) {
                                continue;
                            }
                        } else {
                            return false;
                        }

                        MsgBody.DbInceptBody dbInceptBody = new MsgBody.DbInceptBody();


                        // 获取文件记录数
                        String fullFile = file.getPath();
                        BufferedReader recordReader = new BufferedReader(new FileReader(fullFile));
                        LineNumberReader lineNumberReader = new LineNumberReader(new FileReader(fullFile));
                        lineNumberReader.skip(Long.MAX_VALUE);
                        long lines = lineNumberReader.getLineNumber();


                        int i = 0;
                        List<String> inRecords = new ArrayList<>();
                        String recordStr;
                        while ((recordStr = recordReader.readLine()) != null) {
                            if ((!recordStr.equals("")) && recordStr.length() > 0) {
                                JSONObject object = JSONObject.parseObject(recordStr);
                                if (!object.containsKey("TICKET_COUNT")) {
                                    if (file.getName().startsWith("JS02")) {
                                        object.put("BILLING_LINE_ID", 22);
                                    } else if (file.getName().startsWith("JS06")) {
                                        object.put("BILLING_LINE_ID", 16);
                                    }
                                }

                                inRecords.add(object.toJSONString());
                                i++;
                            }
                        }
                        recordReader.close();
                        if (i != lines) {
                            lines = i;
                        }
                        log.info("读取记录数:" + inRecords.size() + " ,lines:" + lines);
                        String fileName = null;
                        dbInceptBody.FILE_NAME = file.getName();
                        if (common.getRecordTransferMode().equals("FILE")) {
                            if (file.getName().startsWith("JS02") || file.getName().startsWith("JS06")) {
                                // 写话单文件
                                String writePath = sTargetPath + "/" + file.getName();
                                dbInceptBody.FILE_NAME = file.getName();
                                BufferedWriter recordWriter = new BufferedWriter(new FileWriter(writePath, false));
                                for (String rec : inRecords) {
                                    recordWriter.write(rec);
                                    recordWriter.newLine();
                                }
                                recordWriter.close();
                            }
                            dbInceptBody.LOCAL_PATH = sTargetPath;
                            fileName = sTargetPath + "/" + file.getName();
                        } else if (common.getRecordTransferMode().equals("CTG_MQ")) {
                            TpProcessMQ tpProcessMQ = common.getProcessMQMap().get(zkClientApi.getProcessID());
                            dbInceptBody.LOCAL_PATH = tpProcessMQ.getRecord_producer_topic();

                            fileName = file.getName();
                        } else if (common.getRecordTransferMode().equals("CTG_CACHE")) {
                            fileName = file.getName();
                            dbInceptBody.LOCAL_PATH = common.getRecordCacheHashkey();// + "-" + fileName;
                        } else if (common.getRecordTransferMode().equals("HDFS")) {
                            dbInceptBody.LOCAL_PATH = sHdfsPath;
                            fileName = sHdfsPath + "/" + file.getName();
                        }

                        if (common.getRecordTransferMode().equals("CTG_MQ") ||
                                common.getRecordTransferMode().equals("CTG_CACHE") ||
                                common.getRecordTransferMode().equals("HDFS")) {
                            transfer.outputRecord(fileName, inRecords);
                        }

                      /*  // 移动文件
                        File oldFile=new File(sSourcePath+file.getName());
                        File newFile=new File(sTargetPath+file.getName());
                        log.info("rename "+sSourcePath+file.getName()+" to "+sTargetPath+file.getName());

                        if(oldFile.renameTo(newFile)){
                            log.info("rename "+oldFile.getPath()+" to "+newFile.getPath() +" 成功");
                        }else{
                            log.info("rename "+oldFile.getPath()+" to "+newFile.getPath() +" 失败");
                        }*/

                        if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                            log.info("话单流转：" + E_RecordTransferMode.CTG_MQ.getName() + " ,不发消息给调度");
                            continue;
                        }

                        String hostIdTmp = System.getenv("HOST_ID");
                        if (hostIdTmp == null) {
                            hostIdTmp = processId.substring(4, 6);
                        }
                        dbInceptBody.HOST_ID = hostIdTmp;
                        dbInceptBody.MODULE_ID = processId.substring(0, 4);
                        dbInceptBody.PROCESS_ID = processId;
                        dbInceptBody.RECORD_CNT = String.valueOf(lines);
                        dbInceptBody.FIRST = sParam;
                        dbInceptBody.MSG_TYPE = "REQUEST";
                        dbInceptBody.CREATE_DATE = getSystemDateStr();
                        dbInceptBody.TASK_TYPE = MsgBody.TASK_TYPE.T_RECORD.getType();

                        String json = JSONObject.toJSONString(dbInceptBody);
                        taskMessage.sendMessage("10100000", json);
                        log.info("采集文件上报消息发送成功，json:" + json);
                    }
                }

                Thread.sleep(3000);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
                log.error("FileNotFoundException:" + e.getMessage());
            } catch (IOException e) {
                e.printStackTrace();
                log.error("IOException:" + e.getMessage());
            } catch (InterruptedException e) {
                e.printStackTrace();
                log.error("InterruptedException:" + e.getMessage());
            }

            eExit = true;
        }

        return true;
    }

    boolean sync() {
        log.info("sParam:" + sParam + "，sParamValue:" + sParamValue);
        List<String> realProcess = new ArrayList<>();
        for (TpProcess pro : proZkList) {
            //log.info("pro:" + pro.toString());
            if (sParam.equals("module_id") && String.valueOf(pro.getModule_id()).equals(sParamValue)) {
                realProcess.add(String.valueOf(pro.getProcess_id()));
            } else if (sParam.equals("process_id") && String.valueOf(pro.getProcess_id()).equals(sParamValue)) {
                realProcess.add(String.valueOf(pro.getProcess_id()));
            }
        }

        for (String proDebug : realProcess) {
            log.info("进行同步配置的进程:" + proDebug);
        }

        if (realProcess.size() == 0) {
            log.info("sParam:" + sParam + ",sParamValue:" + sParamValue + ",没有相应的进程在运行，无需在线同步配置，程序启动立即生效");
            return true;
        }

        int iNo = 1;
        String newJson = new String();
        String json = new String();
        for (String process_id : realProcess) {
            MsgBody.ConfigSyncBody body = new MsgBody.ConfigSyncBody();
            body.CONFIG_SYNC_ID = getSystemDateStr() + "_" + String.valueOf(iNo);
            body.TASK_TYPE = MsgBody.TASK_TYPE.T_CONFIG.getType();
            body.HOST_ID = process_id.substring(4, 6);
            body.PROCESS_ID = process_id;
            body.MODULE_ID = process_id.substring(0, 4);
            body.LOAD_ID = sParamLoadId;
            body.MSG_COMMENT = "配置同步请求";
            body.MSG_TYPE = "REQUEST";
            body.STATE = "";
            body.ERROR_MSG = "";
            body.CREATE_DATE = getSystemDateStr();

            if (iNo == 1) {
                newJson += JSON.toJSONString(body);
            } else {
                newJson += "\n" + JSON.toJSONString(body);
            }
            iNo++;
        }

        List<String> oldJsonList = zkClientApi.getDataByMessageConfig();
        if (oldJsonList != null) {
            for (String tmpJson : oldJsonList) {
                if (!tmpJson.equals("")) {
                    json += tmpJson + "\n";
                }
            }
        }

        json += newJson;
        zkClientApi.setDataByMessageConfig(json);
        log.info("json:" + json);

        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            e.printStackTrace();
            log.error("InterruptedException" + e.getMessage());
            return false;
        }
        return true;
    }

    boolean rollback() {
        String processId = zkClientApi.getProcessID();
        List<String> list = new ArrayList<>();

        if (sParamValue.indexOf(",") > -1) {
            String[] split = sParamValue.split(",");
            for (String str : split) {
                list.add(str);
            }
            if (list.size() > 0) {
                log.info(sParamValue + ",指定多个任务重做");
            }
        } else if (sParamValue.indexOf("-") > -1 && sParamValue.indexOf("-") == sParamValue.lastIndexOf("-")) {
            log.info(sParamValue + ",范围失败任务重做");
        } else if (sParamValue != null && StringUtils.isNumber(sParamValue)) {
            log.info(sParamValue + ",单个任务重做");
        } else {
            log.error("输入的重做任务号无效");
            return false;
        }

        JSONObject object = new JSONObject();
        MsgBody.RollbackBody body = new MsgBody.RollbackBody();
        body.MSG_TYPE = "REQUEST";
        body.TASK_TYPE = MsgBody.TASK_TYPE.T_ROLLBACK.getType();
        body.TASK_ID = sParamValue;
        if (sParamRbModuleId != null) {
            body.RB_MODULE_ID = sParamRbModuleId;
        }
        body.PROCESS_ID = processId;
        body.MSG_COMMENT = "任务重做";
        body.STATE = "";
        body.ERROR_MSG = "";
        body.CREATE_DATE = getSystemDateStr();

        String json = JSON.toJSONString(body);
        taskMessage.sendMessage("10100000", json);
        log.info("任务重做消息发送成功，json:" + json);
        return true;
    }

    boolean process() {
        MsgBody.StartStopProcessBody body = new MsgBody.StartStopProcessBody();
        body.COMMAND_ID = getSystemDateStr() + "_1";
        body.TASK_TYPE = MsgBody.TASK_TYPE.T_SS_PROCESS.getType();
        body.MSG_TYPE = "REQUEST";
        body.REQUEST_PROCESS_ID = zkClientApi.getProcessID();
        body.STATE = "";
        body.ACTION = modeSub;
        body.ACTION_DATE = "NOW";
        body.CREATE_DATE = getSystemDateStr();
        if ("module_id".equals(sParam)) {
            body.PROCESS_ID = "";
            if (sParamValue != null) {
                String[] moduleList = sParamValue.split(",");
                for (int i = 0; i < moduleList.length; i++) {
                    body.MODULE_ID = moduleList[i];
                    if (body.MODULE_ID != null) {
                        String json = JSON.toJSONString(body);
                        taskMessage.sendMessage("10100000", json);
                        log.info("控制进程退出命令，json:" + json);
                    }
                }
            }
        } else if ("process_id".equals(sParam)) {
            body.MODULE_ID = "";
            if (sParamValue != null) {
                String[] processList = sParamValue.split(",");
                for (int i = 0; i < processList.length; i++) {
                    body.PROCESS_ID = processList[i];
                    if (body.PROCESS_ID != null) {
                        String json = JSON.toJSONString(body);
                        taskMessage.sendMessage("10100000", json);
                        log.info("控制进程退出命令，json:" + json);
                    }
                }
            }
        }

        return true;
    }

    boolean clear() {
        int i = 0, cnt = 0;
        List<MQResult> mqResultList = new LinkedList<>();
        do {
            mqResultList.clear();
            ctgMqPullApi.recviceRecords(mqResultList);
            if (mqResultList.size() > 0) {
                ctgMqPullApi.ackRecordMessages(mqResultList);
            }

            cnt += mqResultList.size();
            log.info("第 " + ++i + " 次，读取记录数:" + mqResultList.size());
        } while (mqResultList.size() != 0);

        log.info("总记录数:" + cnt);
        return true;
    }

//    public static void main(String[] args) {
//        String s = "zhjs_data:TP_RULE_FILTER_301030201_2021-11-10_15";
//        boolean matches = Pattern.matches("\\*2021-11", s);
//        System.out.println(matches);
//    }


    boolean hbaseOperation() {
        List<String> tableList = new ArrayList<>();
        HBaseClientApi hBaseClientApi = HBaseClientApi.newInstance();
        if (!hBaseClientApi.init()) {
            log.error("hbase初始化失败");
        }
        log.info("hbase 初始化成功！");
        Properties props = zkClientApi.getPropertiesFromZK("Filter.properties");
        Properties hbaseProps = zkClientApi.getPropertiesFromZK("HBase.properties");
        String hashKeyPrefix = props.getProperty("hashKeyPrefix");
        String namespace = hbaseProps.getProperty("namespace");
        Map<String, List<String>> tableSplitMap = new HashMap<>();
        DbPool.setZkClientApi(zkClientApi);
        DbPool.setDruidMonitorEnable(false);
        if (!DbPool.init()) {
            log.error("DbPool.init() faile,数据库连接失败！");
            return false;
        }
        log.info("sParamValue:" + sParamValue);
        if ("create".equals(sParam)) {
            long starttime = System.currentTimeMillis();
            if (sParamValue != null) {//获取表名列表
                generateRegionTables(tableList, props, hashKeyPrefix, namespace, tableSplitMap);
            }
            if (sParamValue.contains("-name")) {
                String tablename = namespace + ":" + sParamValue.replaceFirst("-name", "");
                hBaseClientApi.createTable(tablename, "collect_cdr_id");
            } else {
                //hbase建表
                for (String tableName : tableList) {
                    hBaseClientApi.createTableAsync(tableName, "collect_cdr_id");
                }
            }
            long endtime = System.currentTimeMillis();
            System.out.println("建表耗时：" + (endtime - starttime) / 1000 + "秒");
        } else if ("createTable".equals(sParam)) {
            String sql = "";
            if (sParamValue != null && sParamValue.contains("name")) {
                String tableName = sParamValue.replace("name", "");
                sql = "select a.table_name table_name,string_agg(distinct(c.en_name),',') en_name from tpl_indb_table a,tpl_indb_table_format_item b,tpr_resource_attr c where a.table_format_id = b.table_format_id and b.indb_field_id=c.attr_id and a.table_name='" + tableName + "' group by a.table_name";
            } else {
                sql = "select a.table_name table_name,string_agg(distinct(c.en_name),',') en_name from tpl_indb_table a,tpl_indb_table_format_item b,tpr_resource_attr c where a.table_format_id = b.table_format_id and b.indb_field_id=c.attr_id group by a.table_name";
            }
            List<TplIndbTableItem> tplIndbTableItems = DBUtils.queryList(TplIndbTableItem.class, DbPool.getConn(), sql);
            System.out.println(tplIndbTableItems.toString());
            //建表
            for (TplIndbTableItem tplIndbTableItem : tplIndbTableItems) {
                String tableName = tplIndbTableItem.getTable_name();
                if (tableName.contains(".")) {
                    StringBuilder sb = new StringBuilder(tableName);
                    tableName = namespace + ":" + sb.substring(sb.indexOf(".") + 1);
                }
                hBaseClientApi.createTableAsync(tableName, tplIndbTableItem.getEn_name().split(","));
            }
        } else if ("deleteTable".equals(sParam)) {
            String sql = "";
            if (sParamValue != null && sParamValue.contains("name")) {
                String tableName = namespace + ":" + sParamValue.replace("name", "");
                hBaseClientApi.deleteTable(tableName);
            } else {
                sql = "select distinct(table_name) table_name from tpl_indb_table";
                List<TplIndbTableItem> tplIndbTableItems = DBUtils.queryList(TplIndbTableItem.class, DbPool.getConn(), sql);
                for (TplIndbTableItem item : tplIndbTableItems) {
                    hBaseClientApi.deleteTable(namespace + ":" + item.getTable_name());
                }
            }
        } else if ("delete".equals(sParam)) {
            if ("all".equals(sParamValue)) {
                TableName[] tables = hBaseClientApi.getTablesByNameSpace(namespace);
                for (TableName tablename : tables) {
                    hBaseClientApi.deleteTable(tablename.getNameAsString());
                }
            } else if (sParamValue.contains("-name")) {
                String tablename = namespace + ":" + sParamValue.replaceFirst("-name", "");
                hBaseClientApi.deleteTable(tablename);
            } else {
                //获取表名列表
                generateRegionTables(tableList, props, hashKeyPrefix, namespace, tableSplitMap);
                for (int i = 0; i < tableList.size(); i++) {
                    hBaseClientApi.deleteTable(tableList.get(i));
                }
            }
        } else if ("list".equals(sParam)) {
            hBaseClientApi.listTablesByNameSpace(namespace);
        } else if ("exist".equals(sParam)) {
            String tablename = namespace + ":" + sParamValue;
            boolean tableExist = hBaseClientApi.isTableExist(tablename);
            log.info(tablename + "," + (tableExist == true ? "存在" : "不存在"));
        } else if ("scan".equals(sParam)) {
            String tablename = namespace + ":" + sParamValue;
            ResultScanner results = hBaseClientApi.scanTable(tablename);
            int num = hBaseClientApi.printScanner(results);
            log.info("表数据量：" + num);
            hBaseClientApi.printTabledesc(tablename);
        } else if ("checkandput".equals(sParam)) {
            String[] split = sParamValue.split("\\,");
            String tablename = namespace + ":" + split[0];
            String rowkey = split[1];
            String value = split[2];
            boolean flag = hBaseClientApi.checkAndPutTableData(tablename, rowkey, "collect_cdr_id", value);
            log.info("数据插入结果：" + (flag == true ? "成功" : "失败"));
        } else if ("compare".equals(sParam)) {
            System.out.println("TableName\tExist");
            List<String> tabNotExistList = new ArrayList<>();
            for (int i = 0; i < tableList.size(); i++) {
                String tabName = tableList.get(i);
                boolean tableExist = hBaseClientApi.isTableExist(tabName);
                System.out.println(tabName + "\t" + (tableExist == true ? "存在" : "不存在"));
                if (!tableExist) {
                    tabNotExistList.add(tabName);
                }
            }
            log.info("以下表不存在!");
            for (int i = 0; i < tabNotExistList.size(); i++) {
                log.info(tabNotExistList.get(i));
            }
        } else if ("puttest".equals(sParam)) {
            int cnt = Integer.valueOf(sParamValue);
            String tabName = namespace + ":test";
            String prefix = "20220208101032|***********|592|***********|20220208101116";
            hBaseClientApi.deleteTable(tabName);
            hBaseClientApi.createTable(tabName, "collect_cdr_id");
            try {
                Thread.sleep(1000 * 10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            long s = System.currentTimeMillis();
            for (int i = 0; i < cnt; i++) {
                StringBuilder sb = new StringBuilder(i);
                hBaseClientApi.checkAndPutTableData(tabName, sb.reverse().toString() + prefix, "collect_cdr_id", "1");
            }
            long end = System.currentTimeMillis();
            System.out.println("耗时" + (end - s) + "毫秒");


            String tabNameSplit = namespace + ":testsplit";
            hBaseClientApi.deleteTable(tabNameSplit);
            String[] split = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
            hBaseClientApi.createTableAsyncSplit(tabNameSplit, Arrays.asList(split), "collect_cdr_id");
            try {
                Thread.sleep(1000 * 10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            s = System.currentTimeMillis();
            for (int i = 0; i < cnt; i++) {
                StringBuilder sb = new StringBuilder(i);
                hBaseClientApi.checkAndPutTableData(tabNameSplit, sb.reverse().toString() + prefix, "collect_cdr_id", "1");
            }
            end = System.currentTimeMillis();
            System.out.println("分区表耗时" + (end - s) + "毫秒");
        }


        return true;
    }

    boolean debug() {
        MsgBody.DebugBody body = new MsgBody.DebugBody();
        body.COMMAND_ID = getSystemDateStr() + "_1";
        body.MSG_TYPE = "REQUEST";
        body.TASK_TYPE = MsgBody.TASK_TYPE.T_DEBUG.getType();
        body.REQUEST_PROCESS_ID = zkClientApi.getProcessID();
        body.STATE = "";
        body.LEVEL = modeSub;
        body.CREATE_DATE = getSystemDateStr();
        if ("module_id".equals(sParam)) {
            body.PROCESS_ID = "";
            if (sParamValue != null) {
                String[] moduleList = sParamValue.split(",");
                for (int i = 0; i < moduleList.length; i++) {
                    body.MODULE_ID = moduleList[i];
                    if (body.MODULE_ID != null) {
                        String json = JSON.toJSONString(body);
                        taskMessage.sendMessage("10100000", json);
                        log.info("控制进程的调试开关，json:" + json);
                    }
                }
            }
        } else if ("process_id".equals(sParam)) {
            body.MODULE_ID = "";
            if (sParamValue != null) {
                String[] processList = sParamValue.split(",");
                for (int i = 0; i < processList.length; i++) {
                    body.PROCESS_ID = processList[i];
                    if (body.PROCESS_ID != null) {
                        String json = JSON.toJSONString(body);
                        taskMessage.sendMessage("10100000", json);
                        log.info("控制进程的调试开关，json:" + json);
                    }
                }
            }
        }

        return true;
    }

    boolean redislen() {
        //Long len = transfer.isExistsKey(sParam);
        //System.out.println("查询缓存中 " + sParam + ",记录数为：" + len);
        return true;
    }

    private void generateRegionTables(List<String> tableList, Properties props, String hashKeyPrefix, String namespace, Map<String, List<String>> tableSplitMap) {
        List<FilterRegionConfig> filterRegionConfigs;// 获取数据库连接
        String sql = "select source_event_type_id,comments,hash_key_date_format,per from " + props.getProperty("database_name") + "filter_region_config order by id asc";
        filterRegionConfigs = DBUtils.queryList(FilterRegionConfig.class, DbPool.getConn(), sql);
        //多个月份建表
        String[] valueSplit = sParamValue.split(",");
        for (String sValue : valueSplit) {
            Calendar cal = Calendar.getInstance();
            cal.set(Integer.valueOf(sValue.substring(0, 4)), Integer.valueOf(sValue.substring(4, 6)) - 1, 1);
            int monthDays = cal.getActualMaximum(Calendar.DATE);
            String tablename = null;
            //按照配置表建表
            for (int i = 0; i < filterRegionConfigs.size(); i++) {
                FilterRegionConfig filterRegionConfig = filterRegionConfigs.get(i);
                String hash_key_date_format = filterRegionConfig.getHash_key_date_format();
                if (hash_key_date_format.contains("dd")) {
                    for (int j = 1; j <= monthDays; j++) {
                        tablename = namespace == null ? "" : (namespace + ":") + hashKeyPrefix + filterRegionConfigs.get(i).getSource_event_type_id()
                                + "_" + sValue + String.format("%02d", j);
                        tableList.add(tablename);
                    }
                } else if (hash_key_date_format.contains("MM")) {
                    tablename = namespace == null ? "" : (namespace + ":") + hashKeyPrefix + filterRegionConfigs.get(i).getSource_event_type_id()
                            + "_" + sValue;
                    tableList.add(tablename);
                }
            }
        }

        log.info("所有表：" + tableList.size());
    }

    /**
     * 获取分区数组
     *
     * @param filterRegionConfig
     * @param date
     * @return
     */
    private List<String> getSplitList(FilterRegionConfig filterRegionConfig, String date) {
        List<String> splitList = new ArrayList<>();
        String hash_key_date_format = filterRegionConfig.getHash_key_date_format();
        int per = filterRegionConfig.getPer();
        hash_key_date_format.split("-");
        Calendar cal = Calendar.getInstance();
        String[] split = date.split("-");

        cal.set(Integer.valueOf(split[0]), Integer.valueOf(split[1]) - 1, 1);
        int monthDays = cal.getActualMaximum(Calendar.DATE);
        if (hash_key_date_format.split("-").length == 3) {
            //分片到日
            for (int j = 1; j <= monthDays; j++) {
                splitList.add(date + "-" + String.format("%02d", j));
            }
        } else if (hash_key_date_format.split("-").length == 4) {
            //分片到小时
            for (int i = 1; i <= monthDays; i++) {
                for (int j = 0; j < 24; j += per) {
                    String splitFormat = date.replace("-", "") + String.format("%02d", i);
                    splitFormat += String.format("%02d", j) + "0000";
                    splitList.add(splitFormat);
                }
            }
        }
        return splitList;
    }

    @Override
    public void run() {
        log.info("Manager Thread run start");
        if (mode.equals(RUN_MODE.SIMULATOR.mode)) {
            if (!simulator()) {
                log.error("simulator false");
            }
        } else if (mode.equals(RUN_MODE.DB2FILE.mode)) {
            if (!db2file()) {
                log.error("db2file false");
            }
        } else if (mode.equals(RUN_MODE.SYNC.mode)) {
            if (!sync()) {
                log.error("sync false");
            }
        } else if (mode.equals(RUN_MODE.ROLLBACK.mode)) {
            if (!rollback()) {
                log.error("rollback false");
            }
        } else if (mode.equals(RUN_MODE.CONTROL.mode)) {
            if (!process()) {
                log.error("process false");
            }
        } else if (mode.equals(RUN_MODE.CLEAR.mode)) {
            if (!clear()) {
                log.error("clear false");
            }
        } else if (mode.equals(RUN_MODE.HBASE.mode)) {
            if (!hbaseOperation()) {
                log.error("hbase table " + sParam + " false");
            }
        } else if (mode.equals(RUN_MODE.DEBUG.mode)) {
            if (!debug()) {
                log.error("debug false");
            }
        } else if (mode.equals(RUN_MODE.REDISLEN.mode)) {
            if (!redislen()) {
                log.error("redislen false");
            }
        }

        log.info("Manager Thread run close");
        onExit();
    }

    void onExit() {
        log.info("Manager Thread exit");
        taskMessage.close();
        if (ctgMqPullApi != null) {
            ctgMqPullApi.close();
        }
        //zkClientApi.close();
        CmdClient.close();
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public void setModeSub(String modeSub) {
        this.modeSub = modeSub;
    }

    public void setsParam(String sParam) {
        this.sParam = sParam;
    }

    public void setsParamValue(String sParamValue) {
        this.sParamValue = sParamValue;
    }

    public void setsParamRbModuleId(String sParamRbModuleId) {
        this.sParamRbModuleId = sParamRbModuleId;
    }

    public void setsParamLoadId(String sParamLoadId) {
        this.sParamLoadId = sParamLoadId;
    }

    public void setsFileName(String sFileName) {
        this.sFileName = sFileName;
    }

    public String getSystemDateStr() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(System.currentTimeMillis());
        return simpleDateFormat.format(date);
    }

    public enum RUN_MODE {
        SIMULATOR(1, "simulator", "模拟采集发包"),
        DB2FILE(2, "db2file", "出库生成话单文件"),
        KAFKA(3, "kafka", "写Kafka数据"),
        SYNC(4, "sync", "发送同步请求"),
        CONTROL(5, "control", "发送进程启停请求"),
        ROLLBACK(6, "rollback", "发送任务重做请求"),
        CLEAR(7, "clear", "清空topic消息"),
        HBASE(8, "hbase", "hbase表操作"),
        DEBUG(9, "debug", "调试开关命令"),
        REDISLEN(10, "redislen", "redis的Key记录数查询");

        int id;
        String mode;
        String remark;

        RUN_MODE(int id, String mode, String remark) {
            this.id = id;
            this.mode = mode;
            this.remark = remark;
        }

        public String getMode() {
            return mode;
        }
    }
}
