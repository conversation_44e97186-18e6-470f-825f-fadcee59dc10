<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.itco</groupId>
        <artifactId>settle</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.itco</groupId>
    <artifactId>bill-public</artifactId>
    <version>5.0.1-SNAPSHOT</version>
    <name>bill-public</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <org.apache.hadoop.hadoop-hdfs>2.7.3</org.apache.hadoop.hadoop-hdfs>
        <org.apache.hadoop.hadoop-common>2.7.3</org.apache.hadoop.hadoop-common>
        <org.apache.hadoop.hadoop-client>2.7.3</org.apache.hadoop.hadoop-client>
        <org.apache.hbase.hbase-client>2.2.4</org.apache.hbase.hbase-client>
        <org.slf4j.slf4j-log4j12>1.7.25</org.slf4j.slf4j-log4j12>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>5.2.2.RELEASE</version>
        </dependency>

       <!-- <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
            <version>${org.apache.hadoop.hadoop-hdfs}</version>
            <exclusions>
                <exclusion> <groupId>org.slf4j</groupId> <artifactId>slf4j-log4j12</artifactId></exclusion>
                <exclusion> <groupId>log4j</groupId> <artifactId>log4j</artifactId> </exclusion>
                <exclusion> <groupId>javax.servlet</groupId> <artifactId>servlet-api</artifactId> </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>${org.apache.hadoop.hadoop-common}</version>
            <exclusions>
                <exclusion> <groupId>org.slf4j</groupId> <artifactId>slf4j-log4j12</artifactId></exclusion>
                <exclusion> <groupId>log4j</groupId> <artifactId>log4j</artifactId> </exclusion>
                <exclusion> <groupId>javax.servlet</groupId> <artifactId>servlet-api</artifactId> </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
            <version>${org.apache.hadoop.hadoop-client}</version>
            <exclusions>
                <exclusion> <groupId>org.slf4j</groupId> <artifactId>slf4j-log4j12</artifactId></exclusion>
                <exclusion> <groupId>log4j</groupId> <artifactId>log4j</artifactId> </exclusion>
                <exclusion> <groupId>javax.servlet</groupId> <artifactId>servlet-api</artifactId> </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>-->

    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-dev</id>
            <url>http://10.11.1.190:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-dev</id>
            <url>http://10.11.1.190:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
     <!--<distributionManagement>
        <repository>
            <id>yundao-fj-iss</id>
            <url>http://project-repo02.ctyundao.cn/artifactory/upload-fj-iss-13319-release</url>
        </repository>
        <snapshotRepository>
            <id>yundao-fj-iss</id>
            <url>http://project-repo02.ctyundao.cn/artifactory/upload-fj-iss-13319-snapshot</url>
        </snapshotRepository>
    </distributionManagement>
-->
</project>
