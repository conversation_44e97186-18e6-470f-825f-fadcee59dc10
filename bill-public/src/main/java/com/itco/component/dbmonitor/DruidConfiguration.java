package com.itco.component.dbmonitor;

import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.util.StringUtils;
import com.itco.component.zookeeper.ZkClientApi;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;


//@Component
//@DependsOn("loadZookeeper")
public class DruidConfiguration {
    final Log log= LogFactory.getLog(DruidConfiguration.class);

    // 通过构造函数注入ZkClientApi
    private final ZkClientApi zkClientApi;

    @Autowired
    public DruidConfiguration(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    //static String druidPath = "/config/file/druid.properties";
    static String druidPath = "druid.properties";

    private String url;
    private String username;
    private String password;
    private String driverClassName;
    private int initialSize = 6;
    private int minIdle = 5;
    private int maxActive = 20;
    private long maxWait = 60000;
    String databaseName = "postgres";

    static int confLoadMode = 0;

    public static void setConfLoadMode(int mode) {
        confLoadMode = mode;
    }

    //@Bean
    //@ConditionalOnProperty(name = "datasource.druid.enabled", havingValue = "true")
    public DynamicDataSourceRouter dataSourceRouter() {
        // 初始化zookeeper
        if (!loadZookeeper()) {
            log.error("loadZookeeper() failure");
            return null;
        }

        DynamicDataSourceRouter router = new DynamicDataSourceRouter();
        if (!loadDruidPostgres(router)) {
            log.error("loadDruidPostgres(router) failure");
            return null;
        }

        // 启动监听
        MasterConfigWatcher.setZkClientApi(zkClientApi);
        MasterConfigWatcher.startMonitorMaster();
        return router;
    }


    boolean loadZookeeper() {
        if (!zkClientApi.init(new MasterConfigWatcher())) {
            System.out.println("zkClientApi.init() failure");
            return false;
        }

        if (!zkClientApi.loadInitTable()) {
            System.out.println("zkClientApi.loadInitTable() failure");
            return false;
        }

        System.out.println("zookeeper 初始化完成");
        return true;
    }


    @Override
    public String toString() {
        return "DataSourceConfig{" +
                "url='" + url + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", driverClassName='" + driverClassName + '\'' +
                ", initialSize=" + initialSize +
                ", minIdle=" + minIdle +
                ", maxActive=" + maxActive +
                ", maxWait=" + maxWait +
                '}';
    }

    public boolean loadDruidPostgres(DynamicDataSourceRouter router) {
        int database_cnt = 0;
        Properties druidProperties = null;
        //属性文件位于src根目录时,加"/"则不要使用ClassLoader,如果使用ClassLoader则无需"/"
        if (confLoadMode == 0) {
            druidProperties = zkClientApi.getPropertiesFromZK(druidPath);
            String strTmp = druidProperties.getProperty("database_cnt");
            if (!StringUtils.isEmpty(strTmp)) {
                database_cnt = Integer.parseInt(strTmp);
            }
        } else {
            try {
                String jdbcProperties = "/" + druidPath;
                Map<String, String> env = System.getenv();
                String configPath = env.get("CONFIG");
                if (configPath == null || configPath.equals("")) {
                    // windows 使用资源目录下的zk.properties ideal直接测试代码
                    Resource resource = new ClassPathResource(jdbcProperties);
                    if (resource == null) {
                        log.error("缺少资源文件:" + jdbcProperties);
                        return false;
                    }
                    druidProperties = PropertiesLoaderUtils.loadProperties(resource);
                    log.info("资源文件:" + jdbcProperties);
                } else {
                    //  linux主机使用环境变量CONFIG路径下的zk.properties
                    BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + jdbcProperties));
                    if (bufferedReader == null) {
                        log.error("配置文件不存在:" + configPath + jdbcProperties);
                        return false;
                    }
                    druidProperties = new Properties();
                    druidProperties.load(bufferedReader);
                    log.info("配置文件:" + configPath + jdbcProperties);
                }
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }
        }

        for (int i = 1; i <= database_cnt; i++) {
            String strMode = druidProperties.getProperty(databaseName + ".mode." + i);
            String strDriver = druidProperties.getProperty(databaseName + ".driver." + i);
            String strUrl = druidProperties.getProperty(databaseName + ".url." + i);
            String strUsername = druidProperties.getProperty(databaseName + ".username." + i);
            String strPublicKey = druidProperties.getProperty(databaseName + ".publicKey." + i);
            String strPassword = druidProperties.getProperty(databaseName + ".password." + i);

            if (StringUtils.isEmpty(strDriver) || StringUtils.isEmpty(strUrl) || StringUtils.isEmpty(strUsername) || StringUtils.isEmpty(strPublicKey) || StringUtils.isEmpty(strPassword)) {
                log.error("database:" + i + ",信息不完整，请核实");
                log.error("strMode:" + strMode + ",strDriver:" + strDriver + ",strUrl:" + strUrl + ",strUsernamee:" + strUsername + ",strPublicKey:" + strPublicKey + ",strPassword:" + strPassword);
                return false;
            }

            if ("master".equals(strMode)) {
                log.info("总共:" + database_cnt + " 个数据源，当前默认数据库连接是：" + i);
                MasterConfigWatcher.setCurrentKey(String.valueOf(i));
            }

            try {
                password = ConfigTools.decrypt(strPublicKey, strPassword);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }

            DruidDataSource dataSource = new DruidDataSource();
            dataSource.setUrl(strUrl);
            dataSource.setUsername(strUsername);
            dataSource.setPassword(password);
            dataSource.setDriverClassName(strDriver);
            dataSource.setInitialSize(initialSize);
            dataSource.setMinIdle(minIdle);
            dataSource.setMaxActive(maxActive);
            dataSource.setMaxWait(maxWait);
            //dataSource.setDefaultAutoCommit(true);

            dataSource.setSocketTimeout(300000);
            dataSource.setConnectTimeout(10000);
            dataSource.setTimeBetweenEvictionRunsMillis(60000);
            dataSource.setMinEvictableIdleTimeMillis(300000);
            dataSource.setTestWhileIdle(true);
            dataSource.setTestOnBorrow(false);
            dataSource.setTestOnReturn(false);
            dataSource.setPoolPreparedStatements(true);
            dataSource.setKeepAlive(true);
            dataSource.setMaxOpenPreparedStatements(50);
            dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);

            router.addDataSource(String.valueOf(i), dataSource);
        }
        return true;
    }
}
