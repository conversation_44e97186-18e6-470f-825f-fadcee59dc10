package com.itco.component.dbmonitor;

import com.alibaba.druid.util.StringUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;

import java.sql.Connection;
import java.util.Properties;

@Slf4j
//@Component
public class MasterConfigWatcher implements Watcher {
    static String druidPath = "/config/file/druid.properties";
    String databaseName = "postgres";
    static String currentKey;

    static ZkClientApi zkClientApi = null;
    static String mode = "client";

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        MasterConfigWatcher.zkClientApi = zkClientApi;
    }

    public static void setMode(String mode) {
        MasterConfigWatcher.mode = mode;
    }

    public static boolean startMonitorMaster() {
        //log.info("重新监听:" + masterPath);
        // 监听自己进程目录，获取消息
        zkClientApi.getData(druidPath, true);
        return true;
    }

    boolean loadDruidPostgres(String path) {
        int database_cnt = 0;
        Properties druidProperties = zkClientApi.getPropertiesFromZK(path);
        String strTmp = druidProperties.getProperty("database_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            database_cnt = Integer.parseInt(strTmp);
        }

        for (int i = 1; i <= database_cnt; i++) {
            String strMode = druidProperties.getProperty(databaseName + ".mode." + i);
            if (StringUtils.isEmpty(strMode)) {
                log.error("数据源解析异常。" + strTmp);
            }
            if ("master".equals(strMode)) {
                log.info("总共:" + database_cnt + " 个数据源，当前默认数据库连接是：" + i);
                MasterConfigWatcher.setCurrentKey(String.valueOf(i));
            }
        }

        return true;
    }

    @Override
    public synchronized void process(WatchedEvent event) {
        Event.KeeperState keeperState = event.getState();
        Event.EventType eventType = event.getType();
        String path = event.getPath();

        if (Event.KeeperState.SyncConnected == keeperState) {
            // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
            // ConnectedReadOnly:连接只读,Disconnected:连接失败
            if (Event.EventType.None == eventType) {
                // 如果建立建立成功,让后程序往下走
                log.info("ClientWatcher" + "，zk 建立连接成功!");
            } else if (Event.EventType.NodeCreated == eventType) {
                log.info("ClientWatcher" + "，事件通知,新增node节点" + path);
            } else if (Event.EventType.NodeDataChanged == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被修改....");
                if ("monitor".equals(mode)) {
                    if (!loadDruidPostgres(path)) {
                        log.error("loadDruidPostgres(path) failure");
                    }
                } else if ("client".equals(mode)){
                    DbPool.close();
                    Connection conn = DbPool.getConn();
                    DbPool.close(conn);
                }
                log.info("重新监听:" + path);
                startMonitorMaster();
            } else if (Event.EventType.NodeDeleted == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被删除....");
            } else if (Event.EventType.NodeChildrenChanged == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "，子节点被修改....");
            }
        }
    }

    public static void setCurrentKey(String currentKey) {
        MasterConfigWatcher.currentKey = currentKey;
    }

    public static String getCurrentKey() {
        return currentKey;
    }
}