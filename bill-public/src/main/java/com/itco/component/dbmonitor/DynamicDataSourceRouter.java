package com.itco.component.dbmonitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
public class DynamicDataSourceRouter extends AbstractRoutingDataSource implements InitializingBean {
    private Map<Object, Object> dynamicTargetDataSources = new ConcurrentHashMap<>();

    @Override
    protected Object determineCurrentLookupKey() {
        // 这里应该返回当前线程所使用的数据源的key
        // 你可以实现一些线程局部存储的逻辑(ThreadLocal)，以决定哪个key是当前线程要用的数据源

        return MasterConfigWatcher.getCurrentKey();
    }

    @Override
    public void afterPropertiesSet() {
        // 初始化数据源映射
        setTargetDataSources(dynamicTargetDataSources);
        super.afterPropertiesSet();
    }

    // 添加数据源
    public void addDataSource(String key, DataSource dataSource) {
        dynamicTargetDataSources.put(key, dataSource);
        afterPropertiesSet(); // 重建resolvedDataSources
    }

    // 移除数据源
    public void removeDataSource(String key) {
        dynamicTargetDataSources.remove(key);
        afterPropertiesSet(); // 重建resolvedDataSources
    }

    // 获取所有数据源 (为了遵守封装性，通常不建议暴露内部集合的直接引用)
    public Map<Object, Object> getCurrentDataSources() {
        return new ConcurrentHashMap<>(dynamicTargetDataSources);
    }

    public DataSource determineTargetDataSource() {
        return (DataSource) dynamicTargetDataSources.get(determineCurrentLookupKey());
    }
}