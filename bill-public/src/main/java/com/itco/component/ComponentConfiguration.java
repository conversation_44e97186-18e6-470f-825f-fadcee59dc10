package com.itco.component;

import com.itco.component.dbmonitor.DruidConfiguration;
import com.itco.component.dbmonitor.DynamicDataSourceRouter;
import com.itco.component.dbmonitor.MasterConfigWatcher;
import com.itco.component.ctgcache.JedisClientApi;
import com.itco.component.ctgmq.CtgMqPullApi;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;

import java.util.Properties;

//@Component
public class ComponentConfiguration {
    final static Log log = LogFactory.getLog(ComponentConfiguration.class);

    private ZkClientApi zkClientApi;
    private Common common;

    @Value(value = "${billing_line_id}")
    private String billing_line_id = "11";

    @Value(value = "${module_code}")
    private String module_code = "";

    @Value(value = "${component.zookeeper.monitor.enabled:false}")
    private boolean component_zookeeper_monitor_enable = false;

    @Value(value = "${component.datasource.monitor.enabled:false}")
    private boolean component_datasource_monitor_enable = false;

    @Order(1)
    @Bean()
    @ConditionalOnProperty(name = "component.zookeeper.enabled", havingValue = "true")
    public ZkClientApi loadZookeeper() {
        log.info("module_code:"+module_code+",billing_line_id:"+billing_line_id);
        // 初始化zookeeper
        zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId(billing_line_id);
        zkClientApi.setModuleCode(module_code);

        if (component_zookeeper_monitor_enable) {
            if (!zkClientApi.init(new MasterConfigWatcher())) {
                log.error(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
                throw new BeanCreationException("无法初始化 ZkClientApi");
            }
        } else {
            if (!zkClientApi.init()) {
                log.error(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
                throw new BeanCreationException("无法初始化 ZkClientApi");
            }
        }
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());

        MasterConfigWatcher.setZkClientApi(zkClientApi);
        return zkClientApi;
    }

    @DependsOn({"loadZookeeper"})
    @Bean
    @ConditionalOnProperty(name = "component.common.enabled", havingValue = "true")
    public Common loadCommon() {
        if (!zkClientApi.loadInitTable()) {
            log.error("zkClientApi.loadInitTable() failure");
            throw new BeanCreationException("无法初始化loadInitTable()");
        }

        Common common = new Common();
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("zkClientApi.loadCommonProperties(common) failure");
            throw new BeanCreationException("无法初始化Common");
        }

        log.info("common:" + common.toString());
        return common;
    }

    @DependsOn({"loadZookeeper", "loadCommon"})
    @Bean
    @ConditionalOnProperty(name = "component.datasource.enabled", havingValue = "true")
    public DynamicDataSourceRouter loadDynamicDataSourceRouter() {
        DruidConfiguration druidConfiguration = new DruidConfiguration(zkClientApi);
        DynamicDataSourceRouter router = new DynamicDataSourceRouter();
        if (!druidConfiguration.loadDruidPostgres(router)) {
            log.error("loadDruidPostgres(router) failure");
            throw new BeanCreationException("无法初始化DynamicDataSourceRouter");
        }

        // 启动监听
        if (component_datasource_monitor_enable) {
            MasterConfigWatcher.startMonitorMaster();
        }

        return router;
    }

    @Order(4)
    @Bean
    @ConditionalOnProperty(name = "component.ctgcache.enabled", havingValue = "true")
    public JedisClientApi loadCtgCache() {
        String database = "databaseRecord";
        Properties propsCtg = zkClientApi.getPropertiesFromZK("ctgCache.properties");

        JedisClientApi jedisClientApi = new JedisClientApi();
        jedisClientApi.setDatabase(database);
        jedisClientApi.setProps(propsCtg);
        if (!jedisClientApi.init()) {
            System.out.println("jedisClientApi.init() faile");
            throw new BeanCreationException("无法初始化JedisClientApi");
        }

        return jedisClientApi;
    }

    @Order(5)
    @Bean
    @ConditionalOnProperty(name = "component.ctgmq.enabled", havingValue = "true")
    public CtgMqPullApi loadCtgMq() {
        CtgMqPullApi ctgMqPullApi = new CtgMqPullApi();
        ctgMqPullApi.setZkClientApi(zkClientApi);
        ctgMqPullApi.setCommon(common);
        // 消息类初始化
        if (!ctgMqPullApi.init()) {
            log.error("ctgMqPullApi.init() 失败");
            // Bean初始化失败，抛出异常以中止应用启动
            throw new BeanCreationException("无法初始化CtgMqPullApi");
        }
        return ctgMqPullApi;
    }


}
