package com.itco.component.ctgcache;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Properties;

/**
 * @ClassName: JedisUtil
 * @Description: ctgCache缓存工具类
 * <AUTHOR>
 * @Date 2021/11/19
 * @Version 1.0
 */
public class JEDISUtil {
    private static Log log = LogFactory.getLog(JEDISUtil.class);
    
    // 缓存客户端实现
    private CacheClientApi cacheClient = null;
    
    // 缓存客户端类型
    private boolean useRedis = false;
    
    long lastConnTime = 0;//链接每次刷新的时间
    static int checkDate_10min = 600 * 1000;
    static int checkDate_6min = 360 * 1000;
    static Properties propsCtg = null;
    static String database = null;

    public boolean init() {
        // 判断使用哪种缓存实现
        useRedis = propsCtg != null && "true".equalsIgnoreCase(propsCtg.getProperty("use.redis", "false"));
        
        log.info("========== 缓存初始化 ==========");
        log.info("当前缓存模式: " + (useRedis ? "Redis" : "ctgcache"));
        
        if (useRedis) {
            // 使用Redis实现
            log.info("初始化Redis客户端...");
            cacheClient = new RedisClientApi();
        } else {
            // 使用ctgcache实现
            log.info("初始化ctgcache客户端...");
            cacheClient = new JedisClientApi();
        }
        
        cacheClient.setDatabase(database);
        cacheClient.setProps(propsCtg);
        
        if (!cacheClient.init()) {
            log.error(useRedis ? "Redis初始化失败" : "ctgcache初始化失败");
            return false;
        }
        
        log.info("缓存初始化成功");
        log.info("数据库名称: " + database);
        log.info("============================");
        
        lastConnTime = System.currentTimeMillis();
        return true;
    }

    public static void setPropsCtg(Properties props) {
        propsCtg = props;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public CacheClientApi getJedisClientApi() {
        if (System.currentTimeMillis() - lastConnTime >= checkDate_6min) {
            synchronized (JEDISUtil.class) {
                if (System.currentTimeMillis() - lastConnTime > checkDate_6min) {
                    try {
                        log.info("上一次使用缓存间隔:" + (System.currentTimeMillis() - lastConnTime) + ",超过 " + checkDate_6min + " 毫秒，重新获取");
                        lastConnTime = System.currentTimeMillis();
                        cacheClient.jedisReset();
                        log.info("重新获取缓存连接成功:" + (System.currentTimeMillis() - lastConnTime));
                    } catch (JedisException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else if (System.currentTimeMillis() - lastConnTime >= checkDate_10min) {
            synchronized (JEDISUtil.class) {
                if (System.currentTimeMillis() - lastConnTime > checkDate_10min) {
                    try {
                        log.info("上一次使用缓存间隔:" + (System.currentTimeMillis() - lastConnTime) + ",超过 " + checkDate_10min + " 毫秒，重新连接");
                        lastConnTime = System.currentTimeMillis();
                        cacheClient.jedisReConnection();
                        log.info("重新连接缓存成功:" + (System.currentTimeMillis() - lastConnTime));
                    } catch (JedisException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        lastConnTime = System.currentTimeMillis();
        return cacheClient;
    }

    public void reset() {
        try {
            cacheClient.jedisReset();
        } catch (JedisException e) {
            e.printStackTrace();
        }
    }

    public void reConnection() {
        try {
            cacheClient.jedisReConnection();
        } catch (JedisException e) {
            e.printStackTrace();
        }
    }
    
    // 以下为便捷方法，方便调用方直接使用
    
    /**
     * 检查字段是否存在
     */
    public boolean isExistsField(String hash, String field) {
        return cacheClient.isExistsField(hash, field);
    }
    
    /**
     * 检查key是否存在
     */
    public boolean isExistsKey(String key) {
        return cacheClient.isExistsKey(key);
    }
    
    /**
     * 设置hash字段值
     */
    public boolean hSet(String hash, String field, String value) {
        return cacheClient.hSet(hash, field, value);
    }
    
    /**
     * 获取hash字段值
     */
    public String hGet(String hash, String field) throws JedisException {
        return cacheClient.hGet(hash, field);
    }
    
    /**
     * 获取hash字段数量
     */
    public Long hLen(String hash) {
        return cacheClient.hLen(hash);
    }
    
    /**
     * 删除key
     */
    public boolean deleteKey(String hash) {
        return cacheClient.deleteKey(hash);
    }
    
    /**
     * 设置过期时间
     */
    public boolean expire(String hash, int seconds) {
        return cacheClient.Expire(hash, seconds);
    }
    
    /**
     * 删除hash字段
     */
    public boolean deleteHashkey(String hash, String field) {
        return cacheClient.deleteHashkey(hash, field);
    }

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁的键
     * @param value 锁的值（通常用于标识加锁者）
     * @return 是否成功获取锁
     */
    public boolean lock(String lockKey, String value) {
        return cacheClient.lock(lockKey, value);
    }
    
    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的键
     * @param value 锁的值（确保是当前锁的持有者）
     * @return 是否成功释放锁
     */
    public boolean unLock(String lockKey, String value) {
        return cacheClient.unLock(lockKey, value);
    }
}
