package com.itco.component.ctgcache;

import com.ctg.itrdc.cache.pool.CtgJedisPoolException;
import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.ctg.itrdc.cache.vjedis.jedis.HostAndPort;
import com.ctg.itrdc.cache.vjedis.jedis.JedisPoolConfig;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPool;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPoolConfig;
import com.itco.component.jdbc.DecodePassword;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

public class Jedis<PERSON>lient<PERSON><PERSON> implements CacheClientApi {
    static Log log = LogFactory.getLog(JedisClientApi.class);
    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "PX";
    private static final Long RELEASE_SUCCESS = 1L;

    static Properties props = null;
    List<HostAndPort> hostAndPortList = new ArrayList<>(); //接入机地址列表
    JedisConfig jConfig = null;
    CtgVJedisPool pool = null;
    VProxyJedis jedis = null;
    String database = "databaseTable";

    Long version = new Long(1);

    public void setProps(Properties properties) {
        props = properties;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public boolean init() {
        //  读取redis.properties
        jConfig = new JedisConfig();

        jConfig.setHost(props.getProperty("host"));
        jConfig.setMaxIdle(Integer.valueOf(props.getProperty("maxIdle")));
        jConfig.setMaxTotal(Integer.valueOf(props.getProperty("maxTotal")));
        jConfig.setMinIdle(Integer.valueOf(props.getProperty("minIdle")));
        jConfig.setMaxWaitMillis(Integer.valueOf(props.getProperty("maxWaitMillis")));
        jConfig.setDatabase(props.getProperty(database));

        String username = props.getProperty("username");
        String publicKeyTmp = props.getProperty("publicKey");
        String passwordTmp = props.getProperty("password");
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        jConfig.setPassword(username + "#" + passwd);
        jConfig.setPeriod(Integer.valueOf(props.getProperty("period")));
        jConfig.setMonitorTimeout(Integer.valueOf(props.getProperty("monitorTimeout")));

        log.info(username + "," + database + ":" + props.getProperty(database));
        // 初始化redis操作类
        // 解析配置IP和端口号
        getHostAddr(jConfig.getHost());

        GenericObjectPoolConfig poolConfig = new JedisPoolConfig(); //线程池配置
        poolConfig.setMaxIdle(jConfig.getMaxIdle()); //最大空闲连接数
        poolConfig.setMaxTotal(jConfig.getMaxTotal()); // 最大连接数（空闲+使用中）
        poolConfig.setMinIdle(jConfig.getMinIdle()); //保持的最小空闲连接数
        poolConfig.setMaxWaitMillis(jConfig.getMaxWaitMillis()); //连接时最大的等待时间

        CtgVJedisPoolConfig config = new CtgVJedisPoolConfig(hostAndPortList);
        config.setDatabase(jConfig.getDatabase()); //分组对应的桶位

        config.setPassword(jConfig.getPassword()); // "用户#密码"
        config.setPoolConfig(poolConfig); //线程池配置
        config.setPeriod(jConfig.getPeriod());  //后台监控执行周期，毫秒
        config.setMonitorTimeout(jConfig.getMonitorTimeout());  //后台监控ping命令超时时间,毫秒
        //config.setMonitorLog(true);    //后台监控日志是否打印

        pool = new CtgVJedisPool(config); //创建连接池
        try {
            jedis = pool.getResource();
        } catch (CtgJedisPoolException e) {
            log.error("CtgJedisPoolException:" + e.getMessage());
            return false;
        }

        return true;
    }

    public boolean jedisReset() throws JedisException {
        try {
            jedis.close();
            log.info("92 jedis.close 成功");
        } catch (Exception e) {
            log.error("94 jedis.close 失败");
        }
        try {
            jedis = pool.getResource();
        } catch (CtgJedisPoolException e) {
            log.error("jedisReset CtgJedisPoolException: " + e.getMessage());
            try {
                pool.close();
                log.info("101 pool.close() jedisReset()成功");
            } catch (Exception ex) {
                log.info("102 pool.close() jedisReset()失败");
            }
            log.info("重新初始化jedisReset.init()");
            if (!init()) {
                log.error("jedisReset()-> init() jedisReset()重新初始化失败");
                throw new JedisException("114", "jedisReset()-> init() jedisReset()重新初始化失败");
            }
        }
        return true;
    }

    public boolean jedisReConnection() throws JedisException {
        try {
            jedis.close();
            log.info("123 jedis.close() jedisReConnection()成功");
            pool.close();
            log.info("125 pool.close() jedisReConnection()成功");
        } catch (Exception e) {
            log.error("127 jedis.close() or pool.close() jedisReConnection()失败");
        }

        if (!init()) {
            log.error("jedisReConnection()-> init() 重新初始化失败");
            throw new JedisException("132", "jedisReConnection()-> init()  jedisReConnection()重新初始化失败");
        }

        return true;
    }

    public void close() {
        try {
            jedis.close();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        try {
            pool.close();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    boolean getHostAddr(String host) {
        int iPosition = 0, iStart = 0;
        while ((iPosition = host.indexOf(",", iStart)) != -1) {
            String str = host.substring(iStart, iPosition);
            String ip = str.substring(0, str.indexOf(":"));
            String port = str.substring(str.indexOf(":") + 1);

            HostAndPort hostAndPort = new HostAndPort(ip, Integer.valueOf(port));
            hostAndPortList.add(hostAndPort);
            iStart = iPosition + 1;
        }

        String str = host.substring(iStart);
        String ip = str.substring(0, str.indexOf(":"));
        String port = str.substring(str.indexOf(":") + 1);

        HostAndPort hostAndPort = new HostAndPort(ip, Integer.valueOf(port));
        hostAndPortList.add(hostAndPort);
        return true;
    }

    /*返回hash 的filed值存不存在:true -> 存在，false -> 不存在*/
    public boolean isExistsField(String hash, String field) {
        return jedis.hexists(hash, field);
    }

    /*返回hash 的filed值存不存在:true -> 存在，false -> 不存在*/
    public boolean isExistsKey(String key) {
        boolean vRet = jedis.exists(key);
        if (!vRet) {
            return false;
        }

        return true;
    }


    /*设置hash key值*/
    public boolean hSet(String hash, String field, String value) {
        jedis.hset(hash, field, value);
        return true;
    }

    /*查询hash key值*/
    public String hGet(String hash, String field) throws JedisException {
        try {
            return jedis.hget(hash, field);
        } catch (Exception e) {
            log.error("183:" + e.getMessage());
            if (e.getMessage().indexOf("Read timed out") > -1) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
                try {
                    return jedis.hget(hash, field);
                } catch (Exception e2) {
                    log.error("193:" + e2.getMessage());
                }
            }
        }
        jedisReset();
        return jedis.hget(hash, field);
    }

    /*查询hash key值*/
    public Long hLen(String hash) {
        Long vRet = jedis.hlen(hash);
        return vRet;
    }

    public boolean deleteKey(String hash) {
        jedis.del(hash);
        return true;
    }

    public boolean Expire(String hash, int seconds) {
        jedis.expire(hash, seconds);
        return true;
    }

    public boolean deleteHashkey(String hash, String field) {
        jedis.hdel(hash, field);
        return true;
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @param value
     * @return
     */
    public boolean lock(String lockKey, String value) {
        // 锁10秒超时，失效，60秒抛异常
        //jedis.lock(name, value, 10 * 1000, 60 * 1000);

        String result = jedis.set(lockKey, value, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, 10 * 1000);

        if (LOCK_SUCCESS.equals(result)) {
            return true;
        }
        return false;
    }

    /**
     * 解锁
     *
     * @param lockKey
     * @param value
     * @return
     */
    public boolean unLock(String lockKey, String value) {
        //jedis.unlock(lockKey, value);
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(value));

        if (RELEASE_SUCCESS.equals(result)) {
            return true;
        }
        return false;
    }
}
