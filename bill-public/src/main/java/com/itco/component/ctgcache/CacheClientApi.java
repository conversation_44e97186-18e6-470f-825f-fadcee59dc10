package com.itco.component.ctgcache;

import java.util.Properties;

/**
 * 缓存客户端API接口
 * 定义了缓存客户端需要实现的通用方法
 */
public interface CacheClientApi {
    
    /**
     * 设置配置信息
     */
    void setProps(Properties properties);
    
    /**
     * 设置数据库名
     */
    void setDatabase(String database);
    
    /**
     * 初始化
     */
    boolean init();
    
    /**
     * 重置连接
     */
    boolean jedisReset() throws JedisException;
    
    /**
     * 重新连接
     */
    boolean jedisReConnection() throws JedisException;
    
    /**
     * 关闭连接
     */
    void close();
    
    /**
     * 检查字段是否存在
     */
    boolean isExistsField(String hash, String field);
    
    /**
     * 检查key是否存在
     */
    boolean isExistsKey(String key);
    
    /**
     * 设置hash字段值
     */
    boolean hSet(String hash, String field, String value);
    
    /**
     * 获取hash字段值
     */
    String hGet(String hash, String field) throws JedisException;
    
    /**
     * 获取hash字段数量
     */
    Long hLen(String hash);
    
    /**
     * 删除key
     */
    boolean deleteKey(String hash);
    
    /**
     * 设置过期时间
     */
    boolean Expire(String hash, int seconds);
    
    /**
     * 删除hash字段
     */
    boolean deleteHashkey(String hash, String field);
    
    /**
     * 加锁
     *
     * @param lockKey 锁的键
     * @param value 锁的值（通常用于标识加锁者）
     * @return 是否成功获取锁
     */
    boolean lock(String lockKey, String value);
    
    /**
     * 解锁
     *
     * @param lockKey 锁的键
     * @param value 锁的值（确保是当前锁的持有者）
     * @return 是否成功释放锁
     */
    boolean unLock(String lockKey, String value);
} 