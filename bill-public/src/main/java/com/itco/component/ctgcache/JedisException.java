package com.itco.component.ctgcache;

public class JedisException extends Exception {
    String id;
    String error_msg;

    public JedisException(String id, String error) {
        this.id = id;
        error_msg = error;
    }

    public String getException_id() {
        return id;
    }

    public String getError_msg() {
        return error_msg;
    }

    @Override
    public String toString() {
        return "JedisException{" +
                "exception_id=" + id +
                ", error_msg='" + error_msg + '\'' +
                '}';
    }

    @Override
    public String getMessage() {
        return toString();
    }
}
