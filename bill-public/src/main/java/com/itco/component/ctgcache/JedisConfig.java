package com.itco.component.ctgcache;

public class JedisConfig {
    //接入机地址信息
    String host;
    //最大空闲连接数
    int maxIdle;
    //最大连接数（空闲+使用中）
    int maxTotal;
    ////保持的最小空闲连接数
    int minIdle;
    //最大等待时间
    int maxWaitMillis;
    //默认桶位
    String database;
    //用户名密码
    String password;
    //检查周期
    int period;
    //监控命令超时时间
    int monitorTimeout;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getMaxIdle() {
        return maxIdle;
    }

    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMaxTotal() {
        return maxTotal;
    }

    public void setMaxTotal(int maxTotal) {
        this.maxTotal = maxTotal;
    }

    public int getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(int minIdle) {
        this.minIdle = minIdle;
    }

    public int getMaxWaitMillis() {
        return maxWaitMillis;
    }

    public void setMaxWaitMillis(int maxWaitMillis) {
        this.maxWaitMillis = maxWaitMillis;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public int getMonitorTimeout() {
        return monitorTimeout;
    }

    public void setMonitorTimeout(int monitorTimeout) {
        this.monitorTimeout = monitorTimeout;
    }
}
