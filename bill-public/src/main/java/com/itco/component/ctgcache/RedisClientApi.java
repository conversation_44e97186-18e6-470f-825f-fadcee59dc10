package com.itco.component.ctgcache;

import com.itco.component.jdbc.DecodePassword;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import redis.clients.jedis.*;

import java.util.HashSet;
import java.util.Collections;
import java.util.Properties;
import java.util.Set;

/**
 * Redis客户端适配器，实现与JedisClientApi相同的接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RedisClientApi implements CacheClientApi {
    private static final Log LOG = LogFactory.getLog(RedisClientApi.class);
    
    /**
     * 分布式锁相关常量
     */
    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "PX";
    private static final Long RELEASE_SUCCESS = 1L;
    
    /**
     * 默认配置值
     */
    private static final String DEFAULT_MODE = "single";
    private static final String DEFAULT_HOST = "localhost:6379";
    private static final int DEFAULT_MAX_TOTAL = 10;
    private static final int DEFAULT_MAX_IDLE = 5;
    private static final int DEFAULT_MIN_IDLE = 3;
    private static final long DEFAULT_MAX_WAIT_MILLIS = 3000L;
    private static final int DEFAULT_TIMEOUT = 2000;
    private static final int DEFAULT_RETRY_TIMES = 5;
    private static final int DEFAULT_LOCK_EXPIRE_TIME = 10000;
    
    /**
     * Redis相关配置
     */
    private JedisPool jedisPool;
    private JedisCluster jedisCluster;
    private boolean isClusterMode;
    
    /**
     * 配置相关
     */
    private static Properties props;
    private String database = "databaseTable";
    
    @Override
    public void setProps(Properties properties) {
        props = properties;
    }

    @Override
    public void setDatabase(String database) {
        this.database = database;
    }
    
    @Override
    public boolean init() {
        try {
            // 获取Redis模式配置
            String mode = props.getProperty("redis.mode", DEFAULT_MODE);
            isClusterMode = "cluster".equalsIgnoreCase(mode);
            
            // 获取Redis主机信息
            String hostInfo = props.getProperty("host", DEFAULT_HOST);
            LOG.info("使用缓存服务器连接信息: " + hostInfo);
            
            // 配置连接池参数
            JedisPoolConfig poolConfig = createPoolConfig();
            
            // 处理密码配置
            String password = handlePasswordConfig();
            
            if (isClusterMode) {
                return initClusterMode(hostInfo, poolConfig, password);
            } else {
                return initSingleMode(hostInfo, poolConfig, password);
            }
        } catch (Exception e) {
            LOG.error("Redis初始化失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 创建连接池配置
     */
    private JedisPoolConfig createPoolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(Integer.parseInt(props.getProperty("maxTotal", String.valueOf(DEFAULT_MAX_TOTAL))));
        poolConfig.setMaxIdle(Integer.parseInt(props.getProperty("maxIdle", String.valueOf(DEFAULT_MAX_IDLE))));
        poolConfig.setMinIdle(Integer.parseInt(props.getProperty("minIdle", String.valueOf(DEFAULT_MIN_IDLE))));
        poolConfig.setMaxWaitMillis(Long.parseLong(props.getProperty("maxWaitMillis", String.valueOf(DEFAULT_MAX_WAIT_MILLIS))));
        return poolConfig;
    }
    
    /**
     * 处理密码配置
     */
    private String handlePasswordConfig() {
        String password = props.getProperty("password");
        if (password == null || password.isEmpty()) {
            LOG.info("未配置密码，将使用无密码连接");
            return null;
        }
        
        String redisUseRawPassword = props.getProperty("redis.use.raw.password", "false");
        if ("true".equalsIgnoreCase(redisUseRawPassword)) {
            LOG.info("配置使用原始密码，跳过解密步骤");
            return password;
        }
        
        try {
            String publicKey = props.getProperty("publicKey");
            if (publicKey != null && !publicKey.isEmpty()) {
                String decodedPassword = DecodePassword.decryption(publicKey, password);
                LOG.info("已成功解密Redis密码");
                return decodedPassword;
            }
            LOG.warn("未找到publicKey，无法解密密码，将使用原始密码");
        } catch (Exception e) {
            LOG.error("密码解密失败，将使用原始密码: " + e.getMessage(), e);
        }
        return password;
    }
    
    /**
     * 初始化集群模式
     */
    private boolean initClusterMode(String hostInfo, JedisPoolConfig poolConfig, String password) {
        try {
            Set<HostAndPort> nodes = parseClusterNodes(hostInfo);
            int connectionTimeout = Integer.parseInt(props.getProperty("connectionTimeout", String.valueOf(DEFAULT_TIMEOUT)));
            int soTimeout = Integer.parseInt(props.getProperty("soTimeout", String.valueOf(DEFAULT_TIMEOUT)));
            
            if (password != null) {
                jedisCluster = new JedisCluster(nodes, connectionTimeout, soTimeout, DEFAULT_RETRY_TIMES, password, poolConfig);
            } else {
                jedisCluster = new JedisCluster(nodes, connectionTimeout, soTimeout, DEFAULT_RETRY_TIMES, poolConfig);
            }
            
            // 使用字符串命令替代已过时的ping()方法
            String pingResponse = jedisCluster.echo("PING");
            if (pingResponse == null || pingResponse.isEmpty()) {
                LOG.error("Redis集群连接测试失败：无法获取PING响应");
                return false;
            }
            LOG.info("Redis集群连接初始化成功，节点数量: " + nodes.size());
            return true;
        } catch (Exception e) {
            LOG.error("Redis集群连接测试失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 初始化单机模式
     */
    private boolean initSingleMode(String hostInfo, JedisPoolConfig poolConfig, String password) {
        try {
            String[] hostParts = hostInfo.split(",")[0].split(":");
            String host = hostParts[0];
            int port = Integer.parseInt(hostParts[1]);
            int timeout = Integer.parseInt(props.getProperty("connectionTimeout", String.valueOf(DEFAULT_TIMEOUT)));
            
            if (password != null) {
                jedisPool = new JedisPool(poolConfig, host, port, timeout, password);
            } else {
                jedisPool = new JedisPool(poolConfig, host, port, timeout);
            }
            
            try (Jedis jedis = jedisPool.getResource()) {
                // 使用字符串命令替代已过时的ping()方法
                String pingResponse = jedis.echo("PING");
                if (pingResponse == null || pingResponse.isEmpty()) {
                    LOG.error("Redis单机连接测试失败：无法获取PING响应");
                    return false;
                }
                LOG.info("Redis单机连接初始化成功，地址: " + host + ":" + port);
                return true;
            }
        } catch (Exception e) {
            LOG.error("Redis单机连接测试失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 解析集群节点信息
     */
    private Set<HostAndPort> parseClusterNodes(String hostInfo) {
        String[] hostList = hostInfo.split(",");
        Set<HostAndPort> nodes = new HashSet<>();
        
        for (String hostPart : hostList) {
            String[] parts = hostPart.split(":");
            String host = parts[0];
            int port = Integer.parseInt(parts[1]);
            nodes.add(new HostAndPort(host, port));
        }
        
        return nodes;
    }
    
    @Override
    public boolean jedisReset() throws JedisException {
        try {
            if (isClusterMode) {
                return true;
            }
            return true;
        } catch (Exception e) {
            LOG.error("jedisReset失败: " + e.getMessage(), e);
            throw new JedisException("1001", "jedisReset失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean jedisReConnection() throws JedisException {
        try {
            close();
            return init();
        } catch (Exception e) {
            LOG.error("jedisReConnection失败: " + e.getMessage(), e);
            throw new JedisException("1002", "jedisReConnection失败: " + e.getMessage());
        }
    }
    
    @Override
    public void close() {
        try {
            if (isClusterMode && jedisCluster != null) {
                jedisCluster.close();
                LOG.info("关闭Redis集群连接");
            } else if (jedisPool != null) {
                jedisPool.close();
                LOG.info("关闭Redis单机连接");
            }
        } catch (Exception e) {
            LOG.error("关闭Redis连接失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isExistsField(String hash, String field) {
        try {
            if (isClusterMode) {
                return jedisCluster.hexists(hash, field);
            }
            try (Jedis jedis = jedisPool.getResource()) {
                return jedis.hexists(hash, field);
            }
        } catch (Exception e) {
            LOG.error("检查字段存在失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean isExistsKey(String key) {
        try {
            if (isClusterMode) {
                return jedisCluster.exists(key);
            }
            try (Jedis jedis = jedisPool.getResource()) {
                return jedis.exists(key);
            }
        } catch (Exception e) {
            LOG.error("检查键存在失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean hSet(String hash, String field, String value) {
        try {
            if (isClusterMode) {
                jedisCluster.hset(hash, field, value);
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.hset(hash, field, value);
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("设置hash字段值失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public String hGet(String hash, String field) throws JedisException {
        try {
            if (isClusterMode) {
                return jedisCluster.hget(hash, field);
            }
            try (Jedis jedis = jedisPool.getResource()) {
                return jedis.hget(hash, field);
            }
        } catch (Exception e) {
            LOG.error("获取hash字段值失败: " + e.getMessage(), e);
            throw new JedisException("1003", "获取hash字段值失败: " + e.getMessage());
        }
    }
    
    @Override
    public Long hLen(String hash) {
        try {
            if (isClusterMode) {
                return jedisCluster.hlen(hash);
            }
            try (Jedis jedis = jedisPool.getResource()) {
                return jedis.hlen(hash);
            }
        } catch (Exception e) {
            LOG.error("获取hash字段数量失败: " + e.getMessage(), e);
            return 0L;
        }
    }
    
    @Override
    public boolean deleteKey(String hash) {
        try {
            if (isClusterMode) {
                jedisCluster.del(hash);
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.del(hash);
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("删除key失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean Expire(String hash, int seconds) {
        try {
            if (isClusterMode) {
                jedisCluster.expire(hash, seconds);
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.expire(hash, seconds);
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("设置过期时间失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean deleteHashkey(String hash, String field) {
        System.out.println("删除hash字段: " + hash + ", field: " + field);
        try {
            if (isClusterMode) {
                jedisCluster.hdel(hash, field);
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.hdel(hash, field);
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("删除hash字段失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean lock(String lockKey, String value) {
        try {
            String result;
            if (isClusterMode) {
                result = jedisCluster.set(lockKey, value, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, DEFAULT_LOCK_EXPIRE_TIME);
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    result = jedis.set(lockKey, value, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, DEFAULT_LOCK_EXPIRE_TIME);
                }
            }
            return LOCK_SUCCESS.equals(result);
        } catch (Exception e) {
            LOG.error("获取锁失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean unLock(String lockKey, String value) {
        try {
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Object result;
            
            if (isClusterMode) {
                result = jedisCluster.eval(script, Collections.singletonList(lockKey), Collections.singletonList(value));
            } else {
                try (Jedis jedis = jedisPool.getResource()) {
                    result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(value));
                }
            }
            return RELEASE_SUCCESS.equals(result);
        } catch (Exception e) {
            LOG.error("释放锁失败: " + e.getMessage(), e);
            return false;
        }
    }
} 