package com.itco.component.jdbc;

import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.alibaba.druid.util.StringUtils;
import com.itco.component.dbmonitor.MasterConfigWatcher;
import com.itco.component.zookeeper.ZkClientApi;
import lombok.SneakyThrows;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.Properties;

/**
 * @ClassName: DbPool
 * @Description: 数据库连接
 * <AUTHOR>
 * @Date 2021/12/24
 * @Version 1.0
 */
public class DbPool {
    static Log log = LogFactory.getLog(DbPool.class);

    static String jdbcFileName = new String("jdbc.properties");
    static String druidFileName = new String("druid.properties");
    static int confLoadMode = 0;
    static boolean autoCommit = true;

    static ZkClientApi zkClientApi = null;
    static Properties jProp = null;
    static DruidDataSource dataSource = null;

    static int connectTimeOut = 1200000;
    static int socketTimeOut = 1200000;

    static boolean initFlag = false;
    final static String databaseName = "postgres";
    static boolean druidMonitorEnable = false;

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        if (DbPool.zkClientApi == null && !druidMonitorEnable) {
            DbPool.zkClientApi = zkClientApi;
        }
    }

    public static boolean isInitFlag() {
        return initFlag;
    }

    public static void setDruidMonitorEnable(boolean druidMonitorEnable) {
        DbPool.druidMonitorEnable = druidMonitorEnable;
    }

    public static boolean initZookeeper() {
        if (!initFlag) {
            if (zkClientApi != null) {
                zkClientApi.close();
            }

            // 初始化zookeeper
            zkClientApi = new ZkClientApi();
            if (!zkClientApi.init(new MasterConfigWatcher())) {
                log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
                return false;
            }

            MasterConfigWatcher.setZkClientApi(zkClientApi);
            log.info("initZookeeper() 初始化完成，process_id:" + zkClientApi.getProcessID());
            initFlag = true;
        }
        return true;
    }

    public static void setConfLoadMode(int mode) {
        confLoadMode = mode;
    }

    public static void setAutoCommit(boolean autoCommit) {
        DbPool.autoCommit = autoCommit;
    }

    public static boolean isAutoCommit() {
        return autoCommit;
    }

    public static boolean loadProp() {
        if (druidMonitorEnable) {
            if (!initZookeeper()) {
                log.error("initZookeeper() failure");
                return false;
            }

            if (zkClientApi.isDruidPropertiesEnable()) {
                log.info("数据源: druid.properties");
                if (!loadDruidPostgres()) {
                    log.error("loadDruidPostgres() faile，连接数据库失败。");
                    return false;
                }
            }
        } else {
            log.info("数据源: jdbc.properties");
            if (!loadJdbcPostgres()) {
                log.error("loadJdbcPostgres() faile，连接数据库失败。");
                return false;
            }
        }

        return true;
    }

    static boolean loadJdbcPostgres() {
        try {
            //属性文件位于src根目录时,加"/"则不要使用ClassLoader,如果使用ClassLoader则无需"/"
            if (confLoadMode == 0) {
                String configJson = zkClientApi.getDataByPath("/config/file/" + jdbcFileName);
                jProp = new Properties();
                jProp.load(new ByteArrayInputStream(configJson.getBytes()));
            } else {
                String jdbcProperties = "/" + jdbcFileName;
                Map<String, String> env = System.getenv();
                String configPath = env.get("CONFIG");
                if (configPath == null || configPath.equals("")) {
                    // windows 使用资源目录下的zk.properties ideal直接测试代码
                    Resource resource = new ClassPathResource(jdbcProperties);
                    if (resource == null) {
                        log.error("缺少资源文件:" + jdbcProperties);
                        return false;
                    }
                    jProp = PropertiesLoaderUtils.loadProperties(resource);
                    log.info("资源文件:" + jdbcProperties);
                } else {
                    //  linux主机使用环境变量CONFIG路径下的zk.properties
                    BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + jdbcProperties));
                    if (bufferedReader == null) {
                        log.error("配置文件不存在:" + configPath + jdbcProperties);
                        return false;
                    }
                    jProp = new Properties();
                    jProp.load(bufferedReader);
                    log.info("配置文件:" + configPath + jdbcProperties);
                }
            }
            String publicKey = jProp.getProperty("publicKey");
            String password = jProp.getProperty("password");

            if (jProp.getProperty("connectTimeOut") != null) {
                connectTimeOut = Integer.parseInt(jProp.getProperty("connectTimeOut"));
            }
            if (jProp.getProperty("socketTimeOut") != null) {
                socketTimeOut = Integer.parseInt(jProp.getProperty("socketTimeOut"));
            }

            String passwd = DecodePassword.decryption(publicKey, password);
            jProp.put(DruidDataSourceFactory.PROP_PASSWORD, passwd);
            //log.info(jProp.toString());
            log.info("url:" + jProp.getProperty("url") + ",username:" + jProp.getProperty("username"));
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @SneakyThrows
    static boolean loadDruidPostgres() {
        int database_cnt = 0;
        Properties druidProperties = null;
        //属性文件位于src根目录时,加"/"则不要使用ClassLoader,如果使用ClassLoader则无需"/"
        if (confLoadMode == 0) {
            String configJson = zkClientApi.getDataByPath("/config/file/" + druidFileName);
            druidProperties = new Properties();
            druidProperties.load(new ByteArrayInputStream(configJson.getBytes()));
        } else {
            try {
                String jdbcProperties = "/" + druidFileName;
                Map<String, String> env = System.getenv();
                String configPath = env.get("CONFIG");
                if (configPath == null || configPath.equals("")) {
                    // windows 使用资源目录下的zk.properties ideal直接测试代码
                    Resource resource = new ClassPathResource(jdbcProperties);
                    if (resource == null) {
                        log.error("缺少资源文件:" + jdbcProperties);
                        return false;
                    }
                    druidProperties = PropertiesLoaderUtils.loadProperties(resource);
                    log.info("资源文件:" + jdbcProperties);
                } else {
                    //  linux主机使用环境变量CONFIG路径下的zk.properties
                    BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + jdbcProperties));
                    if (bufferedReader == null) {
                        log.error("配置文件不存在:" + configPath + jdbcProperties);
                        return false;
                    }
                    druidProperties = new Properties();
                    druidProperties.load(bufferedReader);
                    log.info("配置文件:" + configPath + jdbcProperties);
                }
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }
        }

        String strTmp = druidProperties.getProperty("database_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            database_cnt = Integer.parseInt(strTmp);
        } else {
            log.error(jdbcFileName + "，未配置：database_cnt 的值");
            return false;
        }

        String initialSize = "6";
        String maxActive = "20";
        String maxWait = "60000";
        String minIdle = "5";

        String tmp = druidProperties.getProperty("initialSize");
        if (!StringUtils.isEmpty(tmp)) {
            initialSize = tmp;
        }
        tmp = druidProperties.getProperty("maxActive");
        if (!StringUtils.isEmpty(tmp)) {
            maxActive = tmp;
        }
        tmp = druidProperties.getProperty("maxWait");
        if (!StringUtils.isEmpty(tmp)) {
            maxWait = tmp;
        }
        tmp = druidProperties.getProperty("minIdle");
        if (!StringUtils.isEmpty(tmp)) {
            minIdle = tmp;
        }
        tmp = druidProperties.getProperty("autoCommit");
        if ("false".equals(tmp)) {
            autoCommit = false;
        }

        for (int i = 1; i <= database_cnt; i++) {
            String strMode = druidProperties.getProperty(databaseName + ".mode." + i);
            if (!"master".equals(strMode)) {
                continue;
            }

            log.info("总共:" + database_cnt + " 个数据源，当前默认数据库连接是：" + i);
            MasterConfigWatcher.setCurrentKey(String.valueOf(i));

            String strDriver = druidProperties.getProperty(databaseName + ".driver." + i);
            String strUrl = druidProperties.getProperty(databaseName + ".url." + i);
            String strUsername = druidProperties.getProperty(databaseName + ".username." + i);
            String strPublicKey = druidProperties.getProperty(databaseName + ".publicKey." + i);
            String strPassword = druidProperties.getProperty(databaseName + ".password." + i);

            if (StringUtils.isEmpty(strDriver) || StringUtils.isEmpty(strUrl) || StringUtils.isEmpty(strUsername) || StringUtils.isEmpty(strPublicKey) || StringUtils.isEmpty(strPassword)) {
                log.error("database:" + i + ",信息不完整，请核实");
                log.error("strMode:" + strMode + ",strDriver:" + strDriver + ",strUrl:" + strUrl + ",strUsernamee:" + strUsername + ",strPublicKey:" + strPublicKey + ",strPassword:" + strPassword);
                return false;
            }

            String passwordTmp = null;
            try {
                passwordTmp = ConfigTools.decrypt(strPublicKey, strPassword);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }

            log.info("mode:" + strMode + ",Url:" + strUrl + ",username:" + strUsername);
            jProp = new Properties();
            jProp.put(DruidDataSourceFactory.PROP_URL, strUrl);
            jProp.put(DruidDataSourceFactory.PROP_USERNAME, strUsername);
            jProp.put(DruidDataSourceFactory.PROP_PASSWORD, passwordTmp);
            jProp.put(DruidDataSourceFactory.PROP_INITIALSIZE, initialSize);
            jProp.put(DruidDataSourceFactory.PROP_MAXACTIVE, maxActive);
            jProp.put(DruidDataSourceFactory.PROP_MAXWAIT, maxWait);
            jProp.put(DruidDataSourceFactory.PROP_MINIDLE, minIdle);
        }

        if (druidMonitorEnable && zkClientApi.isDynamicDatasourceEnable()) {
            MasterConfigWatcher.startMonitorMaster(); // 启动监听
        }
        return true;
    }

    public static synchronized boolean init() {
        try {
            if (dataSource == null) {
                if (!loadProp()) {
                    log.error("loadDruidPostgres() faile，连接数据库失败。");
                    return false;
                }
                dataSource = (DruidDataSource) DruidDataSourceFactory.createDataSource(jProp);
                dataSource.setConnectTimeout(connectTimeOut);
                dataSource.setSocketTimeout(socketTimeOut);
            }
            return true;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static synchronized Connection getConn() {
        try {
            if (dataSource == null) {
                if (!loadProp()) {
                    log.error("loadProp() faile，连接数据库失败。");
                    return null;
                }
                //System.out.println("jprop:" + jProp);
                dataSource = (DruidDataSource) DruidDataSourceFactory.createDataSource(jProp);
                dataSource.setConnectTimeout(connectTimeOut);
                dataSource.setSocketTimeout(socketTimeOut);
            }
            Connection conn = dataSource.getConnection();
            if (autoCommit) {
                conn.setAutoCommit(true);
            } else {
                conn.setAutoCommit(false);
            }
            return conn;
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void close() {
        if (dataSource != null) {
            dataSource.close();
            dataSource = null;
        }
    }

    public static synchronized void close(Connection conn) {
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static void close(PreparedStatement ps, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static synchronized void close(Connection conn, PreparedStatement ps, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

    }

    public static DruidDataSource getDataSource() {
        return dataSource;
    }
}
