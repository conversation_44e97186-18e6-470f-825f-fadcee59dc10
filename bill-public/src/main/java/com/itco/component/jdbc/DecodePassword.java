package com.itco.component.jdbc;

import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.druid.util.DruidPasswordCallback;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class DecodePassword extends DruidPasswordCallback {
    static Log log= LogFactory.getLog(DecodePassword.class);

    public static String decryption(String publicKey, String password) {
        try {
            return ConfigTools.decrypt(publicKey, password);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }
}
