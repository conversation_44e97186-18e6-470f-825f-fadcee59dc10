package com.itco.component.jdbc;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.*;

public class DBUtils {
    static Log log = LogFactory.getLog(DBUtils.class);

    /**
     * 封装通用的更新操作，对所有更新(INSERT,UPDATE，DELETE)有关的操作都能通过该方法实现
     *
     * @param sql
     * @return
     */
    public static boolean exeUpdate(Connection conn, String sql, Object... obj) {
        PreparedStatement ps = null;
        boolean bRet = false;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            bRet = ps.executeUpdate() > 0;
            return bRet;
        } catch (SQLException e) {
            log.error(e);
        } finally {
            close(ps, null);
        }
        return false;
    }

    public static boolean exeUpdateThrowException(Connection conn, String sql, Object... obj) throws SQLException {
        PreparedStatement ps = null;
        ps = conn.prepareStatement(sql);
        for (int i = 0; i < obj.length; i++) {
            ps.setObject(i + 1, obj[i]);
        }
        boolean bRet = ps.executeUpdate() > 0;
        close(ps, null);
        return bRet;
    }

    public static boolean exeInsertBatch(Connection conn, String sql, List<List<Object>> listss) {
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(sql);
            for (List<Object> lists : listss) {
                for (int i = 0; i < lists.size(); i++) {
                    ps.setObject(i + 1, lists.get(i));
                }
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException e) {
            log.error(e);
            return false;
        } finally {
            close(ps, null);
        }

        return true;
    }

    public static Object query(Connection conn, String sql, Object... params) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Object object = null;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();

            // 遍历结果集
            while (rs.next()) {
                object = rs.getObject(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("SQLException：" + e);
            return null;
        } catch (SecurityException e) {
            e.printStackTrace();
            log.error("SecurityException：" + e.getMessage());
        } finally {
            close(ps, rs);
        }
        return object;
    }

    public static <T> List<T> queryList(Class<T> t, Connection conn, String sql, Object... params) {
        List<T> list = new ArrayList<>();
        T obj = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            ResultSetMetaData rsmd = rs.getMetaData();
            // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
            Map<String, Object> map = new HashMap<>();
            // 遍历结果集
            while (rs.next()) {
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = rsmd.getColumnLabel(i + 1);
                    //获取列类型的int表示形式，以及列类型名称
                    //System.out.println("列名："+rsmd.getColumnName(i+1)+",列类型:"+rsmd.getColumnType(i + 1)+"----"+rsmd.getColumnTypeName(i+1));
                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname, value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    // 获取map集合键集(列名集合)
                    Set<String> columnNames = map.keySet();
                    // 创建对象
                    obj = t.newInstance();//new Student() //java.lang.Object
                    for (String column : columnNames) {
                        // 根据键获取值
                        Object value = map.get(column);

                        //当数据对象不为空时，才注入数据到属性中
                        if (Objects.nonNull(value)) {
                            // 获取属性对象
                            Field f = t.getDeclaredField(column);

                            // 设置属性为可访问状态
                            f.setAccessible(true);
                            // 为属性设置
                            f.set(obj, value);
                        }
                    }
                    list.add(obj);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("SQLException：" + e);
            return null;
        } catch (InstantiationException e) {
            e.printStackTrace();
            log.error("InstantiationException：" + e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            log.error("IllegalAccessException：" + e.getMessage());
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
            log.error(sql);
            log.error("NoSuchFieldException：" + e.getMessage());
        } catch (SecurityException e) {
            e.printStackTrace();
            log.error("SecurityException：" + e.getMessage());
        } finally {
            close(ps, rs);
        }
        return list;
    }

    /**
     * 使用驼峰映射查询
     *
     * @param t
     * @param conn
     * @param sql
     * @param params
     * @param <T>
     * @return
     */
    public static <T> List<T> queryListByCamelCase(Class<T> t, Connection conn, String sql, Object... params) {
        List<T> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();

            while (rs.next()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = rsmd.getColumnName(i);
                    Object value = rs.getObject(i);
                    // 转换为驼峰命名形式
                    String propertyName = convertToCamelCase(columnName);
                    map.put(propertyName, value);
                }
                // 创建对象并从Map中注入属性值
                T obj = null;
                try {
                    obj = createObjectFromMap(t, map);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InstantiationException e) {
                    e.printStackTrace();
                }
                list.add(obj);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("SQLException：" + e);
            return null;
        } finally {
            close(ps, rs);
        }
        return list;
    }

    private static <T> T createObjectFromMap(Class<T> t, Map<String, Object> map) throws IllegalAccessException, InstantiationException {
        T obj = t.newInstance();
        Field[] fields = t.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if (map.containsKey(fieldName)) {
                Object value = map.get(fieldName);
                field.setAccessible(true);
                field.set(obj, value);
            }
        }
        return obj;
    }

    private static String convertToCamelCase(String columnName) {
        String[] parts = columnName.split("_");
        StringBuilder result = new StringBuilder();
        result.append(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            result.append(parts[i].substring(0, 1).toUpperCase());
            result.append(parts[i].substring(1));
        }
        return result.toString();
    }

    /*根据sql语句查询数据库表
     * 返回字段名和结果的list 集合*/
    public static List<Map<String, Object>> queryMap(Connection conn, String sql, Object... params) {
        List<Map<String, Object>> recordMapList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();
            ResultSetMetaData rsmd = rs.getMetaData();
            //ORM操作（对象关系映射）
            while (rs.next()) {
                Map<String, Object> recordMap = new HashMap<>();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    //获取指定列的列名称
                    String cname = rsmd.getColumnLabel(i + 1);
                    //获取列值
                    Object value = rs.getObject(cname);
                    recordMap.put(cname, value);
                }
                recordMapList.add(recordMap);
            }
        } catch (SQLException e) {
            log.error(e);
            return null;
        } catch (SecurityException e) {
            log.error(e);
            return null;
        } finally {
            close(ps, rs);
        }
        return recordMapList;
    }

    public int loadTableRecord(Connection conn, String sql, int cnt, List<Map<String, Object>> list, Object... params) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultSetMetaData rsmd = null;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            rsmd = rs.getMetaData();

            if (list == null) {
                list = new ArrayList<>();
            } else {
                list.clear();
            }

            // 遍历结果集
            while (rs.next()) {
                // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
                Map<String, Object> map = new HashMap<>();
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = rsmd.getColumnLabel(i + 1);

                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname.toLowerCase(), value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    list.add(map);
                }
            }
        } catch (SQLException e) {
            log.error(e);
            e.printStackTrace();
            return -1;
        } finally {
            close(ps, rs);
        }

        return list.size();
    }


    static void close(PreparedStatement ps, ResultSet rs) {
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                log.error(e);
                e.printStackTrace();
            }
        }
    }
}