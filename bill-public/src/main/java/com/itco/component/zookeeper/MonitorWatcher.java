package com.itco.component.zookeeper;

import lombok.extern.slf4j.Slf4j;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MonitorWatcher implements Watcher {
    Map<String, String> moduleMap = new HashMap<>();

    Map<String, List<String>> monitorValue = new HashMap<>();

    ZkClientApi zkClientApi = null;

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void register(String path, boolean child) {
        List<String> list = monitorValue.get(path);
        if (list == null) {
            monitorValue.put(path, new ArrayList<>());
        }

        if (child) {
            log.info("重新监听节点:" + path);
            //zkClientApi.getChildPath(path, true);
        } else {
            log.info("重新监听子节点:" + path);
            zkClientApi.getData(path, true);
        }
    }



    @Override
    public void process(WatchedEvent event) {
        Event.KeeperState keeperState = event.getState();
        Event.EventType eventType = event.getType();
        String path = event.getPath();

        if (Event.KeeperState.SyncConnected == keeperState) {
            // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
            // ConnectedReadOnly:连接只读,Disconnected:连接失败
            if (Event.EventType.None == eventType) {
                // 如果建立建立成功,让后程序往下走
                log.info("ZkMonitor" + "，zk 建立连接成功!");
            } else if (Event.EventType.NodeCreated == eventType) {
                log.info("ZkMonitor" + "，事件通知,新增node节点" + path);
            } else if (Event.EventType.NodeDataChanged == eventType) {
                log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "被修改....");
            } else if (Event.EventType.NodeDeleted == eventType) {
                log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "被删除....");

            } else if (Event.EventType.NodeChildrenChanged == eventType) {
                log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "，子节点被修改....");
                String moduleCode = path.substring(path.lastIndexOf("/") + 1);

                /*List<String> gModuleList = zkClientApi.getChildPath(path, true);
                monitorValue.get(path).addAll(gModuleList);*/
            }
        }
    }


}
