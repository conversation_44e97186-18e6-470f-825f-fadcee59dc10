package com.itco.component.zookeeper;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.jdbc.DecodePassword;
import com.itco.entity.common.*;
import com.itco.framework.Version;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.zookeeper.*;
import org.apache.zookeeper.data.ACL;
import org.apache.zookeeper.data.Id;
import org.apache.zookeeper.data.Stat;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import static com.itco.entity.common.ErrorType.*;
import static com.itco.framework.Factory.getSystemDateStr;

public class ZkClientApi implements Watcher {
    private static Log log = LogFactory.getLog(ZkClientApi.class);
    private String zkProperties = "/zk.properties";

    // zookeeper 连接配置参数
    String ip;
    String hostID = null;
    String moduleCode = null;//模块英文名称，继承类传入
    String billingLineId = null;//实例编号
    String processType = null;//进程类型
    int sessionTimeoutMs;

    static String userName;
    static String password;
    static String shaBase64Passwd;

    TpModule tpModule;//通过模块名称，找到
    TpProcess tpProcess;//进程实例
    String systemProcessID;//系统进程编号
    String moduleID;//模块标识
    String processID;//进程实例标识
    String dspchProcessID;//调度进程标识
    boolean isDispatch = false;//是否调度

    String processHome = "/process";//zookeeper目录树，进程注册父节点
    String processAssistHome = "/process/Assist";//进程注册辅助目录
    String processLockHome = "/process/Lock";//进程锁目录
    String configHome = "/config";//zookeeper目录树，配置文件父节点
    String configTpModule = null;//模块配置路径
    String configTpProcess = null;//进程配置路径
    String messageConfig = null;//进程同步路径
    String messageProcess = null;//进程启停主目录
    String messageTask = null;//使用ZK收发任务消息路径

    int iMonitorCycleSecs = 6;   //调度进程同步监控间隔，单位秒
    long lMonitorStartTime = 0; //同上监控时间
    int iConnectCycleSecs = 6; //zookeeper 断开后，尝试重连间隔，单位秒
    long lStartConnectTime = 0;  //上次连接时间
    String sConnectionPath = null;
    int iWaitingProcessExitSecs = 60; //等待进程退出时间
    String uploadHomeConfig = null;//upload 配置文件读取目录

    int iConnectionLossCnt = 0; //连续失去连接次数
    int iSectionExpiredCnt = 0; //zk section失效次数
    int iConnectionRefusedCnt = 0; //连接拒绝次数

    Map<String, TpModule> moduleMap = new HashMap<>();  //模块信息 moduleMap.put(tpModuleTmp.module_code, tpModuleTmp);
    Map<String, TpProcess> processMap = new HashMap<>(); //进程信息 processMap.put(String.valueOf(tpProcess.process_id), tpProcess);
    Map<String, TpConfigCenter> tpConfigCenterMap = new HashMap<>();//配置中心 tpConfigCenterMap.put(tpConfigCenter.getEn_name(), tpConfigCenter);

    static volatile LinkedBlockingQueue<Map<String, List<String>>> gChangeModuleList = new LinkedBlockingQueue<>();//进程启停时，模块名称

    static volatile ZooKeeper zookeeper = null;//zk对象

    int errorCode = 0;
    String errorMsg = new String();

    String digest = "digest";
    String lockPath = null;
    boolean lockFlag = false;

    String version = null;

    String dbmonitorInstances;
    String dbmonitorDatabase;

    boolean druidPropertiesEnable = false;
    boolean dynamicDatasourceEnable = false;

    /**
     * 初始化
     *
     * @return true:成功，false:失败
     */
    public boolean init() {
        try {

            // linux 主机获取zk.properties
            Properties props = new Properties();
            Map<String, String> env = System.getenv();
            String configPath = env.get("CONFIG");
            if (configPath == null || configPath.equals("")) {
                // windows 使用资源目录下的zk.properties ideal直接测试代码
                Resource resource = new ClassPathResource(zkProperties);
                if (resource == null) {
                    log.error("缺少资源文件:" + zkProperties);
                    return false;
                }
                props = PropertiesLoaderUtils.loadProperties(resource);
                log.info("资源文件:" + zkProperties);
            } else {
                //  linux主机使用环境变量CONFIG路径下的zk.properties
                BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + zkProperties));
                if (bufferedReader == null) {
                    log.error("配置文件不存在:" + configPath + zkProperties);
                    return false;
                }
                props.load(bufferedReader);
                log.info("配置文件:" + configPath + zkProperties);
            }

            userName = props.getProperty("process.zk.param.userName");
            shaBase64Passwd = props.getProperty("process.zk.param.shaBase64Passwd");
            String publicKeyTmp = props.getProperty("process.zk.param.publicKey");
            String passwordTmp = props.getProperty("process.zk.param.password");
            password = DecodePassword.decryption(publicKeyTmp, passwordTmp);

            //log.info("userName:" + userName + ", password:" + password + ", shaBase64Passwd:" + shaBase64Passwd);

            ip = props.getProperty("process.zk.param.ip");
            sessionTimeoutMs = Integer.parseInt(props.getProperty("process.zk.param.sessionTimeoutMs"));

            if (props.getProperty("configHome") != null) {
                configHome = props.getProperty("configHome");
            }
            if (props.getProperty("processHome") != null) {
                processHome = props.getProperty("processHome");
            }
            if (props.getProperty("processAssistHome") != null) {
                processAssistHome = props.getProperty("processAssistHome");
            }
            if (props.getProperty("dbmonitor.instances") != null) {
                dbmonitorInstances = props.getProperty("dbmonitor.instances");
            }
            if (props.getProperty("dbmonitor.database") != null) {
                dbmonitorDatabase = props.getProperty("dbmonitor.database");
            }

            if (props.getProperty("processLockHome") != null) {
                processLockHome = props.getProperty("processLockHome");
            }

            configTpModule = props.getProperty("configTpModule");
            configTpProcess = props.getProperty("configTpProcess");

            uploadHomeConfig = props.getProperty("uploadHomeConfig");

            messageConfig = props.getProperty("message.config");
            messageProcess = props.getProperty("message.process");
            messageTask = props.getProperty("message.task");

            if (props.getProperty("connection_cycle_second") != null) {
                iConnectCycleSecs = Integer.parseInt(props.getProperty("connection_cycle_second"));
            }
            if (props.getProperty("wait_process_exit_secs") != null) {
                iWaitingProcessExitSecs = Integer.parseInt(props.getProperty("wait_process_exit_secs"));
            }
            log.info("zookeeper 地址:" + ip);
        } catch (IOException e) {
            log.error(e);
            return false;
        }

        try {
            if (isDispatch) {
                zookeeper = new ZooKeeper(ip, sessionTimeoutMs, this);
                zookeeper.addAuthInfo(digest, new String(userName + ":" + password).getBytes());

                //模块表记录
                if (!loadTpModule()) {
                    return false;
                }

                // 注册监听路径
                for (Map.Entry<String, TpModule> module : moduleMap.entrySet()) {
                    log.info("zooKeeper.getChildren -> " + processHome + "/" + module.getKey());
                    zookeeper.getChildren(processHome + "/" + module.getKey(), true);
                }
            } else {
                zookeeper = new ZooKeeper(ip, sessionTimeoutMs, new Watcher() {
                    @Override
                    public void process(WatchedEvent watchedEvent) {
                        // 判断是否建立连接
                        if (Event.KeeperState.SyncConnected == watchedEvent.getState()) {
                            // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
                            // ConnectedReadOnly:连接只读,Disconnected:连接失败
                            if (Event.EventType.None == watchedEvent.getType()) {
                                // 如果建立建立成功,让后程序往下走
                                log.info("Watcher" + ",zk 建立连接成功!");
                            }
                        }
                    }
                });
                zookeeper.addAuthInfo(digest, new String(userName + ":" + password).getBytes());
            }
        } catch (IOException e) {
            log.error("IOException 184:" + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error("InterruptedException 147:" + e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("KeeperException 147:" + e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 初始化
     *
     * @return true:成功，false:失败
     */
    public boolean init(Watcher watcher) {
        try {

            // linux 主机获取zk.properties
            Properties props = new Properties();
            Map<String, String> env = System.getenv();
            String configPath = env.get("CONFIG");
            if (configPath == null || configPath.equals("")) {
                // windows 使用资源目录下的zk.properties ideal直接测试代码
                Resource resource = new ClassPathResource(zkProperties);
                if (resource == null) {
                    log.error("缺少资源文件:" + zkProperties);
                    return false;
                }
                props = PropertiesLoaderUtils.loadProperties(resource);
                log.info("资源文件:" + zkProperties);
            } else {
                //  linux主机使用环境变量CONFIG路径下的zk.properties
                BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + zkProperties));
                if (bufferedReader == null) {
                    log.error("配置文件不存在:" + configPath + zkProperties);
                    return false;
                }
                props.load(bufferedReader);
                log.info("配置文件:" + configPath + zkProperties);
            }

            userName = props.getProperty("process.zk.param.userName");
            shaBase64Passwd = props.getProperty("process.zk.param.shaBase64Passwd");
            String publicKeyTmp = props.getProperty("process.zk.param.publicKey");
            String passwordTmp = props.getProperty("process.zk.param.password");
            password = DecodePassword.decryption(publicKeyTmp, passwordTmp);

            //log.info("userName:" + userName + ", password:" + password + ", shaBase64Passwd:" + shaBase64Passwd);

            ip = props.getProperty("process.zk.param.ip");
            sessionTimeoutMs = Integer.parseInt(props.getProperty("process.zk.param.sessionTimeoutMs"));

            if (props.getProperty("configHome") != null) {
                configHome = props.getProperty("configHome");
            }
            if (props.getProperty("processHome") != null) {
                processHome = props.getProperty("processHome");
            }
            if (props.getProperty("processAssistHome") != null) {
                processAssistHome = props.getProperty("processAssistHome");
            }
            if (props.getProperty("dbmonitor.instances") != null) {
                dbmonitorInstances = props.getProperty("dbmonitor.instances");
            }
            if (props.getProperty("dbmonitor.database") != null) {
                dbmonitorDatabase = props.getProperty("dbmonitor.database");
            }
            if ("true".equals(props.getProperty("druid.properties.enabled"))) {
                druidPropertiesEnable = true;
            }
            if ("true".equals(props.getProperty("dynamic.datasource.enabled"))) {
                dynamicDatasourceEnable = true;
            }

            if (props.getProperty("processLockHome") != null) {
                processLockHome = props.getProperty("processLockHome");
            }

            configTpModule = props.getProperty("configTpModule");
            configTpProcess = props.getProperty("configTpProcess");

            uploadHomeConfig = props.getProperty("uploadHomeConfig");

            messageConfig = props.getProperty("message.config");
            messageProcess = props.getProperty("message.process");
            messageTask = props.getProperty("message.task");

            if (props.getProperty("connection_cycle_second") != null) {
                iConnectCycleSecs = Integer.parseInt(props.getProperty("connection_cycle_second"));
            }
            if (props.getProperty("wait_process_exit_secs") != null) {
                iWaitingProcessExitSecs = Integer.parseInt(props.getProperty("wait_process_exit_secs"));
            }
            log.info("zookeeper 地址:" + ip);
        } catch (IOException e) {
            log.error(e);
            return false;
        }

        try {
            if (isDispatch) {
                zookeeper = new ZooKeeper(ip, sessionTimeoutMs, watcher);
                zookeeper.addAuthInfo(digest, new String(userName + ":" + password).getBytes());

                //模块表记录
                if (!loadTpModule()) {
                    return false;
                }

                // 注册监听路径
                for (Map.Entry<String, TpModule> module : moduleMap.entrySet()) {
                    log.info("zooKeeper.getChildren -> " + processHome + "/" + module.getKey());
                    zookeeper.getChildren(processHome + "/" + module.getKey(), true);
                }
            } else {
                zookeeper = new ZooKeeper(ip, sessionTimeoutMs, watcher);
                zookeeper.addAuthInfo(digest, new String(userName + ":" + password).getBytes());
            }
        } catch (IOException e) {
            log.error("IOException 184:" + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error("InterruptedException 147:" + e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("KeeperException 147:" + e.getMessage());
            return false;
        }
        return true;
    }

    boolean loadTpConfigCenter() {
        tpConfigCenterMap.clear();
        try {
            String JsonValue = new String(zookeeper.getData(configHome, false, null));
            int iBeginPos = 0, iPos = 0;
            while ((iPos = JsonValue.indexOf("\n", iBeginPos)) != -1) {
                //zk 模块编号和模块名称获取
                String json = JsonValue.substring(iBeginPos, iPos);
                iBeginPos = iPos + 1;

                TpConfigCenter tpConfigCenter = JSON.parseObject(json, TpConfigCenter.class);
                tpConfigCenterMap.put(tpConfigCenter.getEn_name(), tpConfigCenter);
            }

            String json = JsonValue.substring(iBeginPos);
            TpConfigCenter tpConfigCenter = JSON.parseObject(json, TpConfigCenter.class);
            tpConfigCenterMap.put(tpConfigCenter.getEn_name(), tpConfigCenter);

        } catch (KeeperException e) {
            log.error("loadTpConfigCenter KeeperException 215:" + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error("loadTpConfigCenter InterruptedException 147:" + e.getMessage());
            return false;
        }
        return true;
    }

    boolean loadTpModule() {
        moduleMap.clear();
        String JsonValue = null;
        try {
            JsonValue = new String(zookeeper.getData(configTpModule, false, null));
            if (JsonValue == null) {
                log.info(configTpModule + " zookeeper is not exists");
                return false;
            }

            int iBeginPos = 0, iPos = 0;
            while ((iPos = JsonValue.indexOf("\n", iBeginPos)) != -1) {
                //zk 模块编号和模块名称获取
                String json = JsonValue.substring(iBeginPos, iPos);
                iBeginPos = iPos + 1;

                TpModule tpModuleTmp = JSON.parseObject(json, TpModule.class);
                if (tpModuleTmp.getModule_type().equals(MODULE_TYPE.MA.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.MB.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.MC.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.TC.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.PC.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.ME.type) ||
                        tpModuleTmp.getModule_type().equals(MODULE_TYPE.MF.type)) {
                    moduleMap.put(tpModuleTmp.getModule_code(), tpModuleTmp);
                }
            }

            String json = JsonValue.substring(iBeginPos);
            TpModule tpModuleTmp = JSON.parseObject(json, TpModule.class);
            if (tpModuleTmp.getModule_type().equals(MODULE_TYPE.MA.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.MB.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.MC.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.TC.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.PC.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.ME.type) ||
                    tpModuleTmp.getModule_type().equals(MODULE_TYPE.MF.type)) {
                moduleMap.put(tpModuleTmp.getModule_code(), tpModuleTmp);
            }
        } catch (KeeperException e) {
            log.error("loadTpModule KeeperException:" + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        }

        return true;
    }

    boolean loadTpProcess() {
        processMap.clear();
        String JsonValue = null;
        try {
            JsonValue = new String(zookeeper.getData(configTpProcess, false, null));

            int iBeginPos = 0, iPos = 0;
            while ((iPos = JsonValue.indexOf("\n", iBeginPos)) != -1) {
                //zk 模块编号和模块名称获取
                String json = JsonValue.substring(iBeginPos, iPos);
                iBeginPos = iPos + 1;

                TpProcess tpProcess = JSON.parseObject(json, TpProcess.class);
                processMap.put(String.valueOf(tpProcess.getProcess_id()), tpProcess);
            }

            //zk 模块编号和模块名称获取
            String json = JsonValue.substring(iBeginPos);
            TpProcess tpProcess = JSON.parseObject(json, TpProcess.class);
            processMap.put(String.valueOf(tpProcess.getProcess_id()), tpProcess);

        } catch (KeeperException e) {
            log.error("loadTpProcess KeeperException:" + e.getMessage());
            return false;
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        }

        return true;
    }

    /*
     * @decription 默认锁
     * @param path:
     * @return java.lang.String
     * <AUTHOR>
     * @createDate 2022/9/22
     */
    public String lock(String path) {
        return lock(path, 10);
    }

    /**
     * 加锁
     *
     * @param path 加锁路径
     * @return
     * @ 休眠时间不能调整，会影响入库性能。
     */
    public String lock(String path, int sleep) {
        String childPath = path.substring(0, path.lastIndexOf("/"));
        // 创建一个临时序列路径，排队
        String seqEPath = createEphemeralSequentialPath(path, getSystemDateStr());
        if (seqEPath == null) {
            log.error("createEphemeralPath(" + path + ") 失败");
            return null;
        } else {
            Long currSeq = Long.parseLong(seqEPath.substring(seqEPath.lastIndexOf("/") + 1));
            boolean flag = false;
            do {
                flag = false;
                List<String> seqList = getChildPath(childPath);
                for (String seq : seqList) {
                    Long tmp = Long.parseLong(seq.substring(seq.lastIndexOf("/") + 1));
                    // 排队等待，直到排到
                    if (currSeq > tmp) {
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    try {
                        Thread.sleep(sleep);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } while (flag);
        }
        return seqEPath;
    }

    /**
     * 加锁
     *
     * @param path 加锁路径
     * @return
     */
    public String lock0(String path) {
        lockFlag = false;
        String childPath = path.substring(0, path.lastIndexOf("/"));
        String seqEPath = createEphemeralSequentialPath(path, getSystemDateStr());
        if (seqEPath == null) {
            log.error("createEphemeralPath(" + path + ") 失败");
            return null;
        } else {
            Long currSeq = Long.parseLong(seqEPath.substring(seqEPath.lastIndexOf("/") + 1));
            currSeq--;
            String sTmp = childPath + "/" + String.format("%010d", currSeq);
            try {
                Stat stat = zookeeper.exists(sTmp, new LockWatch(sTmp));
                if (stat == null) {
                    lockFlag = true;
                }
            } catch (KeeperException e) {
                e.printStackTrace();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (!lockFlag) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return seqEPath;
    }

    /**
     * 解锁
     *
     * @param path 释放锁路径
     * @return
     */
    public boolean unLock(String path) {
        if (!deletePath(path)) {
            log.error("释放锁失败:" + path);
            return false;
        }
        return true;
    }

    /**
     * 进程启动辅助目录，排队使用
     * 获取锁之后，根据进程配置
     *
     * @return
     */
    public String getBillingLineIdByZkLock() {
        String path = processAssistHome + "/" + moduleCode + "/";
        //获取锁，堵塞
        lockPath = lock(path, 500);
        if (null == lockPath) {
            log.error("获取锁失败:" + path);
            return null;
        }
        tpModule = moduleMap.get(moduleCode);
        String billingLineIdTmp = null;
        List<TpProcess> tpProcessList = new ArrayList<>();
        for (Map.Entry<String, TpProcess> map : processMap.entrySet()) {

            if (map.getValue().getModule_id() == tpModule.getModule_id() && Integer.parseInt(hostID) == map.getValue().getHost_id()) {
                if (processType == null && map.getValue().getType() == null) {
                    tpProcessList.add(map.getValue());
                } else if (processType != null && processType.equals(map.getValue().getType())) {
                    tpProcessList.add(map.getValue());
                }
            }
        }

        boolean flag = false;
        if (tpProcessList.size() > 0) {
            List<TpProcess> sorted = tpProcessList.stream().sorted(Comparator.comparing(TpProcess::getProcess_id)).collect(Collectors.toList());
            log.info("符合启动条件的进程信息：");
            for (TpProcess tmp : sorted) {
                System.out.println(tmp.toString());
            }

            Long lastTime = System.currentTimeMillis();
            do {
                if (isDispatch) {
                    // 调度模块，重节点等待
                    lastTime = System.currentTimeMillis();
                }
                for (TpProcess tpProcessTmp : sorted) {
                    String processTempPath = processHome + "/" + tpProcessTmp.getModule_code() + "/" + tpProcessTmp.getProcess_name();

                    if (0 == iExistPath(processTempPath)) {
                        flag = true;
                        billingLineIdTmp = String.format("%02d", tpProcessTmp.getBilling_line_id());
                        break;
                    }
                }
                if (!flag) {
                    log.info("等待获取有效的进程标识，当前已经等待时长:" + ((System.currentTimeMillis() - lastTime) / 1000) + ",最大等待时长:" + iWaitingProcessExitSecs);
                    try {
                        if (isDispatch) {
                            Thread.sleep(3000);
                        } else {
                            Thread.sleep(1000);
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } while (System.currentTimeMillis() - lastTime < (iWaitingProcessExitSecs * 1000) && !flag);
        }

        if (!flag) {
            //没有找到，释放锁
            unLock(lockPath);
            lockPath = null;
        }
        return billingLineIdTmp;
    }

    // 注册启动，空闲
    public boolean register(boolean work_flag) {
        return register(work_flag, "1");
    }

    // 注册启动，空闲
    public boolean register(boolean work_flag, String isPause) {
        try {
            // 初始化表配置
            if (!loadInitTable()) {
                return false;
            }

            //调度模块目前只支持单点模式
            if (isDispatch) {
                hostID = "00";
                billingLineId = "00";

                /*if ("00".equals(billingLineId)) {
                    billingLineId = getBillingLineIdByZkLock();
                } else if ("11".equals(billingLineId)) {
                    billingLineId = "00";
                    getBillingLineIdByZkLock();
                }*/
            } else {
                hostID = System.getenv("HOST_ID");
                if (hostID == null) {
                    hostID = "11";
                }
                if (processType == null) {
                    processType = System.getenv("PROCESS_TYPE");
                }

                if (billingLineId == null) {
                    // 离线模式，默认11
                    if (work_flag) {
                        billingLineId = "11";
                    } else {
                        billingLineId = getBillingLineIdByZkLock();
                    }
                }
            }
            if (billingLineId == null) {
                if (lockPath != null) {
                    unLock(lockPath);
                    lockPath = null;
                }
                log.error("无法获取有效的实例标识，注册失败；请到dcoos_tp_process表配置实例。");
                return false;
            }

            //模块编号
            if (moduleMap.get(moduleCode) == null) {
                log.info(moduleCode + ",模块在TP_MODULE表中不存在!");
                return false;
            }

            tpModule = moduleMap.get(moduleCode);
            moduleID = String.valueOf(tpModule.getModule_id());

            //进程编号
            processID = tpModule.getModule_id() + hostID + billingLineId;
            if (processMap.get(processID) == null) {
                log.info(moduleCode + "-" + processID + ",进程实例在TP_PROCESS表不存在!");
                return false;
            }
            tpProcess = processMap.get(processID);

            //系统进程编号
            String pid = ManagementFactory.getRuntimeMXBean().getName();
            int indexOf = pid.indexOf('@');
            if (indexOf > 0) {
                systemProcessID = pid.substring(0, indexOf);
            }
            dspchProcessID = "10100000";

            // 离线模式不注册，不创建临时节点
            if (work_flag) {
                return true;
            }

            //创建临时目录树
            tpProcess.setSystem_process_id(Integer.valueOf(systemProcessID));
            tpProcess.setProcess_name(moduleCode + "-" + processID);
            tpProcess.setStatus(PROCESS_STATE.TB.state);
            tpProcess.setIsPause(isPause); //注册完成，进程是挂起状态

            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            tpProcess.setUpdate_date("");
            tpProcess.setCreate_date(dateFormat.format(date));
            tpProcess.setExit_date("");
            String hostNameTmp = System.getenv("HOSTNAME");
            if (hostNameTmp != null) {
                tpProcess.setHost_name(hostNameTmp);
            }

            try {
                // 获取主机ip地址
                InetAddress ip = InetAddress.getLocalHost();
                String ipAddress = ip.getHostAddress();
                if (ipAddress != null) {
                    tpProcess.setHost_ip(ipAddress);
                }
            } catch (UnknownHostException e) {
                log.error(e.getMessage());
            }

            if (version != null) {
                tpProcess.setVersion(version);
            }

            if (StringUtils.isEmpty(tpModule.getVersion())) {
                log.info("未配置模块的计划发布版本，正常启动。");
            } else {
                String realVersion = version.substring(version.lastIndexOf("[") + 1, version.lastIndexOf("]"));
                if (!StringUtils.equals(tpModule.getVersion(), realVersion)) {
                    log.error("[计划发布版本:" + tpModule.getVersion() + "] 和 程序实际启动版本:[" + realVersion + "] 不一致，启动失败。");
                    return false;
                }
                log.info("计划发布版本:[" + tpModule.getVersion() + "] 和 程序实际启动版本:[" + realVersion + "] 一致，校验成功，正常注册。");
            }

            //String processJson = JSON.toJSONString(tpProcess);

            String processTempPath = processHome + "/" + tpProcess.getModule_code() + "/" + tpProcess.getProcess_name();
            sConnectionPath = processHome + "/" + tpProcess.getModule_code() + "/" + tpProcess.getProcess_name();

            boolean flag = false;
            {// 如果 进程还在，最大等待10秒
                Long lastTime = System.currentTimeMillis();
                while (1 == iExistPath(processTempPath) && (System.currentTimeMillis() - lastTime < 10 * 1000)) {
                    if (tpModule.getModule_id() == 1011) {
                        // 要客接收模块，需要一主多从，启动已经有主进程存在，则作为备用节点等待
                        lastTime = System.currentTimeMillis();
                        Thread.sleep(3000);
                        if (!flag) {
                            flag = true;
                            log.info("主节点已经启动，从节点数据同步中");
                        }
                    } else {
                        Thread.sleep(1000);
                        log.info("进程已经存在，等待退出。");
                    }
                }
            }

            for(TpModuleVersion tpModuleVersion:Version.getVersionList()){
                tpModuleVersion.setModuleId(moduleID);
            }

            RegisterJsonValue registerJsonValue = new RegisterJsonValue();
            registerJsonValue.setTpProcess(tpProcess);
            registerJsonValue.setModuleVersions(Version.getVersionList());
            String registerJson = JSON.toJSONString(registerJsonValue);

            // 创建路径
            String sTemp = zookeeper.create(processTempPath, registerJson.getBytes(), ZooDefs.Ids.CREATOR_ALL_ACL, CreateMode.EPHEMERAL);
            if (sTemp == null) {
                log.error(processTempPath + ",zookeeper 目录创建失败");
                return false;
            }

            if (lockPath != null) {
                //注册完，释放锁
                unLock(lockPath);
                lockPath = null;
            }

            log.info("注册成功。version size:" + registerJsonValue.getModuleVersions().size() + ",length:" + registerJson.length());
            log.info(registerJsonValue.getTpProcess().toString());
        } catch (KeeperException e) {
            log.error("register KeeperException:" + e.getMessage());
            log.error("请排查该进程时候已经存在。");
            return false;
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        }

        return true;
    }

    public boolean readyWork() {
        return readyWork(null);
    }

    public boolean readyWork(String version) {
        tpProcess.setIsPause("0");
        if (version != null) {
            tpProcess.setVersion(version);
        }
        //String processJson = JSON.toJSONString(tpProcess);
        String processTempPath = processHome + "/" + tpProcess.getModule_code() + "/" + tpProcess.getProcess_name();

        RegisterJsonValue registerJsonValue = new RegisterJsonValue();
        registerJsonValue.setTpProcess(tpProcess);
        registerJsonValue.setModuleVersions(Version.getVersionList());
        String registerJson = JSON.toJSONString(registerJsonValue);

        //  设置值
        boolean bRet = setData(processTempPath, registerJson);
        if (!bRet) {
            log.error(processTempPath + "," + registerJson);
            log.error("readyWork() 修改进程挂起状态为，正常状态失败");
            return false;
        }

        return true;
    }

    List<ACL> getListAcls() {
        ACL acl = new ACL();
        // 创建Id，也可以设置构造方法传入scheme和id
        Id id = new Id(digest, (userName + ":" + shaBase64Passwd));
        acl.setId(id);
        acl.setPerms(ZooDefs.Perms.ALL);
        List<ACL> acls = new ArrayList<>();
        acls.add(acl);

        return acls;
    }

    public boolean setACL(String path) {
        try {
            // 修改ACL
            Stat setACL = zookeeper.setACL(path, getListAcls(), zookeeper.exists(path, false).getAversion());
        } catch (KeeperException e) {
            e.printStackTrace();
            return false;
        } catch (InterruptedException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public boolean getACL(String path) {
        try {

            // 查看ACL
            List<ACL> acls = zookeeper.getACL(path, zookeeper.exists(path, false));
            for (ACL acl : acls) {
                System.out.print("id:" + acl.getId());
            }
        } catch (KeeperException e) {
            e.printStackTrace();
            return false;
        } catch (InterruptedException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    // 基础表配置初始化
    public boolean loadInitTable() {
        //配置中心记录
        if (!loadTpConfigCenter()) {
            return false;
        }
        //模块表记录
        if (!loadTpModule()) {
            return false;
        }
        //进程表记录
        if (!loadTpProcess()) {
            return false;
        }

        return true;
    }

    /**
     * 把配置数据写入zookeeper
     *
     * @param fileName
     * @param data
     * @return
     */
    public boolean setPropertiesToZk(String fileName, String data) {
        TpConfigCenter tpConfigCenter = tpConfigCenterMap.get(fileName);
        if (tpConfigCenter == null) {
            log.error("配置中心(tp_config_center)不存在:" + fileName);
            return false;
        }

        String zkPath = tpConfigCenter.getPath() + "/" + tpConfigCenter.getEn_name();
        return setData(zkPath, data);
    }

    /**
     * 对外提供查询文件接口
     *
     * @param fileName
     * @return
     */
    public Properties getPropertiesFromZK(String fileName) {
        try {
            TpConfigCenter tpConfigCenter = tpConfigCenterMap.get(fileName);
            if (tpConfigCenter == null) {
                log.error("配置中心(tp_config_center)不存在:" + fileName);
                return null;
            }

            String configJson = getDataByPath(tpConfigCenter.getPath() + "/" + tpConfigCenter.getEn_name());
            Properties prop = new Properties();
            prop.load(new ByteArrayInputStream(configJson.getBytes()));

            return prop;
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * 写List数据到zookeeper
     *
     * @param fileName
     * @param list
     * @param <T>
     * @return
     */
    public <T> boolean setTableToZk(String fileName, List<T> list) {
        TpConfigCenter tpConfigCenter = tpConfigCenterMap.get(fileName);
        if (tpConfigCenter == null) {
            log.error("配置中心(tp_config_center)不存在:" + fileName);
            return false;
        }
        String record = new String();
        for (T t : list) {
            if (!record.equals("")) {
                record += "\n" + JSONObject.toJSONString(t, SerializerFeature.WriteNullStringAsEmpty);
            } else {
                record += JSONObject.toJSONString(t, SerializerFeature.WriteNullStringAsEmpty);
            }
        }
        String zkPath = tpConfigCenter.getPath() + "/" + tpConfigCenter.getEn_name();
        return setData(zkPath, record);
    }

    /**
     * 对外提供查询数据库表接口
     *
     * @param tableName 参数名
     * @param t
     * @param <T>
     * @return
     */
    public <T> List<T> getTableFromZK(String tableName, Class<T> t) {
        List<T> tList = new ArrayList<>();

        TpConfigCenter tpConfigCenter = tpConfigCenterMap.get(tableName);
        if (tpConfigCenter == null) {
            log.error("配置中心(tp_config_center)不存在:" + tableName);
            return null;
        }

        String configJson = getDataByPath(tpConfigCenter.getPath() + "/" + tpConfigCenter.getEn_name());

        if (configJson == null || configJson.equals("")) {
            return tList;
        }

        int iBeginPos = 0, iPos = 0;
        while ((iPos = configJson.indexOf("\n", iBeginPos)) != -1) {
            //zk 模块编号和模块名称获取
            String json = configJson.substring(iBeginPos, iPos);
            iBeginPos = iPos + 1;

            T tRet = JSON.parseObject(json, t);
            tList.add(tRet);
        }

        String json = configJson.substring(iBeginPos);
        T tRet = JSON.parseObject(json, t);
        tList.add(tRet);

        return tList;
    }


    // 修改模块的进程状态为处理中
    public boolean switchModuleState(String state) {

        try {
            TpProcess tpProcess = processMap.get(processID);
            String processTempPath = processHome + "/" + tpProcess.getModule_code() + "/" + tpProcess.getProcess_name();

            byte[] bTemp = zookeeper.getData(processTempPath, false, null);
            if (bTemp == null) {
                log.info("zookeeper.getData目录:" + processTempPath + " ，获取值失败");
                return false;
            }
            String json = new String(bTemp);

            TpProcess processDef = JSON.parseObject(json, TpProcess.class);
            processDef.setStatus(state);

            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd :HH:mm:ss");

            processDef.setUpdate_date(dateFormat.format(date));

            Stat retStat = zookeeper.setData(processTempPath, JSON.toJSONBytes(processDef), -1);
            if (retStat == null) {
                log.info("zookeeper.setData目录:" + processTempPath + " ,设置值失败");
                return false;
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("switchModuleState KeeperException:" + e.getMessage());
            return false;
        }

        return true;
    }

    //客户端使用
    public Map<String, List<RegisterJsonValue>> getModuleChangeEvent(String moduleCode, List<String> moduleList) {
        Map<String, List<RegisterJsonValue>> proListMap = new HashMap<>();
        List<RegisterJsonValue> listProcess = new ArrayList<>();
        String moduleNameTmp = moduleCode;

        if (moduleList.size() == 0) {
            proListMap.put(moduleNameTmp, new ArrayList<>());
            return proListMap;
        }

        try {
            for (String processName : moduleList) {
                String json = new String(zookeeper.getData(processHome + "/" + moduleNameTmp + "/" + processName, false, null));

                //根据/configuration/tp_module的值，判断/process下没有的模块目录，创建
                //TpProcess tpProcess = JSON.parseObject(json, TpProcess.class);
                RegisterJsonValue registerJsonValue = JSON.parseObject(json, RegisterJsonValue.class);

                listProcess.add(registerJsonValue);
            }

            if (listProcess.size() > 0) {
                proListMap.put(moduleNameTmp, listProcess);
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        } catch (KeeperException e) {
            log.error("getModuleChargeEvent KeeperException:" + e.getMessage());
        }

        return proListMap;
    }

    // 调度使用
    public List<TpProcess> getProcessListFromZk() {
        List<TpProcess> list = new ArrayList<>();
        for (Map.Entry<String, TpModule> module : moduleMap.entrySet()) {
            try {
                List<String> listPro = zookeeper.getChildren(processHome + "/" + module.getKey(), false);

                for (String proName : listPro) {
                    String json = new String(zookeeper.getData(processHome + "/" + module.getKey() + "/" + proName, false, null));

                    //根据/config/table/tp_module的值，判断/process下没有的模块目录，创建
                    /*TpProcess tpProcessDef = JSON.parseObject(json, TpProcess.class);
                    list.add(tpProcessDef);*/
                    RegisterJsonValue registerJsonValue = JSON.parseObject(json, RegisterJsonValue.class);
                    list.add(registerJsonValue.getTpProcess());
                }
            } catch (InterruptedException e) {
                log.error(e.getMessage());
            } catch (KeeperException e) {
                log.error("getProcessListFromZk KeeperException:" + e.getMessage());
            }
        }
        return list;
    }


    @Override
    public void process(WatchedEvent event) {
        Event.KeeperState keeperState = event.getState();
        Event.EventType eventType = event.getType();
        String path = event.getPath();

        try {
            if (Event.KeeperState.SyncConnected == keeperState) {
                // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
                // ConnectedReadOnly:连接只读,Disconnected:连接失败
                if (Event.EventType.None == eventType) {
                    // 如果建立建立成功,让后程序往下走
                    log.info("ZkMonitor" + "，zk 建立连接成功!");
                } else if (Event.EventType.NodeCreated == eventType) {
                    log.info("ZkMonitor" + "，事件通知,新增node节点" + path);
                } else if (Event.EventType.NodeDataChanged == eventType) {
                    log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "被修改....");
                } else if (Event.EventType.NodeDeleted == eventType) {
                    log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "被删除....");
                } else if (Event.EventType.NodeChildrenChanged == eventType) {
                    //log.info("ZkMonitor" + "，事件通知,当前node节点" + path + "，子节点被修改....");
                    String moduleCode = path.substring(path.lastIndexOf("/") + 1);

                    //log.info("重新监听:" + processHome + "/" + moduleCode);
                    List<String> gModuleList = zookeeper.getChildren(processHome + "/" + moduleCode, true);

                    Map<String, List<String>> mapTmp = new HashMap<>();
                    mapTmp.put(moduleCode, gModuleList);
                    gChangeModuleList.offer(mapTmp);
                }
            }

        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (KeeperException e) {
            e.printStackTrace();
        }
    }

    public void close() {
        try {
            if (zookeeper != null) {
                zookeeper.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setiMonitorCycleSecs(int iMonitorCycleSecs) {
        this.iMonitorCycleSecs = iMonitorCycleSecs;
    }

    public boolean isDispatch() {
        return isDispatch;
    }

    public void setDispatch(boolean dispatch) {
        isDispatch = dispatch;
    }

    public String getBillingLineId() {
        return billingLineId;
    }

    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getModuleID() {
        return moduleID;
    }

    public String getProcessID() {
        return processID;
    }

    public void setProcessID(String processID) {
        this.processID = processID;
    }

    public String getDspchProcessID() {
        return dspchProcessID;
    }

    public TpModule getTpModule() {
        return tpModule;
    }

    public TpProcess getTpProcess() {
        return tpProcess;
    }

    public String getProcessPath() {
        return processHome;
    }

    public String getProcessAssistHome() {
        return processAssistHome;
    }

    public String getProcessLockHome() {
        return processLockHome;
    }

    public String getUploadHomeConfig() {
        return uploadHomeConfig;
    }

    public boolean setData(String path, String value) {
        try {

            Stat stat = zookeeper.setData(path, value.getBytes(), -1);
            if (stat == null) {
                log.info("setData(" + path + "," + value + ") faile");
                return false;
            }
            return true;
        } catch (KeeperException e) {
            log.error("setData KeeperException:" + e.getMessage());
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
        return false;
    }

    public String getDataByPath(String path) {
        try {
            return new String(zookeeper.getData(path, false, null));
        } catch (InterruptedException e) {
            log.error("getDataByPath InterruptedException:" + e.getMessage());
        } catch (KeeperException e) {
            log.error("getDataByPath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
        }
        return null;
    }

    public boolean setDataByMessageConfig(String value) {
        try {
            Stat stat = zookeeper.setData(messageConfig, value.getBytes(), -1);
            if (stat == null) {
                log.info("setData(" + messageConfig + "," + value + ") faile");
                return false;
            }
            return true;
        } catch (KeeperException e) {
            log.error("setData KeeperException:" + e.getMessage());
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    public List<String> getDataByMessageConfig() {
        errorCode = 0;
        errorMsg = "";
        //  定时
        long startDate = System.currentTimeMillis() / 1000;
        if (lMonitorStartTime != 0 && (startDate - lMonitorStartTime < iMonitorCycleSecs)) {
            return null;
        }
        lMonitorStartTime = startDate;

        try {
            // 不存在创建
            int iRet = iExistPath(messageConfig);
            if (0 == iRet) {
                int start = 0, end = 0;
                String configPath = messageConfig;
                if (configPath.substring(configPath.length() - 1).equals("/")) {
                    configPath = configPath.substring(0, configPath.length() - 1);
                }
                while ((end = configPath.indexOf("/", start + 1)) > 0) {
                    String path = configPath.substring(0, end);
                    if (0 == iExistPath(path)) {
                        createPath(path, "");
                    }
                    start = end;
                }
                createPath(messageConfig, "");
            } else if (iRet == -1) {
                return null;
            }

            List<String> list = new ArrayList<>();
            String configJson = getDataByPath(messageConfig);
            if (configJson == null || configJson.equals("")) {
                return null;
            }

            int iBeginPos = 0, iPos = 0;
            while ((iPos = configJson.indexOf("\n", iBeginPos)) != -1) {
                //zk 模块编号和模块名称获取
                String json = configJson.substring(iBeginPos, iPos);
                iBeginPos = iPos + 1;

                list.add(json);
            }

            String json = configJson.substring(iBeginPos);
            list.add(json);

            //置空
            setData(messageConfig, "");

            return list;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public List<String> getChildPath(String path) {
        try {
            return zookeeper.getChildren(path, false, null);
        } catch (KeeperException e) {
            log.error("getChildPath KeeperException:" + e.getMessage());
            return null;
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public boolean createPath(String path, String value) {
        try {
            // 创建路径
            //zookeeper.create(path, value.getBytes(), ZooDefs.Ids.OPEN_ACL_UNSAFE, CreateMode.PERSISTENT); //无认证

            zookeeper.create(path, value.getBytes(), ZooDefs.Ids.CREATOR_ALL_ACL, CreateMode.PERSISTENT);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("createPath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return false;
        }
        return true;
    }

    public boolean createFullPath(String path) {
        if (0 == iExistPath(path)) {
            int start = 0, end = 0;
            String configPath = path;
            if (configPath.substring(configPath.length() - 1).equals("/")) {
                configPath = configPath.substring(0, configPath.length() - 1);
            }
            while ((end = configPath.indexOf("/", start + 1)) > 0) {
                String pathTmp = configPath.substring(0, end);
                if (0 == iExistPath(pathTmp)) {
                    createPath(pathTmp, "");
                }
                start = end;
            }
            createPath(path, "");
        }
        return true;
    }

    public String createEphemeralPath(String path, String value) {
        try {
            return zookeeper.create(path, value.getBytes(), ZooDefs.Ids.CREATOR_ALL_ACL, CreateMode.EPHEMERAL);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return null;
        } catch (KeeperException e) {
            log.error("createPath createEphemeralPath:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return null;
        }
    }

    public String createEphemeralSequentialPath(String path, String value) {
        try {
            return zookeeper.create(path, value.getBytes(), ZooDefs.Ids.CREATOR_ALL_ACL, CreateMode.EPHEMERAL_SEQUENTIAL);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return null;
        } catch (KeeperException e) {
            log.error("createPath createEphemeralSequentialPath:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return null;
        }
    }

    public boolean deletePath(String path) {
        try {
            // 创建路径
            zookeeper.delete(path, -1);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("deletePath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return false;
        }
        return true;
    }

    public int isConnectionLoss() {
        return isConnectionLoss(sConnectionPath);
    }

    public int isConnectionLoss(String path) {
        //  定时
        long startDate = System.currentTimeMillis() / 1000;
        if (lStartConnectTime != 0 && (startDate - lStartConnectTime < iConnectCycleSecs)) {
            return 10;
        }

        try {
            lStartConnectTime = startDate;
            Stat exists = zookeeper.exists(path, false);
            if (exists == null) {
                return 0;
            }

            // 异常计数清零
            iConnectionLossCnt = 0;
            iConnectionRefusedCnt = 0;
            iSectionExpiredCnt = 0;
            return 1;
        } catch (InterruptedException e) {
            errorCode = ZOOKEEPER_EXCEPTION_INTERRUPTED_EXCEPTION;
            errorMsg = e.getMessage();
            log.error("1269,iExistPath InterruptedException:" + e);
        } catch (KeeperException ke) {
            log.error("1271,KeeperException:" + ke);
            errorMsg = ke.getMessage();
            if (ke.getMessage().indexOf("ConnectionLoss for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
                iConnectionLossCnt++;
            } else if (ke.getMessage().indexOf("Connection refused") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_REFUSED;
                iConnectionRefusedCnt++;
            } else if (ke.getMessage().indexOf("Session expired for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;
                iSectionExpiredCnt++;
            } else {
                errorCode = ZOOKEEPER_EXCEPTION_KEEPER_EXCEPTION;
            }
        }
        return -1;
    }


    public int iExistPath(String path) {
        try {
            Stat exists = zookeeper.exists(path, false);
            if (exists == null) {
                return 0;
            }
            return 1;
        } catch (InterruptedException e) {
            errorCode = ZOOKEEPER_EXCEPTION_INTERRUPTED_EXCEPTION;
            errorMsg = e.getMessage();
            log.error("1318,iExistPath InterruptedException:" + e);
        } catch (KeeperException ke) {
            log.error("1320,KeeperException:" + ke);
            if (ke.getMessage().indexOf("ConnectionLoss for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
                errorMsg = ke.getMessage();
            } else if (ke.getMessage().indexOf("Connection refused") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_REFUSED;
                errorMsg = ke.getMessage();
            } else if (ke.getMessage().indexOf("Session expired for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;
                errorMsg = ke.getMessage();
            }
        }
        return -1;
    }

    public Stat iExistPath(String path, boolean flag) {
        try {
            Stat exists = zookeeper.exists(path, flag);
            return exists;
        } catch (InterruptedException e) {
            errorCode = ZOOKEEPER_EXCEPTION_INTERRUPTED_EXCEPTION;
            errorMsg = e.getMessage();
            log.error("1356,iExistPath InterruptedException:" + e);
        } catch (KeeperException ke) {
            log.error("1358,KeeperException:" + ke);
            if (ke.getMessage().indexOf("ConnectionLoss for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
                errorMsg = ke.getMessage();
            } else if (ke.getMessage().indexOf("Connection refused") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_CONNECTION_REFUSED;
                errorMsg = ke.getMessage();
            } else if (ke.getMessage().indexOf("Session expired for") > -1) {
                errorCode = ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;
                errorMsg = ke.getMessage();
            }
        }
        return null;
    }

    public String getConfigHome() {
        return configHome;
    }

    public String getMessageConfig() {
        return messageConfig;
    }

    public String getMessageProcess() {
        return messageProcess;
    }

    public String getMessageTask() {
        return messageTask;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public static Map<String, List<String>> pollgChangeModuleList() {
        return gChangeModuleList.poll();
    }

    public boolean loadCommonProperties(Common common) {
        Properties p = getPropertiesFromZK("Common.properties");
        if (p != null) {
            if (moduleID != null && (!"".equals(moduleID))) {
                common.setModule_id(Integer.parseInt(moduleID));
                common.setModuleCode(tpModule.getModule_code());
            }
            if (processID != null && (!"".equals(processID))) {
                common.setProcess_id(Integer.parseInt(processID));
            }
            if (null != p.getProperty("system")) {
                common.setSSystem(p.getProperty("system"));
            }
            if (null != p.getProperty("latn_id")) {
                common.setSLatnId(p.getProperty("latn_id"));
            }
            if (null != p.getProperty("deployment")) {
                common.setDeployment(p.getProperty("deployment"));
            }
            if (null != p.getProperty("modulo")) {
                common.setModulo(Integer.parseInt(p.getProperty("modulo")));
            }
            if (null != p.getProperty("taskCnt")) {
                common.setTaskCnt(Integer.parseInt(p.getProperty("taskCnt")));
            }
            if (null != p.getProperty("mqOnceCnt")) {
                common.setMqOnceCnt(Integer.parseInt(p.getProperty("mqOnceCnt")));
            }
            if (null != p.getProperty("record_transfer_mode")) {
                common.setRecordTransferMode(p.getProperty("record_transfer_mode"));
            }
            if (null != p.getProperty("task_transfer_mode")) {
                common.setTaskTransferMode(p.getProperty("task_transfer_mode"));
            }
            if (null != p.getProperty("time_out_seconds")) {
                common.setLTimeOutSeconds(Long.parseLong(p.getProperty("time_out_seconds")) * 1000);
            }
            if (null != p.getProperty("perl_out_cnt")) {
                common.setIPerlOutCnt(Integer.parseInt(p.getProperty("perl_out_cnt")));
            }
            if (null != p.getProperty("perl_low")) {
                common.setIPerlLow(Integer.parseInt(p.getProperty("perl_low")));
            }
            if (null != p.getProperty("dbIncept_mode")) {
                common.setIDbIncept_mode(Integer.parseInt(p.getProperty("dbIncept_mode")));
            }
            if (null != p.getProperty("jar_class")) {
                common.setJarClass(p.getProperty("jar_class"));
            }
            if (null != p.getProperty("jar_path")) {
                common.setJarPath(p.getProperty("jar_path"));
            }
            if (null != p.getProperty("td_process_rollback_seconds")) {
                common.setLTDProcessExitRollbackSeconds(Long.parseLong(p.getProperty("td_process_rollback_seconds")));
            }
            if ("true".equals(p.getProperty("is_exists_error_2_fcl"))) {    //任务出现错单，是否设置任务失败FCL
                common.setExistsError2FCL(true);
            }
        }

        if (common.getTaskTransferMode().equals("CTG_MQ") || common.getRecordTransferMode().equals("CTG_MQ")) {
            List<TpProcessMQ> list = getTableFromZK("tp_process_mq", TpProcessMQ.class);
            if (list == null) {
                log.error("tp_config_center未配置tp_process_mq表，或者未上载成功");
                return false;
            }

            Map<String, TpProcessMQ> map = new HashMap<>();
            for (TpProcessMQ tpProcessMQ : list) {
                map.put(String.valueOf(tpProcessMQ.getProcess_id()), tpProcessMQ);
            }
            common.setProcessMQMap(map);
        }

        return true;
    }

    public int getiConnectionLossCnt() {
        return iConnectionLossCnt;
    }

    public int getiSectionExpiredCnt() {
        return iSectionExpiredCnt;
    }

    public int getiConnectionRefusedCnt() {
        return iConnectionRefusedCnt;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDbmonitorInstances() {
        return dbmonitorInstances;
    }

    public String getDbmonitorDatabase() {
        return dbmonitorDatabase;
    }

    public boolean isDruidPropertiesEnable() {
        return druidPropertiesEnable;
    }

    public boolean isDynamicDatasourceEnable() {
        return dynamicDatasourceEnable;
    }

    public void toShow() {
        for (Map.Entry<String, TpConfigCenter> itertor : tpConfigCenterMap.entrySet()) {
            log.info(itertor.getValue().toString());
        }

        for (Map.Entry<String, TpModule> itertor : moduleMap.entrySet()) {
            log.info(itertor.getValue().toString());
        }
        for (Map.Entry<String, TpProcess> itertor : processMap.entrySet()) {
            log.info(itertor.getValue().toString());
        }
    }

    public void getData(String path, boolean watch) {
        try {
            zookeeper.getData(path, watch, null);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (KeeperException e) {
            e.printStackTrace();
        }
        return;
    }

    public enum PROCESS_STATE {
        TA(1, "TA", "关闭"),
        TB(2, "TB", "空闲"),
        TC(3, "TC", "处理中"),
        TD(4, "TD", "异常关闭"),
        TE(5, "TE", "已分配任务");

        int order_id;
        String state;
        String remark;

        public String getState() {
            return state;
        }

        PROCESS_STATE(int order_id, String state, String remark) {
            this.order_id = order_id;
            this.state = state;
            this.remark = remark;
        }
    }

    public enum MODULE_TYPE {
        MA(1, "MA", "调度"),
        MB(2, "MB", "采集"),
        MC(3, "MC", "话单处理模块"),
        MD(4, "MD", "账务模块"),
        ME(5, "ME", "传输"),
        MF(6, "MF", "工具"),
        TC(7, "TC", "试算话单处理"),
        PC(8, "PC", "预计费话单处理");

        int order_id;
        String type;
        String remark;

        MODULE_TYPE(int order_id, String type, String remark) {
            this.order_id = order_id;
            this.type = type;
            this.remark = remark;
        }

        public String getType() {
            return type;
        }
    }

    public enum START_MODE {
        NORMAL(1, "NORMAL", "生产方式启动"),
        TRIAL(2, "TRIAL", "试算方式启动"),
        PRE(3, "PRE", "预出账方式启动");

        int id;
        String mode;
        String remark;

        START_MODE(int id, String mode, String remark) {
            this.id = id;
            this.mode = mode;
            this.remark = remark;
        }

        public String getMode() {
            return mode;
        }
    }


    class LockWatch implements Watcher {
        String path = null;

        LockWatch(String path) {
            this.path = path;
        }

        @Override
        public void process(WatchedEvent event) {
            Event.KeeperState keeperState = event.getState();
            Event.EventType eventType = event.getType();
            String pathTmp = event.getPath();

            if (Event.KeeperState.SyncConnected == keeperState) {
                // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
                // ConnectedReadOnly:连接只读,Disconnected:连接失败
                if (Event.EventType.None == eventType) {
                    // 如果建立建立成功,让后程序往下走
                    log.info("LockWatch" + "，zk 建立连接成功!");
                } else if (Event.EventType.NodeCreated == eventType) {
                    log.info("LockWatch" + "，事件通知,新增node节点" + pathTmp);
                } else if (Event.EventType.NodeDataChanged == eventType) {
                    log.info("LockWatch" + "，事件通知,当前node节点" + pathTmp + "被修改....");
                } else if (Event.EventType.NodeDeleted == eventType) {
                    //log.info("LockWatch" + "，事件通知,当前node节点" + pathTmp + "被删除....");
                    if (pathTmp.equals(path)) {
                        lockFlag = true;
                    }
                } else if (Event.EventType.NodeChildrenChanged == eventType) {
                    log.info("LockWatch" + "，事件通知,当前node节点" + pathTmp + "，子节点被修改....");
                }
            }
        }
    }
}

