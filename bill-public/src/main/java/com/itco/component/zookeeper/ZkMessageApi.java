package com.itco.component.zookeeper;

import com.itco.component.jdbc.DecodePassword;
import com.itco.entity.common.TpModule;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.zookeeper.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class ZkMessageApi {
    private static Log log = LogFactory.getLog(ZkMessageApi.class);
    private String zkProperties = "/zk.properties";
    private String BILL_PUBLIC_VERSION = "bill-public-2.0.1.jar";

    Map<String, String> map = new HashMap<>();

    // zookeeper 连接配置参数
    String ipAddress;
    int sessionTimeoutMs;
    static String userName;
    static String password;
    static String shaBase64Passwd;

    String moduleCode;//模块英文名称，继承类传入
    String messageTask = null;//使用ZK收发任务消息路径

    TpModule tpModule;//通过模块名称，找到
    String processID;//进程实例标识
    String billingLineId = "00";//实例编号

    Map<String, TpModule> moduleIdMap = new HashMap<>();// moduleIdMap.put(module_id,tpModuleTmp);

    static volatile ZooKeeper zookeeper = null;//zk对象

    int errorCode = 0;
    String errorMsg = new String();

    String digest = "digest";


    public void setModuleIdMap(List<TpModule> moduleList) {
        for (TpModule tpModule : moduleList) {
            moduleIdMap.put(String.valueOf(tpModule.getModule_id()), tpModule);
        }
    }

    /**
     * upload初始化
     *
     * @return true:成功，false:失败
     */
    public boolean init(Watcher watcher) {
        log.info("公共库版本: " + BILL_PUBLIC_VERSION);

        try {
            // linux 主机获取zk.properties
            Properties props = new Properties();
            Map<String, String> env = System.getenv();
            String configPath = env.get("CONFIG");
            if (configPath == null || configPath.equals("")) {
                // windows 使用资源目录下的zk.properties ideal直接测试代码
                Resource resource = new ClassPathResource(zkProperties);
                if (resource == null) {
                    log.error("缺少资源文件:" + zkProperties);
                    return false;
                }
                props = PropertiesLoaderUtils.loadProperties(resource);
                log.info("资源文件:" + zkProperties);
            } else {
                //  linux主机使用环境变量CONFIG路径下的zk.properties
                BufferedReader bufferedReader = new BufferedReader(new FileReader(configPath + zkProperties));
                if (bufferedReader == null) {
                    log.error("配置文件不存在:" + configPath + zkProperties);
                    return false;
                }
                props.load(bufferedReader);
                log.info("配置文件:" + configPath + zkProperties);
            }

            userName = props.getProperty("process.zk.param.userName");
            shaBase64Passwd = props.getProperty("process.zk.param.shaBase64Passwd");
            String publicKeyTmp = props.getProperty("process.zk.param.publicKey");
            String passwordTmp = props.getProperty("process.zk.param.password");
            password = DecodePassword.decryption(publicKeyTmp, passwordTmp);

            log.info("userName:" + userName + ", password:" + password + ", shaBase64Passwd:" + shaBase64Passwd);

            ipAddress = props.getProperty("process.zk.param.ip");
            sessionTimeoutMs = Integer.valueOf(props.getProperty("process.zk.param.sessionTimeoutMs"));

            if (props.getProperty("message.task") != null) {
                messageTask = props.getProperty("message.task");
            }

            log.info("zookeeper 地址:" + ipAddress);
        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }

        try {
            zookeeper = new ZooKeeper(ipAddress, sessionTimeoutMs, watcher);

            zookeeper.addAuthInfo(digest, new String(userName + ":" + password).getBytes());
        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }
        return true;
    }

    public boolean startMessageMonitor() {
        // 监听自己进程目录，获取消息
        try {
            String path = messageTask + "/" + moduleCode + "-" + processID;
            //log.info("重新监听:" + path);
            // 监听自己进程目录，获取消息
            zookeeper.getChildren(path, true);
        } catch (KeeperException e) {
            e.printStackTrace();
            return false;
        } catch (InterruptedException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public void close() {
        try {
            zookeeper.close();
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

    public void setProcessID(String processID) {
        this.processID = processID;
    }

    public String getProcessID() {
        return processID;
    }

    public TpModule getTpModule() {
        return tpModule;
    }

    public TpModule getTpModuleByProcessID(String processID) {
        TpModule tpModule = moduleIdMap.get(processID.substring(0, 4));
        return tpModule;
    }

    public String getProcessPath() {
        return messageTask;
    }

    public String getDataByPath(String path) {
        try {
            return new String(zookeeper.getData(path, false, null));
        } catch (InterruptedException e) {
            log.error("getDataByPath InterruptedException:" + e.getMessage());
        } catch (KeeperException e) {
            log.error("getDataByPath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
        }
        return null;
    }

    public List<String> getChildPath(String path) {
        try {
            return zookeeper.getChildren(path, false, null);
        } catch (KeeperException e) {
            log.error("getChildPath KeeperException:" + e.getMessage());
            return null;
        } catch (InterruptedException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public boolean createPath(String path, String value) {
        try {
            // 创建路径
            //zookeeper.create(path, value.getBytes(), ZooDefs.Ids.OPEN_ACL_UNSAFE, CreateMode.PERSISTENT); //无认证

            zookeeper.create(path, value.getBytes(), ZooDefs.Ids.CREATOR_ALL_ACL, CreateMode.PERSISTENT_SEQUENTIAL);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("createPath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return false;
        }
        return true;
    }

    public boolean deletePath(String path) {
        try {
            // 创建路径
            zookeeper.delete(path, -1);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            return false;
        } catch (KeeperException e) {
            log.error("deletePath KeeperException:" + e.getMessage());
            errorCode = -1;
            errorMsg = e.getMessage();
            return false;
        }
        return true;
    }

    public String getMessageTask() {
        return messageTask;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

}

