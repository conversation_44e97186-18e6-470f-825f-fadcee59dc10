package com.itco.component.ctgmq;

import com.alibaba.fastjson.JSONObject;
import com.ctg.mq.api.CTGMQFactory;
import com.ctg.mq.api.IMQProducer;
import com.ctg.mq.api.IMQPullConsumer;
import com.ctg.mq.api.PropertyKeyConst;
import com.ctg.mq.api.bean.ConsumeStatus;
import com.ctg.mq.api.bean.MQMessage;
import com.ctg.mq.api.bean.MQResult;
import com.ctg.mq.api.enums.MQConsumeFromWhere;
import com.ctg.mq.api.exception.MQException;
import com.ctg.mq.api.exception.MQProducerException;
import com.itco.entity.common.Common;
import com.itco.component.jdbc.DecodePassword;
import com.itco.entity.common.TpProcessMQ;
import com.itco.component.zookeeper.ZkClientApi;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Properties;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * @ClassName: CtgMqPullApi
 * @Description: ctgmq pull 方式消费
 * <AUTHOR>
 * @Date 2021/7/26
 * @Version 1.0
 */
@Data
public class CtgMqPullApi {
    Log log = LogFactory.getLog(CtgMqPullApi.class);
    private String propertiesCtgMq = "ctgMq.properties";

    String sendRecordTopicName = null;
    String recRecordTopicName = null;
    String recRecordConsumerGroup = null;

    IMQProducer imqProducer = null;
    IMQPullConsumer imqPullConsumer = null;

    ZkClientApi zkClientApi = null;
    Common common = null;
    TpProcessMQ tpProcessMQ = null;

    volatile LinkedBlockingQueue<String> g_RecordQueue = new LinkedBlockingQueue<String>();
    volatile LinkedBlockingQueue<String> g_ReceProQueue = new LinkedBlockingQueue<String>();

    public boolean init() {
        tpProcessMQ = common.getProcessMQMap().get(zkClientApi.getProcessID());
        if (tpProcessMQ == null) {
            log.error("tp_process_mq表未配置，" + zkClientApi.getProcessID() + " 的MQ队列");
            return false;
        }
        sendRecordTopicName = tpProcessMQ.getRecord_producer_topic();
        recRecordTopicName = tpProcessMQ.getRecord_consumer_topic();
        recRecordConsumerGroup = tpProcessMQ.getRecord_consumer_group();
        log.info("sendRecordTopicName:" + sendRecordTopicName);
        log.info("recRecordTopicName:" + recRecordTopicName);
        log.info("recRecordConsumerGroup:" + recRecordConsumerGroup);

        Properties ctgProperties = zkClientApi.getPropertiesFromZK(propertiesCtgMq);
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName, ctgProperties.getProperty("producerGroupRecordName"));
        properties.setProperty(PropertyKeyConst.InstanceName, ctgProperties.getProperty("instanceName"));
        properties.setProperty(PropertyKeyConst.NamesrvAddr, ctgProperties.getProperty("namesrvAddr"));
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, ctgProperties.getProperty("authId"));

        String publicKeyTmp = ctgProperties.getProperty("publicKey");
        String passwordTmp = ctgProperties.getProperty("password");
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, passwd);
        //properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, ctgProperties.getProperty("authPwd"));
        properties.setProperty(PropertyKeyConst.ClusterName, ctgProperties.getProperty("clusterName"));
        properties.setProperty(PropertyKeyConst.TenantID, ctgProperties.getProperty("tenantID"));

        log.info(ctgProperties.getProperty("authId"));
        int result = -1;
        // 创建生产者
        imqProducer = CTGMQFactory.createProducer(properties);

        try {
            result = imqProducer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("producer.connect return: " + result);
            return false;
        }

        properties.setProperty(PropertyKeyConst.ConsumeFromWhere, MQConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET.name());
        properties.setProperty(PropertyKeyConst.ConsumerGroupName, recRecordConsumerGroup);

        // 创建消费者
        imqPullConsumer = CTGMQFactory.createPullConsumer(properties);

        try {
            result = imqPullConsumer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("imqConsumer.connect return: " + result);
            return false;
        }

        return true;
    }

    public boolean initDbIncept() {
        tpProcessMQ = common.getProcessMQMap().get(zkClientApi.getProcessID());
        if (tpProcessMQ == null) {
            log.error("tp_process_mq表未配置，" + zkClientApi.getProcessID() + " 的MQ队列");
            return false;
        }

        sendRecordTopicName = tpProcessMQ.getRecord_producer_topic();
        recRecordTopicName = tpProcessMQ.getRecord_consumer_topic();
        recRecordConsumerGroup = tpProcessMQ.getRecord_consumer_group();
        log.info("sendRecordTopicName:" + sendRecordTopicName);
        log.info("recRecordTopicName:" + recRecordTopicName);
        log.info("recRecordConsumerGroup:" + recRecordConsumerGroup);

        Properties ctgProperties = zkClientApi.getPropertiesFromZK(propertiesCtgMq);
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName, ctgProperties.getProperty("producerGroupRecordName"));
        properties.setProperty(PropertyKeyConst.InstanceName, ctgProperties.getProperty("instanceName"));
        properties.setProperty(PropertyKeyConst.NamesrvAddr, ctgProperties.getProperty("namesrvAddr"));
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, ctgProperties.getProperty("authId"));

        String publicKeyTmp = ctgProperties.getProperty("publicKey");
        String passwordTmp = ctgProperties.getProperty("password");
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, passwd);
        //properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, ctgProperties.getProperty("authPwd"));
        properties.setProperty(PropertyKeyConst.ClusterName, ctgProperties.getProperty("clusterName"));
        properties.setProperty(PropertyKeyConst.TenantID, ctgProperties.getProperty("tenantID"));

        int result = -1;
        // 创建生产者
        imqProducer = CTGMQFactory.createProducer(properties);

        try {
            result = imqProducer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("producer.connect return: " + result);
            return false;
        }

        return true;
    }

    public boolean initCmdClientClear(String topic, String consumer) {
        Properties ctgProperties = zkClientApi.getPropertiesFromZK(propertiesCtgMq);
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName, ctgProperties.getProperty("producerGroupRecordName"));
        properties.setProperty(PropertyKeyConst.InstanceName, ctgProperties.getProperty("instanceName"));
        properties.setProperty(PropertyKeyConst.NamesrvAddr, ctgProperties.getProperty("namesrvAddr"));
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, ctgProperties.getProperty("authId"));

        String publicKeyTmp = ctgProperties.getProperty("publicKey");
        String passwordTmp = ctgProperties.getProperty("password");
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, passwd);
        //properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, ctgProperties.getProperty("authPwd"));
        properties.setProperty(PropertyKeyConst.ClusterName, ctgProperties.getProperty("clusterName"));
        properties.setProperty(PropertyKeyConst.TenantID, ctgProperties.getProperty("tenantID"));

        int result = -1;
        // 创建生产者
        imqProducer = CTGMQFactory.createProducer(properties);

        try {
            result = imqProducer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("producer.connect return: " + result);
            return false;
        }

        properties.setProperty(PropertyKeyConst.ConsumeFromWhere, MQConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET.name());
        properties.setProperty(PropertyKeyConst.ConsumerGroupName, consumer);
        // 创建消费者
        imqPullConsumer = CTGMQFactory.createPullConsumer(properties);

        try {
            result = imqPullConsumer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("imqConsumer.connect return: " + result);
            return false;
        }

        recRecordTopicName = topic;
        log.info("recRecordTopicName:" + recRecordTopicName);
        log.info("ConsumerGroupName:" + consumer);

        return true;
    }

    public boolean initCmdClientSimulator() {
        tpProcessMQ = common.getProcessMQMap().get(zkClientApi.getProcessID());
        if (tpProcessMQ == null) {
            log.error("tp_process_mq表未配置，" + zkClientApi.getProcessID() + " 的MQ队列");
            return false;
        }

        sendRecordTopicName = tpProcessMQ.getRecord_producer_topic();
        log.info("sendRecordTopicName:" + sendRecordTopicName);

        Properties ctgProperties = zkClientApi.getPropertiesFromZK(propertiesCtgMq);
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName, ctgProperties.getProperty("producerGroupRecordName"));
        properties.setProperty(PropertyKeyConst.InstanceName, ctgProperties.getProperty("instanceName"));
        properties.setProperty(PropertyKeyConst.NamesrvAddr, ctgProperties.getProperty("namesrvAddr"));
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, ctgProperties.getProperty("authId"));

        String publicKeyTmp = ctgProperties.getProperty("publicKey");
        String passwordTmp = ctgProperties.getProperty("password");
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, passwd);
        //properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, ctgProperties.getProperty("authPwd"));
        properties.setProperty(PropertyKeyConst.ClusterName, ctgProperties.getProperty("clusterName"));
        properties.setProperty(PropertyKeyConst.TenantID, ctgProperties.getProperty("tenantID"));

        int result = -1;
        // 创建生产者
        imqProducer = CTGMQFactory.createProducer(properties);

        try {
            result = imqProducer.connect();
        } catch (MQException e) {
            log.error("connect faile MQException" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("producer.connect return: " + result);
            return false;
        }

        return true;
    }

    public boolean sendRecords(List<String> records) {
        String rec = JSONObject.toJSONString(records);
        MQMessage mqMessage = new MQMessage(sendRecordTopicName, "", "", rec.getBytes());
        try {
            imqProducer.send(mqMessage);
        } catch (MQProducerException e) {
            log.error("sendRecords() faile" + e.getMessage());
            return false;
        }
        return true;
    }

    public boolean recviceRecords(List<MQResult> records) {
        try {
            List<MQResult> mqResultList = null;
            do {
                mqResultList = imqPullConsumer.consumeMessagesByTopic(
                        //topic名称
                        recRecordTopicName,
                        //过滤条件
                        "",
                        //一次拉取数量，最大32条
                        32,
                        //拉取超时时间
                        3000);
                for (int i = 0; i < mqResultList.size(); i++) {
                    records.add(mqResultList.get(i));
                }
            } while (records.size() < common.getTaskCnt() && mqResultList.size() > 0);
        } catch (MQException e) {
            log.error("recviceRecords() faile :" + e.getMessage());
            return false;
        }
        return true;
    }

    public boolean ackRecordMessages(List<MQResult> results) {
        try {
            imqPullConsumer.ackMessages(results, ConsumeStatus.CONSUME_SUCCESS);
        } catch (MQException e) {
            log.error("ackRecordMessages() faile ," + e.getMessage());
            return false;
        }

        return true;
    }

    public void close() {
        try {
            if (imqProducer != null) {
                imqProducer.close();
            }
            if (imqPullConsumer != null) {
                imqPullConsumer.close();
            }
        } catch (MQException e) {
            log.error(e.getMessage());
        }
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void setCommon(Common common) {
        this.common = common;
    }
}
