//
//package com.itco.util.signal;
//
//import org.apache.commons.logging.Log;
//import org.apache.commons.logging.LogFactory;
//import sun.misc.Signal;
//import sun.misc.SignalHandler;
//
//
///**
// * @ClassName: HandleSignal
// * @Description: HandleSignal
// * <AUTHOR>
// * @Date 2022/5/10
// * @Version 1.0
// */
//
//public class HandleSignal implements SignalHandler {
//    static Log log = LogFactory.getLog(HandleSignal.class);
//
//    static boolean bWhileFlag = false;
//    static boolean bWhileMsgFlag = false;
//
//    private void signalCallback(Signal signal) {
//
//        // 信号量名称
//        String name = signal.getName();
//        // 信号量数值
//        int number = signal.getNumber();
//        // 当前进程名
//        String currentThreadName = Thread.currentThread().getName();
//        log.info("[Thread:" + currentThreadName + "] receved signal: " + name + " == kill -" + number);
//
//    }
//
//    @Override
//    public void handle(Signal signalName) {
//        signalCallback(signalName);
//
//        log.info("HandleSignal handle");
//        log.info("HandleSignal 关闭程序循环开关");
//        bWhileFlag = true;
//
//    }
//
//    public static boolean isbWhileFlag() {
//        return bWhileFlag;
//    }
//
//    public static void setbWhileFlag(boolean bWhileFlag) {
//        HandleSignal.bWhileFlag = bWhileFlag;
//    }
//
//    public static boolean isbWhileMsgFlag() {
//        return bWhileMsgFlag;
//    }
//
//    public static void setbWhileMsgFlag(boolean bWhileMsgFlag) {
//        HandleSignal.bWhileMsgFlag = bWhileMsgFlag;
//    }
//
//    public static void seteExit(boolean bWhileFlag, boolean bWhileMsgFlag) {
//        HandleSignal.bWhileFlag = bWhileFlag;
//        HandleSignal.bWhileMsgFlag = bWhileMsgFlag;
//    }
//}
