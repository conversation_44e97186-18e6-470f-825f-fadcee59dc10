package com.itco.framework;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class Factory {
    static Log log = LogFactory.getLog(Factory.class);

    static boolean bWhileFlag = false;
    static boolean bWhileMsgFlag = false;

    static int module_id = 1013;
    static int process_id = 10131111;

    static ConfigurableApplicationContext context = null;

    // 退出之前需要关闭容器
    public static synchronized void close() {
        log.info("main exit,SpringApplication.exit(context) begin");
        if (context != null) {
            int exit = SpringApplication.exit(context);
            context = null;
            System.exit(exit);
        }
        log.info("main exit,SpringApplication.exit(context) end");
    }

    public static synchronized String getSystemDateStr() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String dateStr = df.format(new Date(System.currentTimeMillis()));// new Date()为获取当前系统时间，也可使用当前时间戳
        return dateStr;
    }

    public static synchronized String getSystemDateStr(String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);//设置日期格式
        String dateStr = df.format(new Date(System.currentTimeMillis()));// new Date()为获取当前系统时间，也可使用当前时间戳

        return dateStr;
    }

    public static Long getMicroSecond() {
        return System.nanoTime() / 1000;
    }

    // 设置异常单，并填写错误信息
    public static boolean setTicketTypeAbn(Map<String, Object> record, String msg) {
        return setTicketType(record, 1, null, msg);
    }

    // 设置异常单，并填写错误信息
    public static boolean setTicketTypeAbn(Map<String, Object> record, Integer error_type, String msg) {
        return setTicketType(record, 1, error_type, msg);
    }

    // 设置不计费话单，并填写错误信息
    public static boolean setTicketTypeOther(Map<String, Object> record, String msg) {
        return setTicketType(record, 2, null, msg);
    }

    // 设置不计费话单，并填写错误信息
    public static boolean setTicketTypeOther(Map<String, Object> record, Integer error_type, String msg) {
        return setTicketType(record, 2, error_type, msg);
    }

    // 设置异常单，并填写错误信息
    public static boolean setErrorMsg(Map<String, Object> record, String msg) {
        return setTicketType(record, null, null, msg);
    }

    // 设置异常单，并填写错误信息
    public static boolean setTicketType(Map<String, Object> record, Integer ticket_type, Integer error_type, String msg) {
        if (error_type != null) {
            record.put("error_type", error_type);
        }

        if (ticket_type != null) {
            record.put("ticket_type", ticket_type);
        }

        String errorMsg = ";" + module_id + "," + ticket_type + "," + error_type + "," + msg + ";";
        Object s = record.get("error_msg");
        if (s == null) {
            record.put("error_msg", errorMsg);
        } else {
            record.put("error_msg", s.toString() + errorMsg);
        }
        return true;
    }


    public static int getModule_id() {
        return module_id;
    }

    public static void setModule_id(int module_id) {
        Factory.module_id = module_id;
    }

    public static int getProcess_id() {
        return process_id;
    }

    public static void setProcess_id(int process_id) {
        Factory.process_id = process_id;
    }

    public static ConfigurableApplicationContext getContext() {
        return context;
    }

    public static void setContext(ConfigurableApplicationContext context) {
        Factory.context = context;
    }

    public static void seteExit(boolean eExit, boolean eMsgExit) {
        Factory.bWhileFlag = eExit;
        Factory.bWhileMsgFlag = eMsgExit;
    }

    public static boolean isbWhileMsgFlag() {
        return bWhileMsgFlag;
    }

    public static void setbWhileMsgFlag(boolean bWhileMsgFlag) {
        Factory.bWhileMsgFlag = bWhileMsgFlag;
    }

    public static void setbWhileFlag(boolean b) {
        bWhileFlag = b;
    }

    public static boolean isbWhileFlag() {
        return bWhileFlag;
    }

    // 异常信息入日志文件
    public static String printStackTraceToString(Exception e) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(byteArrayOutputStream));
        if (byteArrayOutputStream != null) {
            String exception = byteArrayOutputStream.toString();
            /*if (exception.length() <= 800) {
                return exception;
            } else {
                return exception.substring(0, 800);
            }*/
            return exception;
        }
        return null;
    }

//    // 进程注销信号量注册
//    static HandleSignal handleSignal = new HandleSignal();
//    kill pid
//    public static void signalHandle() {
//        Signal.handle(new Signal("TERM"), handleSignal);
//        //Signal.handle(new Signal("QUIT"), handleSignal);
//        Signal.handle(new Signal("USR2"), handleSignal);
//        Signal.handle(new Signal("INT"), handleSignal);
//    }
}
