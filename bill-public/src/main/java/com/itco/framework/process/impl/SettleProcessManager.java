package com.itco.framework.process.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.entity.process.E_RecordTransferMode;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.framework.log.LogLevelSpring;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.process.Process;
import com.itco.framework.record.impl.settle.TransferSettle;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Executors;

import static com.itco.framework.Factory.getSystemDateStr;
import static com.itco.framework.Factory.printStackTraceToString;

public class SettleProcessManager extends Process {
    static Log log = LogFactory.getLog(SettleProcessManager.class);

    TransferSettle transfer = null;  // 文件流转类(离线模式) 、 ctgMq流转类 、数据库表

    Class<? extends TransferSettle> clazzTransferSettle = null;

    int iCurrCnt = 0;

    Long lStartTime = null;
    Long lRedoRuleTime = null;

    int iReloadCnt = 0;

    @Override
    public boolean Init() {
        // 父类初始化
        if (!super.Init()) {
            log.error("Process.init() 失败");
            return false;
        }

        // 初始化读取数据接口类
        if (!syncPropertiesAndCalcRecord(new MsgBody.ConfigSyncBody())) {
            log.error("super.syncPropertiesAndCalcRecord() faile");
            return false;
        }

        // 初始化消息队列
        if (!super.LoadTaskManager()) {
            log.error("super.LoadTaskManager() faile");
            return false;
        }

        // 离线模式不进行任务接收
        if (!common.isWorkFlag()) {
            // 进程申请接收任务。
            if (!super.processReadyWork()) {
                log.error(process_id + " ,进程准备接收任务失败。");
                return false;
            }
            log.info(process_id + " ,进程准备接收任务成功。");
        }

        DbPool.setZkClientApi(zkClientApi);
        if (!DbPool.init()) {
            log.error("DbPool.init() faile,数据库连接失败！");
            return false;
        }
        log.info("SettleProcess.init() moduleCode:" + moduleCode + ",calcClassName:" + calcClassName + ",transferClassName:" + transferClassName + ",billingLineId:" + billingLineId);
        lStartTime = System.currentTimeMillis();
        lRedoRuleTime = System.currentTimeMillis();

        // kill pid
        // Factory.signalHandle();
        return true;
    }

    boolean loadTransfer() {
        if (transfer != null) {
            transfer.close();
            transfer = null;
        }

        transfer = transferFactory.getTransferSettleInstance(clazzTransferSettle, transferClassName, common.getRecordTransferMode());
        if (transfer == null) {
            log.info("transferFactory.getTransferSettleInstance() faile");
            return false;
        }

        // 初始化话单读写取类
        transfer.setZkClientApi(zkClientApi);
        transfer.setCommon(common);
        if (!transfer.init()) {
            log.error("transfer.init(p) 失败");
            return false;
        }

        return true;
    }

    // 初始化线程池
    boolean loadCalcRecordManager(String LOAD_ID) {
        CalcRecordManager.setbInitFlag(false);
        for (CalcRecordManager calc : calcList) {
            calc.OnExit();
            CalcRecordManager.setbInitFlag(true);
        }
        calcList.clear();//实例化处理类，要清理

        // 初始化线程池数量
        fixedThreadPool = Executors.newFixedThreadPool(common.getIMaxProcessCnt());
        CalcRecordManager.setbInitFlag(false);
        CalcRecordManager.setCommon(common);
        CalcRecordManager.setZkClientApi(zkClientApi);

        for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
            CalcRecordManager calcRecordManager = calcRecordFactory.getCalcInstance(clazzCalcRecordManager, calcClassName);
            if (null == calcRecordManager) {
                log.error("话单处理类实例化失败：" + clazzCalcRecordManager.getName());
                return false;
            }

            calcRecordManager.setName("处理线程【" + (i + 1) + "】");
            calcRecordManager.setThreadNum(i);
            if (LOAD_ID == null || "".equals(LOAD_ID)) {
                calcRecordManager.setLoadId(0);
            } else if (StringUtils.isNumber(LOAD_ID)) {
                calcRecordManager.setLoadId(Integer.parseInt(LOAD_ID));
            } else {
                calcRecordManager.setLoadId(0);
            }
            String moduleType = zkClientApi.getTpModule().getModule_type();
            if (moduleType.equals(ZkClientApi.MODULE_TYPE.MC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.NORMAL.getMode());
                if (!calcRecordManager.OnInit()) {
                    log.error(CalcRecordManager.class + ",初始化失败");
                    return false;
                }
            } else if (moduleType.equals(ZkClientApi.MODULE_TYPE.TC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.TRIAL.getMode());
                if (!calcRecordManager.OnInitTrial()) {
                    log.error(CalcRecordManager.class + ",试算初始化失败");
                    return false;
                }
            } else if (moduleType.equals(ZkClientApi.MODULE_TYPE.PC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.PRE.getMode());
                if (!calcRecordManager.OnInitPre()) {
                    log.error(CalcRecordManager.class + ",预计费初始化失败");
                    return false;
                }
            } else {
                log.error(zkClientApi.getModuleID() + ",非法的模块类型:" + moduleType);
                return false;
            }
            calcList.add(calcRecordManager);
            CalcRecordManager.setbInitFlag(true);
        }

        return true;
    }

    //  通过命令接收到从zookeeper同步配置的命令
    boolean syncPropertiesAndCalcRecord(MsgBody.ConfigSyncBody configSyncBody) {
        log.info("syncPropertiesAndCalcRecord start 【LOAD_ID:" + configSyncBody.LOAD_ID + ",iReloadCnt:" + iReloadCnt + " 】");

        // 父类初始化可以热加载配置。
        if (!super.LoadCommonAndModuleProperties()) {
            log.error("super.LoadCommonAndModuleProperties() faile");
            return false;
        }

        // 初始化读取数据接口类
        if (!loadTransfer()) {
            log.error("loadTransfer() faile");
            return false;
        }

        // 初始化处理线程
        if (!loadCalcRecordManager("")) {
            log.error("loadCalcRecordManager() faile");
            return false;
        }

        log.info("syncPropertiesAndCalcRecord end 【 iReloadCnt:" + iReloadCnt + " 】");
        lRedoRuleTime = System.currentTimeMillis();
        iReloadCnt++;

        return true;
    }

    boolean startStopProcess(MsgBody.StartStopProcessBody body) {
        boolean bRet = true;
        log.info("startStopProcess start 【COMMAND_ID:" + body.COMMAND_ID + "】");
        if ("EXIT".equals(body.ACTION)) {
            Factory.setbWhileFlag(true);
        } else {
            body.ERROR_MSG = "无法识别的启停命令:" + body.ACTION;
            bRet = false;
        }
        log.info("startStopProcess End 【COMMAND_ID:" + body.COMMAND_ID + "】" + bRet);
        return bRet;
    }

    @Override
    public void run() {
        log.info("LoDoRecordManager start,处理线程数量:" + calcList.size());
        while (!Factory.isbWhileFlag()) {
            if (!common.isWorkFlag()) {
                // 是否进行定时规则重载。
                if (common.getLRedoRuleSeconds() > 0L) {
                    if (!autoReloadConfig()) {
                        log.info("规则重新加载失败，程序退出。");
                        Factory.setbWhileFlag(true);
                        continue;
                    }
                }

                //1、优先处理同步操作
                MsgBody.ConfigSyncBody configBody = null;
                while ((configBody = taskMsgManager.pollConfigSync()) != null) {
                    if (!syncPropertiesAndCalcRecord(configBody)) {
                        configBody.STATE = "ERROR";
                    } else {
                        configBody.STATE = "OK";
                    }

                    configBody.MSG_TYPE = "RESPONSE";
                    configBody.UPDATE_DATE = getSystemDateStr();
                    taskMsgManager.offerResponseJosnTask(JSON.toJSONString(configBody));
                }

                //  处理进程启停命令
                MsgBody.StartStopProcessBody startStopProcessBody = null;
                while ((startStopProcessBody = taskMsgManager.pollStartStopBody()) != null) {
                    // 启动前10秒不处理退出命令
                    if (System.currentTimeMillis() - lStartTime > 10000) {
                        if (!startStopProcess(startStopProcessBody)) {
                            startStopProcessBody.STATE = "ERROR";
                        } else {
                            startStopProcessBody.STATE = "OK";
                        }
                        startStopProcessBody.MSG_TYPE = "RESPONSE";
                        startStopProcessBody.UPDATE_DATE = getSystemDateStr();
                        taskMsgManager.offerResponseJosnTask(JSON.toJSONString(startStopProcessBody));
                    }
                }

                // 处理日志级别控制
                MsgBody.DebugBody debugBody = null;
                while ((debugBody = taskMsgManager.polllDebugBody()) != null) {
                    LogLevelSpring.debugProcess(common, debugBody);
                }
            }

            //2、读取调度下发任务
            MsgBody.StandardBody body = new MsgBody.StandardBody();
            if (!common.isWorkFlag()) {
                if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                    //模块通过读取CTG_MQ自己读取话单
                    body.FILE_NAME = common.getProcessMQMap().get(zkClientApi.getProcessID()).getRecord_consumer_topic();
                } else {
                    body = taskMsgManager.pollRecordTask(); //调度分发任务模式
                }
            } else {
                body = transfer.offLineBody();  //离线模式
            }

            if (body == null && !common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                // 没有数据，休眠1秒
                ThreadSleep(1000);
                continue;
            }

            //3、初始化应答包
            body.init_response();
            transfer.clear();//清除上一条任务的内部变量
            int batchCnt = 0;//清除每个线程处理话单数
            CalcRecordManager.setDealThreadCnt(0);//清除处理线程数

            // 开始时间戳
            Long startSec = getMicroSecond();
            Long startIntSec = getMicroSecond();

            boolean bRet = false;
            // 读取话单
            try {
                bRet = transfer.inputRecord(body);
            } catch (Exception e) {
                sErrorMsg = "transfer.inputRecord(body):" + e.getMessage();
                body.STATE = "ERROR";
                body.ERROR_MSG = sErrorMsg;
                bRet = false;
            }
            if (!bRet) {
                if (!common.isWorkFlag()) {
                    taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
                    if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                        log.error("读取话单失败，退出");
                        break;
                    }
                } else {
                    log.info("body:" + body);
                    Factory.seteExit(true, true);//离线模式只执行一次
                }
                continue;
            }

            Long recordSize = new Long(transfer.getInRecords().size());
            Long readUseTimes = getMicroSecond() - startIntSec;

            //  如果是CTG_MQ话单，判断是否有记录
            if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                if (transfer.getInRecords().size() == 0) {
                    //log.info("读取记录数:" + transfer.getInRecords().size());
                    ThreadSleep(3000);
                    continue;
                }
            }

            // 校验读取话单记录数和消息记录数 ,CTG_MQ不校验
            if (!common.isWorkFlag() && !common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                if (transfer.getInRecords().size() + transfer.SkipRecordCnt() != Integer.parseInt(body.RECORD_CNT)) {
                    sErrorMsg = " 消息内RECORD_CNT:" + body.RECORD_CNT + " 和 读取记录数:" + transfer.getInRecords().size() + " skipRecordCnt:" + transfer.SkipRecordCnt() + " 不一致，异常。";
                    log.error(sErrorMsg);
                    body.STATE = "ERROR";
                    body.ERROR_MSG = sErrorMsg;
                    //transfer.onEnd(body);
                    taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
                    if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                        log.error("校验读取话单记录数和消息记录数不一致，退出");
                        break;
                    }
                    continue;
                }
            }

            Long lReadPerl = recordSize * 1000000 / (readUseTimes > 0 ? readUseTimes : 1);
            log.info("\n" + "读取记录数:" + transfer.getInRecords().size() + ",字节数:" + transfer.getInRecordStrSize() + "，耗时(微秒)：" + readUseTimes + ",性能：" + lReadPerl + ",话单源:" + body.FILE_NAME);
            if (transfer.getInRecords().size() <= common.getIMaxRecordCnt()) {
                batchCnt = transfer.getInRecords().size();
                CalcRecordManager calcRecordManager = calcList.get(0);
                calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.DEAL.getState());
                calcRecordManager.setInRecordList(transfer.getInRecords());

                fixedThreadPool.execute(calcRecordManager);
                CalcRecordManager.dealThreadCntPlusPlus();
            } else {
                batchCnt = transfer.getInRecords().size() / common.getIMaxProcessCnt();

                for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
                    CalcRecordManager calcRecordManager = calcList.get(i);
                    calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.DEAL.getState());
                    if ((i + 1) == common.getIMaxProcessCnt()) {
                        calcRecordManager.setInRecordList(new LinkedList<>(transfer.getInRecords().subList(i * batchCnt, transfer.getInRecords().size())));
                    } else {
                        calcRecordManager.setInRecordList(new LinkedList<>(transfer.getInRecords().subList(i * batchCnt, (i + 1) * batchCnt)));
                    }

                    fixedThreadPool.execute(calcRecordManager);
                    CalcRecordManager.dealThreadCntPlusPlus();
                }
            }

            // 等待线程处理完成
            waitThreadOver(body);
            // flag 容器化部署，经常在这里后面就进程退出了  begin
            Long writeUseTimes = 0L;
            Long lWritePerl = 0L;

            // 输出话单
            if ("OK".equals(body.STATE)) {
                startIntSec = getMicroSecond();
                // 输出正常话单
                // if (common.isOutRecord() && transfer.getOutRecords().size() > 0) {
                if (common.isOutRecord()) {
                    try {
                        log.info("transfer.outputRecord(body) begin");
                        bRet = transfer.outputRecord(body);
                        log.info("transfer.outputRecord(body) end");
                    } catch (Exception e) {
                        sErrorMsg = "transfer.outputRecord(body):" + e.getMessage();
                        log.error(sErrorMsg);
                        body.STATE = "ERROR";
                        body.ERROR_MSG = sErrorMsg;
                        bRet = false;
                    }
                    if (!bRet) {
                        if (!common.isWorkFlag()) {
                            log.info("transfer.outputRecord(body) end false");
                            taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
                            if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                                log.error("输出话单文件失败，退出");
                                break;
                            }
                        } else {
                            log.info("body:" + body);
                            Factory.seteExit(true, true);//离线模式只执行一次
                        }
                        continue;
                    }
                }

                // 输出错误话单
                if (common.isOutErrorRecord() && transfer.getOutErrRecords().size() > 0) {
                    try {
                        log.info("transfer.outputErrorRecord(body) begin");
                        bRet = transfer.outputErrorRecord(body);
                        log.info("transfer.outputErrorRecord(body) end");
                    } catch (Exception e) {
                        sErrorMsg = "transfer.outputErrorRecord(body):" + e.getMessage();
                        log.error(sErrorMsg);
                        body.STATE = "ERROR";
                        body.ERROR_MSG = sErrorMsg;
                        bRet = false;
                    }
                    if (!bRet) {
                        if (!common.isWorkFlag()) {
                            log.info("transfer.outputErrorRecord(body) end false");
                            taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
                            if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                                log.error("输出错误文件失败，退出");
                                break;
                            }
                        } else {
                            log.info("body:" + body);
                            Factory.seteExit(true, true);//离线模式只执行一次
                        }
                        continue;
                    }
                }

                writeUseTimes = getMicroSecond() - startIntSec;
                lWritePerl = recordSize * 1000000 / (writeUseTimes > 0 ? writeUseTimes : 1);
                if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                    log.info("\n" + "写入记录数:" + transfer.getOutRecords().size() + ",字节数:" + transfer.getOutRecordStrSize() + "，耗时(微秒)：" + writeUseTimes + "，性能：" + lWritePerl);
                } else {
                    log.info("\n" + "写入记录数:" + transfer.getOutRecords().size() + ",字节数:" + transfer.getOutRecordStrSize() + "，耗时(微秒)：" + writeUseTimes + "，性能：" + lWritePerl + ",tag:" + body.FILE_NAME);
                }

                // flag 容器化部署，经常在这里后面就进程退出了  end
            }

            // 线程收尾处理
            if (!OnOverThread(body)) {
                body.STATE = "ERROR";
                body.ERROR_MSG = "OnOverThread(body) 失败," + sErrorMsg;
                log.info(body.ERROR_MSG);
            }

            // 消息包填写
            body.UPDATE_DATE = getSystemDateStr();
            body.RECORD_CNT = String.valueOf(transfer.getOutRecords().size() + transfer.SkipRecordCnt());
            // 性能统计
            Long endSec = getMicroSecond();
            Long lPerl = (recordSize * 1000000) / ((endSec - startSec) > 0 ? (endSec - startSec) : 1);
            body.DEAL_TIME = String.valueOf(endSec - startSec);

            // 性能统计
            calcFunctionPerl(body);

            // 重置线程状态为空闲
            resetThreadState();

            // 发送任务处理应答消息
            if (!common.isWorkFlag() && !common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
            }

            log.info("1- 准备进入onEnd");
            // 收尾处理，清空话单。
            if ("OK".equals(body.STATE)) {
                transfer.onEnd(body);  //只有成功的任务，才能删除输入话单
                log.info("2 - 进入onEnd");
            }
            log.info("3 - 退出onEnd");

            if (common.isWorkFlag()) {
                log.info("body:" + body);
                Factory.seteExit(true, true);//离线模式只执行一次
            }

            String sPrint = "\n" + "-----话单文件处理情况----------------------------------------------------------";
            sPrint += "\n" + "话单输入记录数:" + transfer.getInRecords().size() + ",输入字节数:" + transfer.getInRecordStrSize() + ",读取话单时长（微秒）：" + readUseTimes + ",读取性能：" + lReadPerl + ",输出记录数:" + transfer.getOutRecords().size() + ",输出字节数:" + transfer.getOutRecordStrSize() + ",写入话单时长（微秒）:" + writeUseTimes + ",写入性能:" + lWritePerl;
            sPrint += "\n" + "PROCESS_ID:" + zkClientApi.getProcessID() + ",话单文件名:" + body.FILE_NAME + ",输入话单数:" + transfer.getInRecords().size() + ",输出话单数:" + transfer.getOutRecords().size() + ",处理时长:" + (endSec - startSec) + "微秒,处理线程数:" + CalcRecordManager.getDealThreadCnt() + ",每个线程处理:" + batchCnt
                    + ",性能：" + lPerl;
            sPrint += "\n" + "-------------------------------------------------------------------------------";
            log.info(sPrint);

            if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                log.error("任务失败，退出");
                break;
            }
        }

        // 执行退出操作
        exit();

        log.info("run() exit");
    }

    boolean autoReloadConfig() {
        Boolean bRet = true;
        if (System.currentTimeMillis() - lRedoRuleTime > common.getLRedoRuleSeconds()) {
            // 定时进程规则重载
            MsgBody.ConfigSyncBody configBody = new MsgBody.ConfigSyncBody();
            configBody.LOAD_ID = "自动重载规则:" + getSystemDateStr();
            configBody.CONFIG_SYNC_ID = getSystemDateStr() + "_" + String.valueOf(iReloadCnt);
            configBody.TASK_TYPE = MsgBody.TASK_TYPE.T_CONFIG.getType();
            configBody.HOST_ID = String.valueOf(process_id).substring(4, 6);
            configBody.PROCESS_ID = String.valueOf(process_id);
            configBody.MODULE_ID = String.valueOf(process_id).substring(0, 4);
            configBody.MSG_COMMENT = "自动重载规则";
            configBody.MSG_TYPE = "AUTO_RELOAD";
            configBody.STATE = "";
            configBody.ERROR_MSG = "";
            configBody.CREATE_DATE = getSystemDateStr();


            createConfigSyncToTable(configBody);
            if (!syncPropertiesAndCalcRecord(configBody)) {
                bRet = false;
                configBody.STATE = "ERROR";
                configBody.ERROR_MSG = "规则重新加载失败，程序退出。";
                configBody.UPDATE_DATE = getSystemDateStr();
            } else {
                bRet = true;
                configBody.STATE = "OK";
                configBody.UPDATE_DATE = getSystemDateStr();
            }
            saveConfigSyncToTable(configBody);
        }
        return bRet;
    }

    boolean calcFunctionPerl(MsgBody.StandardBody body) {
        String sPrint = new String();
        Map<Integer, FunctionPerl> mPerlLow = new HashMap<>();
        Map<Integer, FunctionPerl> mPerlTmp = calcList.get(0).getFunctionPerl();
        if (mPerlTmp != null) {
            // 输出单个话单文件因子性能
            sPrint = "\n" + "-----因子性能------------------------------------------------------------------";
            for (Map.Entry<Integer, FunctionPerl> iter : mPerlTmp.entrySet()) {
                sPrint += "\n" + iter.getValue().toString();
                if (mPerlLow.get(iter.getKey()) == null) {
                    mPerlLow.put(iter.getKey(), iter.getValue());
                } else {
                    FunctionPerl tmp = mPerlLow.get(iter.getKey());
                    tmp.setPlusCntPerl(iter.getValue().getCnt(), iter.getValue().getDt());
                }
            }
            log.info(sPrint);
            mPerlTmp.clear();

            // 每超过5000条话单输出一次性能慢函数到表
            iCurrCnt += transfer.getInRecords().size();
            if (iCurrCnt >= common.getIPerlOutCnt()) {
                List<List<Object>> listPerl = new ArrayList<>();
                sPrint = "\n" + "----当前处理:" + iCurrCnt + " 条话单,平均性能低于:" + common.getIPerlLow() + " 的因子如下 ---------";
                for (Map.Entry<Integer, FunctionPerl> iter : mPerlLow.entrySet()) {
                    if (iter.getValue().getPerl() < common.getIPerlLow()) {
                        sPrint += "\n" + iter.toString();
                        List<Object> listTmp = new ArrayList<>();
                        listTmp.add(body.TASK_ID);
                        listTmp.add(process_id);
                        listTmp.add(iter.getValue().getFunction_id());
                        listTmp.add(iter.getValue().getEn_name());
                        listTmp.add(iter.getValue().getCh_name());
                        listTmp.add(iter.getValue().getCnt());
                        listTmp.add(iter.getValue().getDt());
                        listTmp.add(iter.getValue().getPerl());
                        listTmp.add(iter.getValue().getFailCnt());
                        listTmp.add(iter.getValue().getFailDt());
                        listTmp.add(iter.getValue().getFailPerl());
                        listTmp.add(getSystemDateStr());
                        listPerl.add(listTmp);
                    }
                }

                if (listPerl.size() > 0) {
                    Connection connTmp = DbPool.getConn();
                    DBUtils.exeInsertBatch(connTmp, FunctionPerl.getSql(), listPerl);
                    DbPool.close(connTmp);
                }
                log.info(sPrint);
                mPerlLow.clear();

                iCurrCnt = 0;
            }
        }
        return true;
    }

    /**
     * @param body
     * @return
     */
    boolean waitThreadOver(MsgBody.StandardBody body) {
        Long lStartTime = System.currentTimeMillis();
        while (true) {
            int i = 0;
            int nCnt = 0, aCnt = 0, oCnt = 0, pCnt = 0, eCnt = 0, rCnt = 0, vCnt = 0;

            // 等待所有的线程处理完成
            for (CalcRecordManager calc : calcList) {
                if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.END.getState()) ||
                        calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                    i++;
                }
            }

            if (i == CalcRecordManager.getDealThreadCnt()) {
                for (CalcRecordManager calc : calcList) {
                    if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.END.getState()) ||
                            calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                        transfer.getOutRecords().addAll(calc.getOutRecordList());
                        transfer.getOutErrRecords().addAll(calc.getOutErrorList());
                        transfer.setOutNormalCnt(transfer.getOutNormalCnt() + calc.getOutRecordList().size());

                        eCnt += calc.getOutErrorList().size();

                        nCnt += calc.getNormalCnt();
                        aCnt += calc.getAbnCnt();
                        oCnt += calc.getOtherCnt();
                        pCnt += calc.getPreCnt();
                        rCnt += calc.getRepeatCnt();
                        vCnt += calc.getInvalidCnt();
                    }
                }

                body.REPEAT_CNT = rCnt;
                body.ERROR_CNT = eCnt;
                body.NORMAL_CNT = nCnt;
                body.PRE_CNT = pCnt;
                body.ABN_CNT = aCnt;
                body.OTHER_CNT = oCnt;
                body.INVALID_CNT = vCnt;

                log.info("所有的线程处理完成：" + CalcRecordManager.getDealThreadCnt());
                String sPrint = "TASK_ID:" + body.TASK_ID + ",PROCESS_ID:" + zkClientApi.getProcessID() + ",输入话单数-> " + transfer.getInRecords().size() + ",输出话单数-> " + transfer.getOutRecords().size() + ",正常单: " + body.NORMAL_CNT + ",预结单: " + body.PRE_CNT +
                        ",异常单: " + body.ABN_CNT + ",OTHER单: " + body.OTHER_CNT + ",无效单: " + body.INVALID_CNT + ",出错单: " + body.ERROR_CNT + ",重单：" + body.REPEAT_CNT;

                log.info("\n" + sPrint);
                break;
            }

            if (System.currentTimeMillis() - lStartTime > common.getLTimeOutSeconds()) {
                // 超时60，设置任务失败
                body.STATE = "ERROR";
                body.ERROR_MSG = "任务处理超时，强制退出，任务失败。";
                //一个文件一分钟没有处理完成，就认为处理超时
                int j = 1;
                for (CalcRecordManager calc : calcList) {
                    if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.DEAL.getState())) {
                        calc.setDealState(CalcRecordManager.THREAD_STATE.TIMEOUT.getState());
                        String sPring = "第 " + j + " 个线程处理超时，强制退出；超时时长：" + common.getLTimeOutSeconds() / 1000 + " 秒";
                        log.info(sPring);
                    }
                    j++;
                }

                //线程池关闭,重新创建线程池
                fixedThreadPool.shutdown();
                fixedThreadPool = Executors.newFixedThreadPool(common.getIMaxProcessCnt());
                break;
            }
        }

        if ("OK".equals(body.STATE)) {
            if (body.ERROR_CNT > 0 && common.isExistsError2FCL()) {
                body.STATE = "ERROR";
                body.ERROR_MSG = String.format("话单处理存在 %d 条错误单，任务处理失败。", body.ERROR_CNT);
                log.error(body.ERROR_MSG);
            } else {
                // 线程有一个失败，就全部失败
                for (CalcRecordManager calc : calcList) {
                    if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                        body.STATE = "ERROR";
                        body.ERROR_MSG = calc.getThreadNum() + " 号线程执行失败," + "线程有一个失败，就全部失败，失败原因:" + calc.getErrorMsg();
                        log.error(body.ERROR_MSG);
                        break;
                    }
                }
            }
        }

        return true;
    }


    boolean OnOverThread(MsgBody.StandardBody body) {
        Map<String, List<List<KeyValue>>> dataMap = new HashMap<>();
        for (CalcRecordManager calcRecordManager : calcList) {
            for (Map.Entry<String, List<List<KeyValue>>> mapTmp : calcRecordManager.getDataMap().entrySet()) {
                List<List<KeyValue>> lists = dataMap.get(mapTmp.getKey());
                if (null == lists) {
                    lists = new ArrayList<>();
                    dataMap.put(mapTmp.getKey(), lists);
                }
                lists.addAll(mapTmp.getValue());
            }
        }

        boolean flag = true;
        for (CalcRecordManager calcRecordManager : calcList) {
            if (flag && "OK".equals(body.STATE)) {
                if (!calcRecordManager.OnOver(dataMap)) {
                    sErrorMsg = "calcRecordManager.OnOver() faile";
                    log.error("calcRecordManager.OnOver() faile");
                    return false;
                }
                flag = false;
            }
            calcRecordManager.clearRecordList();
        }
        dataMap.clear();
        return true;
    }

    public void resetThreadState() {
        for (CalcRecordManager calcRecordManager : calcList) {
            calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.IDLE.getState());
        }
    }

    public void setClazzTransferSettle(Class<? extends TransferSettle> clazzTransferSettle) {
        this.clazzTransferSettle = clazzTransferSettle;
    }

    public void setClazzTaskMessage(Class<? extends TaskMessage> clazzTaskMessage) {
        this.clazzTaskMessage = clazzTaskMessage;
    }

    public void setClazzCalcRecordManager(Class<? extends CalcRecordManager> clazzCalcRecordManager) {
        this.clazzCalcRecordManager = clazzCalcRecordManager;
    }

    boolean exit() {
        // 关闭所有处理子线程
        for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
            CalcRecordManager calcRecordManager = calcList.get(i);
            calcRecordManager.OnExit();
        }

        // 关闭话单读写接口
        transfer.close();

        // 关闭任务获取接口
        if (!common.isWorkFlag()) {
            taskMsgManager.close();
            // 关闭消息循环
            Factory.setbWhileMsgFlag(true);
        }

        // 关闭zookeeper
        zkClientApi.close();

        // 关闭springboot
        Factory.close();
        return true;
    }

    public synchronized boolean createConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        String insertSql = "insert into dcoos_config_sync_body(config_sync_id,task_type,host_id,process_id,module_id,load_id,msg_comment,msg_type,state,error_msg,update_date) values (?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdateThrowException(connTmp, insertSql, sync.CONFIG_SYNC_ID, sync.TASK_TYPE, sync.HOST_ID, sync.PROCESS_ID, sync.MODULE_ID, sync.LOAD_ID, sync.MSG_COMMENT, sync.MSG_TYPE, sync.STATE, sync.ERROR_MSG, sync.UPDATE_DATE);
        } catch (SQLException e) {
            printStackTraceToString(e);
            log.error("createConfigSyncToTable exception:" + e.getMessage());
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean saveConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        String updateSql = "update dcoos_config_sync_body set msg_type=?,state=?,error_msg=?,update_date=? where config_sync_id=?";
        boolean ret = false;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdateThrowException(connTmp, updateSql, sync.MSG_TYPE, sync.STATE, sync.ERROR_MSG, sync.UPDATE_DATE, sync.CONFIG_SYNC_ID);
        } catch (SQLException e) {
            printStackTraceToString(e);
            log.error("saveConfigSyncToTable exception:" + e.getMessage());
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }
}
