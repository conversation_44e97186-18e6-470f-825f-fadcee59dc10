package com.itco.framework.process;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.process.MsgBody;
import com.itco.framework.calcrecord.CalcRecordFactory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.framework.Factory;
import com.itco.framework.message.TaskMsgManager;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.message.TaskMessageFactory;
import com.itco.framework.record.TransferFactory;
import com.itco.framework.record.impl.common.TransferCommon;
import com.itco.framework.record.impl.settle.TransferSettle;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;

import static com.itco.framework.Factory.getSystemDateStr;

public abstract class Process implements Runnable {
    static Log log = LogFactory.getLog(Process.class);

    protected final static ZkClientApi zkClientApi = new ZkClientApi();
    protected final static Common common = new Common(); //公共配置类
    protected final static TaskMsgManager taskMsgManager = new TaskMsgManager();  // 调度消息通信接口

    protected List<CalcRecordManager> calcList = new ArrayList<>(); // 管理话单处理实例
    protected ExecutorService fixedThreadPool = null;  // 线程池类

    protected String sErrorMsg = null;//异常描述

    protected int module_id = 1013;
    protected int process_id = 10131111;
    protected String billingLineId = null;
    protected String moduleCode = null;

    protected String transferClassName = null;  //数据接口类名称
    protected String calcClassName = null;  //执行线程类名称
    protected String taskMessageClassName = null;   //消息接口类名称

    protected Class<? extends TaskMessage> clazzTaskMessage = null;
    protected Class<? extends CalcRecordManager> clazzCalcRecordManager = null;

    protected TransferFactory transferFactory = new TransferFactory();
    protected TaskMessageFactory taskMessageFactory = new TaskMessageFactory();
    protected CalcRecordFactory calcRecordFactory = new CalcRecordFactory();


    public ZkClientApi getZkClientApi() {
        return zkClientApi;
    }

    public Common getCommon() {
        return common;
    }

    // 设置输出要素串
    public void setIsElement(int element) {
        if (element == 0) {
            String Tmp = System.getenv("ElEMENT");
            if (StringUtils.isNumber(Tmp)) {
                element = Integer.parseInt(Tmp);
            }
        }
        common.setIElement(element);
    }

    // 设置打印开关
    public void setDebug(int debug) {
        if (debug == 0) {
            String Tmp = System.getenv("PROCESS_DEBUG");
            if (StringUtils.isNumber(Tmp)) {
                debug = Integer.parseInt(Tmp);
            }
        }
        common.setIDebug(debug);
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

    public void setTransferClassName(String transferClassName) {
        this.transferClassName = transferClassName;
    }

    public void setCalcClassName(String calcClassName) {
        this.calcClassName = calcClassName;
    }

    /*
     * @decription 初始化话
     * @param :
     * @return boolean
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    protected boolean Init() {
        log.info("super.init() moduleCode:" + moduleCode + ",calcClassName:" + calcClassName + ",transferClassName:" + transferClassName + ",billingLineId:" + billingLineId);
        if (moduleCode == null || calcClassName == null) {
            log.error("模块编码或者执行线程类为空，启动失败。");
            return false;
        }

        // 1、正常模式、离线模式 判断
        try {
            Resource resource = new ClassPathResource("/application.properties");
            Properties propApp = PropertiesLoaderUtils.loadProperties(resource);
            String flag = propApp.getProperty("workFlag");
            if (flag != null && flag.equals("offline")) {
                common.setWorkFlag(true);
                common.setOffworkPath(propApp.getProperty("workDir"));
                common.setOffWorkFileName(propApp.getProperty("workFileName"));
                log.info("离线模式：" + common.getOffworkPath() + "\\" + common.getOffWorkFileName());
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }

        log.info("版本信息:" + common.getVersion());

        // 1、连接zookeeper，注册进程
        if (!loadZookeeper()) {
            log.error("zookeeperInit() 初始化失败");
            return false;
        }

        return true;
    }

    /*
     * @decription 连接zookeeper初始化
     * @param :
     * @return boolean
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    boolean loadZookeeper() {
        zkClientApi.setVersion(common.getVersion());
        zkClientApi.setModuleCode(moduleCode);
        zkClientApi.setBillingLineId(billingLineId);
        if (!zkClientApi.init()) {
            Factory.setbWhileFlag(true);
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        if (!zkClientApi.register(common.isWorkFlag())) {
            Factory.setbWhileFlag(true);
            log.info(zkClientApi.getModuleCode() + " 模块的进程注册失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        module_id = Integer.parseInt(zkClientApi.getModuleID());
        process_id = Integer.parseInt(zkClientApi.getProcessID());

        Factory.setModule_id(module_id);
        Factory.setProcess_id(process_id);

        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());
        return true;
    }

    protected boolean LoadCommonAndModuleProperties() {
        // 读取Common.properties
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        // 读取module.properties
        if (common.isModulePropertiesFlag()) {
            String propFileName = zkClientApi.getModuleCode() + ".properties";
            Properties p = zkClientApi.getPropertiesFromZK(propFileName);
            if (p == null) {
                log.error("配置中心没有找到配置文件：" + propFileName + ",模块启动失败。");
                return false;
            } else {
                if (null != p.getProperty("transfer_class_name")) {   //数据采集类名
                    transferClassName = p.getProperty("transfer_class_name");
                }
                String calcTmp = "calc_class_name_" + process_id;
                if (null != p.getProperty(calcTmp)) {   //话单处理类名
                    calcClassName = p.getProperty(calcTmp);
                    log.info("配置了进程级别的实现类：" + calcTmp + "," + calcClassName);
                } else if (null != p.getProperty("calc_class_name")) {   //话单处理类名
                    calcClassName = p.getProperty("calc_class_name");
                    log.info("配置了模块级别的实现类：calc_class_name" + "," + calcClassName);
                }

                if (null != p.getProperty("max_process_cnt")) {   //最大子线程数量
                    common.setIMaxProcessCnt(Integer.valueOf(p.getProperty("max_process_cnt")));
                }
                if (null != p.getProperty("max_record_cnt")) {    //最大记录数
                    common.setIMaxRecordCnt(Integer.valueOf(p.getProperty("max_record_cnt")));
                }
                if (null != p.getProperty("redo_rule_seconds")) {    //定时重载规则时间间隔
                    common.setLRedoRuleSeconds(Long.valueOf(p.getProperty("redo_rule_seconds")));
                }

                if ("false".equals(p.getProperty("is_out_record"))) {    //是否输出话单
                    common.setOutRecord(false);
                }
                if ("false".equals(p.getProperty("is_error_exit"))) {    //任务出错，是否退出
                    common.setErrorExit(false);
                }

                if (null != p.getProperty("debug")) {
                    String dProcessId = p.getProperty("debug");
                    String[] list = dProcessId.split(",");
                    for (int i = 0; i < list.length; i++) {
                        if (zkClientApi.getProcessID().equals(list[i])) {
                            common.setIDebug(1);
                            break;
                        }
                    }
                }

                if ("false".equals(p.getProperty("task_msg_flag"))) {
                    common.setTaskMsgFlag(false);
                } else {
                    common.setTaskMsgFlag(true);
                }
                if (null != p.getProperty("perl_out_cnt")) {   //性能输出开关
                    common.setIPerlOutCnt(Integer.parseInt(p.getProperty("perl_out_cnt")));
                }
                if (null != p.getProperty("perl_low")) {    //性能慢阀值
                    common.setIPerlLow(Integer.parseInt(p.getProperty("perl_low")));
                }
            }
        }

        log.info(common.toString());
        return true;
    }

    protected boolean LoadTaskManager() {
        // 初始化完成之后，再启动任务消息线程。
        if (!common.isWorkFlag() && common.isTaskMsgFlag()) {
            TaskMsgManager.setCommon(common);
            TaskMsgManager.setZkClientApi(zkClientApi);

            TaskMessage taskMessage = taskMessageFactory.getTaskMessageInstance(clazzTaskMessage, taskMessageClassName, common.getTaskTransferMode());
            taskMsgManager.setTaskMessage(taskMessage);
            if (!taskMsgManager.init()) {
                log.error("taskMsgManager init false");
                Factory.setbWhileFlag(true);
                return false;
            }

            Thread taskMessageThread = new Thread(taskMsgManager);
            taskMessageThread.start();
        }
        return true;
    }

    protected boolean processReadyWork() {
        // 添加版本信息
        if (common.getRule_function_version() == null) {
            common.setVersion("[" + common.getBill_public_version() + "][" + common.getDeploy_version() + "][" + common.getCode_version() + "]");
        } else {
            common.setVersion("[" + common.getBill_public_version() + "][" + common.getRule_function_version() + "][" + common.getDeploy_version() + "][" + common.getCode_version() + "]");
        }

        // 修改zookeeper上的，挂起状态，和版本信息
        if (!zkClientApi.readyWork(common.getVersion())) {
            log.error("zkClientApi.readyWork() faile()");
            return false;
        }

        // 调度存在的情况下，进行发送消息，
        if (1 == zkClientApi.iExistPath("/process/Dispatch/Dispatch-10100000")) {
            MsgBody.StartStopProcessBody body = new MsgBody.StartStopProcessBody();
            body.COMMAND_ID = getSystemDateStr() + "_1";
            body.TASK_TYPE = MsgBody.TASK_TYPE.T_SS_PROCESS.getType();
            body.MSG_TYPE = "REQUEST";
            body.PROCESS_ID = zkClientApi.getProcessID();
            body.REQUEST_PROCESS_ID = zkClientApi.getProcessID();
            body.STATE = "";
            body.ACTION = "START";
            body.ACTION_DATE = "NOW";
            body.CREATE_DATE = getSystemDateStr();
            body.VERSION = common.getVersion();
            String json = JSON.toJSONString(body);
            log.info("申请修改进程状态为，非挂起状态，json:" + json);
            taskMsgManager.offerResponseJosnTask(json);
        }

        return true;
    }

    /*
     * @decription 初始化
     * @param null
     * @return
     * <AUTHOR>
     * @createDate 2022/6/7
     */
    public abstract void run();

    /*
     * @decription 获取时间纳秒
     * @param :
     * @return java.lang.Long
     * <AUTHOR>
     * @createDate 2022/8/23
     */
    public Long getMicroSecond() {
        return System.nanoTime() / 1000;
    }


    public void ThreadSleep(long millisSec) {
        try {
            Thread.sleep(millisSec);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /*
     * @decription TODO
     * @param name
     * @return 返回值
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public CalcRecordManager getCalcInstance(String name) {
        Class<?> clazzCalcRecordManager = null;
        CalcRecordManager calcRecordManager = null;
        try {
            clazzCalcRecordManager = Class.forName(name);
            calcRecordManager = (CalcRecordManager) clazzCalcRecordManager.newInstance();
            return calcRecordManager;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + name + "," + e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public CalcRecordManager getCalcInstance(Class<? extends CalcRecordManager> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + CalcRecordManager.class + "," + e.getMessage());
        }
        return null;
    }

    /*
     * @decription 实例化对象
     * @param name: 类名称
     * @return com.itco.process.record.common.TransferCommon
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public TransferCommon getTransferCommonInstance(String name) {
        Class<?> clazzTransferCommon = null;
        TransferCommon transferCommon = null;
        try {
            clazzTransferCommon = Class.forName(name);
            transferCommon = (TransferCommon) clazzTransferCommon.newInstance();
            return transferCommon;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + name + "," + e.getMessage());
            return null;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TransferCommon getTransferCommonInstance(Class<? extends TransferCommon> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + TransferCommon.class + "," + e.getMessage());
        }
        return null;
    }

    public TransferSettle getTransferSettleInstance(String name) {
        Class<?> clazzTransferSettle = null;
        TransferSettle transferSettle = null;
        try {
            clazzTransferSettle = Class.forName(name);
            transferSettle = (TransferSettle) clazzTransferSettle.newInstance();
            return transferSettle;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + name + "," + e.getMessage());
            return null;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TransferSettle getTransferSettleInstance(Class<? extends TransferSettle> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + TransferSettle.class + "," + e.getMessage());
        }
        return null;
    }
}
