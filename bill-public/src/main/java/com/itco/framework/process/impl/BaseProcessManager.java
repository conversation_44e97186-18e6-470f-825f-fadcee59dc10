package com.itco.framework.process.impl;

import com.itco.framework.Factory;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.process.Process;
import com.itco.framework.record.impl.settle.TransferSettle;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class BaseProcessManager extends Process {
    static Log log = LogFactory.getLog(BaseProcessManager.class);

    TransferSettle transfer = null;  // 文件流转类(离线模式) 、 ctgMq流转类 、数据库表
    TaskMessage taskMessage = null;
    int messageMode = 1;

    public boolean Init() {
        common.setModulePropertiesFlag(false);
        common.setTaskMsgFlag(false);

        // 父类初始化
        if (!super.Init()) {
            log.error("super.init() 失败");
            return false;
        }

        // 初始化可以热加载配置。
        if (!super.LoadCommonAndModuleProperties()) {
            log.error("super.LoadCommonAndModuleProperties() faile");
            return false;
        }

        // 初始化读取数据接口类
        if (!loadTransfer()) {
            log.error("loadTransfer() faile");
            return false;
        }

        // 初始化消息队列接口类
        if (!loadTaskMessage()) {
            log.error("loadTaskMessage() faile");
            return false;
        }

        return true;
    }

    @Override
    public void run() {

    }

    boolean loadTransfer() {
        transfer = transferFactory.getTransferSettleInstance(null, null, common.getRecordTransferMode());
        if (transfer == null) {
            log.error("transferFactory.getTransferCommonInstance(" + common.getTaskTransferMode() + ") 创建失败");
            return false;
        }

        // 4、初始化话单读写取类
        transfer.setZkClientApi(zkClientApi);
        transfer.setCommon(common);
        if (!transfer.init()) {
            log.error("transfer.init(p) 失败");
            return false;
        }

        return true;
    }

    boolean loadTaskMessage() {
        // 初始化完成之后，再启动任务消息线程。
        taskMessage = taskMessageFactory.getTaskMessageInstance(clazzTaskMessage, taskMessageClassName, common.getTaskTransferMode());
        taskMessage.setMode(messageMode);
        // 初始化
        taskMessage.setZkClientApi(zkClientApi);
        taskMessage.setCommon(common);
        if (!taskMessage.init()) {
            Factory.setbWhileFlag(true);
            return false;
        }
        return true;
    }

    public TransferSettle getTransfer() {
        return transfer;
    }

    public TaskMessage getTaskMessage() {
        return taskMessage;
    }

    public void setMessageMode(int messageMode) {
        this.messageMode = messageMode;
    }
}
