package com.itco.framework.process.impl;


import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.entity.process.CommonBody;
import com.itco.entity.process.E_RecordTransferMode;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.framework.log.LogLevelSpring;
import com.itco.framework.process.Process;
import com.itco.framework.record.impl.common.TransferCommon;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;

/*
 * 公共模块框架
 * 启动初始化，运行
 * <AUTHOR>
 * @createDate 2022/6/7
 */
public class CommonProcessManager extends Process {
    static Log log = LogFactory.getLog(CommonProcessManager.class);

    boolean bRet = false;
    Long startTime = null;

    TransferCommon transfer = null;

    Class<? extends TransferCommon> clazzTransferCommon = null;

    public boolean Init() {
        common.setModulePropertiesFlag(false);
        common.setTaskMsgFlag(false);

        // 父类初始化
        if (!super.Init()) {
            log.error("super.init() 失败");
            return false;
        }

        // 初始化处理线程类
        if (!syncPropertiesAndCalcRecord(new MsgBody.ConfigSyncBody())) {
            log.error("syncPropertiesAndCalcRecord() faile");
            return false;
        }

        // 初始化读取数据接口类
        if (!loadTransfer()) {
            log.error("super.loadTransfer() faile");
            return false;
        }

        startTime = System.currentTimeMillis();
        return true;
    }

    boolean loadTransfer() {
        transfer = transferFactory.getTransferCommonInstance(clazzTransferCommon, transferClassName);
        if (transfer == null) {
            log.error("transferFactory.getTransferCommonInstance(" + clazzTransferCommon + "," + transferClassName + ") 创建失败");
            return false;
        }

        // 4、初始化话单读写取类
        transfer.setZkClientApi(zkClientApi);
        transfer.setCommon(common);
        if (!transfer.init()) {
            log.error("transfer.init(p) 失败");
            return false;
        }

        return true;
    }


    boolean loadCalcRecordManager(String LOAD_ID) {
        // 初始化线程池数量
        fixedThreadPool = Executors.newFixedThreadPool(common.getIMaxProcessCnt());

        CalcRecordManager.setbInitFlag(false);
        CalcRecordManager.setCommon(common);
        CalcRecordManager.setZkClientApi(zkClientApi);
        calcList.clear();//实例化处理类，要清理

        for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
            CalcRecordManager calcRecordManager = calcRecordFactory.getCalcInstance(clazzCalcRecordManager, calcClassName);
            if (null == calcRecordManager) {
                log.error("话单处理类实例化失败：" + clazzCalcRecordManager.getName());
                return false;
            }

            calcRecordManager.setName("处理线程【" + (i + 1) + "】");
            calcRecordManager.setThreadNum(i);
            if (LOAD_ID == null || "".equals(LOAD_ID)) {
                calcRecordManager.setLoadId(0);
            } else if (StringUtils.isNumber(LOAD_ID)) {
                calcRecordManager.setLoadId(Integer.parseInt(LOAD_ID));
            } else {
                calcRecordManager.setLoadId(0);
            }
            String moduleType = zkClientApi.getTpModule().getModule_type();
            if (moduleType.equals(ZkClientApi.MODULE_TYPE.MC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.NORMAL.getMode());
                if (!calcRecordManager.OnInit()) {
                    log.error(CalcRecordManager.class + ",初始化失败");
                    return false;
                }
            } else if (moduleType.equals(ZkClientApi.MODULE_TYPE.TC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.TRIAL.getMode());
                if (!calcRecordManager.OnInitTrial()) {
                    log.error(CalcRecordManager.class + ",试算初始化失败");
                    return false;
                }
            } else if (moduleType.equals(ZkClientApi.MODULE_TYPE.PC.getType())) {
                calcRecordManager.setStartMode(ZkClientApi.START_MODE.PRE.getMode());
                if (!calcRecordManager.OnInitPre()) {
                    log.error(CalcRecordManager.class + ",预计费初始化失败");
                    return false;
                }
            } else {
                log.error(zkClientApi.getModuleID() + ",非法的模块类型:" + moduleType);
                return false;
            }
            calcList.add(calcRecordManager);
            CalcRecordManager.setbInitFlag(true);
        }

        return true;
    }


    //  通过命令接收到从zookeeper同步配置的命令
    boolean syncPropertiesAndCalcRecord(MsgBody.ConfigSyncBody configSyncBody) {
        log.info("syncPropertiesAndCalcRecord start 【LOAD_ID:" + configSyncBody.LOAD_ID + "】");

        // 初始化可以热加载配置。
        if (!super.LoadCommonAndModuleProperties()) {
            log.error("super.LoadCommonAndModuleProperties() faile");
            return false;
        }

        // 初始化处理线程
        if (!loadCalcRecordManager("")) {
            log.error("loadCalcRecordThread() faile");
            return false;
        }

        log.info("syncConfigFromzookeeper end");
        return true;
    }

    boolean startStopProcess(MsgBody.StartStopProcessBody body) {
        boolean bRet = true;
        log.info("startStopProcess start 【COMMAND_ID:" + body.COMMAND_ID + "】");
        if (body.ACTION.equals("EXIT")) {
            Factory.setbWhileFlag(true);
        } else {
            body.ERROR_MSG = "无法识别的启停命令:" + body.ACTION;
            bRet = false;
        }
        log.info("startStopProcess End 【COMMAND_ID:" + body.COMMAND_ID + "】" + bRet);
        return bRet;
    }

    /**
     * @return
     */
    boolean waitThreadOver(CommonBody body) {
        Long lStartTime = System.currentTimeMillis();
        while (true) {
            int i = 0;
            int nCnt = 0, aCnt = 0, oCnt = 0, eCnt = 0, rCnt = 0;

            // 等待所有的线程处理完成
            for (CalcRecordManager calc : calcList) {
                if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.END.getState()) ||
                        calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                    i++;
                }
            }

            if (i == CalcRecordManager.getDealThreadCnt()) {
                for (CalcRecordManager calc : calcList) {
                    if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.END.getState()) ||
                            calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                        transfer.getOutRecords().addAll(calc.getOutRecordList());
                        transfer.getOutErrRecords().addAll(calc.getOutErrorList());
                        transfer.setOutNormalCnt(transfer.getOutNormalCnt() + calc.getOutRecordList().size());

                        eCnt += calc.getOutErrorList().size();

                        nCnt += calc.getNormalCnt();
                        aCnt += calc.getAbnCnt();
                        oCnt += calc.getOtherCnt();
                        rCnt += calc.getRepeatCnt();
                    }
                }

                body.ERROR_CNT = eCnt;
                body.NORMAL_CNT = nCnt;
                body.ABN_CNT = aCnt;
                body.OTHER_CNT = oCnt;

                log.info("所有的线程处理完成：" + CalcRecordManager.getDealThreadCnt());
                String sPrint = "TASK_ID:" + body.TASK_ID + ",PROCESS_ID:" + zkClientApi.getProcessID() + ",输入话单数-> " + transfer.getInRecords().size() + ",输出话单数-> " + transfer.getOutRecords().size() + " ,正常单: " + body.NORMAL_CNT + ",异常单: " + body.ABN_CNT + ",OTHER单: " + body.OTHER_CNT +
                        ",出错单: " + body.ERROR_CNT;
                log.info("\n" + sPrint);

                break;
            }

            if (System.currentTimeMillis() - lStartTime > common.getLTimeOutSeconds()) {
                // 超时60，设置任务失败
                body.STATE = "ERROR";
                body.ERROR_MSG = "任务处理超时，强制退出，任务失败。";
                //一个文件一分钟没有处理完成，就认为处理超时
                int j = 0;
                for (CalcRecordManager calc : calcList) {
                    if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.DEAL.getState())) {
                        calc.setDealState(CalcRecordManager.THREAD_STATE.TIMEOUT.getState());
                        String sPring = "第 " + j + " 个线程处理超时，强制退出";
                        log.info(sPring);
                    }
                    j++;
                }

                //线程池关闭,重新创建线程池
                fixedThreadPool.shutdown();
                fixedThreadPool = Executors.newFixedThreadPool(common.getIMaxProcessCnt());
                break;
            }
        }

        if ("OK".equals(body.STATE)) {
            // 线程有一个失败，就全部失败
            for (CalcRecordManager calc : calcList) {
                if (calc.getDealState().equals(CalcRecordManager.THREAD_STATE.ERROR.getState())) {
                    body.STATE = "ERROR";
                    body.ERROR_MSG = calc.getThreadNum() + " 号线程执行失败," + "线程有一个失败，就全部失败，失败原因:" + calc.getErrorMsg();
                    log.error(body.ERROR_MSG);
                    break;
                }
            }
        }
        return true;
    }

    boolean OnOverThread(CommonBody body) {
        Map<String, List<List<KeyValue>>> dataMap = new HashMap<>();
        for (CalcRecordManager calcRecordManager : calcList) {
            for (Map.Entry<String, List<List<KeyValue>>> mapTmp : calcRecordManager.getDataMap().entrySet()) {
                List<List<KeyValue>> lists = dataMap.get(mapTmp.getKey());
                if (null == lists) {
                    lists = new ArrayList<>();
                    dataMap.put(mapTmp.getKey(), lists);
                }
                lists.addAll(mapTmp.getValue());
            }
        }

        boolean flag = true;
        for (CalcRecordManager calcRecordManager : calcList) {
            if (flag && "OK".equals(body.STATE)) {
                if (!calcRecordManager.OnOver(dataMap)) {
                    sErrorMsg = "calcRecordManager.OnOver() faile";
                    log.error("calcRecordManager.OnOver() faile");
                    return false;
                }
                flag = false;
            }
            calcRecordManager.clearRecordList();
        }
        dataMap.clear();
        return true;
    }

    public void resetThreadState() {
        for (CalcRecordManager calcRecordManager : calcList) {
            calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.IDLE.getState());
        }
    }


    public void ThreadSleep(long millisSec) {
        try {
            Thread.sleep(millisSec);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public void run() {
        int iCurrCnt = 0;

        log.info("LoDoRecordManager start,处理线程数量:" + calcList.size());
        while (!Factory.isbWhileFlag()) {
            if (common.isTaskMsgFlag()) {
                //1、优先处理同步操作
                MsgBody.ConfigSyncBody configBody = null;
                while ((configBody = taskMsgManager.pollConfigSync()) != null) {
                    if (!syncPropertiesAndCalcRecord(configBody)) {
                        configBody.STATE = "ERROR";
                    } else {
                        configBody.STATE = "OK";
                    }

                    configBody.MSG_TYPE = "RESPONSE";
                    configBody.UPDATE_DATE = Factory.getSystemDateStr();
                    taskMsgManager.offerResponseJosnTask(JSON.toJSONString(configBody));
                }

                //  处理进程启停命令
                MsgBody.StartStopProcessBody startStopProcessBody = null;
                while ((startStopProcessBody = taskMsgManager.pollStartStopBody()) != null) {
                    // 启动前10秒不处理退出命令
                    if (System.currentTimeMillis() - startTime > 10000) {
                        if (!startStopProcess(startStopProcessBody)) {
                            startStopProcessBody.STATE = "ERROR";
                        } else {
                            startStopProcessBody.STATE = "OK";
                        }
                        startStopProcessBody.MSG_TYPE = "RESPONSE";
                        startStopProcessBody.UPDATE_DATE = Factory.getSystemDateStr();
                        taskMsgManager.offerResponseJosnTask(JSON.toJSONString(startStopProcessBody));
                    }
                }

                MsgBody.DebugBody debugBody = null;
                while ((debugBody = taskMsgManager.polllDebugBody()) != null) {
                    LogLevelSpring.debugProcess(common, debugBody);
                }
            }

            CommonBody body = new CommonBody();
            // 离线模式
            if (common.isWorkFlag()) {
                body = transfer.offLineBody();
            }

            // 1、清除缓存变量
            //clear();
            transfer.clear();//清除上一条任务的内部变量

            int batchCnt = 0;//清除每个线程处理话单数
            CalcRecordManager.setDealThreadCnt(0);//清除处理线程数

            // 开始时间戳
            Long startSec = getMicroSecond();
            Long startIntSec = getMicroSecond();

            body.init_response();

            //2、读取话单
            try {
                if (common.isWorkFlag()) {
                    bRet = transfer.inputRecord(common.getOffworkPath() + "\\" + common.getOffWorkFileName());
                } else {
                    bRet = transfer.inputRecord(body);
                }
                if (!bRet) {
                    log.error("读取话单失败");
                    return;
                }
            } catch (Exception e) {
                sErrorMsg = "transfer.inputRecord(body):" + e.getMessage();
                bRet = false;
            }

            if (transfer.getInRecords().size() == 0) {
                log.info("读取任务话单记录数为0");
                continue;
            }

            Long recordSize = new Long(transfer.getInRecords().size());
            Long readUseTimes = getMicroSecond() - startIntSec;

            Long lReadPerl = recordSize * 1000000 / (readUseTimes > 0 ? readUseTimes : 1);
            log.info("\n" + "读取记录数:" + transfer.getInRecords().size() + ",字节数:" + transfer.getInRecordStrSize() + "，耗时(微秒)：" + readUseTimes + ",性能：" + lReadPerl + ",话单源:" + body.FILE_NAME);

            if (transfer.getInRecords().size() <= common.getIMaxRecordCnt()) {
                batchCnt = transfer.getInRecords().size();
                CalcRecordManager calcRecordManager = calcList.get(0);
                calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.DEAL.getState());
                calcRecordManager.setInRecordList(transfer.getInRecords());

                fixedThreadPool.execute(calcRecordManager);
                CalcRecordManager.dealThreadCntPlusPlus();
            } else {
                batchCnt = transfer.getInRecords().size() / common.getIMaxProcessCnt();

                for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
                    CalcRecordManager calcRecordManager = calcList.get(i);
                    calcRecordManager.setDealState(CalcRecordManager.THREAD_STATE.DEAL.getState());
                    if ((i + 1) == common.getIMaxProcessCnt()) {
                        calcRecordManager.setInRecordList(transfer.getInRecords().subList(i * batchCnt, transfer.getInRecords().size()));
                    } else {
                        calcRecordManager.setInRecordList(transfer.getInRecords().subList(i * batchCnt, (i + 1) * batchCnt));
                    }

                    fixedThreadPool.execute(calcRecordManager);
                    CalcRecordManager.dealThreadCntPlusPlus();
                }
            }

            // 等待线程处理完成
            waitThreadOver(body);

            // flag 容器化部署，经常在这里后面就进程退出了  begin
            Long writeUseTimes = 0L;
            Long lWritePerl = 0L;

            // 输出话单
            if (common.isOutRecord() && "OK".equals(body.STATE)) {
                startIntSec = getMicroSecond();
                try {
                    log.info("transfer.outputRecord(body) begin");
                    if (common.isWorkFlag()) {
                        bRet = transfer.outputRecord(common.getOffworkPath() + "\\" + "OUT_" + common.getOffWorkFileName());
                    } else {
                        bRet = transfer.outputRecord(body);
                    }
                    log.info("transfer.outputRecord(body) end");
                } catch (Exception e) {
                    sErrorMsg = "transfer.outputRecord(body):" + e.getMessage();
                    log.error(sErrorMsg);
                    body.STATE = "ERROR";
                    body.ERROR_MSG = sErrorMsg;
                    bRet = false;
                }
                if (!bRet) {
                    if (!common.isWorkFlag()) {
                        log.info("transfer.outputRecord(body) end false");
                        taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
                        if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                            log.error("输出话单文件失败，退出");
                            break;
                        }
                    } else {
                        log.info("body:" + body);
                        Factory.seteExit(true, true);//离线模式只执行一次
                    }
                    continue;
                }

                writeUseTimes = getMicroSecond() - startIntSec;
                lWritePerl = recordSize * 1000000 / (writeUseTimes > 0 ? writeUseTimes : 1);
                if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                    log.info("\n" + "写入记录数:" + transfer.getOutRecords().size() + ",字节数:" + transfer.getOutRecordStrSize() + "，耗时(微秒)：" + writeUseTimes + "，性能：" + lWritePerl);
                } else {
                    log.info("\n" + "写入记录数:" + transfer.getOutRecords().size() + ",字节数:" + transfer.getOutRecordStrSize() + "，耗时(微秒)：" + writeUseTimes + "，性能：" + lWritePerl + ",tag:" + body.FILE_NAME);
                }


                // flag 容器化部署，经常在这里后面就进程退出了  end
            }

            // 线程收尾处理
            if (!OnOverThread(body)) {
                body.STATE = "ERROR";
                body.ERROR_MSG = "OnOverThread(body) 失败," + sErrorMsg;
                log.error(body.ERROR_MSG);
            }

            // 消息包填写
            body.UPDATE_DATE = Factory.getSystemDateStr();
            body.RECORD_CNT = String.valueOf(transfer.getOutRecords().size());
            // 性能统计
            Long endSec = getMicroSecond();
            Long lPerl = (recordSize * 1000000) / ((endSec - startSec) > 0 ? (endSec - startSec) : 1);
            body.DEAL_TIME = String.valueOf(endSec - startSec);

            String sPrint = new String();
            Map<Integer, FunctionPerl> mPerlLow = new HashMap<>();
            Map<Integer, FunctionPerl> mPerlTmp = calcList.get(0).getFunctionPerl();
            if (mPerlTmp != null) {
                // 输出单个话单文件因子性能
                sPrint = "\n" + "-----因子性能------------------------------------------------------------------";
                for (Map.Entry<Integer, FunctionPerl> iter : mPerlTmp.entrySet()) {
                    sPrint += "\n" + iter.getValue().toString();
                    if (mPerlLow.get(iter.getKey()) == null) {
                        mPerlLow.put(iter.getKey(), iter.getValue());
                    } else {
                        FunctionPerl tmp = mPerlLow.get(iter.getKey());
                        tmp.setPlusCntPerl(iter.getValue().getCnt(), iter.getValue().getDt());
                    }
                }
                log.info(sPrint);

                mPerlTmp.clear();

                // 每超过5000条话单输出一次性能慢函数到表
                iCurrCnt += transfer.getInRecords().size();
                if (iCurrCnt >= common.getIPerlOutCnt()) {
                    sPrint = "\n" + "----当前处理:" + iCurrCnt + " 条话单,平均性能低于:" + common.getIPerlLow() + " 的因子如下 ---------";
                    for (Map.Entry<Integer, FunctionPerl> iter : mPerlLow.entrySet()) {
                        if (iter.getValue().getPerl() < common.getIPerlLow()) {
                            sPrint += "\n" + iter.toString();
                        }
                    }
                    log.info(sPrint);

                    iCurrCnt = 0;
                    mPerlLow.clear();
                }
            }

            // 重置线程状态为空闲
            resetThreadState();

            // 发送任务处理应答消息
            if (!common.isWorkFlag() && !common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
                taskMsgManager.offerResponseJosnTask(JSON.toJSONString(body));
            }

            // 收尾处理，清空话单。
            if ("OK".equals(body.STATE)) {
                transfer.onEnd(body);  //只有成功的任务，才能删除输入话单
            }

            if (common.isWorkFlag()) {
                log.info("body:" + body);
                Factory.seteExit(true, true);//离线模式只执行一次
            }


            sPrint = "\n" + "-----话单文件处理情况----------------------------------------------------------";
            sPrint += "\n" + "话单输入记录数:" + transfer.getInRecords().size() + ",输入字节数:" + transfer.getInRecordStrSize() + ",读取话单时长（微秒）：" + readUseTimes + ",读取性能：" + lReadPerl + ",输出记录数:" + transfer.getOutRecords().size() + ",输出字节数:" + transfer.getOutRecordStrSize() + ",写入话单时长（微秒）:" + writeUseTimes + ",写入性能:" + lWritePerl;
            sPrint += "\n" + "PROCESS_ID:" + zkClientApi.getProcessID() + ",话单文件名:" + body.FILE_NAME + ",输入话单数:" + transfer.getInRecords().size() + ",输出话单数:" + transfer.getOutRecords().size() + ",处理时长:" + (endSec - startSec) + "微秒,处理线程数:" + CalcRecordManager.getDealThreadCnt() + ",每个线程处理:" + batchCnt
                    + ",性能：" + lPerl;
            sPrint += "\n" + "-------------------------------------------------------------------------------";
            log.info(sPrint);


            if ("ERROR".equals(body.STATE) && common.isErrorExit()) {
                log.error("任务失败，退出");
                break;
            }
        }

        // 执行退出操作
        exit();

        log.info("run() exit");
    }

    public void setClazzTransferCommon(Class<? extends TransferCommon> clazzTransferCommon) {
        this.clazzTransferCommon = clazzTransferCommon;
    }

    public void setClazzCalcRecordManager(Class<? extends CalcRecordManager> clazzCalcRecordManager) {
        this.clazzCalcRecordManager = clazzCalcRecordManager;
    }

    public void setTransferClassName(String transferClassName) {
        this.transferClassName = transferClassName;
    }

    boolean exit() {
        // 关闭所有处理子线程
        for (int i = 0; i < common.getIMaxProcessCnt(); i++) {
            CalcRecordManager calcRecordManager = calcList.get(i);
            calcRecordManager.OnExit();
        }

        // 关闭话单读写接口
        transfer.close();

        // 关闭任务获取接口
        if (!common.isTaskMsgFlag()) {
            taskMsgManager.close();
            // 关闭消息循环
            Factory.setbWhileMsgFlag(true);
        }

        // 关闭zookeeper
        zkClientApi.close();

        // 关闭springboot
        Factory.close();
        return true;
    }


}
