package com.itco.framework.record.impl.settle.impl;

import com.alibaba.fastjson.JSONObject;
import com.ctg.mq.api.bean.MQResult;
import com.itco.component.ctgmq.CtgMqPullApi;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.record.impl.settle.TransferSettle;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: TransferCtgMQ
 * @Description: ctgMq流转话单
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
public class TransferCtgMQ extends TransferSettle {
    static Log log = LogFactory.getLog(TransferCtgMQ.class);

    CtgMqPullApi ctgMqPullApi = new CtgMqPullApi();
    List<MQResult> mqResults = new ArrayList<>();

    @Override
    public boolean init() {
        ctgMqPullApi.setZkClientApi(zkClientApi);
        ctgMqPullApi.setCommon(common);
        // 消息类初始化
        if (!ctgMqPullApi.init()) {
            log.error("ctgMqPullApi.init() 失败");
            return false;
        }

       /* List<MQResult> mqResultTmps = new ArrayList<>();
        if (!ctgMqPullApi.recviceRecords(mqResultTmps)) {
            log.error("init()->ctgMqPullApi.recviceRecords() faile");
            return false;
        }*/

        //ctgMqPullApi.ackRecordMessages(mqResultTmps);
        //log.info("TransferCtgMQ.init() 初始化，清空消费者组:" + ctgMqPullApi.getRecRecordTopicName() + ",记录数:" + mqResultTmps.size());

        return true;
    }

    @Override
    public boolean mkdirByNoExist(String path, boolean flag) {
        return true;
    }

    @Override
    public boolean inputRecord(MsgBody.StandardBody body) {
        List<MQResult> mqResultTmps = new ArrayList<>();
        mqResults.clear();

        if (!ctgMqPullApi.recviceRecords(mqResultTmps)) {
            body.ERROR_MSG = "inputRecord()->recviceRecords() faile";
            body.STATE = "ERROR";
            return false;
        }

        int iSize = 0;
        for (int i = 0; i < mqResultTmps.size(); i++) {
            String str = new String(mqResultTmps.get(i).getMessage().getBody());

            List<String> listTemp = JSONObject.parseArray(str, String.class);
            inRecords.addAll(listTemp);
            iSize += str.length();
        }
        inRecordStrSize = iSize;
        mqResults.addAll(mqResultTmps);
        return true;
    }

    @Override
    public boolean outputRecord(MsgBody.StandardBody body) {
        String moduleId;
        if (!common.isWorkFlag()) {
            moduleId = zkClientApi.getModuleID();
        } else {
            moduleId = String.valueOf(Factory.getModule_id());
        }

        if (body.FILE_NAME.lastIndexOf(".") <= 0) {
            body.FILE_NAME = body.FILE_NAME + "." + moduleId;
        } else {
            String name = body.FILE_NAME.substring(0, body.FILE_NAME.lastIndexOf("."));
            body.FILE_NAME = name + "." + moduleId;
        }

        for (int j = 0; j < outRecords.size(); j += common.getMqOnceCnt()) {
            if (j + common.getMqOnceCnt() < outRecords.size()) {
                List<String> lists = outRecords.subList(j, j + common.getMqOnceCnt());
                ctgMqPullApi.sendRecords(lists);
            } else {
                ctgMqPullApi.sendRecords(outRecords.subList(j, outRecords.size()));
            }
        }
        return true;
    }

    @Override
    public boolean outputErrorRecord(MsgBody.StandardBody body) {
        return true;
    }

    @Override
    public boolean outputRecord(String fileName, List<String> list) {
        log.info("ctgMq outputRecord begin");

        for (int j = 0; j < list.size(); j += common.getMqOnceCnt()) {
            if (j + common.getMqOnceCnt() < inRecords.size()) {
                ctgMqPullApi.sendRecords(inRecords.subList(j, j + common.getMqOnceCnt()));
            } else {
                ctgMqPullApi.sendRecords(inRecords.subList(j, inRecords.size()));
            }
        }
        log.info("ctgMq outputRecord end");
        return true;
    }

    public int SkipRecordCnt() {
        return 0;
    }

    @Override
    public boolean onEnd(MsgBody.StandardBody body) {
        ctgMqPullApi.ackRecordMessages(mqResults);
        log.info("应答话单包数：" + mqResults.size());

        return true;
    }

    @Override
    public int isExistsKey(String key, String field) {
        return 0;
    }

    @Override
    public boolean close() {
        if (ctgMqPullApi != null) {
            ctgMqPullApi.close();
        }
        return true;
    }
}
