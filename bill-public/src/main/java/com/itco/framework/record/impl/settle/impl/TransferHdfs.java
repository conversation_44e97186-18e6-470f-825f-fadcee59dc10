//package com.itco.framework.record.impl.settle.impl;
//
//import com.alibaba.fastjson.JSONException;
//import com.alibaba.fastjson.JSONObject;
//import com.itco.component.hdfs.HDFSUtil;
//import com.itco.entity.process.MsgBody;
//import com.itco.framework.Factory;
//import com.itco.framework.record.impl.settle.TransferSettle;
//import lombok.Data;
//import org.apache.commons.logging.Log;
//import org.apache.commons.logging.LogFactory;
//import org.apache.hadoop.fs.FSDataInputStream;
//import org.apache.hadoop.fs.FSDataOutputStream;
//import org.apache.hadoop.fs.Path;
//
//import java.io.BufferedReader;
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Properties;
//
///*
//@ClassName: TransferHdfs
//@Description: 话单通过hdfs文件流转
//<AUTHOR>
//@Date 2021/8/9
//@Version 1.0
//**/
//@Data
//public class TransferHdfs extends TransferSettle {
//    Log log = LogFactory.getLog(TransferHdfs.class);
//
//    //处理完的输入文件，是否删除开关。
//    static boolean bDelFileFlag = false;
//    //跳过字段
//    List<String> skipFieldList = new ArrayList<>();
//    //跳过话单存放
//    List<String> skipRecords = new ArrayList<>();
//    //话单文件读取
//    HDFSUtil hdfsUtil = null;
//    //输入文件全路径
//    String inputFile = null;
//
//    String kerberos = "/config";
//
//    public TransferHdfs() {
//    }
//
//    public TransferHdfs(String path) {
//        kerberos = path;
//    }
//
//    @Override
//    public boolean init() {
//        String propFileName = zkClientApi.getModuleCode() + ".properties";
//        Properties p = zkClientApi.getPropertiesFromZK(propFileName);
//        if (p != null) {
//            if (null != p.getProperty("del_file_flag")) {   //是否删除前序话单文件
//                if (p.getProperty("del_file_flag").equals("true")) {
//                    bDelFileFlag = true;
//                }
//            }
//            if (null != p.getProperty("skip_field_name")) {  //跳过话单文件记录中存在的字段。保存到文件末尾
//                String[] tmp = p.getProperty("skip_field_name").split(",");
//                for (int i = 0; i < tmp.length; i++) {
//                    skipFieldList.add(tmp[i]);
//                }
//            }
//        }
//
//       /* //不需要认证的模式
//       Properties hdfsProperties = zkClientApi.getPropertiesFromZK(hdfs);
//        if (null != hdfsProperties.getProperty("fs.defaultFS")) {
//            defaultFS = hdfsProperties.getProperty("fs.defaultFS");
//        }
//        if (null != hdfsProperties.getProperty("fs.hdfs.impl")) {
//            hdfsImpl = hdfsProperties.getProperty("fs.hdfs.impl");
//        }
//
//        Configuration conf = new Configuration();
//        //如果没有把配置文件加入bin文件夹，那么需要加入下面两行
//        conf.set("fs.defaultFS", defaultFS);
//        conf.set("fs.hdfs.impl", hdfsImpl);
//        try {
//            fileSystem = FileSystem.get(conf);
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        }*/
//
//        hdfsUtil = new HDFSUtil(kerberos);
//        if (null == hdfsUtil.getHdfs()) {
//            log.error("fileSystem 初始化失败");
//            return false;
//        }
//        return true;
//    }
//
//    @Override
//    public boolean mkdirByNoExist(String path, boolean flag) {
//        try {
//            String f = path;
//            Path pathTmp = new Path(f);
//            if (hdfsUtil.getHdfs().exists(pathTmp)) {
//                return true;
//            }
//
//            log.info(f + ",不存在。");
//            if (flag) {
//                log.info("创建目录：" + f);
//                if (!hdfsUtil.getHdfs().mkdirs(new Path(path))) {
//                    log.info("创建目录失败：" + f);
//                    return false;
//                } else {
//                    return true;
//                }
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//            sErrorMsg = e.getMessage();
//        }
//        return false;
//    }
//
//    @Override
//    public boolean inputRecord(MsgBody.StandardBody body) {
//        inRecordStrSize = 0;
//        if (!common.isWorkFlag()) {
//            inputFile = body.LOCAL_INPUT_PATH + "/" + body.FILE_NAME;
//        } else {
//            inputFile = body.LOCAL_INPUT_PATH + "\\" + body.FILE_NAME;
//        }
//        String recordStr = new String();
//        try {
//            log.info("读取文件：" + inputFile);
//            FSDataInputStream os = hdfsUtil.getHdfs().open(new Path(inputFile));
//            BufferedReader bd = new BufferedReader(new InputStreamReader(os));
//            int iSize = 0;
//            // 一行一行读取
//            while ((recordStr = bd.readLine()) != null) {
//                if ((!recordStr.equals("")) && recordStr.length() > 0) {
//                    JSONObject tmpRecord = JSONObject.parseObject(recordStr);
//                    if (tmpRecord != null) {
//                        if (skipFieldList.size() > 0) { //有配置跳过非话单记录，"TICKET_COUNT"
//                            for (String skipName : skipFieldList) {
//                                if (tmpRecord.containsKey(skipName)) {
//                                    skipRecords.add(recordStr);
//                                } else {
//                                    inRecords.add(recordStr);
//                                    iSize += recordStr != null ? recordStr.length() : 0;
//                                }
//                            }
//                        } else {
//                            inRecords.add(recordStr);
//                            iSize += recordStr != null ? recordStr.length() : 0;
//                        }
//                    }
//                }
//            }
//            bd.close();
//            os.close();
//            inRecordStrSize = iSize;
//        } catch (FileNotFoundException e) {
//            sErrorMsg = "话单文件不存在：" + inputFile + ",msg:" + e.getMessage();
//            body.STATE = "ERROR";
//        } catch (IOException e) {
//            sErrorMsg = "读取文件失败：" + inputFile + ",msg:" + e.getMessage();
//            body.STATE = "ERROR";
//        } catch (JSONException e) {
//            log.error("JSONException :" + e.getMessage());
//            log.error("话单内容:" + recordStr);
//            body.STATE = "ERROR";
//        }
//
//        if (body.STATE.equals("ERROR")) {
//            log.info(sErrorMsg);
//            body.ERROR_MSG = sErrorMsg;
//            return false;
//        }
//        return true;
//    }
//
//    @Override
//    public boolean outputRecord(MsgBody.StandardBody body) {
//        outRecordStrSize = 0;
//        String writePath = new String();
//        try {
//            String moduleId;
//            if (!common.isWorkFlag()) {
//                moduleId = zkClientApi.getModuleID();
//            } else {
//                moduleId = String.valueOf(Factory.getModule_id());
//            }
//            if (body.FILE_NAME.lastIndexOf(".") <= 0) {
//                body.FILE_NAME = body.FILE_NAME + "." + moduleId;
//            } else {
//                String name = body.FILE_NAME.substring(0, body.FILE_NAME.lastIndexOf("."));
//                body.FILE_NAME = name + "." + moduleId;
//            }
//
//            // 写话单文件
//            writePath = body.LOCAL_OUTPUT_PATH + "/" + body.FILE_NAME;
//            System.out.println("写入文件：" + writePath);
//            FSDataOutputStream os = hdfsUtil.getHdfs().create(new Path(writePath));
//            int iSize = 0;
//            for (String rec : outRecords) {
//                os.write(rec.getBytes());
//                os.write("\n".getBytes());
//                iSize += rec != null ? rec.length() : 0;
//            }
//            // 写过滤记录到话单末尾
//            for (String skpStr : skipRecords) {
//                os.write(skpStr.getBytes());
//                os.write("\n".getBytes());
//            }
//
//            os.close();
//            outRecordStrSize = iSize;
//        } catch (IOException e) {
//            sErrorMsg = e.getMessage() + ":" + writePath;
//            body.STATE = "ERROR";
//            return false;
//        }
//        return true;
//    }
//
//    @Override
//    public boolean outputRecord(String fileName, List<String> list) {
//        outRecordStrSize = 0;
//        log.info("hdfs outputRecord begin");
//        try {
//            System.out.println("hdfsUtil.getHdfs().create begin ,writePath:" + fileName);
//            FSDataOutputStream os = hdfsUtil.getHdfs().create(new Path(fileName));
//            System.out.println("hdfsUtil.getHdfs().create end");
//
//            int iSize = 0;
//            for (String str : list) {
//                os.write(str.getBytes());
//                os.write("\n".getBytes());
//                iSize += str != null ? str.length() : 0;
//            }
//            os.close();
//            outRecordStrSize = iSize;
//        } catch (IOException e) {
//            log.error("IOException:" + e.getMessage());
//            return false;
//        }
//        log.info("hdfs outputRecord end");
//        return true;
//    }
//
//    public int SkipRecordCnt() {
//        return skipRecords.size();
//    }
//
//    @Override
//    public boolean onEnd(MsgBody.StandardBody body) {
//        //处理完成的文件删除
//        if (bDelFileFlag && (!common.isWorkFlag())) {
//            try {
//                System.out.println("inputFile:" + inputFile);
//                if (hdfsUtil.getHdfs().isFile(new Path(inputFile)) && hdfsUtil.getHdfs().exists(new Path(inputFile))) {
//                    System.out.println("删除文件：" + inputFile);
//                    hdfsUtil.getHdfs().delete(new Path(inputFile), true);
//                    inputFile = null;
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//                return false;
//            }
//        }
//        return true;
//    }
//
//    @Override
//    public Long findRecordFileCount(String key) {
//        return null;
//    }
//
//    @Override
//    public boolean close() {
//        try {
//            hdfsUtil.getHdfs().close();
//        } catch (IOException e) {
//            e.printStackTrace();
//            return false;
//        }
//        return true;
//    }
//
//}
//
