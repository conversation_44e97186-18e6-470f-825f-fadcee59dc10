package com.itco.framework.record.impl.settle;

import com.itco.entity.process.MsgBody;
import com.itco.framework.record.Transfer;

import java.util.List;

public abstract class TransferSettle extends Transfer {

    @Override
    public boolean init() {
        return true;
    }

    /*
     * @decription 创建不存在的目录
     * @param null
     * @return
     * <AUTHOR>
     * @createDate 2022/6/9
     */
    public abstract boolean mkdirByNoExist(String path, boolean flag);

    /**
     * 读取话单
     *
     * @param body
     * @return 成功 失败
     */
    public abstract boolean inputRecord(MsgBody.StandardBody body);

    /**
     * 输出话单
     *
     * @param body
     * @return 成功 失败
     */
    public abstract boolean outputRecord(MsgBody.StandardBody body);

    /**
     * 输出出错话单
     *
     * @param body
     * @return 成功 失败
     */
    public abstract boolean outputErrorRecord(MsgBody.StandardBody body);

        /**
         * 输出话单，接收模块用
         *
         * @param fileName
         * @param list
         * @return
         */
    public abstract boolean outputRecord(String fileName, List<String> list);



    public abstract int SkipRecordCnt();

    /**
     * @param body
     * @return
     */
    public abstract boolean onEnd(MsgBody.StandardBody body);

    /**
     * 离线话单模式文件读取
     *
     * @return
     */
    public MsgBody.StandardBody offLineBody() {
        MsgBody.StandardBody body = new MsgBody.StandardBody();
        body.LOCAL_INPUT_PATH = common.getOffworkPath();
        body.LOCAL_OUTPUT_PATH = common.getOffworkPath();
        body.FILE_NAME = common.getOffWorkFileName();
        return body;
    }

    /*
     * @decription 缓存查询专用，用来查询福建结算缓存话单文件是否存在
     * @param null:
     * @return
     * <AUTHOR>
     * @createDate 2022/8/20
     */
    public abstract int isExistsKey(String key,String field);
}
