package com.itco.framework.record.impl.settle.impl;

import com.itco.entity.process.MsgBody;
import com.itco.framework.record.impl.settle.TransferSettle;

import java.util.List;

/**
 * @ClassName: TransferAsn1File
 * @Description: 话单通过asn.1文件流转
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
public class TransferAsn1File extends TransferSettle {

    @Override
    public boolean init() {
        return true;
    }

    @Override
    public boolean mkdirByNoExist(String path, boolean flag) {
        return true;
    }

    @Override
    public boolean inputRecord(MsgBody.StandardBody body) {
        return true;
    }

    @Override
    public boolean outputRecord(MsgBody.StandardBody body) {
        return true;
    }

    @Override
    public boolean outputErrorRecord(MsgBody.StandardBody body) {
        return true;
    }

    @Override
    public boolean outputRecord(String fileName, List<String> list) {
        return true;
    }

    public int SkipRecordCnt() {
        return 0;
    }

    @Override
    public boolean onEnd(MsgBody.StandardBody body) {
        return true;
    }

    @Override
    public int isExistsKey(String key, String field) {
        return 0;
    }

    @Override
    public boolean close() {
        return true;
    }
}
