package com.itco.framework.record.impl.settle.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.record.impl.settle.TransferSettle;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * @ClassName: TransferFile
 * @Description: 文件流转话单
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
@Data
public class TransferFile extends TransferSettle {
    Log log = LogFactory.getLog(TransferFile.class);

    //处理完的输入文件，是否删除开关。
    static boolean bDelFileFlag = false;
    //跳过字段
    List<String> skipFieldList = new ArrayList<>();
    //跳过话单存放
    List<String> skipRecords = new ArrayList<>();
    //话单文件读取
    BufferedReader recordReader = null;
    //话单文件输出
    BufferedWriter recordWriter = null;
    //话单错误输出
    BufferedWriter errorWriter = null;
    //输入文件全路径
    String inputFile = null;

    String sErrorMsg;

    String homePath = "";

    @Override
    public boolean init() {
        String propFileName = zkClientApi.getModuleCode() + ".properties";
        Properties p = zkClientApi.getPropertiesFromZK(propFileName);

        if (p != null) {
            if (null != p.getProperty("del_file_flag")) {   //是否删除前序话单文件
                if (p.getProperty("del_file_flag").equals("true")) {
                    bDelFileFlag = true;
                }
            }
            if (null != p.getProperty("skip_field_name")) {  //跳过话单文件记录中存在的字段。保存到文件末尾
                String[] tmp = p.getProperty("skip_field_name").split(",");
                for (int i = 0; i < tmp.length; i++) {
                    skipFieldList.add(tmp[i]);
                }
            }
        }

        if (!common.isWorkFlag()) {
            homePath = System.getenv("HOME");
            if (StringUtils.isEmpty(homePath)) {
                log.error("主机环境变量HOME路径未设置或者为空");
                return false;
            }
        } 

        return true;
    }

    @Override
    public boolean mkdirByNoExist(String path, boolean flag) {
        String f = path;
        File fileTmp = new File(f);
        if (fileTmp.exists()) {
            return true;
        }
        log.info(f + ",不存在。");
        if (flag) {
            log.info("创建目录：" + f);
            if (!fileTmp.mkdirs()) {
                log.info("创建目录失败：" + f);
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    //从话单文件读取话单
    @Override
    public boolean inputRecord(MsgBody.StandardBody body) {
        skipRecords.clear();
        if (!common.isWorkFlag()) {
            inputFile = homePath + body.LOCAL_INPUT_PATH + "/" + body.FILE_NAME;
        } else {
            inputFile = body.LOCAL_INPUT_PATH + "\\" + body.FILE_NAME;
        }
        String recordStr = new String();
        int iSize = 0;
        try {
            recordReader = new BufferedReader(new FileReader(inputFile));
            // 一行一行读取
            while ((recordStr = recordReader.readLine()) != null) {
                if ((!recordStr.equals("")) && recordStr.length() > 0) {
                    JSONObject tmpRecord = JSONObject.parseObject(recordStr);
                    if (tmpRecord != null) {
                        if (skipFieldList.size() > 0) { //有配置跳过非话单记录，"TICKET_COUNT"
                            for (String skipName : skipFieldList) {
                                if (tmpRecord.containsKey(skipName)) {
                                    skipRecords.add(recordStr);
                                } else {
                                    inRecords.add(recordStr);
                                    iSize += recordStr != null ? recordStr.length() : 0;
                                }
                            }
                        } else {
                            inRecords.add(recordStr);
                            iSize += recordStr != null ? recordStr.length() : 0;
                        }
                    }
                }
            }
            inRecordStrSize = iSize;
        } catch (FileNotFoundException e) {
            sErrorMsg = "话单文件不存在：" + inputFile + ",msg:" + e.getMessage();
            body.STATE = "ERROR";
        } catch (IOException e) {
            sErrorMsg = "读取文件失败：" + inputFile + ",msg:" + e.getMessage();
            body.STATE = "ERROR";
        } catch (JSONException e) {
            log.error("JSONException :" + e.getMessage());
            log.error("话单内容:" + recordStr);
            body.STATE = "ERROR";
        } finally {
            try {
                if (recordReader != null) {
                    recordReader.close();
                    recordReader = null;
                }
            } catch (IOException e1) {
                sErrorMsg = "IOException：" + inputFile + e1.getMessage();
                log.info(sErrorMsg);
                body.STATE = "ERROR";
            }
        }

        if (body.STATE.equals("ERROR")) {
            log.info(sErrorMsg);
            body.ERROR_MSG = sErrorMsg;
            return false;
        }
        return true;
    }

    @Override
    public boolean outputRecord(MsgBody.StandardBody body) {
        try {
            String moduleId;
            if (!common.isWorkFlag()) {
                moduleId = zkClientApi.getModuleID();
            } else {
                moduleId = String.valueOf(Factory.getModule_id());
            }

            if (body.FILE_NAME.lastIndexOf(".") <= 0) {
                body.FILE_NAME = body.FILE_NAME + "." + moduleId;
            } else {
                String name = body.FILE_NAME.substring(0, body.FILE_NAME.lastIndexOf("."));
                body.FILE_NAME = name + "." + moduleId;
            }

            int iSize = 0;
            // 写话单文件
            String writePath = homePath + body.LOCAL_OUTPUT_PATH + "/" + body.FILE_NAME;
            recordWriter = new BufferedWriter(new FileWriter(writePath, false));
            for (String rec : outRecords) {
                iSize += rec != null ? rec.length() : 0;
                recordWriter.write(rec);
                recordWriter.newLine();
            }
            outRecordStrSize = iSize;
            // 写过滤记录到话单末尾
            for (String skpStr : skipRecords) {
                recordWriter.write(skpStr);
                recordWriter.newLine();
            }

        } catch (IOException e) {
            sErrorMsg = e.getMessage();
            body.STATE = "ERROR";
            return false;
        } finally {
            try {
                if (recordWriter != null) {
                    recordWriter.close();
                    recordWriter = null;
                }
            } catch (IOException e) {
                sErrorMsg = e.getMessage();
                body.STATE = "ERROR";
                return false;
            }
        }
        return true;
    }

    // 写错误单文件
    public boolean outputErrorRecord(MsgBody.StandardBody body) {
        try {
            String errorFileName = body.FILE_NAME + ".error";
            String errorPath = homePath + body.LOCAL_OUTPUT_PATH + "/" + errorFileName;
            errorWriter = new BufferedWriter(new FileWriter(errorPath, false));
            for (String rec : outErrRecords) {
                errorWriter.write(rec);
                errorWriter.newLine();
            }
        } catch (IOException e) {
            sErrorMsg = e.getMessage();
            body.STATE = "ERROR";
            return false;
        } finally {
            try {
                if (errorWriter != null) {
                    errorWriter.close();
                    errorWriter = null;
                }
            } catch (IOException e) {
                sErrorMsg = e.getMessage();
                body.STATE = "ERROR";
                return false;
            }
        }
        return true;
    }


    public boolean outputRecord(String fileName, List<String> list) {
        try {
            int iSize = 0;
            recordWriter = new BufferedWriter(new FileWriter(fileName, false));
            for (String str : list) {
                iSize += str != null ? str.length() : 0;
                recordWriter.write(str);
                recordWriter.newLine();
            }
            outRecordStrSize = iSize;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (recordWriter != null) {
                    recordWriter.close();
                    recordWriter = null;
                }
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }

        }

        return true;
    }

    public int SkipRecordCnt() {
        return skipRecords.size();
    }


    @Override
    public boolean onEnd(MsgBody.StandardBody body) {
        //处理完成的文件删除
        if (bDelFileFlag && (!common.isWorkFlag())) {
            File fileTmp = new File(inputFile);
            if (fileTmp.isFile() && fileTmp.exists()) {
                fileTmp.delete();
                inputFile = null;
            }
        }

        return true;
    }

    @Override
    public int isExistsKey(String key, String field) {
        return 0;
    }

    @Override
    public boolean close() {
        return true;
    }
}


