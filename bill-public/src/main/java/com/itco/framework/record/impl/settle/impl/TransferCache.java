package com.itco.framework.record.impl.settle.impl;

import com.alibaba.fastjson.JSONObject;
import com.itco.component.ctgcache.JEDISUtil;
import com.itco.component.ctgcache.JedisException;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.record.impl.settle.TransferSettle;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Properties;

/**
 * @ClassName: TransferCache
 * @Description: 分布式缓存流转话单
 * <AUTHOR>
 * @Date 2021/9/30
 * @Version 1.0
 */
@Data
public class TransferCache extends TransferSettle {
    static Log log = LogFactory.getLog(TransferCache.class);

    //处理完的输入文件，是否删除开关。
    static boolean bDelFileFlag = false;
    static String database = "databaseRecord";

    // 缓存接口
    JEDISUtil jedisUtil = new JEDISUtil();

    //输入文件全路径
    String inputFile = null;

    String sErrorMsg;

    @Override
    public boolean init() {
        String propFileName = zkClientApi.getModuleCode() + ".properties";
        Properties p = zkClientApi.getPropertiesFromZK(propFileName);

        if (p != null) {
            if (null != p.getProperty("del_file_flag")) {   //是否删除前序话单文件
                if (p.getProperty("del_file_flag").equals("true")) {
                    bDelFileFlag = true;
                }
            }
        }

        Properties propsCtg = zkClientApi.getPropertiesFromZK("ctgCache.properties");
        jedisUtil.setPropsCtg(propsCtg);
        jedisUtil.setDatabase(database);
        if (!jedisUtil.init()) {
            System.out.println("jedisUtil.init() faile");
            return false;
        }

        return true;
    }

    //从话单文件读取话单
    public boolean inputRecord(MsgBody.StandardBody body) {
        inRecordStrSize = 0;
        try {
            inputFile = body.FILE_NAME;

            String records = jedisUtil.getJedisClientApi().hGet(body.LOCAL_INPUT_PATH + "-" + inputFile, body.FILE_NAME);
            List<String> listTemp = JSONObject.parseArray(records, String.class);
            if (listTemp != null) {
                inRecords.addAll(listTemp);
                inRecordStrSize = records != null ? records.length() : 0;
            }
        } catch (JedisException e) {
            e.printStackTrace();
            log.error("75，" + e.getMessage());
            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                    e.getMessage().indexOf("JedisException") > -1||
                    e.getMessage().indexOf("errorCode:50010010") > -1) {
                try {
                    Thread.sleep(1000);
                    jedisUtil.getJedisClientApi().jedisReset();
                    String records = jedisUtil.getJedisClientApi().hGet(body.LOCAL_INPUT_PATH + "-" + inputFile, body.FILE_NAME);
                    List<String> listTemp = JSONObject.parseArray(records, String.class);
                    if (listTemp != null) {
                        inRecords.addAll(listTemp);
                        inRecordStrSize = records != null ? records.length() : 0;
                    }
                } catch (Exception ex) {
                    log.error("88，" + ex.getMessage());
                    sErrorMsg = "读取话单缓存失败：" + body.LOCAL_INPUT_PATH + "->" + body.FILE_NAME + ",msg:" + e.getMessage();
                    body.STATE = "ERROR";
                }
            } else {
                sErrorMsg = e.getStackTrace().toString();
                body.STATE = "ERROR";
            }
        }

        if (body.STATE.equals("ERROR")) {
            log.info(sErrorMsg);
            body.ERROR_MSG = sErrorMsg;
            return false;
        }
        return true;
    }

    @Override
    public boolean mkdirByNoExist(String path, boolean flag) {
        return true;
    }

    @Override
    public boolean outputRecord(MsgBody.StandardBody body) {
        outRecordStrSize = 0;
        String moduleId;
        if (!common.isWorkFlag()) {
            moduleId = zkClientApi.getModuleID();
        } else {
            moduleId = String.valueOf(Factory.getModule_id());
        }

        if (body.FILE_NAME.lastIndexOf(".") <= 0) {
            body.FILE_NAME = body.FILE_NAME + "." + moduleId;
        } else {
            String name = body.FILE_NAME.substring(0, body.FILE_NAME.lastIndexOf("."));
            body.FILE_NAME = name + "." + moduleId;
        }

        String records = JSONObject.toJSONString(outRecords);
        outRecordStrSize = records != null ? records.length() : 0;
        try {
            jedisUtil.getJedisClientApi().hSet(body.LOCAL_OUTPUT_PATH + "-" + body.FILE_NAME, body.FILE_NAME, records);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("135，" + e.getMessage());
            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                    e.getMessage().indexOf("JedisException") > -1||
                    e.getMessage().indexOf("errorCode:50010010") > -1) {
                try {
                    Thread.sleep(1000);
                    jedisUtil.getJedisClientApi().jedisReset();
                    jedisUtil.getJedisClientApi().hSet(body.LOCAL_OUTPUT_PATH + "-" + body.FILE_NAME, body.FILE_NAME, records);
                } catch (Exception ex) {
                    log.error("142，" + ex.getMessage());
                    sErrorMsg = "200，写话单缓存重新创建连接实例异常，任务失败：" + body.LOCAL_INPUT_PATH + "-" + body.FILE_NAME + "->" + body.FILE_NAME;
                    body.STATE = "ERROR";
                }
            } else {
                sErrorMsg = e.getStackTrace().toString();
                body.STATE = "ERROR";
            }
        }

        if (body.STATE.equals("ERROR")) {
            log.info(sErrorMsg);
            body.ERROR_MSG = sErrorMsg;
            return false;
        }
        return true;
    }

    @Override
    public boolean outputErrorRecord(MsgBody.StandardBody body) {
        // 写异常单文件
        if (outErrRecords.size() > 0) {
            //缓存处理，错单，写文件
            BufferedWriter errorWriter = null;
            String errorFileName = body.FILE_NAME + ".error";
            String errorPath = "/home/" + errorFileName;
            try {
                if (common.getIDebug() > 0) {
                    log.info("\n写错误文件：" + errorPath + ",记录数:" + outErrRecords.size());
                }

                errorWriter = new BufferedWriter(new FileWriter(errorPath, false));
                for (String rec : outErrRecords) {
                    errorWriter.write(rec);
                    errorWriter.newLine();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (errorWriter != null) {
                    try {
                        errorWriter.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    errorWriter = null;
                }
            }
        }

        return true;
    }

    public boolean outputRecord(String fileName, List<String> list) {
        String records = JSONObject.toJSONString(list);
        outRecordStrSize = records != null ? records.length() : 0;
        try {
            jedisUtil.getJedisClientApi().hSet(common.getRecordCacheHashkey() + "-" + fileName, fileName, records);
        } catch (Exception e) {
            log.error("164：" + e.getMessage());
            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                    e.getMessage().indexOf("JedisException") > -1||
                    e.getMessage().indexOf("errorCode:50010010") > -1) {
                try {
                    Thread.sleep(1000);
                    jedisUtil.getJedisClientApi().jedisReset();
                    jedisUtil.getJedisClientApi().hSet(common.getRecordCacheHashkey() + "-" + fileName, fileName, records);
                } catch (Exception ex) {
                    log.error("173，" + ex.getMessage());
                    log.error("写话单缓存超时捕获异常:" + e.getMessage() + "，重新写话单再捕获异常，任务失败：" + common.getRecordCacheHashkey() + "->" + fileName);
                    return false;
                }
            } else {
                log.info("写话单缓存异常");
                return false;
            }
        }
        log.info("写话单缓存字节数：" + outRecordStrSize);
        return true;
    }

    @Override
    public int SkipRecordCnt() {
        return 0;
    }


    @Override
    public boolean onEnd(MsgBody.StandardBody body) {
        log.info("2.2 -------");
        //处理完成的文件删除
        if (bDelFileFlag && (!common.isWorkFlag())) {
            log.info("2.3 ------------");
            try {
                boolean b = jedisUtil.getJedisClientApi().deleteHashkey(body.LOCAL_INPUT_PATH + "-" + inputFile, inputFile);
                log.info("删除状态：" + b);
            } catch (Exception e) {
                if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                        e.getMessage().indexOf("java.net.SocketException") > -1 ||
                        e.getMessage().indexOf("JedisException") > -1||
                        e.getMessage().indexOf("errorCode:50010010") > -1)
                    try {
                        Thread.sleep(1000);
                        jedisUtil.getJedisClientApi().deleteHashkey(body.LOCAL_INPUT_PATH + "-" + inputFile, inputFile);
                    }catch (Exception ex) {
                        try {
                            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                                    e.getMessage().indexOf("JedisException") > -1||
                                    e.getMessage().indexOf("errorCode:50010010") > -1) {
                                jedisUtil.getJedisClientApi().jedisReset();
                                jedisUtil.getJedisClientApi().deleteHashkey(body.LOCAL_INPUT_PATH + "-" + inputFile, inputFile);
                            }
                        } catch (JedisException exception) {
                            exception.printStackTrace();
                            sErrorMsg = "onEnd();写话单缓存重新创建连接实例异常，任务失败：" + body.LOCAL_INPUT_PATH + "-" + inputFile + "->" + body.FILE_NAME;
                            body.STATE = "ERROR";
                            return false;
                        }
                    }
            }
        }

        return true;
    }

    @Override
    public int isExistsKey(String key, String field) {
        try {
            if (jedisUtil.getJedisClientApi().isExistsField(key, field)) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    @Override
    public boolean close() {
        jedisUtil.getJedisClientApi().close();
        return true;
    }
}


