package com.itco.framework.record;

import com.itco.framework.Factory;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.message.impl.TaskMsgCtgMq;
import com.itco.framework.message.impl.TaskMsgZk;
import com.itco.framework.record.impl.common.TransferCommon;
import com.itco.framework.record.impl.settle.TransferSettle;
import com.itco.framework.record.impl.settle.impl.TransferCache;
import com.itco.framework.record.impl.settle.impl.TransferCtgMQ;
import com.itco.framework.record.impl.settle.impl.TransferFile;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class TransferFactory {
    static Log log = LogFactory.getLog(TransferFactory.class);

    /*
     * @decription 实例化对象
     * @param name: 类名称
     * @return com.itco.process.record.common.TransferCommon
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public TransferCommon getTransferCommonInstance(String name) {
        Class<?> clazzTransferCommon = null;
        TransferCommon transferCommon = null;
        try {
            clazzTransferCommon = Class.forName(name);
            transferCommon = (TransferCommon) clazzTransferCommon.newInstance();
            return transferCommon;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + name + "," + e.getMessage());
            return null;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TransferCommon getTransferCommonInstance(Class<? extends TransferCommon> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + TransferCommon.class + "," + e.getMessage());
        }
        return null;
    }

    public TransferCommon getTransferCommonInstance(Class<? extends TransferCommon> clazz, String className) {
        TransferCommon transfer = null;

        if (null == clazz) {
            transfer = getTransferCommonInstance(className);
            if (null == transfer) {
                log.error("数据处理类实例化失败：" + className);
                return null;
            }
            log.info("数据处理类名：" + className);
        } else {
            transfer = getTransferCommonInstance(clazz);
            if (null == transfer) {
                log.error("数据处理类实例化失败：" + clazz.getName());
                return null;
            }
            log.info("数据处理类名：" + clazz.getName());
        }

        return transfer;
    }


    public TransferSettle getTransferSettleInstance(String className) {
        Class<?> clazzTransferSettle = null;
        TransferSettle transferSettle = null;
        try {
            clazzTransferSettle = Class.forName(className);
            transferSettle = (TransferSettle) clazzTransferSettle.newInstance();
            return transferSettle;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + className + "," + e.getMessage());
            return null;
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TransferSettle getTransferSettleInstance(Class<? extends TransferSettle> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + TransferSettle.class + "," + e.getMessage());
        }
        return null;
    }

    public TransferSettle getTransferSettleInstance(Class<? extends TransferSettle> clazz, String className,String mode) {
        TransferSettle transfer = null;
        if (clazz == null && className == null) {
            if (mode.equals("FILE")) {
                clazz = TransferFile.class;
            } else if (mode.equals("CTG_MQ")) {
                clazz = TransferCtgMQ.class;
            } else if (mode.equals("HDFS")) {
                //clazzTransferSettle = TransferHdfs.class;
            } else if (mode.equals("CTG_CACHE")) {
                clazz = TransferCache.class;
            } else {
                log.error("无法识别的话单流转方式：" + mode + ",请配置Common.properties文件(record_transfer_mode=FILE、CTG_MQ、HDFS)");
                return null;
            }
        }

        if (null == clazz) {
            transfer = getTransferSettleInstance(className);
            if (null == transfer) {
                log.error("数据处理类实例化失败：" + className);
                return null;
            }
            log.info("数据处理类名：" + className);
        } else {
            transfer = getTransferSettleInstance(clazz);
            if (null == transfer) {
                log.error("数据处理类实例化失败：" + clazz.getName());
                return null;
            }
            log.info("数据处理类名：" + clazz.getName());
        }

        return transfer;
    }

    public TransferSettle getTransferSettleInstanceByMode(String mode) {
        TransferSettle transfer = null;

        if (mode.equals("FILE")) {
            transfer = new TransferFile();
        } else if (mode.equals("CTG_MQ")) {
            transfer = new TransferCtgMQ();
        } else if (mode.equals("HDFS")) {
            //clazzTransferSettle = TransferHdfs.class;
        } else if (mode.equals("CTG_CACHE")) {
            transfer = new TransferCache();
        } else {
            log.error("无法识别的话单流转方式：" + mode + ",请配置Common.properties文件(record_transfer_mode=FILE、CTG_MQ、HDFS)");
            return null;
        }

        log.info("数据处理类名：" + mode);
        return transfer;
    }
}
