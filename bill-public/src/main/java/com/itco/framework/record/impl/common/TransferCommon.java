package com.itco.framework.record.impl.common;

import com.itco.entity.process.CommonBody;
import com.itco.framework.record.Transfer;
import lombok.Data;

/**
 * @ClassName: Transfer
 * @Description: 话单流转基础类
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
@Data
public abstract class TransferCommon extends Transfer {
    //protected ZkClientApi zkClientApi = null;
    //protected List<String> inRecords = new LinkedList<>();//读取的话单存放list
    //protected List<String> outRecords = new LinkedList<>();//输出的话单存放list
    //protected List<String> outErrRecords = new LinkedList<>();//输出的错误存放list
    //protected Common common = null;
    //protected int inRecordStrSize = 0;//话单文件字节数
    //protected int outRecordStrSize = 0;//话单文件字节数
    //protected String sErrorMsg;

    int outNormalCnt = 0;//输出正常记录数
    int outErrorCnt = 0;//输出错误记录数


    public void clear() {
        inRecords.clear();
        outRecords.clear();
        outErrRecords.clear();

        outNormalCnt = 0;
        outErrorCnt = 0;
    }

    /**
     * 初始化
     *
     * @return 成功 失败
     */
    public abstract boolean init();

    /**
     * 读取话单
     *
     * @param body
     * @return 成功 失败
     */
    public abstract boolean inputRecord(CommonBody body);


    /**
     * 输出话单
     *
     * @param body
     * @return 成功 失败
     */
    public abstract boolean outputRecord(CommonBody body);

    /**
     * @param body
     * @return
     */
    public abstract boolean onEnd(CommonBody body);

    /**
     * @return
     */
    public abstract boolean close();

    /**
     * 离线话单模式文件读取
     *
     * @return
     */
    public CommonBody offLineBody() {
        CommonBody body = new CommonBody();
        body.LOCAL_INPUT_PATH = common.getOffworkPath();
        body.LOCAL_OUTPUT_PATH = common.getOffworkPath();
        body.FILE_NAME = common.getOffWorkFileName();
        return body;
    }
}
