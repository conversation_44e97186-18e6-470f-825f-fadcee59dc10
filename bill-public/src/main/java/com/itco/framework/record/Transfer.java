package com.itco.framework.record;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.util.LinkedList;
import java.util.List;

/**
 * @ClassName: Transfer
 * @Description: 话单流转基础类
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
@Data
public abstract class Transfer {
    static Log log = LogFactory.getLog(Transfer.class);

    protected ZkClientApi zkClientApi = null;
    protected Common common = null;

    protected List<String> inRecords = new LinkedList<>();//读取的话单存放list
    protected List<String> outRecords = new LinkedList<>();//输出的话单存放list
    protected List<String> outErrRecords = new LinkedList<>();//输出的错误存放list

    protected int inRecordStrSize = 0;//话单文件字节数
    protected int outRecordStrSize = 0;//话单文件字节数
    protected String sErrorMsg;

    int outNormalCnt = 0;//输出正常记录数
    int outErrorCnt = 0;//输出错误记录数


    public void clear() {
        inRecords.clear();
        outRecords.clear();
        outErrRecords.clear();

        outNormalCnt = 0;
        outErrorCnt = 0;
    }

    /**
     * 初始化
     *
     * @return 成功 失败
     */
    public abstract boolean init();

    /**
     * @return
     */
    public abstract boolean close();

    public boolean inputRecord(String path) {
        log.info("Transfer inputRecord(" + path + ") begin");
        String recordStr = new String();
        int iSize = 0;

        BufferedReader recordReader = null;
        try {
            recordReader = new BufferedReader(new FileReader(path));
            // 一行一行读取
            while ((recordStr = recordReader.readLine()) != null) {
                if ((!recordStr.equals("")) && recordStr.length() > 0) {
                    JSONObject tmpRecord = JSONObject.parseObject(recordStr);
                    if (tmpRecord != null) {
                        inRecords.add(recordStr);
                        iSize += recordStr != null ? recordStr.length() : 0;
                    }
                }
            }
            inRecordStrSize = iSize;

            log.info("Transfer inputRecord(" + path + ") end");
            return true;
        } catch (FileNotFoundException e) {
            sErrorMsg = "话单文件不存在：" + path + ",msg:" + e.getMessage();
            log.error(sErrorMsg);
        } catch (IOException e) {
            sErrorMsg = "读取文件失败：" + path + ",msg:" + e.getMessage();
            log.error(sErrorMsg);
        } catch (JSONException e) {
            log.error("JSONException :" + e.getMessage());
            log.error("话单内容:" + recordStr);
        } finally {
            try {
                if (recordReader != null) {
                    recordReader.close();
                    recordReader = null;
                }
            } catch (IOException e1) {
                sErrorMsg = "IOException：" + path + e1.getMessage();
                log.error(sErrorMsg);
            }
        }
        return false;
    }


    public boolean outputRecord(String path) {
        log.info("Transfer outputRecord(" + path + ") end");

        BufferedWriter recordWriter = null;
        try {
            int iSize = 0;
            // 写话单文件
            recordWriter = new BufferedWriter(new FileWriter(path, false));
            for (String rec : outRecords) {
                iSize += rec != null ? rec.length() : 0;
                recordWriter.write(rec);
                recordWriter.newLine();
            }
            outRecordStrSize = iSize;

            log.info("Transfer outputRecord(" + path + ") end");
            return true;
        } catch (IOException e) {
            sErrorMsg = e.getMessage();
            return false;
        } finally {
            try {
                if (recordWriter != null) {
                    recordWriter.close();
                    recordWriter = null;
                }
            } catch (IOException e) {
                sErrorMsg = e.getMessage();
                return false;
            }
        }
    }
}
