package com.itco.framework.message.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ctg.mq.api.CTGMQFactory;
import com.ctg.mq.api.IMQProducer;
import com.ctg.mq.api.IMQPushConsumer;
import com.ctg.mq.api.PropertyKeyConst;
import com.ctg.mq.api.bean.MQMessage;
import com.ctg.mq.api.bean.MQResult;
import com.ctg.mq.api.exception.MQException;
import com.ctg.mq.api.exception.MQProducerException;
import com.ctg.mq.api.listener.ConsumerTopicListener;
import com.ctg.mq.api.listener.ConsumerTopicStatus;
import com.itco.component.jdbc.DecodePassword;
import com.itco.entity.common.TpProcessMQ;
import com.itco.entity.process.MsgBody;
import com.itco.framework.message.TaskMessage;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Properties;

/**
 * @ClassName: TaskMsgCtgMq
 * @Description: 使用ctgMq任务消息通讯
 * <AUTHOR>
 * @Date 2021/9/3
 * @Version 1.0
 */
public class TaskMsgCtgMq extends TaskMessage {
    Log log = LogFactory.getLog(TaskMsgCtgMq.class);
    String propertiesCtgMq = "ctgMq.properties";

    String taskSendTopicName;
    String taskReceiveTopicName;

    IMQProducer imqProducer = null;
    IMQPushConsumer imqPushConsumer = null;
    TpProcessMQ tpProcessMQ = null;

    @Override
    public boolean OnInit() {
        log.info("OnInit() start");

        tpProcessMQ = common.getProcessMQMap().get(zkClientApi.getProcessID());
        if (tpProcessMQ == null) {
            log.error("tp_process_mq表未配置，" + zkClientApi.getProcessID() + " 的MQ队列");
            return false;
        }

        taskReceiveTopicName = tpProcessMQ.getTask_consumer_topic();

        log.info("taskSendTopicName:" + taskSendTopicName);
        log.info("task_consumer_topic:" + taskReceiveTopicName);
        log.info("task_consumer_group:" + tpProcessMQ.getTask_consumer_group());

        Properties ctgProperties = zkClientApi.getPropertiesFromZK(propertiesCtgMq);
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName, ctgProperties.getProperty("producerGroupTaskName"));
        properties.setProperty(PropertyKeyConst.InstanceName, ctgProperties.getProperty("instanceName"));
        properties.setProperty(PropertyKeyConst.NamesrvAddr, ctgProperties.getProperty("namesrvAddr"));
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, ctgProperties.getProperty("authId"));

        String publicKeyTmp = ctgProperties.getProperty("publicKey");
        String passwordTmp = ctgProperties.getProperty("password");
        if (publicKeyTmp == null || passwordTmp == null) {
            log.error(propertiesCtgMq + "，未配置用户名密码，publicKey、password");
            return false;
        }
        String passwd = DecodePassword.decryption(publicKeyTmp, passwordTmp);
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, passwd);
        properties.setProperty(PropertyKeyConst.ClusterName, ctgProperties.getProperty("clusterName"));
        properties.setProperty(PropertyKeyConst.TenantID, ctgProperties.getProperty("tenantID"));
        if (null != ctgProperties.getProperty("filter_queue_size")) {
            filterQueueSize = Integer.parseInt(ctgProperties.getProperty("filter_queue_size"));
        }

        log.info(ctgProperties.getProperty("authId") + ":" + passwd);
        int result = -1;
        // 创建生产者
        imqProducer = CTGMQFactory.createProducer(properties);

        try {
            result = imqProducer.connect();
        } catch (MQException e) {
            e.printStackTrace();
            log.error("connect faile MQException:" + e.getExpDesc());
            return false;
        }
        if (result != 0) {
            log.error("producer.connect return: " + result);
            return false;
        }

        if (mode == 0) {
            if (StringUtils.isEmpty(tpProcessMQ.getTask_consumer_topic()) || StringUtils.isEmpty(tpProcessMQ.getTask_consumer_group())) {
                log.error("消费主题: " + tpProcessMQ.getTask_consumer_topic() + " ,消费者组：" + tpProcessMQ.getTask_consumer_group() + " 为空，初始化失败");
                return false;
            }
            properties.setProperty(PropertyKeyConst.ConsumerGroupName, tpProcessMQ.getTask_consumer_group());
            // 创建消费者
            imqPushConsumer = CTGMQFactory.createPushConsumer(properties);

            try {
                result = imqPushConsumer.connect();
            } catch (MQException e) {
                log.error("connect faile MQException" + e.getExpDesc());
                return false;
            }
            if (result != 0) {
                log.error("imqConsumer.connect return: " + result);
                return false;
            }
        }

        log.info("OnInit() end");
        return true;
    }


    @Override
    public boolean sendMessage(String process_id, String msg) {
        //MQMessage mqMessage = new MQMessage(taskSendTopicName, "", "10100000", msg.getBytes());
        TpProcessMQ tmp = common.getProcessMQMap().get(process_id);
        if (tmp == null) {
            log.error("tp_process_mq表不存在：" + process_id + " 进程的MQ配置");
            return false;
        }
        taskSendTopicName = tmp.getTask_consumer_topic();
        MQMessage mqMessage = new MQMessage(taskSendTopicName, "", process_id, msg.getBytes());

        try {
            imqProducer.send(mqMessage);
        } catch (MQProducerException e) {
            log.error(e.getMessage());
            return false;
        }
        return true;
    }


    @Override
    public boolean startReceivedMessage() {
        if (mode == 0) {
            try {
                imqPushConsumer.listenTopic(taskReceiveTopicName, "", new ConsumerMessage());
            } catch (MQException e) {
                log.error(e.getMessage());
                return false;
            }
        }

        return true;
    }


    class ConsumerMessage implements ConsumerTopicListener {
        @Override
        public ConsumerTopicStatus onMessage(List<MQResult> list) {
            for (MQResult result : list) {
                //TODO
                String json = new String(result.getMessage().getBody());
                if (fQueue.contains(json)) {
                    log.error("处理过的消息包，" + json);
                    continue;
                }
                fQueue.offer(json);
                while (fQueue.size() > filterQueueSize) {
                    fQueue.poll();
                }

                JSONObject object = JSONObject.parseObject(json);

                String processId = object.getString("PROCESS_ID");
                String taskType = object.getString("TASK_TYPE");
                if (processId == null) {
                    log.warn("异常消息，没有PROCESS_ID字段。msg:" + json);
                    continue;
                }

                if (taskType == null) {
                    g_ReceTaskQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_DBINCEPT.getType())) {
                    g_ReceDbInceptQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_RECORD.getType())) {
                    g_ReceTaskQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_CONFIG.getType())) {
                    g_ReceConfigQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_ROLLBACK.getType())) {
                    g_ReceRollbackQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_PROCESS.getType())) {
                    g_ReceProQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_DISPATCH.getType())) {
                    g_ReceHeartQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_SS_PROCESS.getType())) {
                    g_ReceSsProcessQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_DEBUG.getType())) {
                    g_ReceDebugQueue.offer(json);
                } else if (taskType.equals(MsgBody.TASK_TYPE.T_RECORD_STATE.getType())) {
                    g_ReceTaskStateQueue.offer(json);
                } else {
                    log.error("无法识别的消息类型：" + taskType + ",msg:" + json);
                    return ConsumerTopicStatus.CONSUME_SUCCESS;//对消息批量确认(成功)
                }
                //log.info("msg:" + json);
            }

            log.info("接收完成!总共：" + list.size() + " -> Process:" + g_ReceProQueue.size() +
                    ",dbincept:" + g_ReceDbInceptQueue.size() +
                    ",TaskRecord:" + g_ReceTaskQueue.size() +
                    ",Sync :" + g_ReceConfigQueue.size() +
                    ",rollback :" + g_ReceRollbackQueue.size() +
                    ",debug :" + g_ReceDebugQueue.size() +
                    ",control :" + g_ReceSsProcessQueue.size() +
                    ",state :" + g_ReceTaskStateQueue.size());

            return ConsumerTopicStatus.CONSUME_SUCCESS;//对消息批量确认(成功)
        }

    }

    public void close() {
        try {
            if (imqProducer != null) {
                imqProducer.close();
            }

            if (mode == 0 && imqPushConsumer != null) {
                imqPushConsumer.close();
            }
        } catch (MQException e) {
            log.error(e.getMessage());
        }
    }

}
