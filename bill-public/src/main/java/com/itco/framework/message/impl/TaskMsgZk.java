package com.itco.framework.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkMessageApi;
import com.itco.entity.common.TpModule;
import com.itco.entity.process.MsgBody;
import com.itco.framework.message.TaskMessage;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;

import java.util.List;

/**
 * @ClassName: TaskMsgZk
 * @Description: 使用zookeeper任务消息通讯
 * <AUTHOR>
 * @Date 2021/9/3
 * @Version 1.0
 */
public class TaskMsgZk extends TaskMessage {
    Log log = LogFactory.getLog(TaskMsgZk.class);

    ZkMessageApi zkMessageApi = new ZkMessageApi();

    @Override
    public boolean OnInit() {
        log.info("OnInit() start");

        zkMessageApi.setModuleCode(zkClientApi.getModuleCode());
        zkMessageApi.setProcessID(zkClientApi.getProcessID());
        zkMessageApi.setModuleIdMap(zkClientApi.getTableFromZK("tp_module", TpModule.class));
        if (!zkMessageApi.init(new ClientWatcher())) {
            log.info(zkMessageApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkMessageApi.getProcessID());
            return false;
        }

        log.info("zookeeper 初始化完成，process_id:" + zkMessageApi.getProcessID());

        String path = zkMessageApi.getMessageTask() + "/" + zkMessageApi.getModuleCode() + "-" + zkMessageApi.getProcessID();
        analysisMsg(path);

        log.info("OnInit() end");
        return true;
    }

    @Override
    public boolean sendMessage(String process_id, String msg) {
        TpModule tpModule = zkMessageApi.getTpModuleByProcessID(process_id);
        if (tpModule == null) {
            log.error("process_id:" + process_id + " ,找不到对应模块信息");
            return false;
        }
        String path = zkMessageApi.getMessageTask() + "/" + tpModule.getModule_code() + "-" + process_id + "/" + zkMessageApi.getProcessID() + "-";
        if (!zkMessageApi.createPath(path, msg)) {
            return false;
        }

        return true;
    }

    synchronized boolean analysisMsg(String path) {
        List<String> listTmp = zkMessageApi.getChildPath(path);
        if (listTmp == null) {
            return false;
        }

        for (String processStr : listTmp) {
            String pathTmp = path + "/" + processStr;
            String json = zkMessageApi.getDataByPath(pathTmp);
            if (json == null) {
                continue;
            }

            JSONObject object = JSONObject.parseObject(json);
            String processId = object.getString("PROCESS_ID");
            String taskType = object.getString("TASK_TYPE");
            if (processId == null) {
                log.info("异常消息，没有PROCESS_ID字段。msg:" + json);
                continue;
            }

            if (taskType == null) {
                g_ReceTaskQueue.offer(json);
            }else if (taskType.equals(MsgBody.TASK_TYPE.T_DBINCEPT.getType())) {
                g_ReceDbInceptQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_RECORD.getType())) {
                g_ReceTaskQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_CONFIG.getType())) {
                g_ReceConfigQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_ROLLBACK.getType())) {
                g_ReceRollbackQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_PROCESS.getType())) {
                g_ReceProQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_DISPATCH.getType())) {
                g_ReceHeartQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_SS_PROCESS.getType())) {
                g_ReceSsProcessQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_DEBUG.getType())) {
                g_ReceDebugQueue.offer(json);
            } else if (taskType.equals(MsgBody.TASK_TYPE.T_RECORD_STATE.getType())) {
                g_ReceTaskStateQueue.offer(json);
            } else {
                log.error("无法识别的消息类型：" + taskType + ",msg:" + json);
                continue;
            }

            //log.info("msg:" + json);
            // 删除已经消费的目录
            zkMessageApi.deletePath(pathTmp);
        }

        // 重新监听
        zkMessageApi.startMessageMonitor();
        return true;
    }

    @Override
    public boolean startReceivedMessage() {
        if (!zkMessageApi.startMessageMonitor()) {
            log.error("zkMessageApi.startMessageMonitor() faile");
            return false;
        }
        return true;
    }


    @Override
    public void close() {
        if (zkMessageApi != null) {
            zkMessageApi.close();
        }
    }

    public class ClientWatcher implements Watcher {

        @Override
        public synchronized void process(WatchedEvent event) {
            Watcher.Event.KeeperState keeperState = event.getState();
            Watcher.Event.EventType eventType = event.getType();
            String path = event.getPath();

            if (Watcher.Event.KeeperState.SyncConnected == keeperState) {
                // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
                // ConnectedReadOnly:连接只读,Disconnected:连接失败
                if (Watcher.Event.EventType.None == eventType) {
                    // 如果建立建立成功,让后程序往下走
                    log.info("ClientWatcher" + "，zk 建立连接成功!");
                } else if (Watcher.Event.EventType.NodeCreated == eventType) {
                    log.info("ClientWatcher" + "，事件通知,新增node节点" + path);
                } else if (Watcher.Event.EventType.NodeDataChanged == eventType) {
                    log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被修改....");
                } else if (Watcher.Event.EventType.NodeDeleted == eventType) {
                    log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被删除....");
                } else if (Watcher.Event.EventType.NodeChildrenChanged == eventType) {
                    log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "，子节点被修改....");

                    //log.info("重新监听:" + path);
                    analysisMsg(path);
                }
            }
        }
    }
}
