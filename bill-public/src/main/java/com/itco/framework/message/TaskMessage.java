package com.itco.framework.message;

import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * @ClassName: TaskMessage
 * @Description: 任务交互基础类
 * <AUTHOR>
 * @Date 2021/9/3
 * @Version 1.0
 */
public abstract class TaskMessage {
    static Log log = LogFactory.getLog(TaskMessage.class);

    protected ZkClientApi zkClientApi = null;
    protected Common common = null;
    protected String receTag;

    public int mode = 0;//0:生产者，消费者 ；其他:生产者模式
    protected boolean bDispatch = false;
    protected Queue<String> fQueue = new LinkedBlockingQueue<>();
    protected int filterQueueSize = 10000;

    protected volatile LinkedBlockingQueue<String> g_ReceHeartQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceDbInceptQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceProQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceTaskQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceConfigQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceRollbackQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceSsProcessQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceDebugQueue = new LinkedBlockingQueue<String>();
    protected volatile LinkedBlockingQueue<String> g_ReceTaskStateQueue = new LinkedBlockingQueue<String>();

    public synchronized String pollHeart() {
        return g_ReceHeartQueue.poll();
    }

    public synchronized String pollProcess() {
        return g_ReceProQueue.poll();
    }

    public synchronized String pollDbIncept(){
        return g_ReceDbInceptQueue.poll();
    }

    public synchronized String pollTaskRecord() {
        return g_ReceTaskQueue.poll();
    }

    public synchronized String pollConfigSyncResponse() {
        return g_ReceConfigQueue.poll();
    }

    public synchronized String pollRollbackRequest() {
        return g_ReceRollbackQueue.poll();
    }

    public synchronized String pollSsProcessRequest() {
        return g_ReceSsProcessQueue.poll();
    }

    public synchronized String poolDebugRequest() {
        return g_ReceDebugQueue.poll();
    }

    public synchronized String poolTaskStateRequest() {
        return g_ReceTaskStateQueue.poll();
    }


    public void clearTaskQueue() {
        g_ReceTaskQueue.clear();
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public int getFilterQueueSize() {
        return filterQueueSize;
    }

    /**
     * 初始化
     *
     * @return
     */
    public boolean init() {
        if (bDispatch) {
            receTag = "";
        } else {
            receTag = zkClientApi.getProcessID();
        }

        if (!OnInit()) {
            log.error("OnInit() faile");
            return false;
        }
        return true;
    }

    /**
     * 初始化
     *
     * @return
     */
    public abstract boolean OnInit();

    /**
     * 发送消息
     *
     * @param msg
     * @return
     */
    public abstract boolean sendMessage(String process_id, String msg);

    /**
     * 开始接收消息
     *
     * @return
     */
    public abstract boolean startReceivedMessage();

    /**
     * 关闭实例
     */
    public abstract void close();

    public void setbDispatch(boolean bDispatch) {
        this.bDispatch = bDispatch;
    }
}
