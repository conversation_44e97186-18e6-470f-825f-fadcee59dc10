package com.itco.framework.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.ErrorType;
import com.itco.entity.common.TgAlertLog;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.log.LogAlert;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;

public class TaskMsgManager implements Runnable {
    static Log log = LogFactory.getLog(TaskMsgManager.class);

    // zookeeper 客户端
    static ZkClientApi zkClientApi = null;
    static Common common = null; //公共配置类

    // 任务消息收发 客户端
    TaskMessage taskMessage = null;

    // 接收到的待处理任务队列
    LinkedBlockingQueue<MsgBody.StandardBody> g_RequestRecordList = new LinkedBlockingQueue<MsgBody.StandardBody>();
    LinkedBlockingQueue<MsgBody.ConfigSyncBody> g_RequestConfigList = new LinkedBlockingQueue<MsgBody.ConfigSyncBody>();
    LinkedBlockingQueue<MsgBody.StartStopProcessBody> g_RequestSsProcessList = new LinkedBlockingQueue<MsgBody.StartStopProcessBody>();
    LinkedBlockingQueue<MsgBody.DebugBody> g_RequestDebugList = new LinkedBlockingQueue<MsgBody.DebugBody>();

    // 处理完成的任务队列
    LinkedBlockingQueue<String> g_ResponseJsonList = new LinkedBlockingQueue<String>();
    Queue<String> redoQueue = new LinkedBlockingQueue<>();
    Map<String, String> redoTaskMap = new HashMap<>();

    int iZookeeperExpiredReconnectCnt = 0;
    int iZookeeperLossReconnectCnt = 0;
    int iZookeeperNoExistsDispatchCnt = 0;

    public void setTaskMessage(TaskMessage taskMessage) {
        this.taskMessage = taskMessage;
    }

    public boolean init() {
        log.info("TaskMessageManager init start:" + common.getModuleCode());

        if (!common.isWorkFlag()) {
            // 初始化
            taskMessage.setZkClientApi(zkClientApi);
            taskMessage.setCommon(common);
            if (!taskMessage.init()) {
                Factory.setbWhileFlag(true);
                return false;
            }
        }

        try {
            Thread.sleep(1000);
            //等待读取退出前的无效消息
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            Thread.currentThread().interrupt();
        }

        //taskMessage.clearTaskQueue();
        log.info("task message 初始化完成");

        if (!taskMessage.startReceivedMessage()) {
            log.error("taskMessage.startReceivedMessage() faile");
            Factory.setbWhileFlag(true);
            return false;
        }

        log.info("TaskMessageManager init close");
        return true;
    }

    boolean resetRegister(boolean flag, String msg) {
        //zkClientApi.close();
        if (flag) {
            boolean bRet = zkClientApi.init();
            if (bRet) {
                //重新注册，之间修改状态为0 ，非挂起
                if (!zkClientApi.register(false, "0")) {
                    log.error(msg + "，进程重新注册失败");
                    return false;
                } else {
                    log.info(msg + "，进程重新注册成功");
                    log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
                }
            }
        } else {
            //重新注册，之间修改状态为0 ，非挂起
            if (!zkClientApi.register(false, "0")) {
                log.error(msg + "，进程重新注册失败");
                return false;
            } else {
                log.info(msg + "，进程重新注册成功");
                log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
            }
        }
        return true;
    }

    @Override
    public void run() {
        log.info("消息管理线程 启动");

        try {
            while (!Factory.isbWhileMsgFlag()) {
                // 接收待处理任务息
                String json = msgReceTaskInterface();
                if (json != null) {
                    log.info("\n" + "接收到消息:" + json);

                    //消息解析，
                    JSONObject object = JSONObject.parseObject(json);
                    String taskType = object.getString("TASK_TYPE");
                    if (taskType == null) {
                        MsgBody.StandardBody body = JSON.parseObject(json, MsgBody.StandardBody.class);
                        body.STATE = new String(MsgBody.TASK_STATE.TA.getState());
                        body.MODULE_ID = body.PROCESS_ID.substring(0, 4);
                        offerRecordTask(body);
                    } else if (MsgBody.TASK_TYPE.T_DISPATCH.getType().equals(taskType)) {

                    } else if (MsgBody.TASK_TYPE.T_PROCESS.getType().equals(taskType)) {

                    } else if (MsgBody.TASK_TYPE.T_CONFIG.getType().equals(taskType)) {
                        MsgBody.ConfigSyncBody body = JSON.parseObject(json, MsgBody.ConfigSyncBody.class);
                        offerConfigSync(body);
                    } else if (MsgBody.TASK_TYPE.T_RECORD.getType().equals(taskType)) {
                        MsgBody.StandardBody body = JSON.parseObject(json, MsgBody.StandardBody.class);
                        body.STATE = new String(MsgBody.TASK_STATE.TA.getState());
                        body.MODULE_ID = body.PROCESS_ID.substring(0, 4);
                        offerRecordTask(body);
                    } else if (MsgBody.TASK_TYPE.T_SS_PROCESS.getType().equals(taskType)) {
                        MsgBody.StartStopProcessBody body = JSON.parseObject(json, MsgBody.StartStopProcessBody.class);
                        offerStartStopBody(body);
                    } else if (MsgBody.TASK_TYPE.T_DEBUG.getType().equals(taskType)) {
                        MsgBody.DebugBody body = JSON.parseObject(json, MsgBody.DebugBody.class);
                        offerDebugBody(body);
                    } else if (MsgBody.TASK_TYPE.T_RECORD_STATE.getType().equals(taskType)) {
                        MsgBody.StandardBody body = JSON.parseObject(json, MsgBody.StandardBody.class);
                        body.MODULE_ID = body.PROCESS_ID.substring(0, 4);
                        //  任务如果已经存在，则重新发送处理成功消息。
                        if (redoQueue.contains(body.TASK_ID)) {
                            offerResponseJosnTask(redoTaskMap.get(body.TASK_ID));
                        }
                    }
                }

                // 发送处理完成任务
                String responseJson = pollResponseJsonTask();
                if (responseJson != null) {
                    JSONObject tmp = JSONObject.parseObject(responseJson);
                    if (tmp.containsKey("TASK_ID") && !redoQueue.contains(tmp.getString("TASK_ID"))) {
                        redoQueue.add(tmp.getString("TASK_ID"));
                        redoTaskMap.put(tmp.getString("TASK_ID"), responseJson);
                        if (redoQueue.size() > taskMessage.getFilterQueueSize()) {
                            redoTaskMap.remove(redoQueue.poll());
                        }
                    }
                    if (!msgSendInterface(zkClientApi.getDspchProcessID(), responseJson)) {
                        //发送失败，重新放回队列中。
                        offerResponseJosnTask(responseJson);
                        Thread.sleep(1000);
                    }
                }

                //  判断zookeeper是否断开,断开重连
                int connectionLoss = zkClientApi.isConnectionLoss();
                if (connectionLoss < 0) {
                    if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_CONNECTION_LOSS) {
                        if (zkClientApi.getiConnectionLossCnt() == 1) {
                            TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                            LogAlert.alert(tgAlertLog);
                        }

                        if (zkClientApi.getiConnectionLossCnt() % 10 == 1 && iZookeeperLossReconnectCnt < 3) {
                            if (!resetRegister(true, "zookeeper失去连接")) {
                                iZookeeperLossReconnectCnt++;
                            } else {
                                iZookeeperExpiredReconnectCnt = 0;
                                iZookeeperLossReconnectCnt = 0;
                            }
                        }
                    } else if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_SESSION_EXPIRED) {
                        if (zkClientApi.getiSectionExpiredCnt() == 1) {
                            TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                            LogAlert.alert(tgAlertLog);
                        }

                        if (zkClientApi.getiSectionExpiredCnt() % 10 == 1 && iZookeeperExpiredReconnectCnt < 3) {
                            // 连续10次超时，就重新连接
                            TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES, Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES + "，zookeeper的section连续10次失效");
                            LogAlert.alert(tgAlertLog);

                            if (!resetRegister(true, "zookeeper的section连续10次失效")) {
                                iZookeeperExpiredReconnectCnt++;
                            } else {
                                iZookeeperLossReconnectCnt = 0;
                                iZookeeperExpiredReconnectCnt = 0;
                            }

                        }
                    }
                } else {
                    if (connectionLoss == 0) {
                        log.error(zkClientApi.getProcessID() + ",zookeeper注册丢失");
                        TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.BILL_PUBLIC_PROCESS_CONNECTION_LOSE, Long.parseLong(zkClientApi.getProcessID()));
                        tgAlertLog.setLog_desc("zookeeper注册丢失");
                        LogAlert.alert(tgAlertLog);

						//重新注册，之间修改状态为0 ，非挂起
                        if (!resetRegister(false, "zookeeper注册丢失")) {
                            iZookeeperNoExistsDispatchCnt++;
                        } else {
                            iZookeeperNoExistsDispatchCnt = 0;
                        }
                    }
                }
                Thread.sleep(200);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            Thread.currentThread().interrupt();
        }

        log.info("消息管理线程 关闭");
    }


    // 消息发送接口
    synchronized boolean msgSendInterface(String key, String msg) {
        if (!taskMessage.sendMessage(key, msg)) {
            return false;
        }

        JSONObject j = JSONObject.parseObject(msg);
        if (j.get("TASK_TYPE") == null || MsgBody.TASK_TYPE.T_RECORD.getType().equals(j.get("TASK_TYPE"))) {
            log.info("任务处理完成，反馈任务状态给调度 :" + "\n" + key + "," + msg);
        } else {
            log.info(key + "," + msg);
        }
        return true;
    }

    public String msgReceTaskInterface() {
        String json = null;

        json = taskMessage.pollDbIncept();
        if (json != null) {
            return json;
        }

        json = taskMessage.pollConfigSyncResponse();
        if (json != null) {
            return json;
        }

        json = taskMessage.pollSsProcessRequest();
        if (json != null) {
            return json;
        }

        json = taskMessage.poolDebugRequest();
        if (json != null) {
            return json;
        }

        json = taskMessage.pollTaskRecord();
        if (json != null) {
            return json;
        }

        return null;
    }

    //  获取应答任务包
    public String pollResponseJsonTask() {
        return g_ResponseJsonList.poll();
    }

    //  发送消息
    public void offerResponseJosnTask(String json) {

        if (!g_ResponseJsonList.offer(json)) {
            log.error("g_ResponseJsonList.offer(json) faile");
        }
    }

    //  话单任务
    public synchronized boolean offerRecordTask(MsgBody.StandardBody body) {
        return g_RequestRecordList.offer(body);
    }

    public synchronized MsgBody.StandardBody pollRecordTask() {
        return g_RequestRecordList.poll();
    }

    public LinkedBlockingQueue<String> getG_ResponseJsonList() {
        return g_ResponseJsonList;
    }

    //  配置同步任务
    public synchronized boolean offerConfigSync(MsgBody.ConfigSyncBody body) {
        return g_RequestConfigList.offer(body);
    }

    public synchronized MsgBody.ConfigSyncBody pollConfigSync() {
        return g_RequestConfigList.poll();
    }

    //  进程启停任务
    public synchronized boolean offerStartStopBody(MsgBody.StartStopProcessBody body) {
        return g_RequestSsProcessList.offer(body);
    }

    public synchronized MsgBody.StartStopProcessBody pollStartStopBody() {
        return g_RequestSsProcessList.poll();
    }

    public synchronized boolean offerDebugBody(MsgBody.DebugBody body) {
        return g_RequestDebugList.offer(body);
    }

    public synchronized MsgBody.DebugBody polllDebugBody() {
        return g_RequestDebugList.poll();
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        TaskMsgManager.zkClientApi = zkClientApi;
    }

    public static void setCommon(Common common) {
        TaskMsgManager.common = common;
    }

    public void close() {
        // 等待消息全部发出
        while (g_ResponseJsonList.size() > 0) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        taskMessage.close();
    }
}
