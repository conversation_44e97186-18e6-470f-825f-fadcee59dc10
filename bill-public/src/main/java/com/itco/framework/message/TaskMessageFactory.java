package com.itco.framework.message;

import com.itco.framework.Factory;
import com.itco.framework.message.impl.TaskMsgCtgMq;
import com.itco.framework.message.impl.TaskMsgZk;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class TaskMessageFactory {
    static Log log = LogFactory.getLog(TaskMessageFactory.class);

    /*
     * @decription TODO
     * @param className
     * @return 返回值
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public TaskMessage getTaskMessageInstance(String className) {
        Class<?> clazzTaskMessage = null;
        TaskMessage taskMessage = null;
        try {
            clazzTaskMessage = Class.forName(className);
            taskMessage = (TaskMessage) clazzTaskMessage.newInstance();
            return taskMessage;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载消息接收类失败:" + className + "," + e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TaskMessage getTaskMessageInstance(Class<? extends TaskMessage> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载消息接收类失败:" + TaskMessage.class + "," + e.getMessage());
        }
        return null;
    }

    public TaskMessage getTaskMessageInstance(Class<? extends TaskMessage> clazz, String className, String mode) {
        TaskMessage taskMessage = null;
        if (clazz == null && className == null) {
            if ("CTG_MQ".equals(mode)) {
                clazz = TaskMsgCtgMq.class;
            } else if ("ZK".equals(mode) || "zookeeper".equals(mode)) {
                clazz = TaskMsgZk.class;
            } else {
                log.error("任务消息管理类初始化失败，配置的消息通讯模式异常 mode：" + mode);
                Factory.setbWhileFlag(true);
                return null;
            }
        }

        if (null == clazz) {
            taskMessage = getTaskMessageInstance(className);
            if (null == taskMessage) {
                log.error("消息接收类实例化失败：" + className);
                return null;
            }
            log.info("消息接收类名：" + className);
        } else {
            taskMessage = getTaskMessageInstance(clazz);
            if (null == taskMessage) {
                log.error("消息接收类实例化失败：" + clazz.getName());
                return null;
            }
            log.info("消息接收类名：" + clazz.getName());
        }
        return taskMessage;
    }

    public TaskMessage getTaskMessageInstanceByMode(String mode) {
        TaskMessage taskMessage = null;

        if ("CTG_MQ".equals(mode)) {
            taskMessage = new TaskMsgCtgMq();
        } else if ("ZK".equals(mode) || "zookeeper".equals(mode)) {
            taskMessage = new TaskMsgZk();
        } else {
            log.error("任务消息管理类初始化失败，配置的消息通讯模式异常 mode：" + mode);
            Factory.setbWhileFlag(true);
            return null;
        }

        log.info("消息接收类名：" + mode);
        return taskMessage;
    }
}
