package com.itco.framework.log;

import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.entity.common.TgAlertLog;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class LogAlert {
    static Log log = LogFactory.getLog(LogAlert.class);

    static SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /*
     * @decription 输出告警日志到config.tg_alert_log
     * @param tgAlertLog:
     * @return boolean
     * <AUTHOR>
     * @createDate 2023/2/10
     */
    public static boolean alert(TgAlertLog tgAlertLog) {
        if (tgAlertLog == null) {
            return true;
        }

        String insertSql = "insert into tg_alert_log(code_id,pf_id,create_date,log_desc,state,state_date) values(?,?,?,?,?,?)";
        boolean ret = false;


        Timestamp createTimestamp;
        Timestamp stateTimestamp;
        Connection connTmp = null;
        try {
            Date createDate = df.parse(tgAlertLog.getCreate_date());
            createTimestamp = new Timestamp(createDate.getTime());
            Date stateDate = df.parse(tgAlertLog.getState_date());
            stateTimestamp = new Timestamp(stateDate.getTime());

            if (!DbPool.isInitFlag()) {
                log.error("DbPool 数据库连接未初始化");
                return false;
            }

            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdate(connTmp, insertSql, tgAlertLog.getCode_id(), tgAlertLog.getPf_id(), createTimestamp, tgAlertLog.getLog_desc(), tgAlertLog.getState(), stateTimestamp);
        } catch (ParseException e) {
            log.error(Factory.printStackTraceToString(e));
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }
}
