package com.itco.framework;

import com.alibaba.druid.support.logging.Log;
import com.alibaba.druid.support.logging.LogFactory;
import com.alibaba.druid.util.StringUtils;
import com.itco.entity.common.TpModuleVersion;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

public class Version {
    static Log log = LogFactory.getLog(Version.class);

    static String code_version = "app-2022-12-30 11:00";        // 默认代码版本
    static String deploy_version;                               // 发布版本
    static Map<String, String> patchMap = new TreeMap<>();
    static Map<String, TpModuleVersion> versionMap = new TreeMap<>();
    static List<TpModuleVersion> versionList = new ArrayList<>();
    static int MAX_VERSION_PRINT = 1;   //最大打印的历史版本

    static String billingLineId = "11";

    /*
     * @decription 加载app的发布版本信息
     * @param :
     * @return boolean
     * <AUTHOR>
     * @createDate 2023/1/17
     */
    static boolean loadResourcesApplication() {
        boolean bFlag = false;
        try {
            Resource resource = new ClassPathResource("/application.properties");
            if (resource.exists()) {
                Properties propApp = PropertiesLoaderUtils.loadProperties(resource);
                String tmpVersion = propApp.getProperty("version");
                if (tmpVersion != null) {
                    deploy_version = tmpVersion;
                    bFlag = true;
                } else {
                    log.error("发布人员请到模块resources目录的下，打开application.properties文件，添加发布版本号，格式:version=xxx_deploy_2099-12-09 14:30");
                    deploy_version = code_version;
                }
            }

            resource = new ClassPathResource("/application.yml");
            if (resource.exists()) {
                Properties propApp = PropertiesLoaderUtils.loadProperties(resource);
                String tmpVersion = propApp.getProperty("version");
                if (tmpVersion != null) {
                    deploy_version = tmpVersion;
                    bFlag = true;
                } else {
                    log.error("发布人员请到模块resources目录的下，打开application.properties文件，添加发布版本号，格式:version=xxx_deploy_2099-12-09 14:30");
                    deploy_version = code_version;
                }
            }

            if (bFlag) {
                log.error("发布人员请到模块resources目录的下，添加application.properties文件，并添加发布版本号，格式:version=xxx_deploy_2099-12-09 14:30");
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }
        return true;
    }

    /*
     * @decription 加载版本日志信息
     * @param :
     * @return boolean
     * <AUTHOR>
     * @createDate 2023/1/17
     */
    static boolean loadResourcesVersion() {
        try {
            Resource resourceVersion = new ClassPathResource("/version.txt");
            if (resourceVersion.exists()) {
                InputStream inputStream = resourceVersion.getInputStream();
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                String data = null;
                boolean bFirstFlagTmp = true, bFlagTmp = false;
                String codeVersionTmp = null;
                StringBuilder codeDesc = new StringBuilder();
                while ((data = bufferedReader.readLine()) != null) {
                    if (data.indexOf("---") > -1) {
                        if (!bFirstFlagTmp) {
                            patchMap.put(codeVersionTmp.trim(), codeDesc.toString());
                        } else {
                            bFirstFlagTmp = false;
                        }
                        codeDesc.delete(0, codeDesc.length());
                        codeDesc.append(data.trim() + "\n");
                        bFlagTmp = true;
                        continue;
                    }

                    if (bFlagTmp) {
                        if (data.indexOf("【发布版本】:") > -1) {
                            codeVersionTmp = data.substring(data.indexOf("【发布版本】:") + "【发布版本】:".length());
                        } else if (data.indexOf("【发布版本】：") > -1) {
                            codeVersionTmp = data.substring(data.indexOf("【发布版本】：") + "【发布版本】：".length());
                        } else if (data.indexOf("【发布版本】") > -1) {
                            codeVersionTmp = data.substring(data.indexOf("【发布版本】") + "【发布版本】".length());
                        }
                        codeDesc.append(data.trim() + "\n");
                        bFlagTmp = false;
                    } else {
                        codeDesc.append(data.trim() + "\n");
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            return false;
        }
        return true;
    }

    public static boolean stringTocovertObject() {
        // 遍历patchMap，将version字符串转换为TpModuleVersion对象

        int count = 0;
        for (Map.Entry<String, String> map : patchMap.entrySet()) {
            String version = map.getKey();
            String desc = map.getValue();
            TpModuleVersion tpModuleVersion = new TpModuleVersion();

            String[] lines = desc.split("【");
            for (String tmp : lines) {
                if (tmp.indexOf("-----") > -1) {
                    continue;
                }
                String line = "【" + tmp.trim();
                if (line.contains("【发布版本】")) {
                    tpModuleVersion.setVersionNumber(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【修订日期】")) {
                    tpModuleVersion.setRevisionDate(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【修订人员】")) {
                    tpModuleVersion.setRevisionPersonnel(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【需求单号】")) {
                    tpModuleVersion.setDemandNumber(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【SVN提交前版本】") || line.contains("【Git提交前版本】")) {
                    tpModuleVersion.setGitPreVersion(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【实现功能】")) {
                    tpModuleVersion.setFunctionality(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【变更文件】")) {
                    tpModuleVersion.setCodeFileChanges(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【修订要点】")) {
                    tpModuleVersion.setRevisionHighlights(line.substring(line.indexOf("】：") + 2).trim());
                } else if (line.contains("【注意事项】")) {
                    tpModuleVersion.setNotes(line.substring(line.indexOf("】：") + 2).trim());
                }
            }
            versionMap.put(version, tpModuleVersion);
            if (count++ >= patchMap.size() - 5) {
                versionList.add(tpModuleVersion);
            }
        }

        /*for (TpModuleVersion tpModuleVersion : versionList) {
            System.out.println("【版本号】:" + tpModuleVersion.getVersionNumber());
            System.out.println("【修订日期】:" + tpModuleVersion.getRevisionDate());
            System.out.println("【修订人员】:" + tpModuleVersion.getRevisionPersonnel());
            System.out.println("【需求单号】:" + tpModuleVersion.getDemandNumber());
            System.out.println("【Git提交前版本】:" + tpModuleVersion.getGitPreVersion());
            System.out.println("【实现功能】:" + tpModuleVersion.getFunctionality());
            System.out.println("【变更文件】:" + tpModuleVersion.getCodeFileChanges());
            System.out.println("【修订要点】:" + tpModuleVersion.getRevisionHighlights());
            System.out.println("【注意事项】:" + tpModuleVersion.getNotes());
            System.out.println("");
        }*/

        return true;
    }


	public static boolean print(String[] args, String name) {
        return print(args, name, 1);
    }

    /*
     * @decription 打印应用版本信息
     * @param args: 命令行入参
     * @param name: 版本名称
     * @return boolean : true 只打印版本信息  ; false  打印版本且正常启动程序。
     * <AUTHOR>
     * @createDate 2022/12/9
     */
    public static boolean print(String[] args, String name, int max_version_print) {
        MAX_VERSION_PRINT = max_version_print;
        boolean flag = false;
        if (!StringUtils.isEmpty(name)) {
            code_version = name;
        }

        for (String arg : args) {
            if (arg.startsWith("-f") && arg.length() > 2) {
                billingLineId = arg.substring(2);
            }
            if ("Version".equals(arg) || "version".equals(arg) || "VERSION".equals(arg)) {
                flag = true;
            }
            if ("-Version".equals(arg) || "-version".equals(arg) || "-VERSION".equals(arg)) {
                flag = true;
            }
            if (flag && arg.indexOf("-n") > -1) {
                if (arg.length() > 2) {
                    MAX_VERSION_PRINT = Integer.parseInt(arg.substring(2));
                }
            }
        }

        // 1、加载app版本
        loadResourcesApplication();

        // 2、加载version.txt 版本日志
        loadResourcesVersion();

        // 3、将version.txt 转换为 TpModuleVersion 对象
        stringTocovertObject();

        // 4、打印版本日志
        printVersionLog(flag);
        return flag;
    }

 // 打印版本
    public static void print() {
        print(1);
    }

    // 打印版本
    public static void print(int max_version_print) {
        MAX_VERSION_PRINT = max_version_print;
        printVersionLog(false);
    }

    // 打印退出信息
    public static void destroy() {
        log.info("进程注销成功。");
        log.info("进程退出成功。");
    }

    // 获取代码版本号
    public static String getCode_version() {
        return code_version;
    }

    // 获取发布版本号
    public static String getDeploy_version() {
        return deploy_version;
    }

    /*
     * @decription 打印版本和版本日志
     * @param flag: true 控制台输出 ，false 日志文件输出
     * @return void
     * <AUTHOR>
     * @createDate 2023/1/17
     */
    public static void printVersionLog(boolean flag) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【代码版本:" + code_version + ",发布版本:" + deploy_version + "】\n");
        if (patchMap.size() > 0) {
            stringBuilder.append("\n版本日志：\n");

            int i = patchMap.size() - 1;
            for (Map.Entry<String, String> map : patchMap.entrySet()) {
                if (i-- < MAX_VERSION_PRINT) {//只打印最近 MAX_VERSION_PRINT 个版本
                    stringBuilder.append(map.getValue());
                    break;
                }
            }
            stringBuilder.append("--------------------------------------------------------------------------------------------------");
        }

        if (flag) {
            System.out.println(stringBuilder.toString());
        } else {
            log.info(stringBuilder.toString());
        }
    }

    /*
     * @decription 获取版本号
     */
    public static String getVersionLog(boolean flag) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【代码版本:" + code_version + ",发布版本:" + deploy_version + "】\n");
        if (patchMap.size() > 0) {
            stringBuilder.append("\n版本日志：\n");

            int i = patchMap.size() - 1;
            for (Map.Entry<String, String> map : patchMap.entrySet()) {
                if (i-- < MAX_VERSION_PRINT) {//只打印最近 MAX_VERSION_PRINT 个版本
                    stringBuilder.append(map.getValue());
                    break;
                }
            }
            stringBuilder.append("--------------------------------------------------------------------------------------------------");
        }

        if (flag) {
            return stringBuilder.toString();
        } else {
            return stringBuilder.toString();
        }
    }

    public static List<TpModuleVersion> getVersionList() {
        return versionList;
    }

    public static String getBillingLineId() {
        return billingLineId;
    }

    public static void setBillingLineId(String billingLineId) {
        Version.billingLineId = billingLineId;
    }
}
