package com.itco.framework.calcrecord;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class CalcRecordFactory {
    static Log log = LogFactory.getLog(CalcRecordFactory.class);

    /*
     * @decription TODO
     * @param className
     * @return 返回值
     * <AUTHOR>
     * @createDate 2022/6/16
     */
    public CalcRecordManager getCalcInstance(String className) {
        Class<?> clazzCalcRecordManager = null;
        CalcRecordManager calcRecordManager = null;
        try {
            clazzCalcRecordManager = Class.forName(className);
            calcRecordManager = (CalcRecordManager) clazzCalcRecordManager.newInstance();
            return calcRecordManager;
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            log.error("加载类失败:" + className + "," + e.getMessage());
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public CalcRecordManager getCalcInstance(Class<? extends CalcRecordManager> clazz) {
        try {
            if (null != clazz) {
                return clazz.newInstance();
            }
        } catch (Exception e) {
            log.error("加载类失败:" + CalcRecordManager.class + "," + e.getMessage());
        }
        return null;
    }

    public CalcRecordManager getCalcInstance(Class<? extends CalcRecordManager> clazz, String className) {
        CalcRecordManager calcRecordManager = null;
        if (null == clazz) {
            calcRecordManager = getCalcInstance(className);
            if (null == calcRecordManager) {
                log.error("话单处理类实例化失败：" + className);
                return null;
            }
            log.info("话单处理类名：" + className);
        } else {
            calcRecordManager = getCalcInstance(clazz);
            if (null == calcRecordManager) {
                log.error("话单处理类实例化失败：" + clazz.getName());
                return null;
            }
            log.info("话单处理类名：" + clazz.getName());
        }

        return calcRecordManager;
    }
}
