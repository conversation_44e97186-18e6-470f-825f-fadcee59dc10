package com.itco.framework.calcrecord;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import lombok.Data;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.*;

@Data
public abstract class CalcRecordManager implements Runnable {
    static Log log = LogFactory.getLog(CalcRecordManager.class);

    protected static ZkClientApi zkClientApi = null;
    protected static Common common = null;
    protected Map<String, List<List<KeyValue>>> dataMap = new HashMap<>();

    static boolean bInitFlag = false;//框架填写，是否初始化，处理线程类，公共配置可以只初始化一次。
    static int dealThreadCnt = 0;//记录这个任务处理线程数量

    //框架初始化填入
    int ThreadNum = 0;//框架分配的线程编号
    String name; //线程名称
    String startMode;//框架填写的模块启动方式，NORMAL、TRIAL、PRE
    List<String> inRecordList = null;//框架分配的待处理话单
    int loadId = 0;//框架接收到的同步配置，加载定价计划批次号

    //本话单管理类使用
    List<String> outRecordList = new LinkedList<>();//输出话单
    List<String> outErrorList = new LinkedList<>();//输出错误单
    volatile String dealState = THREAD_STATE.IDLE.state;//线程状态

    //继承话单处理类使用
    String errorMsg = new String();//线程异常消息填写
    List<String> outRecord = new ArrayList<>(); //每条话单，处理后的话单

    int normalCnt; //正常
    int abnCnt;//异常
    int otherCnt;//不结算
    int repeatCnt;//重单
    int preCnt;//预结单
    int invalidCnt;//ticket_type 无效值的订单

    //每次处理完话单，需要清理
    public boolean clearRecordList() {
        outRecordList.clear();
        outErrorList.clear();
        dataMap.clear();
        errorMsg = "";

        //清理
        normalCnt = 0;
        abnCnt = 0;
        otherCnt = 0;
        repeatCnt = 0;
        preCnt = 0;
        invalidCnt = 0;

        reset();
        return true;
    }

    @Override
    public void run() {
        // 每处理一个话单文件之前初始化。
        clearRecordList();
        int i = 0;
        try {
            log.info("OnTask start  ,name:" + getName());
            //继承类初始化
            if (!OnChildInit()) {
                errorMsg = "线程执行类初始化失败！";
                dealState = THREAD_STATE.ERROR.state;
                return;
            }

            for (String record : inRecordList) {
                i++;
                errorMsg = "";
                outRecord.clear();

                if (common.getIDebug() == 5 || common.getIDebug() == 1) {
                    log.info("start:" + record);
                }
                // 标准模式
                boolean bRet = false;
                try {
                    bRet = OnTask(record);
                    if (!bRet) {
                        log.error("话单处理失败。");
                    }
                } catch (Exception e) {
                    String exceptionString = printStackTraceToString(e);
                    log.error("OnTask(record) Exception :" + exceptionString);

                    if (outRecord.size() == 0) {
                        outRecord.add(record);
                    }
                    errorMsg = ";OnTask(record) Exception执行失败，捕获到异常：" + e.toString();
                }

                if (outRecord.size() > 0) {
                    outRecordList.addAll(outRecord);
                    for (String oRecord : outRecord) {
                        if (common.getIDebug() == 5 || common.getIDebug() == 1) {
                            log.info("end:" + oRecord);
                        }
                        JSONObject json = JSONObject.parseObject(oRecord);
                        String ticket_type = json.getString("ticket_type");
                        if (ticket_type != null) {
                            if (ticket_type.equals("0")) {
                                if (json.containsKey("a_if_deduct_old_owe") && ((JSONArray) json.get("a_if_deduct_old_owe")).size() > 0) {
                                    for (int iFor = 0; iFor < ((JSONArray) json.get("a_if_deduct_old_owe")).size(); iFor++) {
                                        int ifDeductTmp = ((JSONArray) json.get("a_if_deduct_old_owe")).getInteger(iFor);
                                        if (ifDeductTmp == 0) {
                                            preCnt++;
                                        } else if (ifDeductTmp == 1) {
                                            normalCnt++;
                                        }
                                    }
                                } else {
                                    normalCnt++;
                                }
                            } else if (ticket_type.equals("1")) {
                                abnCnt++;
                                String errorType = json.getString("error_type");
                                if ("210027".equals(errorType)) { // 重单
                                    repeatCnt++;
                                }
                            } else if (ticket_type.equals("2")) {
                                otherCnt++;
                            } else if (ticket_type.equals("3")) {
                                preCnt++;
                            } else {
                                invalidCnt++;
                            }
                        } else {
                            invalidCnt++;
                        }
                    }
                }

                if (outErrorList.size() > 0) {
                    if (common.getIDebug() == 5 || common.getIDebug() == 1) {
                        log.info("end error record :" + outErrorList.get(0));
                    }
                }

                if (THREAD_STATE.TIMEOUT.state.equals(dealState)) {
                    log.error("收到超时退出命令，总共：" + inRecordList.size() + "条话单,退出前处理了：" + i + " 条");
                    return;
                }
            }

            if (!OnChildOver()) {
                errorMsg += ";线程执行类退出前操作失败！";
                dealState = THREAD_STATE.ERROR.state;
                return;
            }
            log.info("OnTask close ,name:" + getName());
        } catch (Exception e) {
            log.error("总共：" + inRecordList.size() + "条话单,退出前处理了：" + i + " 条");
            String exceptionString = printStackTraceToString(e);
            log.error("run Exception :" + exceptionString);

            errorMsg += e.toString();
            dealState = THREAD_STATE.ERROR.state;
            return;
        }

        dealState = THREAD_STATE.END.state;
    }

    public String getProcess_id() {
        return zkClientApi.getProcessID();
    }

    public int getiMaxProcessCnt() {
        return common.getIMaxProcessCnt();
    }

    public int getiMaxRecordCnt() {
        return common.getIMaxRecordCnt();
    }

    public static int getDealThreadCnt() {
        return dealThreadCnt;
    }

    public static void setDealThreadCnt(int dealThreadCnt) {
        CalcRecordManager.dealThreadCnt = dealThreadCnt;
    }

    public static void dealThreadCntPlusPlus() {
        CalcRecordManager.dealThreadCnt++;
    }

    public void setOutRecord(String outRecord) {
        this.outRecord.add(outRecord);
    }

    public void setOutErrorRecord(String outErrorRecord) {
        this.outErrorList.add(outErrorRecord);
    }

    public static boolean isbInitFlag() {
        return bInitFlag;
    }

    public static void setbInitFlag(boolean bInitFlag) {
        CalcRecordManager.bInitFlag = bInitFlag;
    }

    public static void setCommon(Common common) {
        CalcRecordManager.common = common;
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        CalcRecordManager.zkClientApi = zkClientApi;
    }

    public static ZkClientApi getZkClientApi() {
        return zkClientApi;
    }

    public void print(String... params) {
        if (CalcRecordManager.getCommon().getIDebug() == 1) {
            for (int i = 0; i < params.length; i++) {
                System.out.print(params[i]);
            }
            System.out.println("");
        }
    }

    @Override
    public String toString() {
        return "CalcRecordManager{" +
                "normalCnt=" + normalCnt +
                ", abnCnt=" + abnCnt +
                ", otherCnt=" + otherCnt +
                ", repeatCnt=" + repeatCnt +
                '}';
    }

    public String printStackTraceToString(Exception e) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(byteArrayOutputStream));
        if (byteArrayOutputStream != null) {
            String exception = byteArrayOutputStream.toString();
            /*if (exception.length() <= 800) {
                return exception;
            } else {
                return exception.substring(0, 800);
            }*/
            return exception;
        }
        return null;
    }

    /*线程启动初始化*/
    public abstract boolean OnInit();

    /*线程启动初始化试算*/
    public abstract boolean OnInitTrial();

    /*线程启动初始化预计费*/
    public abstract boolean OnInitPre();

    /*线程处理话单文件之前初始化*/
    public abstract boolean OnChildInit();

    public abstract void reset();

    /* 入参：
       inRecord：输入话单
       输出话单：getOutRecord(outRecord),
       处理异常：setErrorMsg("异常描述")
       返回值，false:处理异常，true:处理成功*/
    public abstract boolean OnTask(String inRecord);

    /*处理线程话单文件处理之后的操作*/
    public abstract boolean OnChildOver();

    /*所有的线程话单文件处理完成之后的操作*/
    public abstract boolean OnOver(Map<String, List<List<KeyValue>>> data);

    // 线程退出操作
    public abstract boolean OnExit();

    public abstract Map<Integer, FunctionPerl> getFunctionPerl();

    public static Common getCommon() {
        return common;
    }

    public enum THREAD_STATE {
        IDLE(1, "IDLE", "空闲"),
        DEAL(2, "DEAL", "处理"),
        END(3, "END", "正常结束"),
        ERROR(4, "ERROR", "处理异常"),
        TIMEOUT(5, "TIMEOUT", "处理超时");
        int order_id;
        String state;
        String remark;

        THREAD_STATE(int order_id, String state, String remark) {
            this.order_id = order_id;
            this.state = state;
            this.remark = remark;
        }

        public String getState() {
            return state;
        }
    }
}
