package com.itco.entity.table;

public class FilterRegionConfig {
    private Long source_event_type_id;

    private String comments;

    private String hash_key_date_format;

    private int per;

    public Long getSource_event_type_id() {
        return source_event_type_id;
    }

    public void setSource_event_type_id(Long source_event_type_id) {
        this.source_event_type_id = source_event_type_id;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getHash_key_date_format() {
        return hash_key_date_format;
    }

    public void setHash_key_date_format(String hash_key_date_format) {
        this.hash_key_date_format = hash_key_date_format;
    }

    public int getPer() {
        return per;
    }

    public void setPer(int per) {
        this.per = per;
    }

    @Override
    public String toString() {
        return "FilterRegionConfig{" +
                "source_event_type_id=" + source_event_type_id +
                ", comments='" + comments + '\'' +
                ", hash_key_date_format='" + hash_key_date_format + '\'' +
                ", per=" + per +
                '}';
    }
}
