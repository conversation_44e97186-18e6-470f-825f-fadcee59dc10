package com.itco.entity.table;

import java.sql.Timestamp;

public class TprTable {
    int table_id;
    String en_name;
    String ch_name;
    String table_type;
    String key_attr_name;
    String partition_field;
    String redis_db;
    String redis_format;
    String redis_separator;
    String type;
    String upload_sql;
    String increased_upload_sql;
    String file_path;
    String file_foamrt;
    String status_cd;
    String create_staff;
    String update_staff;
    Timestamp create_date;
    Timestamp update_date;
    Timestamp status_date;
    String upload_mode;

    public int getTable_id() {
        return table_id;
    }

    public void setTable_id(int table_id) {
        this.table_id = table_id;
    }

    public String getEn_name() {
        return en_name;
    }

    public void setEn_name(String en_name) {
        this.en_name = en_name;
    }

    public String getCh_name() {
        return ch_name;
    }

    public void setCh_name(String ch_name) {
        this.ch_name = ch_name;
    }

    public String getTable_type() {
        return table_type;
    }

    public void setTable_type(String table_type) {
        this.table_type = table_type;
    }

    public String getKey_attr_name() {
        return key_attr_name;
    }

    public void setKey_attr_name(String key_attr_name) {
        this.key_attr_name = key_attr_name;
    }

    public String getPartition_field() {
        return partition_field;
    }

    public void setPartition_field(String partition_field) {
        this.partition_field = partition_field;
    }

    public String getRedis_db() {
        return redis_db;
    }

    public void setRedis_db(String redis_db) {
        this.redis_db = redis_db;
    }

    public String getRedis_format() {
        return redis_format;
    }

    public void setRedis_format(String redis_format) {
        this.redis_format = redis_format;
    }

    public String getRedis_separator() {
        return redis_separator;
    }

    public void setRedis_separator(String redis_separator) {
        this.redis_separator = redis_separator;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUpload_sql() {
        return upload_sql;
    }

    public void setUpload_sql(String upload_sql) {
        this.upload_sql = upload_sql;
    }

    public String getIncreased_upload_sql() {
        return increased_upload_sql;
    }

    public void setIncreased_upload_sql(String increased_upload_sql) {
        this.increased_upload_sql = increased_upload_sql;
    }

    public String getFile_path() {
        return file_path;
    }

    public void setFile_path(String file_path) {
        this.file_path = file_path;
    }

    public String getFile_foamrt() {
        return file_foamrt;
    }

    public void setFile_foamrt(String file_foamrt) {
        this.file_foamrt = file_foamrt;
    }

    public String getStatus_cd() {
        return status_cd;
    }

    public void setStatus_cd(String status_cd) {
        this.status_cd = status_cd;
    }

    public String getCreate_staff() {
        return create_staff;
    }

    public void setCreate_staff(String create_staff) {
        this.create_staff = create_staff;
    }

    public String getUpdate_staff() {
        return update_staff;
    }

    public void setUpdate_staff(String update_staff) {
        this.update_staff = update_staff;
    }

    public Timestamp getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Timestamp create_date) {
        this.create_date = create_date;
    }

    public Timestamp getUpdate_date() {
        return update_date;
    }

    public void setUpdate_date(Timestamp update_date) {
        this.update_date = update_date;
    }

    public Timestamp getStatus_date() {
        return status_date;
    }

    public void setStatus_date(Timestamp status_date) {
        this.status_date = status_date;
    }

    public String getUpload_mode() {
        return upload_mode;
    }

    public void setUpload_mode(String upload_mode) {
        this.upload_mode = upload_mode;
    }

    @Override
    public String toString() {
        return "TprTable{" +
                "table_id=" + table_id +
                ", en_name='" + en_name + '\'' +
                ", ch_name='" + ch_name + '\'' +
                ", key_attr_name='" + key_attr_name + '\'' +
                ", partition_field='" + partition_field + '\'' +
                ", redis_db='" + redis_db + '\'' +
                ", redis_format='" + redis_format + '\'' +
                ", redis_separator='" + redis_separator + '\'' +
                ", type='" + type + '\'' +
                ", upload_sql='" + upload_sql + '\'' +
                ", increased_upload_sql='" + increased_upload_sql + '\'' +
                ", file_path='" + file_path + '\'' +
                ", file_foamrt='" + file_foamrt + '\'' +
                ", status_cd='" + status_cd + '\'' +
                ", create_staff='" + create_staff + '\'' +
                ", update_staff='" + update_staff + '\'' +
                ", create_date='" + create_date + '\'' +
                ", update_date='" + update_date + '\'' +
                ", status_date='" + status_date + '\'' +
                ", upload_mode='" + upload_mode + '\'' +
                '}';
    }

    public String toShow() {
        return "table_id=" + table_id +
                ", en_name='" + en_name + '\'' +
                ", ch_name='" + ch_name + '\'' +
                ", key_attr_name='" + key_attr_name;
    }
}
