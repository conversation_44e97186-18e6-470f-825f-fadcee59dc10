package com.itco.entity.table;

public class TprTableFormatItem {
    int table_attr_id;
    int table_id;
    int attr_id;
    int posi;
    int order_id;
    String remark;


    public int getTable_attr_id() {
        return table_attr_id;
    }

    public void setTable_attr_id(int table_attr_id) {
        this.table_attr_id = table_attr_id;
    }

    public int getTable_id() {
        return table_id;
    }

    public void setTable_id(int table_id) {
        this.table_id = table_id;
    }

    public int getAttr_id() {
        return attr_id;
    }

    public void setAttr_id(int attr_id) {
        this.attr_id = attr_id;
    }

    public int getPosi() {
        return posi;
    }

    public void setPosi(int posi) {
        this.posi = posi;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TprWideTableItem{" +
                "table_id=" + table_id +
                ", attr_id=" + attr_id +
                ", posi=" + posi +
                ", order_id=" + order_id +
                ", remark='" + remark + '\'' +
                '}';
    }
}
