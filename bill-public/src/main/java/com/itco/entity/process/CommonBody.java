package com.itco.entity.process;

import com.itco.framework.Factory;

public class CommonBody {
    public String TASK_ID;
    public String HOST_ID;
    public String PROCESS_ID;
    public String MODULE_ID;
    public String FILE_NAME;//文件名,
    public String LOCAL_INPUT_PATH;//本地文件路径
    public String LOCAL_OUTPUT_PATH;
    public String RECORD_CNT;//记录数
    public String DEAL_TIME;//处理时长
    public String STATE;//状态，OK：处理完成，ERROR：处理异常
    public String ERROR_MSG;//异常描述

    public int ERROR_CNT;//错误单

    public int NORMAL_CNT;//正常单
    public int PRE_CNT;//预借单
    public int ABN_CNT;//异常单
    public int OTHER_CNT;//不计费话单

    public String CREATE_DATE;//创建时间
    public String UPDATE_DATE;//更新时间
    public String ELEMENT_STR;//处理串

    // 初始化消息包应答状态
    public void init_response() {
        STATE = "OK";
        UPDATE_DATE = Factory.getSystemDateStr();
        NORMAL_CNT = 0;
        PRE_CNT = 0;
        ABN_CNT = 0;
        OTHER_CNT = 0;
    }

    @Override
    public String toString() {
        return "StandardBody{" +
                "TASK_ID='" + TASK_ID + '\'' +
                ", HOST_ID='" + HOST_ID + '\'' +
                ", PROCESS_ID='" + PROCESS_ID + '\'' +
                ", MODULE_ID='" + MODULE_ID + '\'' +
                ", FILE_NAME='" + FILE_NAME + '\'' +
                ", LOCAL_INPUT_PATH='" + LOCAL_INPUT_PATH + '\'' +
                ", LOCAL_OUTPUT_PATH='" + LOCAL_OUTPUT_PATH + '\'' +
                ", RECORD_CNT='" + RECORD_CNT + '\'' +
                ", DEAL_TIME='" + DEAL_TIME + '\'' +
                ", STATE='" + STATE + '\'' +
                ", ERROR_MSG='" + ERROR_MSG + '\'' +
                ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                ", NORMAL_CNT=" + NORMAL_CNT +
                ", PRE_CNT=" + PRE_CNT +
                ", ABN_CNT=" + ABN_CNT +
                ", OTHER_CNT=" + OTHER_CNT +
                ", ERROR_CNT=" + ERROR_CNT +
                ", CREATE_DATE='" + CREATE_DATE + '\'' +
                ", ELEMENT_STR='" + ELEMENT_STR + '\'' +
                '}';
    }
}
