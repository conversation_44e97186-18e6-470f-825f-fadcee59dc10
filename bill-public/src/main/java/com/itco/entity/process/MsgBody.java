package com.itco.entity.process;

import com.itco.framework.Factory;

import java.util.ArrayList;
import java.util.List;

public class MsgBody {
    //采集话单格式
    public static class DbInceptBody {
        public String HOST_ID;//11,
        public String PROCESS_ID;//10111101,
        public String MODULE_ID;
        public String TASK_NAME;//任务名称
        public String FILE_NAME;//文件名,
        public String LOCAL_PATH;//本地文件路径
        public String RECORD_CNT;//记录数
        public String FIRST;//是否首跳
        public String TYPE;//话单业务类型
        public String MSG_TYPE;//REQUEST:请求,RESPONSE:应答
        public String BATCH_ID;//批次号
        public String PERL;//性能
        public String TASK_TYPE;//任务类型
        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间

        @Override
        public String toString() {
            return "DbInceptBody{" +
                    "HOST_ID='" + HOST_ID + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", MODULE_ID='" + MODULE_ID + '\'' +
                    ", TASK_NAME='" + TASK_NAME + '\'' +
                    ", FILE_NAME='" + FILE_NAME + '\'' +
                    ", LOCAL_PATH='" + LOCAL_PATH + '\'' +
                    ", RECORD_CNT='" + RECORD_CNT + '\'' +
                    ", FIRST='" + FIRST + '\'' +
                    ", TYPE='" + TYPE + '\'' +
                    ", MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", BATCH_ID='" + BATCH_ID + '\'' +
                    ", PERL='" + PERL + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    '}';
        }
    }

    //标准话单格式
    public static class StandardBody {
        public String TASK_ID;
        public String HOST_ID;
        public String TASK_NAME;//任务名称
        public String TASK_TYPE;//任务类型1、话单任务
        public String PROCESS_ID;
        public String MODULE_ID;
        public String FILE_NAME;//文件名,
        public String LOCAL_INPUT_PATH;//本地文件路径
        public String LOCAL_OUTPUT_PATH;
        public String RECORD_CNT;//记录数
        public String DEAL_TIME;//处理时长
        public String TYPE;//话单类型
        public String MSG_TYPE;//REQUEST:请求,RESPONSE:应答
        public String STATE;//状态，OK：处理完成，ERROR：处理异常
        public String ERROR_MSG;//异常描述

        public int REPEAT_CNT;//重单
        public int ERROR_CNT;//错误单

        public int NORMAL_CNT;//正常单
        public int PRE_CNT;//预借单
        public int ABN_CNT;//异常单
        public int OTHER_CNT;//不计费话单
        public int INVALID_CNT;//无效单

        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间
        public String ELEMENT_STR;//处理串

        // 初始化消息包应答状态
        public void init_response() {
            STATE = "OK";
            MSG_TYPE = "RESPONSE";
            UPDATE_DATE = Factory.getSystemDateStr();
            NORMAL_CNT = 0;
            PRE_CNT = 0;
            ABN_CNT = 0;
            OTHER_CNT = 0;
            REPEAT_CNT = 0;
            ERROR_CNT = 0;
            INVALID_CNT = 0;
        }

        @Override
        public String toString() {
            return "StandardBody{" +
                    "TASK_ID='" + TASK_ID + '\'' +
                    ", HOST_ID='" + HOST_ID + '\'' +
                    ", TASK_NAME='" + TASK_NAME + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", MODULE_ID='" + MODULE_ID + '\'' +
                    ", FILE_NAME='" + FILE_NAME + '\'' +
                    ", LOCAL_INPUT_PATH='" + LOCAL_INPUT_PATH + '\'' +
                    ", LOCAL_OUTPUT_PATH='" + LOCAL_OUTPUT_PATH + '\'' +
                    ", RECORD_CNT='" + RECORD_CNT + '\'' +
                    ", DEAL_TIME='" + DEAL_TIME + '\'' +
                    ", TYPE='" + TYPE + '\'' +
                    ", MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", STATE='" + STATE + '\'' +
                    ", ERROR_MSG='" + ERROR_MSG + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    ", NORMAL_CNT=" + NORMAL_CNT +
                    ", PRE_CNT=" + PRE_CNT +
                    ", ABN_CNT=" + ABN_CNT +
                    ", OTHER_CNT=" + OTHER_CNT +
                    ", REPEAT_CNT=" + REPEAT_CNT +
                    ", INVALID_CNT=" + INVALID_CNT +
                    ", ERROR_CNT=" + ERROR_CNT +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", ELEMENT_STR='" + ELEMENT_STR + '\'' +
                    '}';
        }
    }

    public static class ConfigSyncBody {
        public String CONFIG_SYNC_ID;
        public String TASK_TYPE;//2 配置重载
        public String HOST_ID;
        public String PROCESS_ID;//
        public String MODULE_ID;
        public String LOAD_ID;
        public String MSG_COMMENT;//任务内容
        public String MSG_TYPE;//REQUEST:请求,RESPONSE:应答
        public String STATE;//状态，OK：处理完成，ERROR：处理异常
        public String ERROR_MSG;//异常描述
        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间

        @Override
        public String toString() {
            return "ConfigSyncBody{" +
                    "CONFIG_SYNC_ID='" + CONFIG_SYNC_ID + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", HOST_ID='" + HOST_ID + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", MODULE_ID='" + MODULE_ID + '\'' +
                    ", LOAD_ID='" + LOAD_ID + '\'' +
                    ", MSG_COMMENT='" + MSG_COMMENT + '\'' +
                    ", MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", STATE='" + STATE + '\'' +
                    ", ERROR_MSG='" + ERROR_MSG + '\'' +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    '}';
        }
    }

    public static class RollbackBody {
        public String MSG_TYPE;//REQUEST:请求,RESPONSE:应答
        public String TASK_ID;
        public String TASK_NAME;
        public String TASK_TYPE;//6 任务重做
        public String RB_MODULE_ID;//重做起始模块
        public String MSG_COMMENT;//任务内容
        public String PROCESS_ID;   // 状态变更进程

        public String STATE;//状态，OK：处理完成，ERROR：处理异常
        public String ERROR_MSG;//异常描述
        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间

        @Override
        public String toString() {
            return "RollbackBody{" +
                    "MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", TASK_ID='" + TASK_ID + '\'' +
                    ", TASK_NAME='" + TASK_NAME + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", RB_MODULE_ID='" + RB_MODULE_ID + '\'' +
                    ", MSG_COMMENT='" + MSG_COMMENT + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", STATE='" + STATE + '\'' +
                    ", ERROR_MSG='" + ERROR_MSG + '\'' +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    '}';
        }
    }

    //客户端命令消息格式
    public static class StartStopProcessBody {
        public String COMMAND_ID;   // 请求ID
        public String TASK_TYPE;    // 7、进程退出
        public String MSG_TYPE;     //REQUEST:请求,RESPONSE:应答
        public String REQUEST_PROCESS_ID; //请求进程ID
        public String PROCESS_ID;   // 状态变更进程
        public String MODULE_ID;   // 状态变更模块  ALL:所有模块退出
        public String ACTION;       // START:启动处理  STOP:暂停处理  EXIT:退出
        public String ACTION_DATE;  //动作执行时间，NOW:立即执行
        public String STATE;        //执行结果
        public String ERROR_MSG;    //错误信息
        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间
        public String VERSION;//版本信息

        @Override
        public String toString() {
            return "StartStopProcessBody{" +
                    "COMMAND_ID='" + COMMAND_ID + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", REQUEST_PROCESS_ID='" + REQUEST_PROCESS_ID + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", MODULE_ID='" + MODULE_ID + '\'' +
                    ", ACTION='" + ACTION + '\'' +
                    ", ACTION_DATE='" + ACTION_DATE + '\'' +
                    ", STATE='" + STATE + '\'' +
                    ", ERROR_MSG='" + ERROR_MSG + '\'' +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    ", VERSION='" + VERSION + '\'' +
                    '}';
        }
    }

    //客户端命令消息格式
    public static class DebugBody {
        public String COMMAND_ID;   // 请求ID
        public String TASK_TYPE;    // 8、日志等级控制
        public String MSG_TYPE;     //REQUEST:请求,RESPONSE:应答
        public String REQUEST_PROCESS_ID; //请求进程ID
        public String PROCESS_ID;   // 状态变更进程
        public String MODULE_ID;   // 状态变更模块  ALL:所有模块退出
        public String LEVEL;       // 0:关闭 ， 1:等级 2:等级
        public String PARAM;       //生效时间，NOW:立即执行
        public String STATE;        //执行结果
        public String ERROR_MSG;    //错误信息
        public String CREATE_DATE;//创建时间
        public String UPDATE_DATE;//更新时间

        @Override
        public String toString() {
            return "DebugBody{" +
                    "COMMAND_ID='" + COMMAND_ID + '\'' +
                    ", TASK_TYPE='" + TASK_TYPE + '\'' +
                    ", MSG_TYPE='" + MSG_TYPE + '\'' +
                    ", REQUEST_PROCESS_ID='" + REQUEST_PROCESS_ID + '\'' +
                    ", PROCESS_ID='" + PROCESS_ID + '\'' +
                    ", MODULE_ID='" + MODULE_ID + '\'' +
                    ", LEVEL='" + LEVEL + '\'' +
                    ", PARAM='" + PARAM + '\'' +
                    ", STATE='" + STATE + '\'' +
                    ", ERROR_MSG='" + ERROR_MSG + '\'' +
                    ", CREATE_DATE='" + CREATE_DATE + '\'' +
                    ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                    '}';
        }
    }

    public static class TaskBody {
        public DbInceptBody dbInceptBody = null;
        public List<StandardBody> standardBody = new ArrayList<>();

        @Override
        public String toString() {
            return "TaskBody{" +
                    "dbInceptBody=" + dbInceptBody +
                    ", standardBody=" + standardBody +
                    '}';
        }
    }


    public enum TASK_STATE {
        TA(1, "TA", "待处理"),
        TB(2, "TB", "处理中"),
        TC(3, "TC", "处理异常"),
        TD(4, "TD", "处理完成");

        int order_id;
        String state;
        String remark;

        TASK_STATE(int order_id, String state, String remark) {
            this.order_id = order_id;
            this.state = state;
            this.remark = remark;
        }

        public String getState() {
            return state;
        }
    }

    public enum TASK_TYPE {
        T_RECORD(1, "RECORD", "话单处理任务"),
        T_CONFIG(2, "CONFIG", "配置同步任务"),
        T_PROCESS(3, "PROCESS", "进程状态任务"),
        T_DISPATCH(4, "DISPATCH", "调度集群任务"),
        T_DBINCEPT(5, "DBINCEPT", "接收获取可用任务数"),
        T_ROLLBACK(6, "ROLLBACK", "任务重做"),
        T_SS_PROCESS(7, "SS_PROCESS", "进程启停任务"),
        T_DEBUG(8, "DEBUG", "日志等级开关"),
        T_RECORD_STATE(9, "RECORD_STATE", "进程话单处理状态");

        int order_id;
        String type;
        String remark;

        public String getType() {
            return type;
        }

        TASK_TYPE(int order_id, String type, String remark) {
            this.order_id = order_id;
            this.type = type;
            this.remark = remark;
        }
    }
}
