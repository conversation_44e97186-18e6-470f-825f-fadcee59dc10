package com.itco.entity.process;

public enum REDO_FLAG {
    CODE_0("0", "正常单"),
    CODE_1("1", "回收重算单"),
    CODE_2("2", "回收冲正单"),
    CODE_3("3", "回退重算单"),
    CODE_4("4", "回退冲正单"),
    CODE_11("11", "回收重算单"),
    CODE_20("20", "回收预开通重算单（预建档重算）"),
    CODE_21("21", "账期前重算单"),

    CODE_13("13", "历史月全回退重算单"),
    CODE_22("22", "当前账期以后回退重算单（历史月不会退）"),

    CODE_14("14", "原来的正常单变为14，被回退，已送财辅"),
    CODE_15("15", "冲销单，已送财辅"),

    CODE_24("24", "原来的正常单变为24，被回退，未送财辅"),
    CODE_25("25", "冲销单，未送财辅"),

    CODE_16("16", "已拆机追缴（即原来的正常单变为16）"),
    CODE_17("17", "拆机追缴冲正单"),

    CODE_26("26", "已倒挂追回回退单"),
    CODE_28("28", "已倒挂追回冲正单"),

    CODE_27("27", "倒挂追回回退单"),
    CODE_29("29", "倒挂追回冲正单");

    String code;
    String ch_name;

    REDO_FLAG(String code, String ch_name) {
        this.code = code;
        this.ch_name = ch_name;
    }

    public String getCode() {
        return code;
    }
}