package com.itco.entity.function;

import lombok.Data;

@Data
public class FunctionPerl {
    int function_id;   //函数标识
    String en_name;    //英文名称
    String ch_name;    //中文名称
    long cnt;               //调用成功次数
    long dt;                //调用成功总时长
    long perl;              //平均成功性能

    long failCnt;           //调用失败次数
    long failDt;            //调用失败总时长
    long failPerl;          //调用失败性能

    public FunctionPerl(int function_id, String en_name, String ch_name) {
        this.function_id = function_id;
        this.en_name = en_name;
        this.ch_name = ch_name;
        this.cnt = 0;
        this.dt = 0;
        this.perl = 0;
        this.failCnt = 0;
        this.failDt = 0;
        this.failPerl = 0;
    }

    public void setPlusCntPerl(long cnt, long dt) {
        this.cnt += cnt;
        this.dt += dt;
        this.perl = (1000000 * this.cnt) / (this.dt > 0 ? this.dt : 1);
    }

    public long getCnt() {
        return cnt;
    }

    public long getDt() {
        return dt;
    }

    public long getPerl() {
        return perl;
    }

    public void setFailPlusCntPerl(long cnt, long dt) {
        this.failCnt += cnt;
        this.failDt += dt;
        this.perl = (1000000 * this.failCnt) / (this.failDt > 0 ? this.failDt : 1);
    }

    @Override
    public String toString() {
        return String.format("{id=%-6d, perl=%-7d(次/每秒), cnt=%-6d(次), dt=%-9d(微秒), faileCnt=%-4d, %-40s}", function_id, perl, cnt, dt, failCnt, en_name);
    }

    static String sql="insert into function_perl(task_id,process_id,function_id,en_name,ch_name,cnt,dt,perl,fail_cnt,fail_dt,fail_perl,create_date) values (?,?,?,?,?,?,?,?,?,?,?,?)";

    public static String getSql() {
        return sql;
    }
}
