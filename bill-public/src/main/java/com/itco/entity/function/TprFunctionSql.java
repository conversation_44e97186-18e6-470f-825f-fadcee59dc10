package com.itco.entity.function;

import lombok.Data;

@Data
public class TprFunctionSql {
    int SQL_ID;
    String SQL_MSG;
    int IS_VALID;//是否需要上载：1 上载，0不上载
    String VALUE_TYPE;//9TA:Integer（32位整数型）9TB:Long（64位整数型）9TC:Real（实数型）9TD:String（字符串型）9TE:Date（日期型）9TF:Integer（32位整数型）数组9TG:Long（64位整数型）数组 9TH:Real（实数型）数组  9TI:String（字符串型）数组 9TJ:Date（日期型）数组
    int VALUE_ORDER;//返回值位置
    String FUNCTION_TYPE;//9FA:预处理函数;9FC:批价函数
    String FUNCTION_NAME;//函数名称
    String REMARK;//备注


}
