package com.itco.entity.function;

import com.alibaba.druid.util.StringUtils;

import java.util.List;

public class ResultValue {
    boolean bRet;       //返回值 0 成功 -1失败
    String error_msg;   //失败信息
    int value_type;    //返回值类型
    String value;       //返回值


    public static ResultValue getResultValue(List<String> list) {
        ResultValue ResultValue = new ResultValue();
        if (list == null) {
            ResultValue.setbRet(false);
            ResultValue.setError_msg("调用结果集为空");
        } else if (list.size() != 4) {
            ResultValue.setbRet(false);
            ResultValue.setError_msg("调用结果集异常");
        } else {
            if (list.get(0).equals("0")) {
                ResultValue.setbRet(true);
            } else {
                ResultValue.setbRet(false);
            }
            ResultValue.setError_msg(list.get(1));

            if (StringUtils.isEmpty(list.get(2))) {
                ResultValue.setValue_type(1);
            } else {
                ResultValue.setValue_type(Integer.parseInt(list.get(2)));
            }
            ResultValue.setValue(list.get(3));
        }
        return ResultValue;
    }

    public boolean isbRet() {
        return bRet;
    }

    public void setbRet(boolean bRet) {
        this.bRet = bRet;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public int getValue_type() {
        return value_type;
    }

    public void setValue_type(int value_type) {
        this.value_type = value_type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "RESULT_VALUE{" +
                "bRet=" + bRet +
                ", error_msg='" + error_msg + '\'' +
                ", value_type=" + value_type +
                ", value='" + value + '\'' +
                '}';
    }
}
