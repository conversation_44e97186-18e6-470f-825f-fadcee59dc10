package com.itco.entity.function;

import lombok.Data;

@Data
public class TprFunction {
    int function_id;//函数标识
    String en_name;//函数名称
    String ch_name;//中文名称
    String function_type;//函数类型（前台只有对功能函数才需要分析参数）
    String result_type;//返回值类型
    int param_num;//参数个数
    String src_path;//对应源程序的相对存放路径
    String lib_path;//库文件路径
    String comments;//备注
    int module_id;//大类模块或者功能点的标识
    String db_pkg_name;
    int latn_id;

}
