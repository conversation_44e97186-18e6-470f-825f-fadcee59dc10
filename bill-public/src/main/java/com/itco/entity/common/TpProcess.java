package com.itco.entity.common;

import lombok.Data;

import java.io.Serializable;

@Data
public class TpProcess implements Serializable {
    long process_id;//进程标识
    int module_id;//模块标识
    int host_id;//主机标识
    String status; //进程状态 TA:关闭，TB:空闲，TC:处理中，TD:异常关闭，TE:已分配任务
    String isPause; //挂起状态 0:正常，1:挂起
    String debugLevel; //日志级别 0:关闭   1:开启
    String type; // 处理业务类型
    int billing_line_id;//进程实例标识
    int perl;//性能
    String module_code;//模块编码
    int system_process_id;//系统进程标识
    String process_name;//进程名称
    String create_date;//启动时间
    String update_date;//更新时间
    String exit_date;//退出时间
    String host_name;//运行主机名称
    String host_ip;//运行主机IP
    String version;//程序版本

    @Override
    public String toString() {
        return "TpProcess{" +
                "process_id=" + process_id +
                ", module_id=" + module_id +
                ", host_id=" + host_id +
                ", host_name=" + host_name +
                ", host_ip=" + host_ip +
                ", version=" + version +
                ", status='" + status + '\'' +
                ", isPause=" + isPause +
                ", debugLevel=" + debugLevel +
                ", billing_line_id=" + billing_line_id +
                ", perl=" + perl +
                ", module_code='" + module_code + '\'' +
                ", system_process_id=" + system_process_id +
                ", process_name='" + process_name + '\'' +
                ", type='" + type + '\'' +
                ", create_date='" + create_date + '\'' +
                ", update_date='" + update_date + '\'' +
                ", exit_date='" + exit_date + '\'' +
                '}';
    }
}
