package com.itco.entity.common;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 应用版本发版日志表(DcoosTpModuleVersion)实体类
 *
 * <AUTHOR>
 * @since 2024-04-20 17:28:23
 */
@Data
public class TpModuleVersion implements Serializable {
    private static final long serialVersionUID = -31730105957853228L;
    /**
     * 序号
     */
    private Integer id;
    /**
     * 模块编号
     */
    private String moduleId;
    /**
     * 发布版本号
     */
    private String versionNumber;
    /**
     * 修订日期
     */
    private String revisionDate;
    /**
     * 修订人员
     */
    private String revisionPersonnel;
    /**
     * 需求单号
     */
    private String demandNumber;
    /**
     * Git提交前版本号
     */
    private String gitPreVersion;
    /**
     * 实现功能
     */
    private String functionality;
    /**
     * 变更代码文件
     */
    private String codeFileChanges;
    /**
     * 修订要点
     */
    private String revisionHighlights;
    /**
     * 注意事项
     */
    private String notes;
    /**
     * 版本代码完成时间
     */
    private String versionCodeCompletionTime;
    /**
     * 版本上线发布时间
     */
    private String versionOnlineReleaseTime;
}

