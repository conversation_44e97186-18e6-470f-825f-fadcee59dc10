package com.itco.entity.common;

import lombok.Data;

@Data
public class TpModule {
    int module_id;
    String module_name;
    int parent_module_id;
    String module_code;
    String module_type;
    int order_id;
    int startup_priority;
    String pro_path;
    String start_command;
    String stop_command;
    int max_process_num;
    String version;


    @Override
    public String toString() {
        return "TpModule{" +
                "module_id=" + module_id +
                ", module_name='" + module_name + '\'' +
                ", parent_module_id=" + parent_module_id +
                ", module_code='" + module_code + '\'' +
                ", module_type='" + module_type + '\'' +
                ", order_id=" + order_id +
                ", startup_priority=" + startup_priority +
                ", pro_path='" + pro_path + '\'' +
                ", start_command='" + start_command + '\'' +
                ", stop_command='" + stop_command + '\'' +
                ", max_process_num=" + max_process_num +
                ", version=" + version +
                '}';
    }
}
