package com.itco.entity.common;

import com.itco.framework.Version;
import lombok.Data;

import java.util.Map;

/**
 * @ClassName: Common
 * @Description: 公共配置类
 * <AUTHOR>
 * @Date 2021/7/27
 * @Version 1.0
 */
@Data
public class Common {
    /*********Common.properties ********begin*****************************************************************************/
    // 系统名称: ISS、TPSS
    String sSystem = "TPSS";

    // 省份标识: 591、371
    String sLatnId = "591";

    // 部署方式：CONOTAINER->容器化部署、VM->虚拟机部署、
    String deployment = "CONTAINER";

    // 话单流转方式: FILE、CTG_MQ、CTG_CACHE、HDFS
    String recordTransferMode = new String("FILE");

    //模块间任务流转方式：CTG_MQ、ZK
    String taskTransferMode = new String("CTG_MQ");
    //缓存话单hash key
    String recordCacheHashkey = new String("RECORD_NORMAL");
    //采集模式、HDFS、DATABASE
    String collection_mode = new String("HDFS");

    //因子库全称
    String jarClass = "com.itco.RuleFunction.RulePricingPlanIss_591";
    String jarPath = "D:\\maven_lib\\com\\itco\\rule-functionIss_591\\2.0.1\\rule-functionIss_591-2.0.1.jar";

    // LoadCfg上载，key散列取模
    int modulo = 320;

    // 缓存累计积压上限，暂停采集。
    int cacheLimitCnt = 300;

    // MQ话单流转方式，接收、采集单次读取记录数
    int taskCnt = 10000;
    // MQ包单条最大话单数，由于MQ限制，最大100
    int mqOnceCnt = 100;

    /*********Common.properties **********end***************************************************************************/

    // 调试开关
    int iDebug = 0;
    String sDebugLevel = "INFO";
    // 是否输出要素串
    int iElement = 0;

    // 模块特有
    int module_id = 1000;
    int process_id = 10000000;
    String moduleCode = "";
    int iMaxProcessCnt = 2;     //默认最大线程数
    int iMaxRecordCnt = 5000;   //默认单线程最大处理记录数

    Long lRedoRuleSeconds = 0L; //默认一个小时重新加载一次规则，单位毫秒
    Long lTimeOutSeconds = 180L * 1000; //线程处理超时，默认3分钟
    Long lTDProcessExitRollbackSeconds = 16L;//TD状态的进程，多久回收任务。默认16秒

    // 性能相关
    int iPerlOutCnt = 5000;     //5000条话单输入一次，因子性能
    int iPerlLow = 100;         //性能低于100的写数据库
    int iCurrCnt = 0;           //当前处理记录数

    boolean isOutRecord = true; //是否输出话单
    boolean isOutErrorRecord = true;//是否输出错误的话单
    boolean isErrorExit = true; //任务出错是否退出
    boolean isExistsError2FCL = false;//存在错单的任务，设置处理状态为ERROR，调度会设置状态为FCL

    int iDbIncept_mode = 0;     //0:接收不接收消息模式，1:接收消息模式

    Map<String, TpProcessMQ> processMQMap = null;

    //////////////////////////版本管理////////////////////////////////////////////////////////////////////////////
    String bill_public_version = "bill-public_2023-03-17 11:00";     //基础库版本
    String rule_function_version = null;                            //因子库版本
    String code_version = null;                                     //代码版本
    String deploy_version = null;                                   //补丁版本
    String version = null;

    ////////////////////////properties///////////////////////////////////////////////////////////////////////////
    //off line 离线模式开关
    boolean workFlag = false; //是否离线模式
    String offworkPath = null;
    String offWorkFileName = null;
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////


    ///////////////////////CommonProcess 使用////////////////////////////////////////////////////////////////////
    boolean modulePropertiesFlag = true; // module.properties配置是否启用
    boolean transferFlag = true; // 话单读取接口是否启动
    boolean taskMsgFlag = true;  // 消息接口是否启用

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public Common() {
        code_version = Version.getCode_version();
        deploy_version = Version.getDeploy_version();
        version = "[" + bill_public_version + "][" + deploy_version + "][" + code_version + "]";
    }

    @Override
    public String toString() {
        return "Common{" +
                "sSystem='" + sSystem + '\'' +
                ", sLatnId='" + sLatnId + '\'' +
                ", deployment='" + deployment + '\'' +
                ", recordTransferMode='" + recordTransferMode + '\'' +
                ", taskTransferMode='" + taskTransferMode + '\'' +
                ", recordCacheHashkey='" + recordCacheHashkey + '\'' +
                ", collection_mode='" + collection_mode + '\'' +
                ", jarClass='" + jarClass + '\'' +
                ", jarPath='" + jarPath + '\'' +
                ", modulo=" + modulo +
                ", taskCnt=" + taskCnt +
                ", mqOnceCnt=" + mqOnceCnt +
                ", iDebug=" + iDebug +
                ", sDebugLevel=" + sDebugLevel +
                ", iElement=" + iElement +
                ", module_id=" + module_id +
                ", process_id=" + process_id +
                ", moduleCode='" + moduleCode + '\'' +
                ", iMaxProcessCnt=" + iMaxProcessCnt +
                ", iMaxRecordCnt=" + iMaxRecordCnt +
                ", lRedoRuleSeconds=" + lRedoRuleSeconds +
                ", lTimeOutSeconds=" + lTimeOutSeconds +
                ", iPerlOutCnt=" + iPerlOutCnt +
                ", iPerlLow=" + iPerlLow +
                ", iCurrCnt=" + iCurrCnt +
                ", isOutRecord=" + isOutRecord +
                ", isOutErrorRecord=" + isOutErrorRecord +
                ", isExistsError2FCL=" + isExistsError2FCL +
                ", isErrorExit=" + isErrorExit +
                ", workFlag=" + workFlag +
                ", offworkPath='" + offworkPath + '\'' +
                ", offWorkFileName='" + offWorkFileName + '\'' +
                ", transferFlag=" + transferFlag +
                ", taskMsgFlag=" + taskMsgFlag +
                ", iDbIncept_mode=" + iDbIncept_mode +
                ", code_version=" + code_version +
                ", deploy_version=" + deploy_version +
                ", version=" + version +
                '}';
    }

}

