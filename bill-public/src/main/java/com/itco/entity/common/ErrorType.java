package com.itco.entity.common;

public class ErrorType {
    // bill-public框架
    public final static int BILL_PUBLIC_PROCESS_REGISTER_FAILE = 10001;    //进程注册失败
    public final static int BILL_PUBLIC_PROCESS_CONNECTION_LOSE = 10002;    //进程注册路径丢失

    // zookeeper
    public final static int ZOOKEEPER_EXCEPTION_CONNECTION_LOSS = 10101;        //连接断开
    public final static int ZOOKEEPER_EXCEPTION_CONNECTION_REFUSED = 10102;     //连接超时
    public final static int ZOOKEEPER_EXCEPTION_SESSION_EXPIRED = 10103;        //session失效
    public final static int ZOOKEEPER_EXCEPTION_KEEPER_EXCEPTION = 10104;       //抛异常
    public final static int ZOOKEEPER_EXCEPTION_INTERRUPTED_EXCEPTION = 10105;    //抛异常
    public final static int ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES = 10106;    //session连续失效10次

}
