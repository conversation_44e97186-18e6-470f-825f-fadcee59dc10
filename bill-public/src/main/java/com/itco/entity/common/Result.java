package com.itco.entity.common;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ResultEntity
 * @Description: 因子返回结果类
 * <AUTHOR>
 * @Date 2022/1/18
 * @Version 1.0
 */
@Data
public class Result {
    // true 因子调用成功，false:因子调用失败
    boolean bRet = true;
    // 因子调用失败原因
    String error_msg = new String();
    // 因子调用返回结果。
    Map<String, Object> result = new HashMap<>();

    public Result() {
    }

    public Result(boolean bRet) {
        this.bRet = bRet;
    }

    public Result(String msg) {
        this.bRet = false;
        error_msg = msg;
    }

    public void reset() {
        reset(true);
    }

    public void reset(boolean b) {
        bRet = b;
        error_msg = "";
    }

    public Result setErrorMsg(String msg) {
        bRet = false;
        error_msg = msg;
        return this;
    }

    public Result setErrorMsg(boolean b, String msg) {
        bRet = b;
        error_msg = msg;
        return this;
    }

    public void put(String key, Object value) {
        result.put(key, value);
    }

    public Object get(String key) {
        return result.get(key);
    }
}
