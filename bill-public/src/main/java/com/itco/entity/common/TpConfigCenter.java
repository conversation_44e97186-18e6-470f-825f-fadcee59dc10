package com.itco.entity.common;

import lombok.Data;

@Data
public class TpConfigCenter {
    int id;
    String en_name;
    String ch_name;
    String path;
    String type;
    String table_sql;
    String state;
    String comments;

    @Override
    public String toString() {
        return "ConfigrationCenter{" +
                "id=" + id +
                ", en_name='" + en_name + '\'' +
                ", ch_name='" + ch_name + '\'' +
                ", path='" + path + '\'' +
                ", type='" + type + '\'' +
                ", table_sql='" + table_sql + '\'' +
                ", state='" + state + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }

}
