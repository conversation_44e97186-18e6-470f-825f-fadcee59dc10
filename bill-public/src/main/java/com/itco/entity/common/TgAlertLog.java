package com.itco.entity.common;

import com.itco.framework.Factory;
import lombok.Data;

@Data
public class TgAlertLog {
    String msg_type;    //消息类型
    Long log_id;        //日志ID，递增序列
    int code_id;        //日志编码
    Long pf_id;         //进程标识
    String create_date; //创建时间
    String log_desc;    //日志描述
    String state;       //状态 9SA:未发送  9SB:已发送
    String state_date;  //更新时间

    public TgAlertLog() {
        msg_type = "alert";
        state = "9SA";
        create_date = Factory.getSystemDateStr();
        state_date = Factory.getSystemDateStr();
    }

    public TgAlertLog(int code_id, Long pf_id) {
        msg_type = "alert";
        this.code_id = code_id;
        this.pf_id = pf_id;
        this.state = "9SA";
        this.create_date = Factory.getSystemDateStr();
        this.state_date = Factory.getSystemDateStr();
    }
}
