package com.itco;

import com.itco.framework.Version;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;


@SpringBootApplication
public class BillApplication {

   /* @Bean
    void start() {
        Factory.setModule_id(1029);
        Factory.setModuleName("asn1");
        Factory.setBillingLineId("11");
        Factory.setClassName("com.itco.splitorder.module.CalcRecordSplitOrderSubThread");

        CommonProcessManager commonProcessManager = new CommonProcessManager();
        commonProcessManager.setClassName("com.itco.splitorder.module.CalcRecordSplitOrderSubThread");
        commonProcessManager.setTransfer(new SplitOrderTranfer());
        commonProcessManager.setaCalcRecordManager();
        if(!commonProcessManager.Init()){
            System.out.println("commonProcessManager.init() faile");
            return;
        }
        new Thread(commonProcessManager).start();
    }*/



    /**
     * bill-public_2023-01-30 11:00 -> 1、新增告警类TgAlertLog和异常类型类ErrorType  2、超时线程编号从1开始
     *
     */
    public static void main(String[] args) {
        if (Version.print(null, "bill-public_code_2024-01-06 11:00")) {
            return;
        }

        SpringApplication.run(BillApplication.class, args);
    }
}
