package com.itco.rulefunction.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class CTableOpt {
    static Log log = LogFactory.getLog(CTableOpt.class);

    protected int iThreadNo = 0;
    // 输入
    protected String tableName = null;
    protected String billingCycleId = null;
    protected String query = null;

    // 结果
    protected List<Map<String, Object>> g_ValueMapList = new ArrayList<>();
    protected int g_GroupId = 0;
    int g_CurrPos = 0;


    public CTableOpt(String name, int i, String billing_cycle_id) {
        tableName = name;
        iThreadNo = i;
        if(billing_cycle_id==null){
            billingCycleId = "202001";
        }else {
            billingCycleId = billing_cycle_id;
        }
    }


    /*普通表查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录*/
    public abstract boolean QueryByIndex(String filed);

    public void Reset() {
        //清空结果值
        clear();
    }

    public void clear() {
        g_ValueMapList.clear();
        g_CurrPos = 0;
        g_GroupId = 0;
    }


    public int Size() {
        return g_ValueMapList.size();
    }

    public boolean Next() {
        g_CurrPos++;
        if (g_ValueMapList.size() >= g_CurrPos) {
            return true;
        }
        return false;
    }

    public Map<String, Object> getValue() {
       /* for(Map.Entry<String, Object> map:g_ValueMapList.get(g_CurrPos-1).entrySet()){
            System.out.print("["+map.getKey()+":"+map.getValue()+"]");
        }
        System.out.println("");*/
        return g_ValueMapList.get(g_CurrPos - 1);
    }

    public int getG_GroupId() {
        return g_GroupId;
    }

    public String getErrorInfo() {
        return tableName + "_" + billingCycleId + "," + query;
    }
}
