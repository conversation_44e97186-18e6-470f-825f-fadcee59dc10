package com.itco.rulefunction.ctgcache;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.ctgcache.JEDISUtil;
import com.itco.component.ctgcache.JedisException;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.table.TprTable;
import com.itco.entity.table.TprTableFormatItem;
import com.itco.entity.table.TprTableIndex;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

public class TableService {
    static Log log = LogFactory.getLog(TableService.class);

    static Properties propsCtg = null;
    static String database = "databaseTable";
    static Common common = null;
    static ZkClientApi zkClientApi = null;
    static Map<Integer, TableService> tableServiceMap = new HashMap<>();
    static int iMaxInstCnt = 10;
    static int iDebug = 0;

    // 上载配置表
    static Map<Integer, TprTable> tprTableMap = new HashMap<>();
    static Map<Integer, List<TprTableFormatItem>> tprTableFormatItemMap = new HashMap<>();
    static Map<String, TprResourceAttr> tprResourceAttrEnNameMap = new HashMap<>();
    static Map<Integer, TprResourceAttr> tprResourceAttrMap = new HashMap<>();
    static Map<Integer, Map<Integer, List<TprTableIndex>>> tprTableIndexMap = new HashMap<>();

    static Map<Integer, TprTableFormatItem> tprTableItemMapF = new HashMap<>();
    static Map<String, TprTable> tprTableNameMap = new HashMap<>();

    static Map<String, Map<String, List<String>>> memory = new HashMap<>();
    static List<Integer> moduleSmallTableList = new ArrayList<>();
    static String billingCycleId = new String("202001");
    // ctgCache 客户端
    JEDISUtil jedisUtil = null;
    private final SimpleDateFormat tFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final SimpleDateFormat rFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    static boolean loadConfig() {
        String propFileName = zkClientApi.getModuleCode() + ".properties";
        Properties p = zkClientApi.getPropertiesFromZK(propFileName);
        if (p == null) {
            log.error("配置中心没有找到配置文件：" + propFileName);
            //return false;
        } else {
            if (null != p.getProperty("load_small_table")) {
                String[] tableTmp = p.getProperty("load_small_table").split(",");
                for (String str : tableTmp) {
                    moduleSmallTableList.add(Integer.parseInt(str));
                }
            }
        }
        log.info("moduleSmallTableList.size():" + moduleSmallTableList.size());
        propsCtg = zkClientApi.getPropertiesFromZK("ctgCache.properties");
        List<TprResourceAttr> attrList = zkClientApi.getTableFromZK("tpr_resource_attr", TprResourceAttr.class);
        List<TprTable> tableList = zkClientApi.getTableFromZK("tpr_table", TprTable.class);
        List<TprTableFormatItem> tableItem = zkClientApi.getTableFromZK("tpr_table_format_item", TprTableFormatItem.class);
        List<TprTableIndex> tableIndex = zkClientApi.getTableFromZK("tpr_table_index", TprTableIndex.class);

        setTprResourceAttrMap(attrList);
        setTprTableMap(tableList);
        setTprTableFormatItemMap(tableItem);
        setTprTableIndexMap(tableIndex);

        return true;
    }

    static boolean loadSmallTable() {
        memory.clear();


        for (Map.Entry<Integer, TprTable> tprTable : tprTableMap.entrySet()) {
            TprTable table = tprTable.getValue();

            if ("SMALL".equals(table.getTable_type())) {
                List<TprTableFormatItem> tableItems = tprTableFormatItemMap.get(table.getTable_id());
                if (tableItems == null) {
                    log.error("未配置表上载字段" + table.toString());
                    return false;
                }

                List<Map<String, Object>> list = new ArrayList<>();
                if (loadTableRecordTelePg(table.getUpload_sql(), list) > 0) {
                    int iCnt = loadCacheRecordByIndex(table, list);
                    if (iCnt < 0) {
                        log.error("加载小表失败:" + table.toString());
                        return false;
                    }
                } else {
                    log.info("\n" + "加载小表无数据：" + table.toString());
                }
            }
        }

        String s = new String();
        for (Map.Entry<String, Map<String, List<String>>> map : memory.entrySet()) {
            s += map.getKey() + "->" + map.getValue().size() + "\n";
        }
        log.info("\n" + s);

        return true;
    }

    public static boolean initCfg() {
        if (!loadConfig()) {
            log.error("loadConfig() 失败");
            return false;
        }
        return true;
    }

    public static boolean initCfgAst() {
        if (!loadConfig()) {
            log.error("loadConfig() 失败");
            return false;
        }
        if (!loadSmallTable()) {
            log.error("loadSmallTable() 失败");
            return false;
        }
        return true;
    }

    // 关闭构造函数
    private TableService() {
    }

    public static synchronized TableService getInstance() {
        if (tableServiceMap.get(0) == null) {
            TableService tableService = new TableService();
            tableService.init();
            tableServiceMap.put(0, tableService);
        }

        return tableServiceMap.get(0);
    }

    public static synchronized TableService getInstance(int i) {
        if (tableServiceMap.size() < iMaxInstCnt) {
            if (tableServiceMap.get(i) == null) {
                TableService tableService = new TableService();
                tableService.init();
                tableServiceMap.put(i, tableService);
            }
            return tableServiceMap.get(i);
        } else {
            log.info("超过最大可实例化数量 size():" + tableServiceMap.size() + ",iMaxInstCnt:" + iMaxInstCnt);
            return null;
        }
    }

    boolean init() {
        jedisUtil = new JEDISUtil();
        jedisUtil.setDatabase(database);
        jedisUtil.setPropsCtg(propsCtg);
        if (!jedisUtil.init()) {
            log.error("\n" + "jedisUtil.init() faile");
            return false;
        }
        return true;
    }

    /*宽表查询使用，宽表上载必须使用主键做tpr_table.key_attr_id,使用主键查询，才能保证查询结果只有一条记录*/
    public String getColuValueByattr(String tableAttrId, JSONObject record) {
        return getColuValueByattr(Integer.valueOf(tableAttrId), record);
    }

    /*宽表查询使用，宽表上载必须使用主键做tpr_table.key_attr_id,使用主键查询，才能保证查询结果只有一条记录*/
    public String getColuValueByattr(int tableAttrId, JSONObject record) {
        String sRet = null;
        TprTableFormatItem item = tprTableItemMapF.get(tableAttrId);
        if (item == null) {
            return null;
        }

        TprTable table = tprTableMap.get(item.getTable_id());
        if (table == null) {
            return null;
        }

        String fieldName = table.getKey_attr_name();
        if (fieldName == null) {
            return null;
        }

        String tableName = table.getEn_name();
        String billingCycleId = record.getString("billing_cycle_id");
        String fieldValue = record.getString(fieldName);
        String keyName = tprResourceAttrMap.get(item.getAttr_id()).getEn_name();

        String hashKey = tableName + "-" + billingCycleId;
        String value = null;
        try {
            value = jedisUtil.getJedisClientApi().hGet(hashKey, fieldValue);
        } catch (JedisException e) {
            e.printStackTrace();
        }
        if (value == null || value.equals("")) {
            return "";
        }

        if ("JSON".equals(table.getRedis_format())) {
            JSONObject jsonObject = JSONObject.parseObject(value);
            sRet = jsonObject.get(keyName).toString();
        } else if ("SEPARATOR".equals(table.getRedis_format())) {
            int orderId = item.getOrder_id();
            String[] arrayValue = null;
            if ("|".equals(table.getRedis_separator())) {
                arrayValue = value.split("\\|", -1);
            } else {
                arrayValue = value.split(table.getRedis_separator(), -1);
            }
            if (arrayValue != null) {
                sRet = arrayValue[orderId - 1];
            }
        } else {
            log.error("无法识别的上载格式类型：" + table.getRedis_format() + "," + table.toString());
            return "";
        }

        return sRet;
    }


    public String getStrValueByAttr(int tableAttrId, JSONObject record) {
        TprTableFormatItem item = tprTableItemMapF.get(tableAttrId);
        if (item == null) {
            return null;
        }

        TprTable table = tprTableMap.get(item.getTable_id());
        if (table == null) {
            return null;
        }

        String fieldName = table.getKey_attr_name();
        if (fieldName == null) {
            return null;
        }

        String billingCycleId = record.getString("billing_cycle_id");
        String fieldValue = record.getString(fieldName);

        String hashKey = table.getEn_name() + "-" + billingCycleId;
        String value = null;
        try {
            value = jedisUtil.getJedisClientApi().hGet(hashKey, fieldValue);
        } catch (JedisException e) {
            e.printStackTrace();
            return "";
        }

        return value;
    }

    public String getStrValueByName(String tableName, String billingCycleId, String filed) {
        String value = null;

        TprTable tprTable = tprTableNameMap.get(tableName);
        if (tprTable == null) {
            log.error("");
            return null;
        }

        //  根据索引查询结果
        String hashKey = tableName + "-" + billingCycleId;
        try {
            value = jedisUtil.getJedisClientApi().hGet(hashKey, filed);
        } catch (JedisException e) {
            e.printStackTrace();
            return "";
        }

        return value;
    }

    Map<String, Object> getMapByValueOnSeparator(TprTable tprTable, String record) {
        String[] filedArray = null;
        if ("|".equals(tprTable.getRedis_separator())) {
            filedArray = record.split("\\|", -1);
        } else {
            filedArray = record.split(tprTable.getRedis_separator(), -1);
        }
        List<TprTableFormatItem> itemList = tprTableFormatItemMap.get(tprTable.getTable_id());

        if (filedArray == null || filedArray.length != itemList.size()) {
            log.error("filedArray.length:" + filedArray.length + ",itemList.size():" + itemList.size());
            log.error("查询失败" + ",缓存信息:" + record);
            log.error(tprTable.getTable_id() + ",缓存字段数和配置表tpr_table_format_item字段数不一致");
            return null;
        }

        Map<String, Object> map = new HashMap<>();
        for (int j = 0; j < itemList.size(); j++) {
            TprResourceAttr tprTableAttr = tprResourceAttrMap.get(itemList.get(j).getAttr_id());
            map.put(tprTableAttr.getEn_name(), filedArray[j]);
        }
        return map;
    }

    public String getStrByMapOnSeparator(TprTable tprTable, Map<String, Object> map) {
        List<TprTableFormatItem> tableItems = getTprTableFormatItemMap().get(tprTable.getTable_id());

        String record = null;
        for (TprTableFormatItem item : tableItems) {
            String enName = getTprResourceAttrMap().get(item.getAttr_id()).getEn_name();
            if (record == null) {
                if (map.get(enName) == null) {
                    record = "";
                } else {
                    record = map.get(enName).toString();
                }
            } else {
                if (map.get(enName) == null) {
                    record += "|";
                } else {
                    record += "|" + map.get(enName).toString();
                }
            }
        }
        return record;
    }

    /*普通表查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录*/
    public boolean QueryByIndex(String tableName, String billingCycleId, String filed, int groupId, List<Map<String, Object>> result) {
        Map<String, Object> queryValue = new HashMap<>();
        List<Map<String, Object>> resultTmp = new ArrayList<>();

        // 解析查询条件
        if (!analysisField(filed, queryValue)) {
            log.error("analysisField falie");
            return false;
        }

        TprTable tprTable = tprTableNameMap.get(tableName);
        if (tprTable == null) {
            log.error("查询表：" + tableName + ",未上载.");
            return false;
        }

        //  查询结果
        List<String> valuelistTmp = new ArrayList<>();
        groupId = smartOrder(tprTable, queryValue);
        if (iDebug == 1) {
            log.info("\n" + "查询匹配索引组:" + groupId + ",查询条件:" + queryValue.toString());
        }

        if (groupId <= 0) {
            log.error(tprTable.getTable_id() + "," + tprTable.getEn_name() + ",query:" + filed + ",没有匹配的索引组");
            return false;
        } else if (groupId > 0) {
            String valueStr = new String();

            for (TprTableIndex tprTableIndex : tprTableIndexMap.get(tprTable.getTable_id()).get(groupId)) {
                String enName = tprResourceAttrMap.get(tprTableIndex.getField_attr_id()).getEn_name();
                if (valueStr.equals("")) {
                    valueStr = queryValue.get(enName).toString();
                } else {
                    valueStr += "|" + queryValue.get(enName).toString();
                }
            }

            //  根据索引查询结果
            String tmpStr = null;
            if ("SMALL".equals(tprTable.getTable_type())) {
                String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId;
                Map<String, List<String>> mapTmp = memory.get(hashKey);
                if (mapTmp != null) {
                    valuelistTmp = mapTmp.get(valueStr);
                    if (valuelistTmp == null || valuelistTmp.size() == 0) {
                        if (iDebug == 1) {
                            log.info("没有匹配的小表数据：" + hashKey + "->" + valueStr);
                        }
                        return true;
                    }
                }
            } else if ("BIG".equals(tprTable.getTable_type())) {
                int hashCode = Math.abs(valueStr.hashCode() % common.getModulo());
                String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId + "-" + hashCode;
                try {
                    tmpStr = jedisUtil.getJedisClientApi().hGet(hashKey, valueStr);
                } catch (JedisException e) {
                    log.error(e.getMessage());
                    return false;
                }

                if (tmpStr == null || tmpStr.equals("")) {
                    if (iDebug == 1) {
                        log.info("没有匹配的大表数据：" + hashKey + "->" + valueStr);
                    }
                    return true;
                }
                //System.out.println("hashKey:" + hashKey + ",valueStr:" + valueStr + ",tmpStr:" + tmpStr);
                valuelistTmp = JSONObject.parseArray(tmpStr, String.class);
            } else {
                log.error("无法识别的大表，小表类型：" + tprTable.getTable_type() + "," + tprTable.toString());
                return false;
            }
        }

        for (int i = 0; i < valuelistTmp.size(); i++) {
            String record = valuelistTmp.get(i);

            if ("JSON".equals(tprTable.getRedis_format())) {
                if (record != null && !record.equals("")) {
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(record);
                        Map<String, Object> map = jsonObject.getInnerMap();
                        resultTmp.add(map);
                    } catch (JSONException e) {
                        log.error(e.getMessage() + ";record:" + record);
                        return false;
                    }
                }
            } else if ("SEPARATOR".equals(tprTable.getRedis_format())) {//非JSON格式
                Map<String, Object> mapTmp = getMapByValueOnSeparator(tprTable, record);
                if (mapTmp != null) {
                    resultTmp.add(mapTmp);
                } else {
                    log.error("record:" + record + ",无法解析成map数据");
                    return false;
                }
            } else {
                log.error("无法识别的上载格式类型：" + tprTable.getRedis_format() + "," + tprTable.toString());
                return false;
            }
        }

        if (iDebug == 1) {
            String msg = new String();
            msg = "\n" + "resultTmp.size():" + resultTmp.size();
            for (Map<String, Object> map : resultTmp) {
                msg += "\n" + map.toString();
            }
            log.info(msg);
        }

        if (resultTmp.size() > 0) {
            result.addAll(resultTmp);
        }

        return true;
    }

    /*条件查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录，并筛选得到符合条件的记录*/
    public boolean QueryByIndex(String tableName, String billingCycleId, String queryStr, String cond, int groupId, List<Map<String, Object>> result) {
        List<String> filed = new ArrayList<>();
        List<String> logic = new ArrayList<>();
        List<String> range = new ArrayList<>();
        // 解析查询条件
        if (!analysisField(cond, filed, logic, range)) {
            log.error("analysisField falie");
            return false;
        } else {
            log.info("成功解析");
            log.info("filed = " + filed.size());
            log.info("logic = " + logic.size());
            log.info("range = " + range.size());
        }

        List<Map<String, Object>> tempResult = new ArrayList<>();
        boolean bRet = QueryByIndex(tableName, billingCycleId, queryStr, groupId, tempResult);

        log.info("tempResult.size() = " + tempResult.size());
        for (Map<String, Object> objectMap : tempResult) {
            log.info(objectMap);
        }

        log.info("result.size() = " + result.size());

        if (bRet) {

            for (Map<String, Object> stringObjectMap : tempResult) {
                int confFlag = 0;

                for (int j = 0; j < filed.size(); j++) {

                    String temp = filed.get(j);
                    log.info(temp);

                    log.info("value = " + stringObjectMap.get("share_rule_type_id").toString());
                    log.info("value = " + stringObjectMap.get(filed.get(j)));

                    int value = Integer.parseInt(stringObjectMap.get("share_rule_type_id").toString());
                    String tempLogic = logic.get(j);
                    boolean passFlag = false;

                    switch (tempLogic) {
                        case ">":
                            if (value > Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case "<":
                            if (value < Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case "=":
                            if (value == Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case ">=":
                            if (value >= Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case "<=":
                            if (value <= Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case "!=":
                            if (value != Integer.parseInt(range.get(j))) {
                                passFlag = true;
                            }
                            break;
                        case "in":
                            String[] rangeList = range.get(j).split("\\,", -1);
                            for (String s : rangeList) {
                                if (value == Integer.parseInt(s)) {
                                    passFlag = true;
                                }
                            }
                            break;
                        case "not in":
                            String[] rangeNoList = range.get(j).split("\\,", -1);
                            int noFlag = 0;
                            for (String s : rangeNoList) {
                                if (value != Integer.parseInt(s)) {
                                    noFlag++;
                                }
                                if (noFlag == rangeNoList.length) {
                                    passFlag = true;
                                }
                            }
                            break;
                        default:
                            log.info("\n" + "传入的运算符错误 " + logic);
                            return false;
                    }
                    if (passFlag) {
                        confFlag++;
                    }
                }
                if (confFlag == filed.size()) { // 符合所有条件
                    result.add(stringObjectMap);
                }
            }

        }

        log.info("result.size() = " + result.size());
        for (Map<String, Object> stringObjectMap : result) {
            log.info("result = " + stringObjectMap);
        }

        return true;
    }

    public boolean setValue(String tableName, String billingCycleId, String filed, String value) {
        String hashKey = tableName + "-" + billingCycleId;
        return jedisUtil.getJedisClientApi().hSet(hashKey, filed, value);
    }

    /**
     * 写入索引时，存在相同的索引字段值一样的情况，这时候会拼接记录位置用逗号隔开
     *
     * @param tableName
     * @param billingCycleId
     * @param groupId
     * @param filed
     * @param value
     * @return
     */
    public boolean setIndexValue(String tableName, String billingCycleId, String groupId, String filed, String value) {
        int hashCode = Math.abs(filed.hashCode()) % common.getModulo();
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId + "-" + hashCode;
        String oldValue = null;
        List<String> oldList = new ArrayList<>();

        if (jedisUtil.getJedisClientApi().isExistsField(hashKey, filed)) {
            try {
                oldValue = jedisUtil.getJedisClientApi().hGet(hashKey, filed);
            } catch (JedisException e) {
                log.error(e.getMessage());
                return false;
            }
            if (oldValue != null && !oldValue.equals("")) {

                //log.info(hashKey + ",filed:" + filed + "-> oldValue:" + oldValue);
                oldList = JSONObject.parseArray(oldValue, String.class);
            }
        }
        oldList.add(value);
        String newValue = JSONObject.toJSONString(oldList);

        return jedisUtil.getJedisClientApi().hSet(hashKey, filed, newValue);
    }

    /**
     * 写入索引时，存在相同的索引字段值一样的情况，这时候会拼接记录位置用逗号隔开
     *
     * @param tableName
     * @param billingCycleId
     * @param groupId
     * @param filed
     * @param value
     * @return
     */
    public boolean setIndexValueByNpcode(String tableName, String billingCycleId, String groupId, String filed, String value) {
        int hashCode = Math.abs(filed.hashCode()) % common.getModulo();
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId + "-" + hashCode;

        String oldValue = null;
        List<String> newList = new ArrayList<>();

        if (jedisUtil.getJedisClientApi().isExistsField(hashKey, filed)) {
            try {
                oldValue = jedisUtil.getJedisClientApi().hGet(hashKey, filed);
            } catch (JedisException e) {
                log.error(e.getMessage());
                return false;
            }
            if (oldValue != null && !oldValue.equals("")) {
                TprTable tprTable = tprTableNameMap.get(tableName);
                if (tprTable == null) {
                    log.error("查询表：" + tableName + ",未上载.");
                    return false;
                }

                //log.info(hashKey + ",filed:" + filed + "-> oldValue:" + oldValue + ",newValue:" + value);
                String npcodeInstIdNewTmp = null;
                JSONObject newJson = null;

                if ("JSON".equals(tprTable.getRedis_format())) {
                    newJson = JSONObject.parseObject(value);
                } else if ("SEPARATOR".equals(tprTable.getRedis_format())) {
                    Map<String, Object> mapTmp = getMapByValueOnSeparator(tprTable, value);
                    if (mapTmp != null) {
                        newJson = new JSONObject(mapTmp);
                    } else {
                        log.error("value:" + value + ",无法解析成map数据");
                        return false;
                    }
                }

                //log.info("newJson:" + newJson.toJSONString());
                npcodeInstIdNewTmp = String.valueOf(newJson.get("npcode_inst_id"));
                Date effDate = null;

                try {
                    effDate = tFormat.parse(newJson.getString("eff_date"));
                } catch (ParseException e) {
                    log.error("新值生效时间格式不合法：" + newJson);
                    return false;
                }

                List<String> listTmp = JSONObject.parseArray(oldValue, String.class);
                for (String oldStr : listTmp) {
                    JSONObject jsonOldTmp = null;

                    if ("JSON".equals(tprTable.getRedis_format())) {
                        jsonOldTmp = JSONObject.parseObject(oldStr);
                    } else if ("SEPARATOR".equals(tprTable.getRedis_format())) {
                        Map<String, Object> oldMapTmp = getMapByValueOnSeparator(tprTable, oldStr);
                        if (oldMapTmp != null) {
                            jsonOldTmp = new JSONObject(oldMapTmp);
                        } else {
                            log.error("oldStr:" + oldStr + ",无法解析成map数据");
                            return false;
                        }
                    }

                    String npcodeInstIdOldTmp = String.valueOf(jsonOldTmp.get("npcode_inst_id"));

                    //如果pcode_inst_id，一致，说明是同一条记录，覆盖。
                    if (!StringUtils.isEmpty(npcodeInstIdNewTmp) && npcodeInstIdNewTmp.equals(npcodeInstIdOldTmp)) {
                        Long codeIdIdNewTmp = Long.parseLong(newJson.get("code_id").toString());
                        Long codeIdOldTmp = Long.parseLong(jsonOldTmp.get("code_id").toString());

                        if (codeIdIdNewTmp > codeIdOldTmp) {
                            // 多个npcode_inst_id情况，code_id大的为准
                            continue;
                        }
                    } else {
                        Object expObject = jsonOldTmp.get("exp_date");
                        if (expObject != null) {
                            Date expDate = null;
                            try {
                                expDate = tFormat.parse(expObject.toString());
                            } catch (ParseException e) {
                                log.error("旧值失效时间不合法：" + oldValue);
                                return false;
                            }
                            if (expDate.after(effDate)) {
                                // 修改旧数据的 失效时间。
                                jsonOldTmp.put("exp_date", newJson.getString("eff_date"));
                                if ("JSON".equals(tprTable.getRedis_format())) {
                                    newList.add(jsonOldTmp.toJSONString());
                                } else if ("SEPARATOR".equals(tprTable.getRedis_format())) {
                                    String strTmp = getStrByMapOnSeparator(tprTable, jsonOldTmp);
                                    newList.add(strTmp);
                                }
                                continue;
                            }
                        }
                    }
                    // 不存在，就累加
                    newList.add(oldStr);
                }
            }
        }

        newList.add(value);
        String newValue = JSONObject.toJSONString(newList);

        return jedisUtil.getJedisClientApi().hSet(hashKey, filed, newValue);
    }

    /**
     * 从redis读取表数据  （入库模块专用）
     *
     * @param table 表名
     * @param field 字段
     * @param t     写入数据
     * @param <T>
     * @return 返回redis查询出来的，数据
     */
    public <T> List<T> getTableFromRedis(String table, String field, Class<T> t) {
        List<T> tList = new ArrayList<>();

        String configJson = null;
        try {
            configJson = jedisUtil.getJedisClientApi().hGet(table, field);
        } catch (JedisException e) {
            e.printStackTrace();
            return null;
            /*log.error("685，" + e.getMessage());
            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                    e.getMessage().indexOf("JedisException") > -1) {
                try {
                    Thread.sleep(1000);
                    jedisUtil.getJedisClientApi().jedisReset();
                    configJson = jedisUtil.getJedisClientApi().hGet(table, field);
                } catch (Exception ex) {
                    log.error("694，" + ex.getMessage());
                }
            }*/
        }

        if (configJson == null || configJson.equals("")) {
            return tList;
        }

        int iBeginPos = 0, iPos = 0;
        while ((iPos = configJson.indexOf("\n", iBeginPos)) != -1) {
            //zk 模块编号和模块名称获取
            String json = configJson.substring(iBeginPos, iPos);
            iBeginPos = iPos + 1;

            T tRet = JSON.parseObject(json, t);
            tList.add(tRet);
        }

        String json = configJson.substring(iBeginPos);
        T tRet = JSON.parseObject(json, t);
        tList.add(tRet);

        return tList;
    }

    /**
     * 从redis读取表数据  （入库模块专用）
     *
     * @param table 表名
     * @param field 字段
     * @return 返回redis查询出来的，数据
     */
    public String getTableFromRedis(String table, String field) {
        String configJson = null;
        try {
            configJson = jedisUtil.getJedisClientApi().hGet(table, field);
        } catch (JedisException e) {
            e.printStackTrace();
            return null;
        }
        return configJson;
    }

    /**
     * 设置表数据到redis （入库模块专用）
     *
     * @param table
     * @param field
     * @param list
     * @param <T>
     * @return
     */
    public <T> boolean setTableToRedis(String table, String field, List<T> list) {
        String record = new String();
        for (T t : list) {
            if (!record.equals("")) {
                record += "\n" + JSONObject.toJSONString(t, SerializerFeature.WriteNullStringAsEmpty);
            } else {
                record += JSONObject.toJSONString(t, SerializerFeature.WriteNullStringAsEmpty);
            }
        }
        boolean bRet = false;
        try {
            bRet = jedisUtil.getJedisClientApi().hSet(table, field, record);
        } catch (Exception e) {
            // 入库更新缓存断点超时，重新写一次。
            e.printStackTrace();
            log.error("750，" + e.getMessage());
            if (e.getMessage().indexOf("java.net.SocketTimeoutException") > -1 ||
                    e.getMessage().indexOf("java.net.SocketException") > -1 ||
                    e.getMessage().indexOf("JedisException") > -1 ||
                    e.getMessage().indexOf("errorCode:50010010") > -1) {
                try {
                    Thread.sleep(1000);
                    jedisUtil.getJedisClientApi().jedisReset();
                    bRet = jedisUtil.getJedisClientApi().hSet(table, field, record);
                } catch (Exception ex) {
                    log.error("759，" + ex.getMessage());
                }
            }
        }

        return bRet;
    }

    /**
     * 设置表数据到redis （入库模块专用）
     *
     * @param table
     * @param field
     * @param value
     * @param <T>
     * @return
     */
    public <T> boolean setTableToRedis(String table, String field, String value) {
        return jedisUtil.getJedisClientApi().hSet(table, field, value);
    }

    public Long getLen(String tableName, String billingCycleId) {
        String hashKey = tableName + "-" + billingCycleId;
        return jedisUtil.getJedisClientApi().hLen(hashKey);
    }

    public Long getIndexLen(String tableName, String billingCycleId, String groupId) {
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId;
        return jedisUtil.getJedisClientApi().hLen(hashKey);
    }

    public Long getIndexLen(String tableName, String billingCycleId, String groupId, String hashCode) {
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId + "-" + hashCode;
        return jedisUtil.getJedisClientApi().hLen(hashKey);
    }

    public boolean deleteKey(String tableName, String billingCycleId) {
        String hashKey = tableName + "-" + billingCycleId;
        return jedisUtil.getJedisClientApi().deleteKey(hashKey);
    }

    public boolean deleteKey(String tableName, String billingCycleId, String groupId) {
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId;
        return jedisUtil.getJedisClientApi().deleteKey(hashKey);
    }

    public boolean deleteKey(String tableName, String billingCycleId, String groupId, String hashCode) {
        String hashKey = tableName + "-" + billingCycleId + "-group-" + groupId + "-" + hashCode;
        return jedisUtil.getJedisClientApi().deleteKey(hashKey);
    }

    public boolean deleteHashKey(String tableName, String billingCycleId, String field) {
        String hashKey = tableName + "-" + billingCycleId;
        return jedisUtil.getJedisClientApi().deleteHashkey(hashKey, field);
    }

    public boolean isExistKey(String key) {
        return jedisUtil.getJedisClientApi().isExistsKey(key);
    }

    public boolean isExistField(String key, String field) {
        return jedisUtil.getJedisClientApi().isExistsField(key, field);
    }


    public boolean close() {
        jedisUtil.getJedisClientApi().close();
        return true;
    }

    public boolean close(int i) {
        jedisUtil.getJedisClientApi().close();
        tableServiceMap.remove(i);
        return true;
    }

    public static void closeAll() {
        for (Map.Entry<Integer, TableService> map : tableServiceMap.entrySet()) {
            map.getValue().close();
        }
        tableServiceMap.clear();
    }

    public boolean reset() {
        jedisUtil.reset();
        return true;
    }

    public boolean reConnection() {
        jedisUtil.reConnection();
        return true;
    }

    /*解析字符串*/
    boolean analysisField(String str, Map<String, Object> map) {
        int pos = str.indexOf(":");
        if (pos < 0 || str == null || str.length() == 0) {
            log.error("输入的参数值不合法，" + str);
            return false;
        }

        String name = str.substring(0, pos);
        String value = str.substring(pos + 1);

        String[] aName = name.split("\\|", -1);
        String[] aValue = value.split("\\|", -1);


        if (aName.length != aValue.length) {
            log.info("输入的参数字段和值数量不匹配");
            return false;
        }
        for (int i = 0; i < aName.length; i++) {
            map.put(aName[i].toLowerCase(), aValue[i]);
        }

        return true;
    }

    /*解析字符串*/
    boolean analysisField(String str, List<String> filed, List<String> logic, List<String> range) {

        do {
            int count = str.indexOf("|");
            String strTemp;
            if (count == 0) {
                str = str.substring(1);
                count = str.indexOf("|");
            }
            if (count == -1) {
                strTemp = str;
            } else {
                strTemp = str.substring(0, count);
            }

            log.info("clear to inDb to clear test");

            int sign; // 运算符位置
            if ((sign = strTemp.indexOf(">")) > 0) {
                int pos = strTemp.indexOf("=");
                filed.add(strTemp.substring(0, sign).trim());// 将字段名存入filed
                if (pos > 0) {
                    logic.add(">=");    // 运算符存入 logic
                    range.add(strTemp.substring(pos + 1).trim()); // 取值范围存入range
                } else {
                    logic.add(">");                         // 运算符存入 logic
                    range.add(strTemp.substring(sign + 1).trim()); // 取值范围存入range
                }
            } else if ((sign = strTemp.indexOf("<")) > 0) {
                int pos = strTemp.indexOf("=");
                filed.add(strTemp.substring(0, sign).trim());
                if (pos > 0) {
                    logic.add("<=");
                    range.add(strTemp.substring(pos + 1).trim());
                } else {
                    logic.add("<");
                    range.add(strTemp.substring(sign + 1).trim());
                }
            } else if ((sign = strTemp.indexOf("=")) > 0) {
                int pos = strTemp.indexOf("!");
                if (pos > 0) {
                    filed.add(strTemp.substring(0, pos).trim());
                    logic.add("!=");
                } else {
                    filed.add(strTemp.substring(0, sign).trim());
                    logic.add("=");
                }
                range.add(strTemp.substring(sign + 1).trim());
            } else if ((sign = strTemp.indexOf("not in")) > 0) {
                filed.add(strTemp.substring(0, sign).trim());
                int pos = strTemp.indexOf("(");
                int end = strTemp.indexOf(")");
                logic.add("not in");
                range.add(strTemp.substring(pos + 1, end).trim());
            } else if ((sign = strTemp.indexOf("in")) > 0) {
                filed.add(strTemp.substring(0, sign).trim());
                int pos = strTemp.indexOf("(");
                int end = strTemp.indexOf(")");
                logic.add("in");
                range.add(strTemp.substring(pos + 1, end).trim());
            }

            if (count != -1) {
                str = str.substring(count);
            } else {
                break;
            }
        } while (str.indexOf("|") >= 0);

        for (int i = 0; i < filed.size(); i++) {
            log.info("filed = " + filed.get(i));
            log.info("logic = " + logic.get(i));
            log.info("range = " + range.get(i));
        }

        return true;
    }

    int smartOrder(TprTable tprTable, Map<String, Object> query) {
        Map<Integer, List<TprTableIndex>> tableIndexMap = tprTableIndexMap.get(tprTable.getTable_id());
        if (tableIndexMap == null) {
            log.error("未配置查询索引tpr_table_index.table_id:" + tprTable.getTable_id());
            return -1;
        }
        Map<Integer, Integer> weightMap = new HashMap<>();
        boolean bFlag = false;
        // 查询索引组编号
        for (Map.Entry<Integer, List<TprTableIndex>> tableIndex : tableIndexMap.entrySet()) {
            int weight = 0;
            for (TprTableIndex index : tableIndex.getValue()) {
                String enName = tprResourceAttrMap.get(index.getField_attr_id()).getEn_name();
                if (query.containsKey(enName)) {
                    weight++;
                    bFlag = true;
                } else {
                    weight--;
                }

            }
            if (weight > 0) {
                weightMap.put(tableIndex.getKey(), weight);
            }
        }
        if (bFlag) {
            List<Map.Entry<Integer, Integer>> sortList = new ArrayList<>(weightMap.entrySet());
            Collections.sort(sortList, (e1, e2) -> (e1.getValue() - e2.getValue()));
            return sortList.get(sortList.size() - 1).getKey();
        }
        return -1;
    }

    public Map<Integer, TprTable> getTprTableMap() {
        return tprTableMap;
    }

    public Map<Integer, List<TprTableFormatItem>> getTprTableFormatItemMap() {
        return tprTableFormatItemMap;
    }

    public Map<Integer, TprTableFormatItem> getTprTableItemMapF() {
        return tprTableItemMapF;
    }

    public Map<Integer, TprResourceAttr> getTprResourceAttrMap() {
        return tprResourceAttrMap;
    }

    public Map<Integer, Map<Integer, List<TprTableIndex>>> getTprTableIndexMap() {
        return tprTableIndexMap;
    }

    public static void setTprTableMap(List<TprTable> tableList) {
        tprTableMap.clear();
        tprTableNameMap.clear();
        for (TprTable table : tableList) {
            if (tprTableMap.get(table.getTable_id()) == null) {
                if ((moduleSmallTableList.indexOf(table.getTable_id()) < 0 && moduleSmallTableList.size() != 0 &&
                        "SMALL".equals(table.getTable_type()))) {
                    continue;
                }
                tprTableMap.put(table.getTable_id(), table);
                tprTableNameMap.put(table.getEn_name().toLowerCase(), table);
            }
        }
    }

    public static void setTprTableFormatItemMap(List<TprTableFormatItem> tableItem) {
        tprTableFormatItemMap.clear();
        tprTableItemMapF.clear();
        for (TprTableFormatItem item : tableItem) {
            if (tprTableFormatItemMap.get(item.getTable_id()) != null) {
                tprTableFormatItemMap.get(item.getTable_id()).add(item);
            } else {
                List<TprTableFormatItem> list = new ArrayList<>();
                list.add(item);
                tprTableFormatItemMap.put(item.getTable_id(), list);
            }
            tprTableItemMapF.put(item.getTable_attr_id(), item);
        }
    }

    public static void setTprResourceAttrMap(List<TprResourceAttr> attrList) {
        tprResourceAttrMap.clear();
        tprResourceAttrEnNameMap.clear();
        for (TprResourceAttr attr : attrList) {
            if (tprResourceAttrMap.get(attr.getAttr_id()) == null) {
                tprResourceAttrMap.put(attr.getAttr_id(), attr);
                tprResourceAttrEnNameMap.put(attr.getEn_name().toLowerCase(), attr);
            }
        }
    }

    public static void setTprTableIndexMap(List<TprTableIndex> tprTableIndexList) {
        tprTableIndexMap.clear();
        for (TprTableIndex tprTableIndex : tprTableIndexList) {
            if (tprTableIndexMap.get(tprTableIndex.getTable_id()) == null) {
                Map<Integer, List<TprTableIndex>> map = new HashMap<>();
                List<TprTableIndex> list = new ArrayList<>();
                list.add(tprTableIndex);
                map.put(tprTableIndex.getIndex_group_id(), list);

                tprTableIndexMap.put(tprTableIndex.getTable_id(), map);
            } else {
                Map<Integer, List<TprTableIndex>> integerListMap = tprTableIndexMap.get(tprTableIndex.getTable_id());
                if (integerListMap.get(tprTableIndex.getIndex_group_id()) == null) {
                    List<TprTableIndex> list = new ArrayList<>();
                    list.add(tprTableIndex);

                    integerListMap.put(tprTableIndex.getIndex_group_id(), list);
                } else {

                    integerListMap.get(tprTableIndex.getIndex_group_id()).add(tprTableIndex);
                }
            }
        }
    }


    public static void setiMaxInstCnt(int iMaxInstCnt) {
        TableService.iMaxInstCnt = iMaxInstCnt;
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        if (TableService.zkClientApi == null) {
            TableService.zkClientApi = zkClientApi;
        }
        DbPool.setZkClientApi(zkClientApi);
    }

    public static void setCommon(Common common) {
        TableService.common = common;
        iDebug = common.getIDebug();
    }

    static int loadTableRecordTelePg(String sql, List<Map<String, Object>> list) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultSetMetaData metaData = null;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            if (connTmp == null) {
                log.error("获取数据库连接失败");
                return -1;
            }
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            metaData = rs.getMetaData();

            if (list == null) {
                list = new ArrayList<>();
            } else {
                list.clear();
            }

            // 遍历结果集
            while (rs.next()) {
                // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
                Map<String, Object> map = new HashMap<>();
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < metaData.getColumnCount(); i++) {
                    // 获取列名
                    String cname = metaData.getColumnLabel(i + 1);
                    //获取列类型的int表示形式，以及列类型名称
                    //System.out.println("列名："+gRsmd.getColumnName(i+1)+",列类型:"+gRsmd.getColumnType(i + 1)+"----"+gRsmd.getColumnTypeName(i+1));
                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname, value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    list.add(map);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            return -1;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }

        return list.size();
    }

    static int loadCacheRecordByIndex(TprTable table, List<Map<String, Object>> list) {
        int cnt = 0;
        String sPrint = String.format("table_id:%-5d ,en_name:%-30s ,format:%-9s ,count:%-6d ,ch_name:%-30s", table.getTable_id(), table.getEn_name(), table.getRedis_format(), list.size(), table.getCh_name());
        log.info("\n" + sPrint);
        List<TprTableFormatItem> tableItems = tprTableFormatItemMap.get(table.getTable_id());
        Map<Integer, List<TprTableIndex>> tableIndexMap = tprTableIndexMap.get(table.getTable_id());
        if (tableItems == null || tableIndexMap == null) {
            log.error("tpr_table_format_item、tpr_table_index配置不全；" + table.toString());
            return -1;
        }

        /*String sPrimaryKey = table.getKey_attr_name();
        if (sPrimaryKey == null) {
            log.error("无法识别的主键标识：" + table.getKey_attr_name());
            return -1;
        }*/

        // 遍历每条记录
        for (Map<String, Object> map : list) {
            // 拼接上载字段
            String record = null;
            if ("JSON".equals(table.getRedis_format())) {
                JSONObject jsonObject = new JSONObject();
                for (TprTableFormatItem item : tableItems) {
                    //log.info("attr:"+item.toString());
                    String enName = tprResourceAttrMap.get(item.getAttr_id()).getEn_name();
                    jsonObject.put(enName, map.get(enName));
                }
                record = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            } else if ("SEPARATOR".equals(table.getRedis_format())) {
                for (TprTableFormatItem item : tableItems) {
                    //log.info("attr:"+item.toString());
                    String enName = tprResourceAttrMap.get(item.getAttr_id()).getEn_name();
                    if (record == null) {
                        if (map.get(enName) == null) {
                            record = new String("");
                        } else {
                            record = map.get(enName).toString();
                        }
                    } else {
                        if (map.get(enName) == null) {
                            record += table.getRedis_separator();
                        } else {
                            record += table.getRedis_separator() + map.get(enName).toString();
                        }
                    }
                }
            } else {
                log.error("无法识别的上载格式类型：" + table.getRedis_format() + "," + table.toString());
                return -1;
            }

            // 拼接索引字段
            for (Map.Entry<Integer, List<TprTableIndex>> IndexGroupMap : tableIndexMap.entrySet()) {
                String groupId = String.valueOf(IndexGroupMap.getKey());

                String hashKey = table.getEn_name() + "-" + billingCycleId + "-group-" + groupId;
                Map<String, List<String>> mapTmp = memory.get(hashKey);
                if (mapTmp == null) {
                    mapTmp = new HashMap<>();
                    memory.put(hashKey, mapTmp);
                }

                String indexStr = new String();
                for (TprTableIndex item : IndexGroupMap.getValue()) {
                    String enName = tprResourceAttrMap.get(item.getField_attr_id()).getEn_name();
                    if (map.containsKey(enName)) {
                        if (map.get(enName) != null) {
                            if (indexStr.equals("")) {
                                indexStr += map.get(enName).toString().trim();
                            } else {
                                indexStr += "|" + map.get(enName).toString().trim();
                            }
                        } else {
                            log.error(table.getTable_id() + "->" + table.getEn_name() + ",index_group_id:" + item.getIndex_group_id() + "->" + "上载索引字段存在空值：" + enName);
                            return -1;
                        }
                    } else {
                        log.error(table.getTable_id() + "->" + table.getEn_name() + ",index_group_id:" + item.getIndex_group_id() + "->" + "上载语句中不存在索引字段：" + enName);
                        return -1;
                    }
                }

                List<String> listTmp = mapTmp.get(indexStr);
                if (listTmp == null) {
                    listTmp = new ArrayList<>();
                }
                listTmp.add(record);
                mapTmp.put(indexStr, listTmp);
                //log.info("hashKey:" + hashKey + ",indexStr:" + indexStr + ",record:" + record);
            }

            cnt++;
        }

        return cnt;
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @param value
     * @return
     */
    public boolean lock(String lockKey, String value) {
        return jedisUtil.getJedisClientApi().lock(lockKey, value);
    }

    /**
     * 解锁
     *
     * @param lockKey
     * @param value
     * @return
     */
    public boolean unLock(String lockKey, String value) {
        return jedisUtil.getJedisClientApi().unLock(lockKey, value);
    }
}
