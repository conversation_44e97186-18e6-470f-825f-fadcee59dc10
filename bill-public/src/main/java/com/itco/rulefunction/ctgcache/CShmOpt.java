package com.itco.rulefunction.ctgcache;

import com.itco.rulefunction.impl.CTableOpt;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class CShmOpt extends CTableOpt {
    static Log log = LogFactory.getLog(CShmOpt.class);

    // 接口
    TableService tableService = null;

    public CShmOpt(String name, int i) {
        super(name.toLowerCase(), i, "202001");
    }

    public CShmOpt(String name, int i, String billing_cycle_id) {
        super(name.toLowerCase(), i, billing_cycle_id);
    }

    /*普通表查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录*/
    public boolean QueryByIndex(String filed) {
        //清空结果值
        Reset();

        if (tableService == null) {
            tableService = TableService.getInstance(iThreadNo);
            if (tableService == null) {
                log.error(getErrorInfo() + ",初始化失败");
                return false;
            }
        }

        if (!tableService.QueryByIndex(tableName, billingCycleId, filed, g_GroupId, g_ValueMapList)) {
            log.error("iThreadNo:" + iThreadNo + "," + tableName + "-" + billingCycleId + ",filed:" + filed + ",失败");
            return false;
        }

        return true;
    }

    /*条件查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录，并筛选得到符合条件的记录*/
    public boolean QueryByIndex(String queryStr, String cond) {
        //清空结果值
        Reset();

        if (tableService == null) {
            tableService = TableService.getInstance(iThreadNo);
            if (tableService == null) {
                log.error(getErrorInfo() + ",初始化失败");
                return false;
            }
        }

        return tableService.QueryByIndex(tableName, billingCycleId, queryStr, cond, g_GroupId, g_ValueMapList);

    }


}
