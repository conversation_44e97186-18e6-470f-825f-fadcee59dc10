package com.itco.rulefunction.database;


import com.itco.rulefunction.impl.CTableOpt;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class CDBaseOpt extends CTableOpt {
    static Log log = LogFactory.getLog(CDBaseOpt.class);

    // 接口
    static CDbutils cDbutils = null;

    public CDBaseOpt(String name, int i) {
        super(name, i, null);
    }

    public CDBaseOpt(String name, int i, String billing_cycle_id) {
        super(name, i, billing_cycle_id);
    }

    /*普通表查询使用，根据索引字段查询，查询多条记录，使用Next()来获取下一条记录*/
    public boolean QueryByIndex(String filed) {
        query = filed;
        //清空结果值
        Reset();

        cDbutils = CDbutils.getInstance();
        if (cDbutils == null) {
            log.error(getErrorInfo() + ",初始化失败");
            return false;
        }

        boolean bRet = cDbutils.QueryByIndex(tableName, billingCycleId, filed, g_GroupId, g_ValueMapList);

        return bRet;
    }
}
