package com.itco.rulefunction.database;

import com.itco.component.jdbc.DecodePassword;
import com.itco.component.zookeeper.ZkClientApi;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.*;

public class CDbutils {
    static Log log = LogFactory.getLog(CDbutils.class);

    Connection conn = null;

    String driver;
    String url;
    String username;
    String publicKey;
    String password;

    static boolean autoCommit = false; //false :自动提交  true:手动提交

    static ZkClientApi zkClientApi = null;
    static CDbutils cDbutils = null;

    public static CDbutils getInstance() {
        if (cDbutils == null) {
            cDbutils = new CDbutils();
            cDbutils.init();
        }

        return cDbutils;
    }

    // 关闭构造函数
    private CDbutils() {
    }

    public static Connection getConnection() {
        Connection tmpConn = null;
        Properties p = null;
        p = zkClientApi.getPropertiesFromZK("jdbc.properties");

        String driver = p.getProperty("driver");
        String url = p.getProperty("url");
        String username = p.getProperty("username");
        String publicKey = p.getProperty("publicKey");
        String password = p.getProperty("password");

        try {
            Class.forName(driver);
            String passwd = DecodePassword.decryption(publicKey, password);
            //log.info("url:" + url + ",username:" + username + ",passwd:" + passwd);
            tmpConn = DriverManager.getConnection(url, username, passwd);

            //tmpConn.setAutoCommit(!autoCommit);

            return tmpConn;
        } catch (SQLException e) {
            log.error(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return null;
    }

    public boolean init() {

        Properties p = null;
        p = zkClientApi.getPropertiesFromZK("jdbc.properties");

        driver = p.getProperty("driver");
        url = p.getProperty("url");
        username = p.getProperty("username");
        publicKey = p.getProperty("publicKey");
        password = p.getProperty("password");

        try {
            Class.forName(driver);
            String passwd = DecodePassword.decryption(publicKey, password);
            //log.info("url:" + url + ",username:" + username + ",passwd:" + passwd);
            conn = DriverManager.getConnection(url, username, passwd);
            return true;
        } catch (SQLException e) {
            log.error(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return false;
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        CDbutils.zkClientApi = zkClientApi;
    }

    public static void setAutoCommit(boolean autoCommit) {
        CDbutils.autoCommit = autoCommit;
    }

    public boolean retConn() {
        try {
            close();
            Class.forName(driver);
            String passwd = DecodePassword.decryption(publicKey, password);
            conn = DriverManager.getConnection(url, username, passwd);

            //conn.setAutoCommit(!autoCommit);

            return true;
        } catch (SQLException e) {
            log.error(e.getMessage());
        } catch (ClassNotFoundException e) {
            log.error(e.getMessage());
        }
        return false;
    }

    void close() {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    void close(PreparedStatement ps) {
        try {
            if (ps != null) {
                ps.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public boolean exeUpdate(String sql, Object... obj) {
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }

            ps.executeUpdate();
            if (autoCommit) {
                conn.commit();
            }
            return true;
        } catch (SQLException e) {
            log.error(e.getMessage());
        } finally {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public boolean exeUpdateThrowException(String sql, Object... obj) throws SQLException {
        PreparedStatement ps = null;
        ps = conn.prepareStatement(sql);
        for (int i = 0; i < obj.length; i++) {
            ps.setObject(i + 1, obj[i]);
        }
        ps.executeUpdate();
        if (autoCommit) {
            conn.commit();
        }
        return true;
    }

    public <T> List<T> queryList(Class<T> t, String sql, Object... params) {
        List<T> list = new ArrayList<>();
        T obj = null;
        PreparedStatement ps = null;

        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            ResultSet rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            ResultSetMetaData rsmd = rs.getMetaData();
            // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
            Map<String, Object> map = new HashMap<>();
            // 遍历结果集
            while (rs.next()) {
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = rsmd.getColumnLabel(i + 1);
                    //获取列类型的int表示形式，以及列类型名称
                    //System.out.println("列名："+rsmd.getColumnName(i+1)+",列类型:"+rsmd.getColumnType(i + 1)+"----"+rsmd.getColumnTypeName(i+1));
                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname, value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    // 获取map集合键集(列名集合)
                    Set<String> columnNames = map.keySet();
                    // 创建对象
                    obj = t.newInstance();//new Student() //java.lang.Object
                    for (String column : columnNames) {
                        // 根据键获取值
                        Object value = map.get(column);

                        //当数据对象不为空时，才注入数据到属性中
                        if (Objects.nonNull(value)) {
                            // 获取属性对象
                            Field f = t.getDeclaredField(column);

                            // 设置属性为可访问状态
                            f.setAccessible(true);
                            // 为属性设置
                            f.set(obj, value);

                           /* String sType=null;
                            for (int i = 0; i < rsmd.getColumnCount(); i++) {
                                if(column.equals(rsmd.getColumnName(i+1))){
                                    sType=rsmd.getColumnTypeName(i+1);
                                }
                            }
                            if(sType.equals("DECIMAL")){
                                // 为属性设置
                                f.set(obj, String.valueOf(value));
                            }else {
                                // 为属性设置
                                f.set(obj, value);
                            }*/
                        }
                    }
                    list.add(obj);
                }
            }
        } catch (SQLException e) {
            log.error(e.getMessage());
        } catch (InstantiationException e) {
            log.error(e.getMessage());
        } catch (IllegalAccessException e) {
            log.error(e.getMessage());
        } catch (NoSuchFieldException e) {
            log.error(e.getMessage());
        } catch (SecurityException e) {
            log.error(e.getMessage());
        }
        return list;
    }


    public List<Map<String, Object>> queryMap(String sql, Object... params) {
        List<Map<String, Object>> recordMapList = new ArrayList<>();
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            ResultSet rs = ps.executeQuery();
            ResultSetMetaData rsmd = rs.getMetaData();
            //ORM操作（对象关系映射）
            while (rs.next()) {
                Map<String, Object> recordMap = new HashMap<>();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    //获取指定列的列名称
                    String cname = rsmd.getColumnLabel(i + 1);
                    //获取列值
                    Object value = rs.getObject(cname);
                    recordMap.put(cname, value);
                }
                recordMapList.add(recordMap);
            }
        } catch (SQLException e) {
            log.error(e.getMessage());
            return null;
        } catch (SecurityException e) {
            log.error(e.getMessage());
            return null;
        }
        return recordMapList;
    }

    public boolean QueryByIndex(String tableName, String billingCycleId, String filed, int groupId, List<Map<String, Object>> result) {

        // 解析查询条件
        Map<String, Object> queryValue = analysisField(filed);
        if (queryValue == null || queryValue.size() == 0) {
            System.out.println("analysisField falie");
            return false;
        }

        String sqlWhere = new String(" where");
        if (billingCycleId == null || billingCycleId.equals("")) {

        } else {
            sqlWhere += " billing_cycle_id=" + billingCycleId;
        }

        if (queryValue.size() > 0) {
            boolean bFrist = true;
            for (Map.Entry<String, Object> map : queryValue.entrySet()) {
                if (bFrist) {
                    sqlWhere += " " + map.getKey() + "=?";
                    bFrist = false;
                } else {
                    sqlWhere += " and " + map.getKey() + "=?";
                }
            }
        }

        try {
            ExeQeurySQL(tableName, sqlWhere, queryValue, result);
            return true;
        } catch (SQLException e) {
            log.error(e.getMessage());
            if (e.getMessage().indexOf("com.mysql.cj.jdbc.exceptions.CommunicationsException") > 0 ||
                    e.getMessage().indexOf("wait_timeout") > 0 ||
                    e.getMessage().indexOf("autoReconnect=true") > 0) {
                log.info("重新获取数据库连接实例：");
                if (retConn()) {
                    try {
                        ExeQeurySQL(tableName, sqlWhere, queryValue, result);
                        return true;
                    } catch (SQLException ex) {
                        log.error(ex.getMessage());
                    }
                }
            }
        } catch (SecurityException e) {
            log.error(e.getMessage());
        }
        return false;
    }

    boolean ExeQeurySQL(String tableName, String sqlWhere, Map<String, Object> queryValue, List<Map<String, Object>> result) throws SQLException {
        String sql = "select * from " + tableName + sqlWhere;

        PreparedStatement ps = null;
        ps = conn.prepareStatement(sql);
        int iFor = 0;
        for (Map.Entry<String, Object> map : queryValue.entrySet()) {
            ps.setObject(iFor + 1, map.getValue());
            iFor++;
        }

        ResultSet rs = ps.executeQuery();
        // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
        ResultSetMetaData rsmd = rs.getMetaData();

        while (rs.next()) {
            // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
            Map<String, Object> map = new HashMap<>();

            // 遍历所有的列
            for (int i = 0; i < rsmd.getColumnCount(); i++) {
                // 获取列名
                String cname = rsmd.getColumnLabel(i + 1);
                // 获取列值
                Object value = rs.getObject(cname);
                // 将列明与列值存储到map中
                map.put(cname, value);
            }

            if (result.size() > 500) {
                log.info("查询结果大于 100 :" + sql);
                break;
            }
            result.add(map);
        }

        ps.close();
        return true;
    }

    /*解析字符串*/
    Map<String, Object> analysisField(String str) {
        Map<String, Object> map = new HashMap<>();
        int pos = str.indexOf(":");
        if (pos < 0 || str == null || str.length() == 0) {
            System.out.println("输入的参数值不合法，" + str);
            return null;
        }

        String name = str.substring(0, pos);
        String value = str.substring(pos + 1);

        String[] aName = name.split("\\|");
        String[] aValue = value.split("\\|");


        if (aName.length != aValue.length) {
            System.out.println("输入的参数字段和值数量不匹配");
            return null;
        }
        for (int i = 0; i < aName.length; i++) {
            map.put(aName[i], aValue[i]);
        }

        return map;
    }
}