
logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.org.springframework.boot=ERROR
logging.level.org.springframework.web=ERROR
logging.level.org.springframework.boot.web=ERROR
logging.level.com.ctg.itrdc.cache=ERROR
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n


#µ÷ÊÔÊ¹ÓÃ¿ª¹Ø workFlag:offline ÀëÏßÄ£Ê½  onlineÆäËûÇé¿ö£ºÔÚÏßµ÷¶ÈÄ£Ê½
workFlag=offline
workDir=D:\\coding\\test
workFileName=N_REAL_20220607195134_2000_4116
