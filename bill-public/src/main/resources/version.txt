--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2024-01-31 11:00
【修订日期】：2024-01-31
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：61572
【实现功能】：
1、出账流程支持动态数据源切换，默认数据源：jdbc.properties，动态数据源：druid.properties
2、zk.properties的下面两个配置做开关

#是否使用druid.properties
druid.properties.enabled=false
#是否使用动态数据源监听切换
dynamic.datasource.enabled=false

【变更文件】：
新增：DbMonitor/src/main/resources/DbMonitor.properties
新增：bill-public/src/main/java/com/itco/component/ComponentConfiguration.java
新增：bill-public/src/main/java/com/itco/component/dbmonitor/DruidConfiguration.java
新增：bill-public/src/main/java/com/itco/component/dbmonitor/DynamicDataSourceRouter.java
新增：bill-public/src/main/java/com/itco/component/dbmonitor/MasterConfigWatcher.java
修改：bill-public/src/main/java/com/itco/component/jdbc/DbPool.java
修改：bill-public/src/main/java/com/itco/component/zookeeper/ZkClientApi.java
修改：bill-public/src/main/java/com/itco/framework/log/LogAlert.java
修改：CmdClient/src/main/java/com/itco/cmdclient/Thread/ManagerThread.java
修改：Rating/src/main/java/com/itco/RatingApplication.java
修改：upload/src/main/java/com/itco/upload/module/Configration.java
修改：bill-public/src/main/resources/zk.properties
修改：DbMonitor/src/main/resources/zk.properties
修改：upload/src/main/resources/zk.properties
修改：bill-public/src/main/resources/version.txt
修改：bill-public/pom.xml

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-03-17 11:00
【修订日期】：2023-03-17
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：55025
【实现功能】：
1、TableService.java携号转网的数据10208上载时，之前只支持JSON格式占用缓存空间大，现在支持SEPARATOR分隔符的方式，减少缓存空间。
2、话单流转使用FILE时，DCOOS_TP_WORK_PATH.local_output_path不用配置绝对路径，只需要相对路径。
3、Common类新增isExistsError2FCL，存在处理出错话单的，是否设置任务失败，默认是否。

【变更文件】：
修改：src/main/java/com/itco/entity/common/Common.java
修改：src/main/java/com/itco/framework/record/impl/settle/impl/TransferFile.java
修改：src/main/java/com/itco/rulefunction/ctgcache/TableService.java
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、如果需要设置存在处理出错的话单，就设置任务失败，is_exists_error_2_fcl=true
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-02-21 11:00
【修订日期】：2023-02-21
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54686
【实现功能】：
1、DbInceptBody、StandardBody、RollbackBody三个消息体添加TASK_NAME字段；
2、调度接收到的任务，保存任务名称到任务表里。
3、zookeeper连接失败，重新连接错了修改调整，涉及调度,DbIncept,Collection,统一任务框架。

【变更文件】：
修改：src/main/java/com/itco/entity/process/MsgBody.java
修改：src/main/java/com/itco/component/zookeeper/ZkClientApi.java
修改：src/main/java/com/itco/framework/message/TaskMsgManager.java
修改：src/main/resources/version.txt
修改：src/main/resources/zk.properties

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-02-14 11:00
【修订日期】：2023-02-14
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54589
【实现功能】：
1、调度、接收、主流程模块出现zookeeper连接异常时，输出告警日志到tg_alert_log表
2、zookeeper的session连续失效10次，触发重新初始化zookeeper接口，重新注册
2、zookeeper的CONNECTION_LOSS、CONNECTION_REFUSED的异常，等待自动重连上
3、DbPool.init()新增接口，初始化使用
4、新增LogAlert输出告警日志

【变更文件】：
修改：src/main/java/com/itco/component/jdbc/DbPool.java
修改：src/main/java/com/itco/component/zookeeper/ZkClientApi.java
修改：src/main/java/com/itco/entity/common/Common.java
修改：src/main/java/com/itco/entity/common/ErrorType.java
修改：src/main/java/com/itco/framework/Factory.java
修改：src/main/java/com/itco/framework/message/TaskMsgManager.java
修改：src/main/java/com/itco/framework/process/impl/SettleProcessManager.java
新增：src/main/java/com/itco/framework/log/LogAlert.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-02-07 11:00
【修订日期】：2023-02-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54545
【实现功能】：
1、run Exception :java.util.ConcurrentModificationException 解决异常问题
2、任务超时时长，单位秒

【变更文件】：
修改：src/main/java/com/itco/framework/process/impl/SettleProcessManager.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-01-30 11:00
【修订日期】：2023-01-30
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54452
【实现功能】：
1、bill-public新增告警类TgAlertLog和异常类型类ErrorType

【变更文件】：
修改：bill-public/src/main/java/com/itco/component/jdbc/DBUtils.java
修改：bill-public/src/main/java/com/itco/entity/common/Common.java
修改：bill-public/src/main/java/com/itco/framework/process/impl/SettleProcessManager.java
新增：bill-public/src/main/java/com/itco/entity/common/ErrorType.java
新增：bill-public/src/main/java/com/itco/entity/common/TgAlertLog.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-01-17 11:00
【修订日期】：2023-01-17
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54263
【实现功能】：
1、StandardBody消息包新增 int INVALID_CNT;//无效单。
2、处理完的话单文件，正常单，预结单，异常单，不计费话单，无效单，统计优化。
3、新增版本日志功能 java -jar Dispatch-0.0.1-SNAPSHOT.jar version -n5 ,可以查看最近的五个版本日志。

【变更文件】：
main/java/com/itco/entity/common/Common.java
main/java/com/itco/entity/process/MsgBody.java
main/java/com/itco/framework/calcrecord/CalcRecordManager.java
main/java/com/itco/framework/message/impl/TaskMsgCtgMq.java
main/java/com/itco/framework/process/impl/SettleProcessManager.java
main/java/com/itco/framework/Version.java

【修订要点】：
  1、新增版本日志。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------