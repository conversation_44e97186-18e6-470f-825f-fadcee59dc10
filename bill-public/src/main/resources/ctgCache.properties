# ç¼å­æ¨¡å¼åæ¢
# false - ä½¿ç¨ctgcacheï¼true - ä½¿ç¨Redis
use.redis=true

# Redisæ¨¡å¼éç½®ï¼åæºæéç¾¤ï¼ï¼ä»å¨use.redis=trueæ¶çæ
redis.mode=single
# redis.mode=cluster

# Redisç¹æ®éç½®
# true - ä½¿ç¨åå§å¯ç ï¼ä¸è§£å¯ï¼ï¼false - ä½¿ç¨è§£å¯åçå¯ç ï¼ä»å¨use.redis=trueæ¶çæ
redis.use.raw.password=false
# å¦æéè¦ä½¿ç¨ææå¯ç ï¼å¯ä»¥è®¾ç½®ä¸ºtrueï¼å¹¶å¨ä¸æ¹è®¾ç½®èªå®ä¹çRediså¯ç 
# ä¾å¦ï¼password=your_redis_password

################# éç¨éç½® #################
# ç¼å­æå¡å¨è¿æ¥ä¿¡æ¯ï¼å¯ä»¥å¡«åå¤ä¸ªèç¹ï¼Rediséç¾¤æ¨¡å¼ä½¿ç¨å¤ä¸ªï¼
host=***********:6169

# ç¨æ·è®¤è¯ä¿¡æ¯
# ctgcacheæ¨¡å¼ä½¿ç¨usernameãpublicKeyåpassword
# Redisæ¨¡å¼ä½¿ç¨passwordï¼å¦æredis.use.raw.password=falseï¼åä¼åè¿è¡è§£å¯ï¼
username=tpss_cache
publicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALZoLDmz7V6ffI/Fzkhwhxebqjm3R21Y7NwY91grkLZXs8SZcb2D3OSuDFbmD7+nFU4gdMu1Yc+a7J973dBQ6lcCAwEAAQ==
password=FcSlC1KHYHtMYsNbUNg6B00bnwWtc+f7CdTivWrCXush5TZoNqfd4Rl556/Z2VYhjn45xL2Ed5TIKSom5EEx+g==

# è¿æ¥æ± éç½®
maxIdle=5
maxTotal=10
minIdle=3
maxWaitMillis=3000
period=3000
monitorTimeout=200

# è¿æ¥è¶æ¶éç½®
connectionTimeout=2000
soTimeout=2000

# æ°æ®åºéç½®
databaseRecord=group.100012_4_ctgcache-itco-01.tpss_591_record
databaseTable=group.100012_4_ctgcache-itco-01.tpss_591_table
databaseLock=group.100012_4_ctgcache-itco-01.tpss_591_lock
#databasefilter=group.itco_cache_01.tpss-filter
#databasepreproc=group.itco_cache_01.tpss-table

