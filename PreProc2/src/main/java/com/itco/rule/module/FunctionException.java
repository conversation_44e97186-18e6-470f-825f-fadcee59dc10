package com.itco.rule.module;

public class FunctionException extends Exception {
    String function_id;
    String error_msg;
    public FunctionException(String id, String error){
        function_id=id;
        error_msg=error;
    }


    public String getFunction_id() {
        return function_id;
    }

    public String getError_msg() {
        return error_msg;
    }

    @Override
    public String toString() {
        return "FunctionException{" +
                "function_id=" + function_id +
                ", error_msg='" + error_msg + '\'' +
                '}';
    }
}
