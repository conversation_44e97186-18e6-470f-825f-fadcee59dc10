package com.itco.rule.module;

import com.alibaba.fastjson.JSONObject;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.entity.process.REDO_FLAG;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.rule.api.GenCode;
import com.itco.rule.entity.entityDef.ErrorType;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Map;
import java.util.Properties;

public class CalcRecordRule extends CalcRecordManager {
    static Log log = LogFactory.getLog(CalcRecordRule.class);
    private GenCode genCode = null;

    /*整个模块初始化
     * 处理线程启动初始化操作，只有程序启动时执行一次。配置了多少个子线程就执行几次。
     * */
    @Override
    public boolean OnInit() {
        log.info("OnInit start ,startMode:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());
        String path = "PreProc2.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);
        String databaseName = props.getProperty("database_name");
        String stFieldName = props.getProperty("st_field_name");
        boolean bRecordFormatFlag = true;
        if (databaseName == null) {
            log.error(path + ",没有配置试算数据库名 database_name");
            return false;
        }
        if (stFieldName == null) {
            log.error(path + ",没有配置source_event_type_id取值字段名st_field_name的值");
            return false;
        }

        if ("false".equals(props.getProperty("record_format_flag"))) {
            bRecordFormatFlag = false;
        }

        log.info("话单格式转换标志:" + bRecordFormatFlag);
        genCode = new GenCode(getThreadNum(), databaseName, stFieldName);
        genCode.setM_debug(common.getIDebug());
        genCode.setbRecordFormatFlag(bRecordFormatFlag);
        genCode.setCommon(common);
        if (!genCode.Initgencode(isbInitFlag())) {
            log.info("连接数据库初始化失败");
            return false;
        }
        log.info("OnInit close");
        return true;
    }


    @Override
    public boolean OnInitTrial() {
        log.info("OnInit start ,moduleType:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());

        String path = "tPreProc2.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);
        String databaseName = props.getProperty("database_name");
        String stFieldName = props.getProperty("st_field_name");
        if (databaseName == null) {
            log.error(path + ",没有配置试算数据库名 database_name");
            return false;
        }
        if (stFieldName == null) {
            log.error(path + ",没有配置source_event_type_id取值字段名st_field_name的值");
            return false;
        }

        genCode = new GenCode(getThreadNum(), databaseName, stFieldName);
        genCode.setM_debug(common.getIDebug());
        genCode.setCommon(common);
        if (!genCode.Initgencode(isbInitFlag())) {
            log.info("连接数据库初始化失败");
            return false;
        }
        log.info("OnInit close");
        return true;
    }

    @Override
    public boolean OnInitPre() {
        log.info("OnInit start ,moduleType:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());

        String path = "pPreProc2.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);
        String databaseName = props.getProperty("database_name");
        String stFieldName = props.getProperty("st_field_name");
        if (databaseName == null) {
            log.error(path + ",没有配置试算数据库名 database_name");
            return false;
        }
        if (stFieldName == null) {
            log.error(path + ",没有配置source_event_type_id取值字段名st_field_name的值");
            return false;
        }


        genCode = new GenCode(getThreadNum(), databaseName, stFieldName);
        genCode.setM_debug(common.getIDebug());
        genCode.setCommon(common);
        if (!genCode.Initgencode(isbInitFlag())) {
            log.info("连接数据库初始化失败");
            return false;
        }
        log.info("OnInit close");
        return true;
    }


    /*     入参：
     inRecord：输入话单
     outRecord：输出话单
     msg：处理异常时，填写异常信息
     返回值，是否处理成功*/
    public boolean OnTask(String inRecord) {
        // 清理话单存放实例
        genCode.clear();

        // 补充回退回收冲正单，跳过
        if (JSONObject.parseObject(inRecord).containsKey("redo_flag")) {
            JSONObject tmp = JSONObject.parseObject(inRecord);
            String redo_flag = tmp.getString("redo_flag");
            if (REDO_FLAG.CODE_2.getCode().equals(redo_flag) || REDO_FLAG.CODE_4.getCode().equals(redo_flag)) {
                setOutRecord(inRecord);
                return true;
            }
        }

        try {
            //构建话单格式
            if (!genCode.buildRecord(inRecord)) {
                setOutErrorRecord(genCode.getErrorRecord());
                return false;
            }
        } catch (Exception e) {
            String exceptionString = printStackTraceToString(e);
            log.error(exceptionString);
            log.error("OnTask()执行失败，执行退出前操作失败！");
            genCode.setErrorMsg(ErrorType.PREPROC2_DEAL_FORMAT_FAILE, e.toString());
            setOutErrorRecord(genCode.getErrorRecord());
            return false;
        }

        try {
            //预处理规则处理
            genCode.Dealrecord(inRecord);
        } catch (Exception e) {
            String exceptionString = printStackTraceToString(e);
            log.error(exceptionString);
            log.error("OnTask()执行失败，执行退出前操作失败！" + e.toString());
            genCode.setErrorMsg(ErrorType.PREPROC2_DEAL_RULE_FAILE, e.toString());
        }

        setOutRecord(genCode.getRecord());
        return true;
    }


    @Override
    public boolean OnOver(Map<String, List<List<KeyValue>>> dataOver) {
        return true;
    }

    /*线程初始化函数
     * 线程每次处理文件之前会执行此函数
     * */
    @Override
    public boolean OnChildInit() {
        return true;
    }

    @Override
    public void reset() {

    }

    /*线程结束函数
     * 线程处理完所有的记录之后，会处理此函数
     * */
    @Override
    public boolean OnChildOver() {
        return true;
    }

    @Override
    public Map<Integer, FunctionPerl> getFunctionPerl() {
        return genCode.getPerl();
    }

    /*模块退出操作
     * 整个模块退出时，需要释放的空间这里操作。
     * */
    @Override
    public boolean OnExit() {
        genCode.close(isbInitFlag());
        return true;
    }
}
