package com.itco.rule.module;

public class CommonEnum {
    public enum DataType {
        TA(1, "TA", "整型"),  //整型
        TB(2, "TB", "长整型"),  //长整型
        TC(3, "TC", "实数型"),  //实数型
        TD(4, "TD", "字符串"),  //字符串
        TE(5, "TE", "日期型"),  //日期型
        TF(6, "TF", "整型数组"),  //整型数组
        TG(7, "TG", "长整型数组"),  //长整型数组
        TH(8, "TH", "实时型数组"),  //实时型数组
        TI(9, "TI", "字符串数组"),  //字符串数组
        TJ(10, "TJ", "日期型数组");  //日期型数组
        int id;
        String name;
        String remark;

        public int getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        DataType(int id, String name, String remark) {
            this.id = id;
            this.name = name;
            this.remark = remark;
        }
    }

}
