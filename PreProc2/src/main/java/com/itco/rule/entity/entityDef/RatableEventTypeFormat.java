package com.itco.rule.entity.entityDef;

public class RatableEventTypeFormat {
    int event_type_id;
    int module_id;
    int redo_flag;
    int event_format_id;
    String comments;

    public int getEvent_type_id() {
        return event_type_id;
    }

    public void setEvent_type_id(int event_type_id) {
        this.event_type_id = event_type_id;
    }

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public int getRedo_flag() {
        return redo_flag;
    }

    public void setRedo_flag(int redo_flag) {
        this.redo_flag = redo_flag;
    }

    public int getEvent_format_id() {
        return event_format_id;
    }

    public void setEvent_format_id(int event_format_id) {
        this.event_format_id = event_format_id;
    }

    @Override
    public String toString() {
        return "RatableEventTypeFormat{" +
                "event_type_id=" + event_type_id +
                ", module_id=" + module_id +
                ", redo_flag=" + redo_flag +
                ", event_format_id=" + event_format_id +
                ", comments='" + comments + '\'' +
                '}';
    }
}
