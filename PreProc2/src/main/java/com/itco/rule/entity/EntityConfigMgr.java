package com.itco.rule.entity;

import com.itco.entity.common.TprResourceAttr;
import com.itco.rule.entity.entityDef.RatableEventFormatItem;
import com.itco.rule.entity.entityDef.RatableEventTypeFormat;
import com.itco.rule.entity.entityDef.SourceEventFormatItem;
import com.itco.rule.entity.entityDef.SourceEventTypeFormat;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EntityConfigMgr {
    static Log log = LogFactory.getLog(EntityConfigMgr.class);
    Map<Integer, TprResourceAttr> mTprResourceAttr = new HashMap<>();
    Map<Integer, SourceEventTypeFormat> mSourceType = new HashMap<>();
    Map<Integer, List<SourceEventFormatItem>> mSourceItem = new HashMap<>();
    Map<Integer, RatableEventTypeFormat> mRatableType = new HashMap<>();
    Map<Integer, List<RatableEventFormatItem>> mRatableItem = new HashMap<>();

    public Map<Integer, TprResourceAttr> getmTprResourceAttr() {
        return mTprResourceAttr;
    }

    public void setmTprResourceAttr(List<TprResourceAttr> list) {
        if (list != null) {
            for (TprResourceAttr attr : list) {
                //log.info(attr.toString());
                attr.setEn_name(attr.getEn_name().toLowerCase());
                mTprResourceAttr.put(attr.getAttr_id(), attr);
            }
        }
    }

    public Map<Integer, SourceEventTypeFormat> getmSourceType() {
        return mSourceType;
    }

    public void setmSourceType(List<SourceEventTypeFormat> list) {
        if (list != null) {
            for (SourceEventTypeFormat source : list) {
                //log.info(source.toString());
                mSourceType.put(source.getSource_event_type_id(), source);
            }
        }
    }

    public Map<Integer, List<RatableEventFormatItem>> getmRatableItem() {
        return mRatableItem;
    }

    public void setmRatableItem(List<RatableEventFormatItem> list) {
        if (list != null) {
            for (RatableEventFormatItem ratable : list) {
                //log.info(ratable.toString());
                List<RatableEventFormatItem> listTmp = mRatableItem.get(ratable.getEvent_format_id());
                if (listTmp == null) {
                    List<RatableEventFormatItem> tmpList = new ArrayList<>();
                    tmpList.add(ratable);
                    mRatableItem.put(ratable.getEvent_format_id(), tmpList);
                } else {
                    listTmp.add(ratable);
                }
            }
        }
    }

    public Map<Integer, List<SourceEventFormatItem>> getmSourceItem() {
        return mSourceItem;
    }

    public void setSourceItem(List<SourceEventFormatItem> list) {
        if (list != null) {
            for(SourceEventFormatItem item:list){
                //System.out.println(item.toString());
                List<SourceEventFormatItem> listTmp=mSourceItem.get(item.getSource_event_format_id());
                if(listTmp==null){
                    List<SourceEventFormatItem> tmpList=new ArrayList<>();
                    tmpList.add(item);
                    mSourceItem.put(item.getSource_event_format_id(),tmpList);
                }else {
                    listTmp.add(item);
                }
            }
        }
    }

    public Map<Integer, RatableEventTypeFormat> getmRatableType() {
        return mRatableType;
    }

    public void setRatableType(List<RatableEventTypeFormat> list) {
        if(list!=null){
            for(RatableEventTypeFormat item:list){
                //System.out.println(item.toString());
                mRatableType.put(item.getEvent_type_id(),item);
            }
        }
    }

    @Override
    public String toString() {
        return "EntityConfigMgr{" +
                "mTprResourceAttr.size() =" + mTprResourceAttr.size() +
                ", mSourceType.size() =" + mSourceType.size() +
                ", mSourceItem.size() =" + mSourceItem.size() +
                ", mRatableType.size() =" + mRatableType.size() +
                ", mRatableItem.size() =" + mRatableItem.size() +
                '}';
    }
}
