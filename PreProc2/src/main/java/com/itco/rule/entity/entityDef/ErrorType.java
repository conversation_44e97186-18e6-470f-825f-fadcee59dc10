package com.itco.rule.entity.entityDef;

public class ErrorType {
    // 预处理2
    public final static int PREPROC2_DEAL_FUNCTION_FAILE = 10120001;         //调用自定义因子失败
    public final static int PREPROC2_DEAL_RULE_ECEPTION = 10120002;          //调用自定义因子失败
    public final static int PREPROC2_NO_FOUNT_RULE_GROUP = 10120003;         //找不到对应的事件规则组
    public final static int PREPROC2_NO_FOUNT_RULE_SUBGROUP = 10120004;      //找不到Rule_subgroup_id
    public final static int PREPROC2_NO_FOUNT_ATTR_ID = 10120005;            //配置的字段tpr_resource_attr不存在
    public final static int PREPROC2_DEAL_FORMAT_FAILE = 10120006;           //话单格式转换失败
    public final static int PREPROC2_DEAL_RULE_FAILE = 10120007;             //规则处理失败


    public final static int PREPROC2_NO_FOUNT_OPR_TYPE = 10121001;           //原始话单中不存在格式匹配关键字段
    public final static int PREPROC2_OPR_TYPE_NO_DIGIT = 10121002;           //原始话单中不存在格式匹配关键字段
    public final static int PREPROC2_OPR_TYPE_NO_FORMAT = 10121003;          //opr_type没有匹配到原始话单格式
    public final static int PREPROC2_SOURCE_TO_TEMP_FAILE = 10121004;        //dealSourceToRecord() 处理失败
    public final static int PREPROC2_RATABLE_NO_FOUNT_EVENT = 10121005;      //ratable_event_type_format表不存在event_type_id
    public final static int PREPROC2_TEMP_TO_RECORD_FAILE = 10121006;        //dealTmpToRecord() 处理失败
    public final static int PREPROC2_RECORD_NO_FOUNT_EVENT= 10121007;        //话单中找不到对应的事件,source_event_type_id字段不存在


}
