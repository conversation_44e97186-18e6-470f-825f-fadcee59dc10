package com.itco.rule.entity.entityDef;

public class RatableEventFormatItem {
    int event_format_id;
    int event_attr_id;
    int sort_id;
    String default_value;
    String comments;

    public int getEvent_format_id() {
        return event_format_id;
    }

    public void setEvent_format_id(int event_format_id) {
        this.event_format_id = event_format_id;
    }

    public int getEvent_attr_id() {
        return event_attr_id;
    }

    public void setEvent_attr_id(int event_attr_id) {
        this.event_attr_id = event_attr_id;
    }

    public int getSort_id() {
        return sort_id;
    }

    public void setSort_id(int sort_id) {
        this.sort_id = sort_id;
    }

    public String getDefault_value() {
        return default_value;
    }

    public void setDefault_value(String default_value) {
        this.default_value = default_value;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "RatableEventFormatItem{" +
                "event_format_id=" + event_format_id +
                ", event_attr_id=" + event_attr_id +
                ", sort_id=" + sort_id +
                ", default_value='" + default_value + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
