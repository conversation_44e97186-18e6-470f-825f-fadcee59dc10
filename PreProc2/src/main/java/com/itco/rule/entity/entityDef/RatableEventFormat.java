package com.itco.rule.entity.entityDef;

public class RatableEventFormat {
    int event_format_id;
    String en_name;
    String ch_name;
    String comments;


    public int getEvent_format_id() {
        return event_format_id;
    }

    public void setEvent_format_id(int event_format_id) {
        this.event_format_id = event_format_id;
    }

    public String getEn_name() {
        return en_name;
    }

    public void setEn_name(String en_name) {
        this.en_name = en_name;
    }

    public String getCh_name() {
        return ch_name;
    }

    public void setCh_name(String ch_name) {
        this.ch_name = ch_name;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "RatableEventFormat{" +
                "event_format_id=" + event_format_id +
                ", en_name='" + en_name + '\'' +
                ", ch_name='" + ch_name + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
