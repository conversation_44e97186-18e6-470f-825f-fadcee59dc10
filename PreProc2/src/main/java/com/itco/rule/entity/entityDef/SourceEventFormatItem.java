package com.itco.rule.entity.entityDef;

public class SourceEventFormatItem {
    int source_event_format_id;
    int event_attr_id;
    int location;
    int length;
    String code;
    String name;
    String end_char;
    String source_field_name;

    public int getSource_event_format_id() {
        return source_event_format_id;
    }

    public void setSource_event_format_id(int source_event_format_id) {
        this.source_event_format_id = source_event_format_id;
    }

    public int getEvent_attr_id() {
        return event_attr_id;
    }

    public void setEvent_attr_id(int event_attr_id) {
        this.event_attr_id = event_attr_id;
    }

    public int getLocation() {
        return location;
    }

    public void setLocation(int location) {
        this.location = location;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnd_char() {
        return end_char;
    }

    public void setEnd_char(String end_char) {
        this.end_char = end_char;
    }

    public String getSource_field_name() {
        return source_field_name;
    }

    public void setSource_field_name(String source_field_name) {
        this.source_field_name = source_field_name;
    }

    @Override
    public String toString() {
        return "SourceEventFormatItem{" +
                "source_event_format_id=" + source_event_format_id +
                ", event_attr_id=" + event_attr_id +
                ", location=" + location +
                ", source_field_name='" + source_field_name + '\'' +
                '}';
    }
}
