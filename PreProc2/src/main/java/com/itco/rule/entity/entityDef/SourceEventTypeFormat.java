package com.itco.rule.entity.entityDef;

public class SourceEventTypeFormat {
    int source_event_type_id;
    int version;
    int source_event_format_id;
    int event_type_id;
    String comments;
    int recycle_erase_add_days;
    int event_class;


    public int getSource_event_type_id() {
        return source_event_type_id;
    }

    public void setSource_event_type_id(int source_event_type_id) {
        this.source_event_type_id = source_event_type_id;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getSource_event_format_id() {
        return source_event_format_id;
    }

    public void setSource_event_format_id(int source_event_format_id) {
        this.source_event_format_id = source_event_format_id;
    }

    public int getEvent_type_id() {
        return event_type_id;
    }

    public void setEvent_type_id(int event_type_id) {
        this.event_type_id = event_type_id;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public int getRecycle_erase_add_days() {
        return recycle_erase_add_days;
    }

    public void setRecycle_erase_add_days(int recycle_erase_add_days) {
        this.recycle_erase_add_days = recycle_erase_add_days;
    }

    public int getEvent_class() {
        return event_class;
    }

    public void setEvent_class(int event_class) {
        this.event_class = event_class;
    }

    @Override
    public String toString() {
        return "SourceEventTypeFormat{" +
                "source_event_type_id=" + source_event_type_id +
                ", version=" + version +
                ", source_event_format_id=" + source_event_format_id +
                ", event_type_id=" + event_type_id +
                ", comments='" + comments + '\'' +
                ", recycle_erase_add_days=" + recycle_erase_add_days +
                ", event_class=" + event_class +
                '}';
    }
}
