package com.itco.rule.api;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.CalcFunction.CalcFunctionLocal;
import com.itco.CalcFunction.CalcVisualFunctionLocal;
import com.itco.commen.PubDef;
import com.itco.component.jdbc.DbPool;
import com.itco.entity.common.Common;
import com.itco.entity.common.Result;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.function.FunctionPerl;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.rule.config.*;
import com.itco.rule.entity.EntityConfigMgr;
import com.itco.rule.entity.entityDef.*;
import com.itco.rule.module.CommonEnum;
import com.itco.rule.module.FunctionException;
import com.itco.rulefunction.ctgcache.TableService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class GenCode {
    final static Log log = LogFactory.getLog(GenCode.class);

    static EntityConfigMgr entityConfigMgr = new EntityConfigMgr();
    static Loadformdb loadformdb = new Loadformdb();
    static Map<Integer, List<RuleSubGroup>> msubgroup = new HashMap<Integer, List<RuleSubGroup>>();
    static Map<Integer, List<RuleSubGroupStep>> m_subgroupsetp = new HashMap<Integer, List<RuleSubGroupStep>>();
    static Map<Integer, RulePattern> m_pattern = new HashMap<Integer, RulePattern>();
    static Map<Integer, List<RuleCondition>> m_condition = new HashMap<Integer, List<RuleCondition>>();
    static Map<Integer, List<Ruleaction>> m_action = new HashMap<Integer, List<Ruleaction>>();
    static Map<Integer, TprRuleGroup> m_rulegroup = new HashMap<Integer, TprRuleGroup>();
    static Map<Integer, TprResourceAttr> mTprAttr = new HashMap<>();

    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式

    //标准话单结构
    JSONObject m_outrecord = null;
    JSONObject m_errorRecord = null;
    String ruleStr = new String();
    String groupStr = new String();
    String partternStr = new String();
    String actionStr = new String();
    String Msg = null;

    Common common = null;
    int m_debug = 0;
    boolean bRecordFormatFlag = true;
    List<String> stFieldList = new ArrayList<>();

    //表查询类
    //TableService tableService = null;
    int iThreadNum = 0;
    //因子调用类
    CalcFunctionLocal calcFunction = null;
    CalcVisualFunctionLocal calcVisualFunctionLocal = null;

    public GenCode(int Num, String databaseName, String fieldName) {
        iThreadNum = Num;
        loadformdb.setDatabaseName(databaseName);

        if (fieldName != null) {
            stFieldList = Arrays.asList(fieldName.split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
        } else {
            stFieldList.add("oper_type");
        }
    }

    public void clear() {
        m_outrecord = null;
        m_errorRecord = null;
    }

    public synchronized void close(boolean bInitFlag) {
        if (!bInitFlag) {
            log.info("关闭所有资源");
            TableService.closeAll();
            CalcFunctionLocal.close();
        }
    }

    public void setErrorMsg(int error_type, String errorMsg) {
        Factory.setTicketTypeAbn(m_errorRecord, error_type, errorMsg);
    }

    public boolean Initgencode(boolean bInitFlag) {
        if (!bInitFlag) {
            log.info("初始化连接数据库");
            DbPool.setZkClientApi(CalcRecordManager.getZkClientApi());

            //loadformdb.setModule_id(common.getModule_id());
            log.info("初始化加载规则配置");
            if (!loadformdb.LoadRulegroup(m_rulegroup)) {
                return false;
            }
            for (Map.Entry<Integer, TprRuleGroup> map : m_rulegroup.entrySet()) {
                System.out.println(map.getValue().toString());
            }
            if (!loadformdb.LoadRuleSubgroup(msubgroup)) {
                return false;
            }
            for (Map.Entry<Integer, List<RuleSubGroup>> map : msubgroup.entrySet()) {
                System.out.println(map.getValue().toString());
            }
            if (!loadformdb.LoadRuleSubgroupStep(m_subgroupsetp)) {
                return false;
            }
            for (Map.Entry<Integer, List<RuleSubGroupStep>> map : m_subgroupsetp.entrySet()) {
                System.out.println("rule_subgroup_id:" + map.getKey());
                for (RuleSubGroupStep ruleSubGroupStep : map.getValue()) {
                    System.out.println(ruleSubGroupStep.toString());
                }
                System.out.println("");
            }
            if (!loadformdb.LoadCondition(m_condition)) {
                return false;
            }
           /* for(Map.Entry<Integer,List<RuleCondition>> map:m_condition.entrySet()){
                System.out.println(map.getValue().toString());
            }*/
            if (!loadformdb.LoadPattern(m_pattern)) {
                return false;
            }
           /* for(Map.Entry<Integer,RulePattern> map:m_pattern.entrySet()){
                System.out.println(map.getValue().toString());
            }*/
            if (!loadformdb.LoadAction(m_action)) {
                return false;
            }
            for (Map.Entry<Integer, List<Ruleaction>> map : m_action.entrySet()) {
                System.out.println(map.getValue().toString());
            }
            if (!loadformdb.LoadResourceattr(mTprAttr)) {
                return false;
            }
            if (!loadformdb.InitReadTableRule(entityConfigMgr)) {
                return false;
            }

            log.info("初始化加载规则配置成功");

            TableService.setZkClientApi(CalcRecordManager.getZkClientApi());
            TableService.setCommon(common);
            if (!TableService.initCfgAst()) {
                log.error("TableService.initCfgAst() 失败");
                return false;
            }
            log.info("缓存查询接口初始化成功");

            // 初始话函数接口
            CalcFunctionLocal.setbInitFlag(bInitFlag);
            CalcFunctionLocal.setCommon(CalcRecordManager.getCommon());
            CalcFunctionLocal.setZkClientApi(CalcRecordManager.getZkClientApi());
            if (!CalcFunctionLocal.initCfg()) {
                log.error("CalcFunctionLocal.initCfg() 失败");
                return false;
            }
            log.info("自定义因子接口初始化成功");

            CalcVisualFunctionLocal.setZkClientApi(CalcRecordManager.getZkClientApi());
            if (!CalcVisualFunctionLocal.initCfg()) {
                log.error("CalcVisualFunctionLocal.initCfg() 失败");
                return false;
            }
            log.info("缓存因子因子调用接口成功");
        }

        calcFunction = CalcFunctionLocal.getInstance(iThreadNum);
        calcVisualFunctionLocal = CalcVisualFunctionLocal.getInstance(iThreadNum);

        log.info("初始化因子调用接口成功：" + iThreadNum);
        return true;
    }

    public String getRecord() {
        return JSONObject.toJSONString(m_outrecord, SerializerFeature.WriteMapNullValue);
    }

    public String getErrorRecord() {
        return JSONObject.toJSONString(m_errorRecord, SerializerFeature.WriteMapNullValue);
    }

    JSONObject dealSourceToTmpRecord(int sourceEventFormatId, JSONObject sRecord) {
        JSONObject oRecord = new JSONObject();

        List<SourceEventFormatItem> lItem = entityConfigMgr.getmSourceItem().get(sourceEventFormatId);
        for (SourceEventFormatItem item : lItem) {
            TprResourceAttr attr = mTprAttr.get(item.getEvent_attr_id());
            if (attr != null) {
                if (sRecord.containsKey(item.getSource_field_name())) {
                    if (m_debug == 1) {
                        System.out.println(attr.getEn_name() + ":" + item.getSource_field_name() + "->" + sRecord.get(item.getSource_field_name()));
                    }
                    oRecord.put(attr.getEn_name().toLowerCase(), sRecord.get(item.getSource_field_name()));
                } else {
                    log.error("sourceEventFormatId：" + sourceEventFormatId + "，原始话单中不存在:" + item.getSource_field_name());
                    return null;
                }
            } else {
                log.error("tpr_resource_attr 表中不存在：" + item.getEvent_attr_id() + "," + item.getName());
                return null;
            }
        }

        return oRecord;
    }

    JSONObject dealTmpToRecord(int eventFormatId, JSONObject tmpRecord) {
        List<RatableEventFormatItem> rItem = entityConfigMgr.getmRatableItem().get(eventFormatId);
        if (rItem == null) {
            log.error("ratable_event_format_item表不存在event_format_id:" + eventFormatId);
            return null;
        }

        JSONObject oRecord = new JSONObject();
        for (RatableEventFormatItem item : rItem) {
            TprResourceAttr attr = entityConfigMgr.getmTprResourceAttr().get(item.getEvent_attr_id());
            if (attr == null) {
                log.error("不存在的话单标识:" + item.toString());
                continue;
            }

            if (m_debug == 1) {
                System.out.println(attr.getEn_name() + "->" + tmpRecord.getString(attr.getEn_name()));
            }

            try {
                if (attr.getData_type().equals(CommonEnum.DataType.TA.getName()) || attr.getData_type().equals(CommonEnum.DataType.TB.getName())) {
                    Object tmp = tmpRecord.getString(attr.getEn_name());
                    if (tmp != null && (!tmp.toString().equals(""))) {
                        long value = Long.valueOf(tmp.toString());
                        oRecord.put(attr.getEn_name(), value);
                    } else {
                        oRecord.put(attr.getEn_name(), 0);
                    }
                } else if (attr.getData_type().equals(CommonEnum.DataType.TC.getName())) {
                    Object tmp = tmpRecord.getString(attr.getEn_name());
                    if (tmp != null && (!tmp.toString().equals(""))) {
                        BigDecimal value = new BigDecimal(tmp.toString());
                        oRecord.put(attr.getEn_name(), value);
                    } else {
                        oRecord.put(attr.getEn_name(), 0);
                    }
                } else if (attr.getData_type().equals(CommonEnum.DataType.TD.getName())) {
                    Object tmp = tmpRecord.getString(attr.getEn_name());
                    if (tmp != null) {
                        String value = tmp.toString();
                        oRecord.put(attr.getEn_name(), value);
                    } else {
                        oRecord.put(attr.getEn_name(), "");
                    }
                } else if (attr.getData_type().equals(CommonEnum.DataType.TE.getName())) {
                    Object tmp = tmpRecord.getString(attr.getEn_name());
                    if (tmp != null) {
                        oRecord.put(attr.getEn_name(), tmp.toString());
                    } else {
                        oRecord.put(attr.getEn_name(), "1970-01-01 12:01:01");
                    }
                } else if (attr.getData_type().equals(CommonEnum.DataType.TF.getName())) {
                    JSONArray tmp = new JSONArray();
                    oRecord.put(attr.getEn_name(), tmp);
                } else if (attr.getData_type().equals(CommonEnum.DataType.TG.getName())) {
                    JSONArray tmp = new JSONArray();
                    oRecord.put(attr.getEn_name(), tmp);
                } else if (attr.getData_type().equals(CommonEnum.DataType.TH.getName())) {
                    JSONArray tmp = new JSONArray();
                    oRecord.put(attr.getEn_name(), tmp);
                } else if (attr.getData_type().equals(CommonEnum.DataType.TI.getName())) {
                    JSONArray tmp = new JSONArray();
                    oRecord.put(attr.getEn_name(), tmp);
                } else if (attr.getData_type().equals(CommonEnum.DataType.TJ.getName())) {
                    JSONArray tmp = new JSONArray();
                    oRecord.put(attr.getEn_name(), tmp);
                }
            } catch (Exception e) {
                log.error("字段:" + attr.getEn_name() + " 的值为:" + tmpRecord.getString(attr.getEn_name()) + " ,无法转换成 " + attr.getData_type());
                return null;
            }
        }

        return oRecord;
    }

    public boolean buildRecord(String inRecord) {
        if (bRecordFormatFlag) {
            // 进行 话单格式转换
            JSONObject sRecord = JSONObject.parseObject(inRecord);
            m_errorRecord = sRecord;

            // 话单格式
            String stFieldName = null;
            for (String fieldName : stFieldList) {
                if (sRecord.containsKey(fieldName)) {
                    stFieldName = fieldName;
                    break;
                }
            }
            if (stFieldName == null) {
                log.info("未配置source_event_type_format表source_event_type_id字段对应的取值字段名，采用默认方式:佣金:oper_type ,结算:SOURCE_TYPE_ID");
                if ("TPSS".equals(common.getSSystem())) {
                    stFieldName = "oper_type";
                } else if ("ISS".equals(common.getSSystem())) {
                    stFieldName = "SOURCE_TYPE_ID";
                } else {
                    stFieldName = "oper_type";
                }
            }

            if (!sRecord.containsKey(stFieldName)) {
                log.error("原始话单中不存在:" + stFieldName + " 字段，处理失败");
                Msg = "原始话单中不存在:" + stFieldName + " 字段，处理失败";
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_NO_FOUNT_OPR_TYPE, Msg);
                return false;
            }
            if (!StringUtils.isNumber(sRecord.getString(stFieldName))) {
                log.error("原始话单中:" + stFieldName + " 字段，值: " + sRecord.getString(stFieldName) + ",异常不是数字");
                Msg = "原始话单中:" + stFieldName + " 字段，值: " + sRecord.getString(stFieldName) + ",异常不是数字";
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_OPR_TYPE_NO_DIGIT, Msg);
                return false;
            }

            int SourceEventTypeId = Integer.valueOf(sRecord.getString(stFieldName));
            SourceEventTypeFormat sourceEventTypeFormat = entityConfigMgr.getmSourceType().get(SourceEventTypeId);
            if (sourceEventTypeFormat == null) {
                Msg = "话单的" + stFieldName + "，没有找到匹配的规则（source_event_type_format）";
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_OPR_TYPE_NO_FORMAT, Msg);
                return false;
            }

            JSONObject tRacord = dealSourceToTmpRecord(sourceEventTypeFormat.getSource_event_format_id(), sRecord);
            if (tRacord == null) {
                Msg = "dealSourceToRecord() 处理失败，source_event_format_id:" + sourceEventTypeFormat.getSource_event_format_id();
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_SOURCE_TO_TEMP_FAILE, Msg);
                return false;
            }
            sRecord.clear();

            if (m_debug == 1) {
                System.out.println("提取原始话单后格式：" + tRacord.toJSONString());
            }

            RatableEventTypeFormat ratableEventTypeFormat = entityConfigMgr.getmRatableType().get(sourceEventTypeFormat.getEvent_type_id());
            if (ratableEventTypeFormat == null) {
                Msg = "ratable_event_type_format表不存在event_type_id:" + sourceEventTypeFormat.getEvent_type_id();
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_RATABLE_NO_FOUNT_EVENT, Msg);
                return false;
            }

            m_outrecord = dealTmpToRecord(ratableEventTypeFormat.getEvent_format_id(), tRacord);
            if (m_outrecord == null) {
                Msg = "dealTmpToRecord() 处理失败，event_format_id:" + ratableEventTypeFormat.getEvent_format_id();
                Factory.setTicketTypeAbn(m_errorRecord, ErrorType.PREPROC2_TEMP_TO_RECORD_FAILE, Msg);
                return false;
            }
            tRacord.clear();
            tRacord = null;

            m_outrecord.put("source_event_type_id", sourceEventTypeFormat.getEvent_type_id());
            m_outrecord.put("event_class", sourceEventTypeFormat.getEvent_class());
        } else {
            // 接收到的话单已经格式转换后
            m_outrecord = JSONObject.parseObject(inRecord);
        }

        return true;
    }

    //处理话单
    public boolean Dealrecord(String inRecord) {
        // 清空要素串
        ruleStr = "";
        m_outrecord.put("deal_time", df.format(new Date(System.currentTimeMillis())));

        if (common.getSSystem().equals("ISS") && common.getSLatnId().equals("591")) {
            // 福建综合结算特色处理 非统一版本
            if (m_outrecord.get("org_start_time") != null && m_outrecord.getString("org_start_time").length() == 14) {
                String orgStartTime = m_outrecord.get("org_start_time").toString();
                String startTime = "20" + orgStartTime.substring(0, orgStartTime.length() - 2);
                m_outrecord.put("start_time", startTime);
            }
            if (m_outrecord.get("org_end_time") != null && m_outrecord.getString("org_end_time").length() == 14) {
                String orgEndTime = m_outrecord.get("org_end_time").toString();
                String endTime = "20" + orgEndTime.substring(0, orgEndTime.length() - 2);
                m_outrecord.put("end_time", endTime);
            }
            //通话量call_amount=org_call_amount/10并向上取整
            if (m_outrecord.get("org_call_amount") != null) {
                String orgcallamount = m_outrecord.get("org_call_amount").toString();

                double bOrgCallAmount = Double.parseDouble(orgcallamount);
                //向上取整
                double amountTime = Math.ceil(bOrgCallAmount / 10);
                int call_amount = (int) amountTime;
                m_outrecord.put("call_amount", call_amount);
                if (m_debug == 1) {
                    System.out.println("org_call_amount:" + orgcallamount);
                    System.out.println("call_amount:" + call_amount);
                }
            }
        }

        if (m_debug == 1) {
            System.out.println("标准话单格式：" + m_outrecord.toJSONString());
        }

        boolean containsKey = m_outrecord.containsKey("source_event_type_id");
        if (!containsKey) {
            Msg = "话单中找不到对应的事件,source_event_type_id字段不存在";
            Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_RECORD_NO_FOUNT_EVENT, Msg);
            return true;
        }

        int redo_flag = 0;
        if (m_outrecord.containsKey("redo_flag")) {
            redo_flag = m_outrecord.getInteger("redo_flag");
        }

        int event_type_id = m_outrecord.getInteger("source_event_type_id");
        if (!m_rulegroup.containsKey(event_type_id)) {
            Msg = "找不到对应的事件类型配置source_event_type_id=" + m_outrecord.getString("source_event_type_id");
            //log.error(Msg);
            return true;
        }

        switch (redo_flag) {
            case PubDef.REDO_FLAG_NORMAL:
            case PubDef.REDO_FLAG_RECYCLE:
            case PubDef.REDO_FLAG_ROLLBACK:
            case PubDef.REDO_FLAG_RECYCLE_TPSS:
            case PubDef.REDO_FLAG_ROLLBACK_TPSS:
            case PubDef.REDO_FLAG_OPENING_TPSS:
            case PubDef.REDO_FLAG_RESETTLE_BEF:
            case PubDef.REDO_FLAG_ROLLBACK_AFT: {
                //获取规则组
                TprRuleGroup rulegroupTmp = m_rulegroup.get(event_type_id);
                if (!msubgroup.containsKey(rulegroupTmp.getRule_group_id())) {
                    Msg = "找不到对应的事件规则组,rule_group_id:" + rulegroupTmp.getRule_group_id();
                    Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_NO_FOUNT_RULE_GROUP, Msg);
                    return true;
                }
                ruleStr += "~:" + rulegroupTmp.getRule_group_id();

                String ruleSubGroupId = null;
                List<RuleSubGroup> ruleSubGroupList = msubgroup.get(rulegroupTmp.getRule_group_id());
                try {
                    for (RuleSubGroup ruleSubGroup : ruleSubGroupList) {
                        ruleSubGroupId = String.valueOf(ruleSubGroup.getRule_subgroup_id());
                        ruleStr += ";!:" + ruleSubGroup.getRule_subgroup_id();
                        int iRet = DealRulesubGroup(ruleSubGroup);
                        if (iRet == 3) {
                            // 返回结果
                            break;
                        }
                    }
                } catch (FunctionException e) {
                    Msg = "collect_cdr_id:" + m_outrecord.getString("collect_cdr_id") + ",FunctionException " + e.getFunction_id() + " 调用失败,error_msg:" + e.getError_msg();
                    Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_DEAL_FUNCTION_FAILE, Msg);
                } catch (Exception e) {
                    if (common.getIDebug() > 0) {
                        e.printStackTrace();
                    }
                    Msg = "collect_cdr_id:" + m_outrecord.getString("collect_cdr_id") + ",rule_group_id:" + rulegroupTmp.getRule_group_id() + "->rule_subgroup_id:" + ruleSubGroupId + " 处理失败。error_msg:" + e.getMessage();
                    Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_DEAL_RULE_ECEPTION, Msg);
                }
            }
            break;
            case PubDef.REDO_FLAG_RECYCLE_ERASE:
            case PubDef.REDO_FLAG_ROLLBACK_ERASE:
                break;
            default:
                break;
        }
        return true;
    }

    //处理一个规则组，一个规则组里面有IF 和else 或者if 。。if，Rule_subgroup_id一个规则组
    public int DealRulesubGroup(RuleSubGroup ruleSubGroup) throws FunctionException {
        int iRet = 0;
        Integer ruleSubGroupId = new Integer(ruleSubGroup.getRule_subgroup_id());
        if (m_debug == 1) {
            System.out.println("");
            log.info("[Tpr_rule_subgroup] rule_group_id:" + ruleSubGroup.getRule_group_id() + ",rule_subgroup_id:" + ruleSubGroup.getRule_subgroup_id() + "," + ruleSubGroup.getComments());
        }

        List<RuleSubGroupStep> ruleSubGroupStepList = m_subgroupsetp.get(ruleSubGroupId);
        if (ruleSubGroupStepList == null) {
            Msg = "规则表[tpr_rule_subgroup_step]找不到Rule_subgroup_id=" + ruleSubGroup.getRule_subgroup_id();
            log.error(Msg);
            Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_NO_FOUNT_RULE_SUBGROUP, Msg);
            return -1;
        }

        for (RuleSubGroupStep ruleSubGroupStep : ruleSubGroupStepList) {
            partternStr = "";
            actionStr = "";
            boolean isSusses = false;
            if (m_condition.containsKey(ruleSubGroupStep.getRule_id())) {
                List<RuleCondition> ruleConditions = m_condition.get(ruleSubGroupStep.getRule_id());
                isSusses = DealRulesubGroupstep(ruleSubGroupStep.getRule_id(), ruleConditions);
                ruleStr += ";@:" + ruleSubGroupStep.getRule_id() + "," + isSusses;
                ruleStr += groupStr;
            } else {//如果没有配置条件，直接执行动作
                isSusses = true;
                ruleStr += ";@:" + ruleSubGroupStep.getRule_id() + "," + isSusses;
            }
            if (m_debug == 1) {
                log.info("[rule_subgroup_id]:" + ruleSubGroupStep.getRule_subgroup_id() + "[tpr_rule_subgroup_step] rule_id:" + ruleSubGroupStep.getRule_id() + ",判断条件结果:" + (isSusses ? "成立" : "不成立"));
            }
            //如果条件成立进行赋值
            if (isSusses) {
                List<Ruleaction> ruleactions = m_action.get(ruleSubGroupStep.getRule_id());
                if (ruleactions == null) {
                    log.info("[rule_subgroup_id]:" + ruleSubGroupStep.getRule_subgroup_id() + "[tpr_rule_subgroup_step] rule_id:" + ruleSubGroupStep.getRule_id() + ",判断条件结果:" + (isSusses ? "成立" : "不成立") + ",没有配置动作。");
                } else {
                    iRet = SetActions(ruleactions);
                    ruleStr += actionStr;
                    if (iRet == 3) {
                        break;
                    }
                }
            }

            if (ruleSubGroup.getMatch_type().equals("9MA")) {
                // 半匹配
                continue;
            } else if (ruleSubGroup.getMatch_type().equals("9MB")) {
                // 全匹配
                //判断上一个if如果成立就不处理剩余的条件
                if (isSusses) {
                    break;
                }
            }
        }
        if (common.getIElement() == 1) {
            m_outrecord.put("rule_str", ruleStr);
        }
        return iRet;
    }

    //处理一个if条件判断,返回if条件成立还是不成立
    public boolean DealRulesubGroupstep(int rule_id, List<RuleCondition> ruleConditions) throws FunctionException {
        groupStr = "";
        //if条件是同组为与，异组为或
        int groupid = -1;
        boolean pattern = false;
        for (RuleCondition ruleCondition : ruleConditions) {

            RulePattern rulePattern = m_pattern.get(ruleCondition.getRule_pattern_id());
            if (groupid != ruleCondition.getGroup_id()) {
                groupid = ruleCondition.getGroup_id();
                //如果if条件同组条件结果不成立，要继续判断，成立不往下判断
                if (!pattern) {
                    pattern = DealRulePattern(rulePattern);
                    if (m_debug == 1) {
                        log.info("rule_id:" + rule_id + ",group_id:" + groupid + ",parrern_id:" + ruleCondition.getRule_pattern_id() + "," + (pattern ? "成立" : "不成立"));
                    }
                } else {
                    groupStr += ";#:" + ruleCondition.getGroup_id() + "," + pattern;
                    break;
                }

                groupStr += ";#:" + ruleCondition.getGroup_id() + "," + pattern;
                groupStr += partternStr;
            } else {
                //同组有一个条件为假不判断同组其他条件
                if (!pattern) {
                    if (m_debug == 1) {
                        log.info("rule_id:" + rule_id + ",group_id:" + groupid + ",parrern_id:" + ruleCondition.getRule_pattern_id() + "," + (pattern ? "成立" : "不成立"));
                    }
                    continue;
                }
                pattern = DealRulePattern(rulePattern);
                groupStr += partternStr;
            }
        }
        if (m_debug == 1) {
            log.info("group_id:" + groupid + "," + (pattern ? "成立" : "不成立"));
        }
        return pattern;
    }

    // 根据比较方式返回比较运算符
    String getSignByCompareType(String compare) {
        if (compare.equals("9CA")) {
            return ">";
        } else if (compare.equals("9CB")) {
            return "<";
        } else if (compare.equals("9CC")) {
            return "==";
        } else if (compare.equals("9CD")) {
            return "!=";
        } else if (compare.equals("9CE")) {
            return ">=";
        } else if (compare.equals("9CF")) {
            return "<=";
        } else if (compare.equals("9CG")) {
            return "IN";
        } else if (compare.equals("9CH")) {
            return "NOT IN";
        } else {
            return "NULL";
        }
    }

    public int SetActions(List<Ruleaction> ruleactions) throws FunctionException {
        int iRet = 0;
        for (Ruleaction ruleaction : ruleactions) {
            String value = null;
            if (ruleaction.getAction_type().equals("9AA")) { //话单赋值
                //话单字段名
                TprResourceAttr attr = mTprAttr.get(Integer.parseInt(ruleaction.getVariable_id()));
                String enname = attr.getEn_name();
                char rigt_ch = ruleaction.getResult_value().charAt(0);
                if (rigt_ch == 'C') { //固定值
                    value = ruleaction.getResult_value().substring(1);
                } else if (rigt_ch == 'F') { //函数
                    value = GetFunction(ruleaction.getResult_value().substring(1));
                } else if (rigt_ch == 'T') { //宽表
                    value = GetTable(ruleaction.getResult_value().substring(1));
                } else if (rigt_ch == '$') { //话单字段
                    TprResourceAttr rightAttr = mTprAttr.get(Integer.parseInt(ruleaction.getResult_value().substring(1)));
                    value = m_outrecord.get(rightAttr.getEn_name().toLowerCase()).toString();
                } else {
                    log.error("无法识别的取值前缀");
                    return -1;
                }

                if (attr.getData_type().equals(CommonEnum.DataType.TA.getName())) {
                    m_outrecord.put(enname, Integer.valueOf(value));
                } else if (attr.getData_type().equals(CommonEnum.DataType.TB.getName())) {
                    m_outrecord.put(enname, Long.valueOf(value));
                }
                if (attr.getData_type().equals(CommonEnum.DataType.TC.getName())) {
                    m_outrecord.put(enname, new BigDecimal(value));
                }
                if (attr.getData_type().equals(CommonEnum.DataType.TD.getName())) {
                    m_outrecord.put(enname, value);
                }
                if (attr.getData_type().equals(CommonEnum.DataType.TE.getName())) {
                    m_outrecord.put(enname, value);
                }
                iRet = 1;
                if (m_debug == 1) {
                    log.info("[tpr_rule_subgroup_step] rule_id:" + ruleaction.getRule_id() + ",动作结果:话单赋值," + enname + ":" + value);
                }
            } else if (ruleaction.getAction_type().equals("9AB")) { //函数调用
                char rigt_ch = ruleaction.getResult_value().charAt(0);
                if (rigt_ch == 'F') {
                    value = GetFunction(ruleaction.getResult_value().substring(1));
                }
                iRet = 2;
                if (m_debug == 1) {
                    log.info("[tpr_rule_subgroup_step] rule_id:" + ruleaction.getRule_id() + ",动作结果:函数调用," + ruleaction.getResult_value().substring(1) + ":" + value);
                }
            } else if (ruleaction.getAction_type().equals("9AC")) { //函数调用，并结束
                char rigt_ch = ruleaction.getResult_value().charAt(0);
                if (rigt_ch == 'F') {
                    value = GetFunction(ruleaction.getResult_value().substring(1));
                }
                iRet = 3;
                if (m_debug == 1) {
                    log.info("[tpr_rule_subgroup_step] rule_id:" + ruleaction.getRule_id() + ",动作结果:规则集结束," + ruleaction.getResult_value().substring(1) + ":" + value);
                }
                break;
            }
            actionStr += ";?:" + ruleaction.getRule_id() + "," + ruleaction.getOrder_id() + "," + value;
        }
        return iRet;
    }

    public boolean DealRulePattern(RulePattern rulePattern) throws FunctionException {
        String left_value = new String();
        String righ_value = new String();
        int patternId = rulePattern.getRule_pattern_id();

        boolean pattern = false;
        if (rulePattern.getCommpare_type().equals("9CA")) { // >
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null || left_value.equals("") || righ_value.equals("")) {
                pattern = false;
            } else if (Double.parseDouble(left_value) > Double.parseDouble(righ_value)) {
                pattern = true;
            }
        } else if (rulePattern.getCommpare_type().equals("9CB")) { // <
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null || left_value.equals("") || righ_value.equals("")) {
                pattern = false;
            } else if (Double.parseDouble(left_value) < Double.parseDouble(righ_value)) {
                pattern = true;
            }
        } else if (rulePattern.getCommpare_type().equals("9CC")) { // ==
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null) {
                if (left_value == null && righ_value == null) {
                    pattern = true;
                } else {
                    pattern = false;
                }
            } else if (left_value.equals(righ_value)) {
                pattern = true;
            }

        } else if (rulePattern.getCommpare_type().equals("9CD")) { // !=
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null) {
                if (left_value == null && righ_value == null) {
                    pattern = false;
                } else {
                    pattern = true;
                }
            } else if (left_value.equals(righ_value)) {
                pattern = false;
            } else {
                pattern = true;
            }
        } else if (rulePattern.getCommpare_type().equals("9CE")) { // >=
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null || left_value.equals("") || righ_value.equals("")) {
                pattern = false;
            } else if (Double.parseDouble(left_value) >= Double.parseDouble(righ_value)) {
                pattern = true;
            }
        } else if (rulePattern.getCommpare_type().equals("9CF")) { // <=
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            righ_value = Getvalue(rulePattern.getRigh_value());//右值
            if (left_value == null || righ_value == null || left_value.equals("") || righ_value.equals("")) {
                pattern = false;
            } else if (Double.parseDouble(left_value) <= Double.parseDouble(righ_value)) {
                pattern = true;
            }
        } else if (rulePattern.getCommpare_type().equals("9CG")) { // IN
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            List<String> righ_value_Tmp = Getvaluelist(rulePattern.getRigh_value());//右值
            if (left_value == null || left_value.equals("") || righ_value_Tmp.size() == 0) {
                pattern = false;
            } else {
                for (String str : righ_value_Tmp) {
                    righ_value += str + "|";
                    if (left_value.equals(str)) {
                        pattern = true;
                        break;
                    }
                }
            }
        } else if (rulePattern.getCommpare_type().equals("9CH")) { // NOT IN
            pattern = true;
            left_value = Getvalue(rulePattern.getLeft_value());//左值
            List<String> righ_value_Tmp = Getvaluelist(rulePattern.getRigh_value());//右值
            if (left_value == null || left_value.equals("") || righ_value_Tmp.size() == 0) {
                pattern = true;
            } else {
                for (String str : righ_value_Tmp) {
                    righ_value += str + "|";
                    if (left_value.equals(str)) {
                        pattern = false;
                        break;
                    }
                }
            }
        }

        if (m_debug == 1) {
            System.out.println("left_value:" + left_value + " " + getSignByCompareType(rulePattern.getCommpare_type()) + " righ_value:" + righ_value + " ->" + pattern);
        }
        partternStr = "";
        partternStr += ";*:" + rulePattern.getRule_pattern_id() + "," + pattern;
        partternStr += ",%:" + left_value;

        return pattern;
    }

    public String Getvalue(String intvalue) throws FunctionException {
        String outvalue = "";
        char ch = intvalue.charAt(0);
        if (ch == '$') {//话单字段
            String enName = mTprAttr.get(Integer.valueOf(intvalue.substring(1))).getEn_name();
            if (!m_outrecord.containsKey(enName)) {
                Factory.setTicketTypeAbn(m_outrecord, ErrorType.PREPROC2_NO_FOUNT_ATTR_ID, "规则处理失败，配置的字段" + enName + "不存在，请检查配置");
                return outvalue;
            }
            if (m_outrecord.get(enName) == null) {
                outvalue = null;
            } else {
                outvalue = m_outrecord.get(enName).toString();
            }
        } else if (ch == 'C') {//常量
            outvalue = intvalue.substring(1);
        } else if (ch == 'F') { //因子
            //调用因子接口
            outvalue = GetFunction(intvalue.substring(1));
        } else if (ch == 'R') { //因子
            //调用参考值因子接口
            outvalue = GetRefValue(intvalue.substring(1));
        } else if (ch == 'T') { //宽表
            //调用表查询接口
            outvalue = GetTable(intvalue.substring(1));
        } else if (ch == 'V') { //缓存因子
            outvalue = GetVisualFunction(intvalue.substring(1));
        }

        return outvalue;
    }

    public String GetRefValue(String refValueId) throws FunctionException {
        Result result;
        try {
            result = calcFunction.calcRefValue(Long.parseLong(refValueId), m_outrecord);
        } catch (Exception e) {
            log.error("calcFunction.calcRefValue(" + refValueId + "):" + e);
            throw new FunctionException(refValueId, e.toString());
        }
        if (!result.isBRet()) {
            log.error("调用参考值：" + refValueId + " 失败," + result.getError_msg());
            throw new FunctionException(refValueId, "调用参考值：" + refValueId + " 失败," + result.getError_msg());
        }

        if (m_debug == 1) {
            log.info("调用参考值：" + refValueId + " 成功，结果：" + result.get("VALUE").toString());
        }
        return result.get("VALUE").toString();
    }

    public String GetFunction(String functionId) throws FunctionException {
        Result result;
        try {
            result = calcFunction.calcFunction(functionId, m_outrecord);
        } catch (Exception e) {
            log.error("calcFunction.calcFunction(" + functionId + "):" + e);
            throw new FunctionException(functionId, e.toString());
        }

        if (!result.isBRet()) {
            log.info("调用函数：" + functionId + " 失败。error_msg:" + result.getError_msg());
            throw new FunctionException(functionId, "调用函数：" + functionId + " 失败。error_msg:" + result.getError_msg());
        }

        Object value = result.get("VALUE");
        String sResult = new String("");
        if (value != null) {
            sResult = value.toString();
        }
        if (m_debug == 1) {
            log.info("调用函数：" + functionId + " 成功，结果：" + sResult);
        }
        return sResult;
    }

    public String GetTable(String tableId) {
       /* Object object = tableService.getColuValueByattr(tableId, m_outrecord);
        if (object == null) {
            return null;
        }
        return object.toString();*/
        return null;
    }

    public String GetVisualFunction(String visualId) throws FunctionException {
        boolean bRet = false;
        Map<String, Object> result = new HashMap<>();
        try {
            bRet = calcVisualFunctionLocal.calcFunction(Integer.parseInt(visualId), 0L, m_outrecord, result);
        } catch (Exception e) {
            log.error("calcFunction.calcFunction(" + visualId + "):" + e.getMessage());
            throw new FunctionException(visualId, e.getMessage());
        }

        if (!bRet) {
            log.info("调用缓存函数：" + visualId + " 失败。error_msg:" + result.get("error_msg"));
            throw new FunctionException(visualId, result.get("error_msg") != null ? result.get("error_msg").toString() : "");
        }

        Object value = result.get("VALUE");
        String sResult = new String("");
        if (value != null) {
            sResult = value.toString();
        }
        if (m_debug == 1) {
            log.info("调用缓存函数：" + visualId + " 成功，结果：" + sResult);
        }
        return value.toString();
    }

    public List<String> Getvaluelist(String intvalue) {
        String[] value = intvalue.split(",");
        List<String> tmp = new ArrayList<>();
        for (int i = 0; i < value.length; i++) {
            tmp.add(value[i].substring(1));
        }
        return tmp;
    }

    public Map<Integer, FunctionPerl> getPerl() {
        Map<Integer, FunctionPerl> map = new HashMap<>();
        map.putAll(CalcFunctionLocal.getmPerl());
        map.putAll(CalcVisualFunctionLocal.getmPerl());
        CalcFunctionLocal.clearPerl();
        CalcVisualFunctionLocal.clearPerl();
        return map;
    }

    public void setbRecordFormatFlag(boolean bRecordFormatFlag) {
        this.bRecordFormatFlag = bRecordFormatFlag;
    }

    public void setM_debug(int m_debug) {
        this.m_debug = m_debug;
    }

    public void setCommon(Common common) {
        this.common = common;
    }
}
