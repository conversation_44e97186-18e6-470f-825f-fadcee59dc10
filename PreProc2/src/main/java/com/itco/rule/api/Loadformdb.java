package com.itco.rule.api;

import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.function.TprFunction;
import com.itco.rule.config.*;
import com.itco.rule.entity.EntityConfigMgr;
import com.itco.rule.entity.entityDef.RatableEventFormatItem;
import com.itco.rule.entity.entityDef.RatableEventTypeFormat;
import com.itco.rule.entity.entityDef.SourceEventFormatItem;
import com.itco.rule.entity.entityDef.SourceEventTypeFormat;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/*
 * * @数据库规则配置加载类
 * @Author: zhoushq
 * @Version: V1.0.0
 * @Date: 20200330
 */
public class Loadformdb {
    static Log logger = LogFactory.getLog(Loadformdb.class);

    String databaseName;
    int module_id = 1012;

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public boolean LoadRuleFormatitem(Map<Integer, List<RuleFormatitem>> m_formatitem) {
        List<RuleFormatitem> ruleFormatitemList = new ArrayList<RuleFormatitem>();
        String sql = "select a.rule_group_id,a.frist_attr_id,(select b.en_name from tpr_resource_attr b where " +
                "b.attr_id=a.frist_attr_id) frist_en_name,a.attr_id,b.en_name,b.data_type FROM tpr_rule_format_item a," +
                "tpr_resource_attr b where  a.attr_id=b.attr_id ORDER BY a.rule_group_id,a.order_id";
        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            int rule_group_id = -1;
            while (rs.next()) {
                RuleFormatitem ruleFormatitem = new RuleFormatitem();
                ruleFormatitem.setRule_group_id(rs.getInt("rule_group_id"));
                ruleFormatitem.setFrist_attr_id(rs.getInt("frist_attr_id"));
                ruleFormatitem.setFist_en_name(rs.getString("frist_en_name"));
                ruleFormatitem.setAttr_id(rs.getInt("attr_id"));
                ruleFormatitem.setEn_name(rs.getString("en_name"));
                ruleFormatitem.setData_type(rs.getString("data_type"));

                if (ruleFormatitem.getFrist_attr_id() > 0 && ruleFormatitem.getFist_en_name() == null) {
                    logger.error("事件话单字段不存在，Frist_attr_id=" + ruleFormatitem.getFrist_attr_id());
                    return false;
                }
                if (rule_group_id != ruleFormatitem.getRule_group_id()) {
                    if (!ruleFormatitemList.isEmpty()) {
                        m_formatitem.put(rule_group_id, ruleFormatitemList);
                    }
                    rule_group_id = ruleFormatitem.getRule_group_id();
                    ruleFormatitemList = new ArrayList<RuleFormatitem>();
                }
                ruleFormatitemList.add(ruleFormatitem);
            }
            if (!ruleFormatitemList.isEmpty()) {
                m_formatitem.put(rule_group_id, ruleFormatitemList);
            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[tpr_rule_format_item]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }

        return true;
    }

    public boolean LoadRuleSubgroup(Map<Integer, List<RuleSubGroup>> msubgroup) {
        List<RuleSubGroup> l_subgroup = new ArrayList<RuleSubGroup>();
        String sql = "select rule_subgroup_id, rule_group_id, order_id, match_type,comments from " + databaseName + "tpr_rule_subgroup  ORDER BY rule_group_id,order_id";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            Integer rule_group_id = new Integer(-1);
            while (rs.next()) {
                RuleSubGroup subgroup = new RuleSubGroup();
                subgroup.setRule_subgroup_id(rs.getInt("rule_subgroup_id"));
                subgroup.setRule_group_id(rs.getInt("rule_group_id"));
                subgroup.setOrder_id(rs.getInt("order_id"));
                subgroup.setMatch_type(rs.getString("match_type"));
                subgroup.setComments(rs.getString("comments"));

                if (rule_group_id != subgroup.getRule_group_id()) {
                    if (!l_subgroup.isEmpty()) {
                        msubgroup.put(new Integer(rule_group_id), l_subgroup);
                    }
                    rule_group_id = subgroup.getRule_group_id();
                    l_subgroup = new ArrayList<RuleSubGroup>();
                }
                l_subgroup.add(subgroup);
            }
            if (!l_subgroup.isEmpty()) {
                msubgroup.put(new Integer(rule_group_id), l_subgroup);
            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[" + databaseName + "tpr_rule_subgroup]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadPattern(Map<Integer, RulePattern> m_pattern) {

        String sql = "SELECT rule_pattern_id, compare_type, left_value, right_value FROM " + databaseName + "tpr_rule_pattern ORDER BY rule_pattern_id";
        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                RulePattern Pattern = new RulePattern();
                Pattern.setRule_pattern_id(rs.getInt("rule_pattern_id"));
                Pattern.setCommpare_type(rs.getString("compare_type"));
                Pattern.setLeft_value(rs.getString("left_value"));
                Pattern.setRigh_value(rs.getString("right_value"));
                m_pattern.put(Pattern.getRule_pattern_id(), Pattern);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[" + databaseName + "tpr_rule_pattern]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadCondition(Map<Integer, List<RuleCondition>> m_condition) {
        List<RuleCondition> l_condition = new ArrayList<RuleCondition>();
        String sql = "select rule_id, group_id, rule_pattern_id  from " + databaseName + "tpr_rule_condition ORDER BY rule_id, group_id,rule_pattern_id;";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            int rule_id = -1;
            while (rs.next()) {
                RuleCondition Condition = new RuleCondition();

                Condition.setRule_id(rs.getInt("rule_id"));
                Condition.setGroup_id(rs.getInt("group_id"));
                Condition.setRule_pattern_id(rs.getInt("rule_pattern_id"));

                if (rule_id != Condition.getRule_id()) {
                    if (!l_condition.isEmpty()) {
                        m_condition.put(rule_id, l_condition);
                    }
                    rule_id = Condition.getRule_id();
                    l_condition = new ArrayList<RuleCondition>();
                }
                l_condition.add(Condition);
            }
            if (!l_condition.isEmpty()) {
                m_condition.put(rule_id, l_condition);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[" + databaseName + "tpr_rule_condition]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadAction(Map<Integer, List<Ruleaction>> m_action) {
        List<Ruleaction> l_ation = new ArrayList<Ruleaction>();
        String sql = "SELECT rule_id, order_id, action_type, variable_id, result_value FROM " + databaseName + "tpr_rule_action ORDER BY rule_id, order_id";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            int rule_id = -1;
            while (rs.next()) {
                Ruleaction ation = new Ruleaction();
                ation.setRule_id(rs.getInt("rule_id"));
                ation.setOrder_id(rs.getInt("order_id"));
                ation.setAction_type(rs.getString("action_type"));
                ation.setResult_value(rs.getString("result_value"));
                ation.setVariable_id(rs.getString("variable_id"));
                if (rule_id != ation.getRule_id()) {
                    if (!l_ation.isEmpty()) {
                        m_action.put(rule_id, l_ation);
                    }
                    rule_id = ation.getRule_id();
                    l_ation = new ArrayList<Ruleaction>();
                }
                l_ation.add(ation);
            }
            if (!l_ation.isEmpty()) {
                m_action.put(rule_id, l_ation);
            }

        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[" + databaseName + "tpr_rule_action]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadRuleSubgroupStep(Map<Integer, List<RuleSubGroupStep>> m_subgroupsetp) {
        List<RuleSubGroupStep> l_subgroup = new ArrayList<RuleSubGroupStep>();
        String sql = "select rule_subgroup_id, order_id, rule_id from " + databaseName + "tpr_rule_subgroup_step  ORDER BY rule_subgroup_id, order_id";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            int subgroup_id = -1;
            while (rs.next()) {
                RuleSubGroupStep ruleSubGroupStep = new RuleSubGroupStep();
                ruleSubGroupStep.setRule_subgroup_id(rs.getInt("rule_subgroup_id"));
                ruleSubGroupStep.setOrder_id(rs.getInt("order_id"));
                ruleSubGroupStep.setRule_id(rs.getInt("rule_id"));

                if (subgroup_id != ruleSubGroupStep.getRule_subgroup_id()) {
                    if (!l_subgroup.isEmpty()) {
                        m_subgroupsetp.put(subgroup_id, l_subgroup);
                    }
                    subgroup_id = ruleSubGroupStep.getRule_subgroup_id();
                    l_subgroup = new ArrayList<RuleSubGroupStep>();
                }
                l_subgroup.add(ruleSubGroupStep);
            }
            if (!l_subgroup.isEmpty()) {
                m_subgroupsetp.put(subgroup_id, l_subgroup);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[" + databaseName + "tpr_rule_subgroup_step]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadRulegroup(Map<Integer, TprRuleGroup> m_rulegroup) {
        String sql = "select rule_group_id,event_type_id,module_id,class_type,comments,log_id,rule_object_type from TPR_RULE_GROUP where module_id = ?";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            ps.setObject(1,module_id);
            rs = ps.executeQuery();

            while (rs.next()) {
                TprRuleGroup tmp = new TprRuleGroup();
                tmp.setRule_group_id(rs.getInt("rule_group_id"));
                tmp.setEvent_type_id(rs.getInt("event_type_id"));
                tmp.setModule_id(rs.getInt("module_id"));
                tmp.setClass_type(rs.getString("class_type"));
                tmp.setComments(rs.getString("comments"));
                tmp.setLog_id(rs.getInt("log_id"));
                tmp.setRule_object_type(rs.getString("rule_object_type"));

                m_rulegroup.put(tmp.getEvent_type_id(), tmp);
            }
            rs.close();
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[TPR_RULE_GROUP]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean LoadResourceattr(Map<Integer, TprResourceAttr> mapAttr) {
        String sql = "select attr_id,lower(en_name) en_name,ch_name,substr(data_type,2) data_type,data_length,data_precision,array_size,comments from tpr_resource_attr ORDER BY attr_id";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                TprResourceAttr attr = new TprResourceAttr();
                attr.setAttr_id(rs.getInt("attr_id"));
                attr.setEn_name(rs.getString("en_name"));
                attr.setCh_name(rs.getString("ch_name"));
                attr.setData_type(rs.getString("data_type"));
                attr.setData_precision(rs.getInt("data_precision"));
                attr.setArray_size(rs.getInt("array_size"));
                attr.setComments(rs.getString("comments"));

                mapAttr.put(attr.getAttr_id(), attr);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[tpr_resource_attr]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public boolean LoadWidetableAttr(Map<String, String> m_wideattr) {
        String sql = "select a.attr_id,lower(b.en_name) en_name from tpr_wide_table_attr a,tpr_resource_attr b where a.KEY_ATTR_ID = b.attr_id";

        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                String attr_id = rs.getString("attr_id");
                String en_name = rs.getString("en_name");
                m_wideattr.put(attr_id, en_name);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[tpr_wide_table_attr]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public int LoadBillingCycle() {
        int billing_cycle_id = 0;
        String sql = "select billing_cycle_id from " + databaseName + "billing_cycle where status_cd='1000'";
        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                String billingCycleId = rs.getString("billing_cycle_id");
                billing_cycle_id = Integer.valueOf(billingCycleId);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[billing_cycle]失败");
            return -1;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return billing_cycle_id;
    }

    public boolean LoadTprFunction(Map<Integer, TprFunction> mapFunction) {
        String sql = "select function_id,en_name,ch_name,function_type,result_type,param_num,src_path,lib_path,module_id,db_pkg_name,latn_id,comments from tpr_function ORDER BY function_id";
        Connection connTmp = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            connTmp = DbPool.getConn();
            ps = connTmp.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                TprFunction function = new TprFunction();
                function.setFunction_id(rs.getInt("function_id"));
                function.setEn_name(rs.getString("en_name"));
                function.setCh_name(rs.getString("ch_name"));
                function.setFunction_type(rs.getString("function_type"));
                function.setResult_type(rs.getString("result_type"));
                function.setParam_num(rs.getInt("param_num"));
                function.setSrc_path(rs.getString("src_path"));
                function.setLib_path(rs.getString("lib_path"));
                function.setModule_id(rs.getInt("module_id"));
                function.setDb_pkg_name(rs.getString("db_pkg_name"));
                function.setLatn_id(rs.getInt("latn_id"));

                mapFunction.put(function.getFunction_id(), function);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            logger.error("加载配置表[tpr_resource_attr]失败");
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }
        return true;
    }

    public boolean InitReadTableRule(EntityConfigMgr entityConfigMgr) {
        String TprResourceAttrsql = "select attr_id,lower(en_name) en_name,ch_name,substr(data_type,2) data_type,data_length,data_precision,array_size,comments from " + databaseName + "tpr_resource_attr order by attr_id";
        String SourceEventTypeFormatSql = "select source_event_type_id,version,source_event_format_id,event_type_id,comments,recycle_erase_add_days,event_class from " + databaseName + "source_event_type_format";
        String SourceEventFormatItemSql = "select source_event_format_id,event_attr_id,location,length,code,name,end_char,source_field_name from " + databaseName + "source_event_format_item order by source_event_format_id,location";
        String RatableEventTypeFormatSql = "select event_type_id,module_id,redo_flag,event_format_id,comments from " + databaseName + "ratable_event_type_format where module_id=1 order by event_type_id";
        String RatableEventFormatItemSql = "select event_format_id,event_attr_id,sort_id,default_value,comments from " + databaseName + "ratable_event_format_item order by event_format_id,sort_id";

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            entityConfigMgr.setmTprResourceAttr(DBUtils.queryList(TprResourceAttr.class, connTmp, TprResourceAttrsql));
            entityConfigMgr.setmSourceType(DBUtils.queryList(SourceEventTypeFormat.class, connTmp, SourceEventTypeFormatSql));
            entityConfigMgr.setSourceItem(DBUtils.queryList(SourceEventFormatItem.class, connTmp, SourceEventFormatItemSql));
            entityConfigMgr.setRatableType(DBUtils.queryList(RatableEventTypeFormat.class, connTmp, RatableEventTypeFormatSql));
            entityConfigMgr.setmRatableItem(DBUtils.queryList(RatableEventFormatItem.class, connTmp, RatableEventFormatItemSql));
        } finally {
            DbPool.close(connTmp);
        }

        logger.info(entityConfigMgr.toString());
        return true;
    }
}
