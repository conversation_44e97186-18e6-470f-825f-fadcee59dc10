package com.itco.rule.config;

public class Mqmessage {

    private String TASK_ID;//任务编号
    private String HOST_ID;
    private String PROCESS_ID;
    private String FILE_NAME;//文件名
    private String LOCAL_INPUT_PATH;// 文件路径
    private String LOCAL_OUTPUT_PATH;//文件路径
    private String RECORD_CNT;//记录数
    private String DEAL_TIME;//处理时长
    private String MSG_TYPE;//--REQUEST:请求,RESPONSE:应答
    private String STATE;//--OK:完成,ERROR:处理异常
    private String UPDATE_DATE;//更新时间

    public String getERROR_MSG() {
        return ERROR_MSG;
    }

    public void setERROR_MSG(String ERROR_MSG) {
        this.ERROR_MSG = ERROR_MSG;
    }

    private String ERROR_MSG;
    public String getTASK_ID() {
        return TASK_ID;
    }

    public void setTASK_ID(String TASK_ID) {
        this.TASK_ID = TASK_ID;
    }

    public String getHOST_ID() {
        return HOST_ID;
    }

    public void setHOST_ID(String HOST_ID) {
        this.HOST_ID = HOST_ID;
    }

    public String getPROCESS_ID() {
        return PROCESS_ID;
    }

    public void setPROCESS_ID(String PROCESS_ID) {
        this.PROCESS_ID = PROCESS_ID;
    }

    public String getFILE_NAME() {
        return FILE_NAME;
    }

    public void setFILE_NAME(String FILE_NAME) {
        this.FILE_NAME = FILE_NAME;
    }

    public String getLOCAL_INPUT_PATH() {
        return LOCAL_INPUT_PATH;
    }

    public void setLOCAL_INPUT_PATH(String LOCAL_INPUT_PATH) {
        this.LOCAL_INPUT_PATH = LOCAL_INPUT_PATH;
    }

    public String getLOCAL_OUTPUT_PATH() {
        return LOCAL_OUTPUT_PATH;
    }

    public void setLOCAL_OUTPUT_PATH(String LOCAL_OUTPUT_PATH) {
        this.LOCAL_OUTPUT_PATH = LOCAL_OUTPUT_PATH;
    }

    public String getRECORD_CNT() {
        return RECORD_CNT;
    }

    public void setRECORD_CNT(String RECORD_CNT) {
        this.RECORD_CNT = RECORD_CNT;
    }

    public String getDEAL_TIME() {
        return DEAL_TIME;
    }

    public void setDEAL_TIME(String DEAL_TIME) {
        this.DEAL_TIME = DEAL_TIME;
    }

    public String getMSG_TYPE() {
        return MSG_TYPE;
    }

    public void setMSG_TYPE(String MSG_TYPE) {
        this.MSG_TYPE = MSG_TYPE;
    }

    public String getSTATE() {
        return STATE;
    }

    public void setSTATE(String STATE) {
        this.STATE = STATE;
    }

    public String getUPDATE_DATE() {
        return UPDATE_DATE;
    }

    public void setUPDATE_DATE(String UPDATE_DATE) {
        this.UPDATE_DATE = UPDATE_DATE;
    }


}
