package com.itco.rule.config;

public class RuleSubGroup {
    int rule_subgroup_id;
    int rule_group_id;
    int order_id;
    String match_type;
    String comments;

    public int getRule_subgroup_id() {
        return rule_subgroup_id;
    }

    public void setRule_subgroup_id(int rule_subgroup_id) {
        this.rule_subgroup_id = rule_subgroup_id;
    }

    public int getRule_group_id() {
        return rule_group_id;
    }

    public void setRule_group_id(int rule_group_id) {
        this.rule_group_id = rule_group_id;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public String getMatch_type() {
        return match_type;
    }

    public void setMatch_type(String match_type) {
        this.match_type = match_type;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "Rulesubgroup{" +
                "rule_subgroup_id=" + rule_subgroup_id +
                ", rule_group_id=" + rule_group_id +
                ", order_id=" + order_id +
                ", match_type='" + match_type + '\'' +
                '}';
    }
}
