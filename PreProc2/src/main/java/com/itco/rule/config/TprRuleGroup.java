package com.itco.rule.config;

public class TprRuleGroup {
    int rule_group_id;
    int event_type_id;
    int module_id;
    String class_type;
    String comments;
    int log_id;
    String rule_object_type;

    public int getRule_group_id() {
        return rule_group_id;
    }

    public void setRule_group_id(int rule_group_id) {
        this.rule_group_id = rule_group_id;
    }

    public int getEvent_type_id() {
        return event_type_id;
    }

    public void setEvent_type_id(int event_type_id) {
        this.event_type_id = event_type_id;
    }

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public String getClass_type() {
        return class_type;
    }

    public void setClass_type(String class_type) {
        this.class_type = class_type;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public int getLog_id() {
        return log_id;
    }

    public void setLog_id(int log_id) {
        this.log_id = log_id;
    }

    public String getRule_object_type() {
        return rule_object_type;
    }

    public void setRule_object_type(String rule_object_type) {
        this.rule_object_type = rule_object_type;
    }

    @Override
    public String toString() {
        return "TprRuleGroup{" +
                "rule_group_id=" + rule_group_id +
                ", event_type_id=" + event_type_id +
                ", module_id=" + module_id +
                ", class_type='" + class_type + '\'' +
                ", comments='" + comments + '\'' +
                ", log_id=" + log_id +
                ", rule_object_type='" + rule_object_type + '\'' +
                '}';
    }
}
