package com.itco.rule.config;

public class RuleFormatitem{
    private int rule_group_id;
    private int frist_attr_id;
    private String fist_en_name;
    private int attr_id;
    private int order_id;
    private String en_name;
    private String data_type;
    public String getFist_en_name() {
        return fist_en_name;
    }

    public void setFist_en_name(String fist_en_name) {
        this.fist_en_name = fist_en_name;
    }
    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }
    public int getRule_group_id() {
        return rule_group_id;
    }

    public void setRule_group_id(int rule_group_id) {
        this.rule_group_id = rule_group_id;
    }

    public int getAttr_id() {
        return attr_id;
    }

    public void setAttr_id(int attr_id) {
        this.attr_id = attr_id;
    }

    public String getEn_name() {
        return en_name;
    }

    public void setEn_name(String en_name) {
        this.en_name = en_name;
    }

    public String getData_type() {
        return data_type;
    }

    public void setData_type(String data_type) {
        this.data_type = data_type;
    }

    public int getFrist_attr_id() {
        return frist_attr_id;
    }

    public void setFrist_attr_id(int frist_attr_id) {
        this.frist_attr_id = frist_attr_id;
    }

    @Override
    public String toString() {
        return "RuleFormatitem{" +
                "rule_group_id=" + rule_group_id +
                ", frist_attr_id=" + frist_attr_id +
                ", fist_en_name='" + fist_en_name + '\'' +
                ", attr_id=" + attr_id +
                ", order_id=" + order_id +
                ", en_name='" + en_name + '\'' +
                ", data_type='" + data_type + '\'' +
                '}';
    }
}
