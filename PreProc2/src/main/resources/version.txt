--------------------------------------------------------------------------------------------------
【发布版本】：PreProc2_code_2023-03-10 11:00
【修订日期】：2023-03-10
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54591
【实现功能】：
1、新增ErrorType，错误编码

【变更文件】：
修改:src/main/java/com/itco/rule/api/GenCode.java
修改:src/main/java/com/itco/rule/module/CalcRecordRule.java
新增:src/main/java/com/itco/rule/entity/entityDef/ErrorType.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：PreProc2_code_2023-02-07 11:00
【修订日期】：2023-02-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54542
【实现功能】：
1、原始话单转标准话单，如果转换失败进异常单，不进错误单。
2、打印异常字段、值，异常信息

【变更文件】：
修改：src/main/java/com/itco/preproc2/PreProc2Application.java
修改：src/main/java/com/itco/rule/api/GenCode.java
修改：src/main/java/com/itco/rule/module/CommonEnum.java
新增：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
  --------------------------------------------------------------------------------------------------