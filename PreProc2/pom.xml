<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.itco</groupId>
        <artifactId>settle</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.itco</groupId>
    <artifactId>PreProc2</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <dependencies>

        <dependency>
            <groupId>com.itco</groupId>
            <artifactId>bill-public</artifactId>
            <version>${settle-version}</version>
        </dependency>
        <dependency>
            <groupId>com.itco</groupId>
            <artifactId>rule-functionTpss_591</artifactId>
            <version>${settle-version}</version>
        </dependency>
        <dependency>
            <groupId>com.itco</groupId>
            <artifactId>rule-visualfunction</artifactId>
            <version>${settle-version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>