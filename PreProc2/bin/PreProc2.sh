#!/bin/sh
## java env
export DCOOS_HOME=
#export JAVA_HOME=$DCOOS_HOME/java_dir/jdk1.8.0_181
#export JRE_HOME=$JAVA_HOME/jre

#LOG_ID=$2
LOG_ID=-f$2
ELEMENT=-e
#DEBUG=-d

# 设置启动java文件名
APP_NAME=PreProc2-0.0.1-SNAPSHOT
JAR_NAME="$DCOOS_HOME/javabin/$APP_NAME.jar"
JAR_LOG="$DCOOS_HOME/javalog/$APP_NAME$LOG_ID.log"


#PID  代表是PID文件
PID="$DCOOS_HOME/javalog/pid/$APP_NAME$LOG_ID.pid"
#echo "PID:" $PID

#使用说明，用来提示输入参数
usage() {
    echo "命令行格式: PreProc2.sh [start|stop|restart|status] 日志编号"
    echo "例如启动日志编号11的进程实例:"
    echo " PreProc2.sh start 11"
    exit 1
}

#检查程序是否在运行
is_exist(){
#  echo $JAR_NAME
  pid=`ps ux|grep "$JAR_NAME $LOG_ID"|grep -v grep|awk '{print $2}' `
  #如果不存在返回1，存在返回0
  if [ -z "${pid}" ]; then
   return 1
  else
    return 0
  fi
}

#启动方法
start(){
  is_exist
  if [ $? -eq "0" ]; then
    echo ">>> ${APP_NAME} is already running PID=${pid} <<<"
  else
    nohup java -Xms1024m -Xmx1024m -jar -Dfile.encoding=UTF-8 $JAR_NAME $LOG_ID $ELEMENT $DEBUG > $JAR_LOG 2>&1 &
    echo $! > $PID
    echo ">>> start ${APP_NAME} ${LOG_ID} ${ELEMENT} ${DEBUG} successed PID=$! <<<"
   fi
  }

#停止方法
stop(){
  is_exist
  pidf=$(cat $PID)
  echo "$pidf"
  echo ">>> api PID = $pidf begin kill $pidf <<<"
  kill $pidf
  rm -rf $PID
  sleep 2
  is_exist
  if [ $? -eq "0" ]; then
    echo ">>> api 2 PID = $pid begin kill -9 $pid  <<<"
    kill -9  $pid
    sleep 2
    echo ">>> $JAR_NAME process stopped <<<"
  else
    echo ">>> ${JAR_NAME} is not running <<<"
  fi
}

#输出运行状态
status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo ">>> ${JAR_NAME} is running PID is ${pid} <<<"
  else
    echo ">>> ${JAR_NAME} is not running <<<"
  fi
}

#重启
restart(){
  stop
  start
}

if [ ! -n "$2" ];then
    if [ "status" != "$1" ];then
      usage
    fi
fi

#根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  *)
    usage
    ;;
esac

#sleep 5

## 打印项目日志
tail -f -n1000 $JAR_LOG
exit 0
