<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.6.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.itco</groupId>
    <artifactId>settle</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>settle</name>
    <description>Demo project for Spring Boot</description>

    <modules>
        <module>bill-public</module>
        <module>rule-functionTpss_591</module>
        <module>rule-visualfunction</module>
        <module>upload</module>
        <module>CmdClient</module>
        <module>Dispatch</module>
        <module>DbIncept</module>
        <module>PreProc2</module>
        <module>Rating</module>
        <module>Cumulant</module>
        <module>TicketIndb</module>
        <module>DbMonitor</module>
        <module>DtRecovery</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sprint-boot.version>2.2.6RELEASE</sprint-boot.version>
        <skipTests>true</skipTests>

        <org.apache.commons.commons-lang3>3.9</org.apache.commons.commons-lang3>
        <commons-collections.commons-collections>3.2.2</commons-collections.commons-collections>
        <commons-logging.commons-logging>1.2</commons-logging.commons-logging>
        <commons-codec.commons-codec>1.10</commons-codec.commons-codec>
        <io.netty.netty-all>4.0.23.Final</io.netty.netty-all>
        <org.postgresql.postgresql>42.2.12</org.postgresql.postgresql>
        <org.apache.zookeeper.zookeeper>3.4.6</org.apache.zookeeper.zookeeper>
        <com.ctg.itrdc.mq.ctg-mq-api>2.4.0</com.ctg.itrdc.mq.ctg-mq-api>
        <com.ctg.mq.ctg-mq-client>2.4.0</com.ctg.mq.ctg-mq-client>
        <com.ctg.mq.ctg-mq-common>2.4.0</com.ctg.mq.ctg-mq-common>
        <com.ctg.mq.ctg-mq-remoting>2.4.0</com.ctg.mq.ctg-mq-remoting>
        <com.ctg.mq.ctg-mq-srvutil>2.4.0</com.ctg.mq.ctg-mq-srvutil>
        <com.ctg.mq.ctg-mq-store>2.4.0</com.ctg.mq.ctg-mq-store>
        <com.ctg.mq.ctg-mq-tools>2.4.0</com.ctg.mq.ctg-mq-tools>
        <com.ctg.itrdc.cache.ctg-cache-nclient>2.4.0</com.ctg.itrdc.cache.ctg-cache-nclient>
        <redis.clients.jedis>2.9.0</redis.clients.jedis>
        <com.alibaba.fastjson>1.2.83</com.alibaba.fastjson>
        <com.alibaba.druid>1.2.12</com.alibaba.druid>
        <joda-time.joda-time>2.9.9</joda-time.joda-time>
        <lombok.version>1.18.10</lombok.version>
        <settle-version>5.0.1-SNAPSHOT</settle-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--JSON组件-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${com.alibaba.fastjson}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${org.apache.commons.commons-lang3}</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.commons-collections}</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>${commons-logging.commons-logging}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.commons-codec}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${io.netty.netty-all}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${com.alibaba.druid}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!--TelePG -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${org.postgresql.postgresql}</version>
        </dependency>

        <!--zookeeper依赖组件-->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${org.apache.zookeeper.zookeeper}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--集团ctgMQ组件-->
        <dependency>
            <groupId>com.ctg.itrdc.mq</groupId>
            <artifactId>ctg-mq-api</artifactId>
            <version>${com.ctg.itrdc.mq.ctg-mq-api}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-client</artifactId>
            <version>${com.ctg.mq.ctg-mq-client}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-common</artifactId>
            <version>${com.ctg.mq.ctg-mq-common}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-remoting</artifactId>
            <version>${com.ctg.mq.ctg-mq-remoting}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-srvutil</artifactId>
            <version>${com.ctg.mq.ctg-mq-srvutil}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-store</artifactId>
            <version>${com.ctg.mq.ctg-mq-store}</version>
        </dependency>
        <dependency>
            <groupId>com.ctg.mq</groupId>
            <artifactId>ctg-mq-tools</artifactId>
            <version>${com.ctg.mq.ctg-mq-tools}</version>
        </dependency>

       <!-- <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.3.0</version>
        </dependency>-->
        <dependency>
            <groupId>com.ctg.itrdc.cache</groupId>
            <artifactId>ctg-cache-nclient</artifactId>
            <version>${com.ctg.itrdc.cache.ctg-cache-nclient}</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.clients.jedis}</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.joda-time}</version>
        </dependency>
    </dependencies>
</project>
