FROM openjdk:8-jre-alpine
RUN mkdir javabin/
RUN mkdir config/
RUN mkdir javalog/
RUN mkdir javalog/pid/
COPY ./bin/upload.sh javabin/
COPY ./bin/jdbc.properties config/
COPY ./bin/ctgMq.properties config/
COPY ./bin/ctgCache.properties config/
COPY ./bin/zk.properties config/
COPY ./bin/Dispatch.properties config/
COPY ./bin/Rating.properties config/
COPY ./bin/TicketIndb.properties config/
COPY ./bin/tPreProc2.properties config/
COPY ./bin/tRating.properties config/
COPY ./bin/tTicketIndb.properties config/
COPY ./bin/PreProc2.properties config/
COPY ./bin/Common.properties config/
COPY ./bin/CmdClient.properties config
COPY ./bin/DbIncept.properties config/
COPY ./bin/Cumulant.properties config/
COPY ./bin/tCumulant.properties config/
COPY ./bin/pCumulant.properties config/
COPY ./bin/druid.properties config/
COPY ./bin/sshInfo.properties config/
COPY ./bin/DbMonitor.properties config/
COPY ./target/upload-0.0.1-SNAPSHOT.jar javabin/
WORKDIR javabin/
RUN chmod 755 *.sh
ENV PATH=/javabin:$PATH
ENV CONFIG /config/
# 修改docker时区为东八区，规避应用程序和北京时间相差8小时问题 
ENV TZ=Asia/Shanghai 
ENV LOG_HOME /javalog/
CMD sh /javabin/upload.sh