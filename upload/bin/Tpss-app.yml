# tomcat
server:
  port: 8087
  session:
    timeout:1800
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /tpss

# database
spring:
  quartz:
    job-store-type: memory # 使用数据库存储
    auto-startup: true # Quartz 是否自动启动
    startup-delay: 0 # 延迟 N 秒启动
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    overwrite-existing-jobs: true # 是否覆盖已有 Job 的配置
    properties:
      org:
        quartz:
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
          concurrent: false
  main:
      allow-bean-definition-overriding: true
  servlet:
    multipart:
      maxFileSize: 1000MB
      maxRequestSize: 1000MB
  primary:
    datasource:
      name: druidDataSource
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://***********:16601/digit_db?currentSchema=config&useUnicode=true&characterEncoding=UTF-8&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true
        username: yaoke_bqjtest
        password: XMsbzVZ1Cjapc5Gm36wl0A3sEBl3R9xR2p6S2AJobr5t8zMpJdMyfHug2tNwu3ZicxJ4cWdqKZUAUJ0W0aUCCw==
        filters: stat,config
        maxActive: 20
        initialSize: 5
        maxWait: 60000
        minIdle: 2
        socketTimeout: 300000
        connectTimeout: 10000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: select 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        keepAlive: true
        maxOpenPreparedStatements: 50
        maxPoolPreparedStatementPerConnectionSize: 20
        connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI2GlyNljp5trjUMXp5tRgxCLjcRmR0Nv0Pyg0+Nc8o/hnqz1QPC45osBw/ucopNg4S9/J35Aw8Ep+7NVwiG3D8CAwEAAQ==
  activiti:
    database-schema-update: true
    history-level: full
    db-history-used: true


  resources:
    static-locations: classpath:static/,file:static/

  # 禁用模板引擎的缓存
  thymeleaf:
    cache: false



#调账模块附件路径:
appendix:
  path: /sgw/wangjw/appendix/
  tpRule:
    path: /sgw/wangjw/tpRule/path

#手工导入依据路径
ManualBillImport:
  importBasisPath: /opt/optps/importBasisPath


#返佣报表数据库模式
report:
  pattern:
    data: tpss_data
    intf: tpss_intf
    publics: tpss_public



#流程图路径:
process: /sgw/wangjw/data/work/process/
#process: /data/work/process/

caculate:
  filePath: /opt/optps/appendix
  Reportdownload: /opt/optps/download/操作指南.doc


# log
logging:
  level:
    root: info
    com.ffcs: info

mybatis:
  configuration:
    call-setters-on-nulls: true

