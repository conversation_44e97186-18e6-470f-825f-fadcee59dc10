
#æå¤§çº¿ç¨æ°
max_process_cnt=6
#åçº¿ç¨å¤çæå¤§è®°å½æ°
max_record_cnt=5000

#å¤çå®çæä»¶æ¯å¦å é¤
del_file_flag=true

#å¥åºçè¯åäºä»¶ç±»åå­æ®µï¼TPL_INDB_TABLE.event_type_id(ç»ç®)ãsource_event_type_id(ä½£é)
eti_field_name=source_event_type_id

#æ¯å¦è¾åºè¯å
is_out_record=false

#æ¯å¦å¥åºå°TelePgãmysql
is_database=true

#æ¯å¦å¥åºå°hbase
is_Hbase=false

#ä»»å¡åºéæ¯å¦éåº
is_error_exit=false

#æ¯å¦å¥åºå°Hdfs(æ ¹æ®äºä»¶ç±»åãè¯åç±»åç®å½å­æ¾)
is_Hdfs=false
#è¾åºæä»¶æ¨¡å¼
is_mode=FILE
hdfs_path=/data/work/TicketIndb

#æ­ç¹è¡¨é
update_point_mode=point_redis_lock_zk

#æ§è¡ç±»
calc_class_name=com.itco.ticketindb.module.CalcRecordTicketIndb

is_no_payment=true
