driver=org.postgresql.Driver
#telePG
url=jdbc:postgresql://***************:18921/bill_db_20230921?currentSchema=config&useUnicode=true&characterEncoding=UTF-8&useSSL=true&serverTimezone=UTC&allowMultiQueries=true
username=bill_app
publicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAPPLFFdMFNyAwHVoV5DD5NK97vCqKkV1OnlpTRIppPtqPHI/SZJOAy4uVtEdPOJZvjsSf4hnkcqilsOg1RgjtKcCAwEAAQ==
password=wa70H4k+l9iiUFZVZ90dyHsXmL/1mG5lTQhD/sP7hKv1d2r2P91sKjgmgwb77WpsVCqLKIi0TvMcn/gSvUlIPA==

initialSize=3
maxActive=100
maxWait=70000
minIdle=1
autoCommit=false

timeBetweenEvictionRunsMillis=60000
minEvictableIdleTimeMillis=30000
validationQuery=SELECT 1
testWhileIdle=true
testOnBorrow=false
testOnReturn=false
poolPreparedStatements=true
maxPoolPreparedStatementPerConnectionSize=20
