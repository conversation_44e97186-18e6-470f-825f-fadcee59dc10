#ç³»ç»åç§°: ISSãTPSS
system=DIGIT
#çä»½æ è¯: 591ã371
latn_id=010
#è¯åæµè½¬æ¹å¼: FILEãHDFSãCTG_MQ
record_transfer_mode=FILE
#æ¨¡åé´ä»»å¡æµè½¬æ¹å¼ï¼CTG_MQãZK
task_transfer_mode=ZK

#CONTAINER:å®¹å¨åé¨ç½²ãVM:èææºé¨ç½²
deployment=VM

#æ¡£æ¡ä¸è½½ï¼åçæ°é
modulo=320

#ä»»å¡å¤çå¤±è´¥ï¼è¶æ¶ç§æ°
time_out_seconds=6000
#TDç¶æçè¿ç¨ï¼å¤ä¹åæ¶ä»»å¡ãé»è®¤16ç§
td_process_rollback_seconds=16

#è¯åæµè½¬æ¶æ¯éå
taskCnt=20000
#MQååæ¡æå¤§è¯åæ°ï¼ç±äºMQéå¶ï¼æå¤§100
mqOnceCnt=100

#è¯åç¼å­HASH KEYåç§°
record_cache_hashkey=RECORD_NORMAL

#æ¨¡åå¤çå¤å°æ¡è¯åç»è®¡ä¸æ¬¡æ§è½ï¼é»è®¤5000
perl_out_cnt=5000
#æ§è½ä½äºæå®å¼è¾åºå°æ§è½æ¥å¿è¡¨ï¼é»è®¤100
perl_low=100

jar_class=com.itco.RuleFunction.RulePricingPlanIss
jar_path=/opt/optps/javalib/rule-functionIss_591-2.0.1.jar
