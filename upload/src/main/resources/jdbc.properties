driver=org.postgresql.Driver

#telePG
url=****************************************************************************************************************************************************************
username=bill_app
publicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJUrHBI2WmciCZ4dwAr+3uc7kqgxfg14JbMpj1GNgZnYcpwPhzgX/R6k53Tsjl0dQ5SUKkTUmD+WSeh2tcG/jNkCAwEAAQ==
password=MHW5+p4WVVMNwfiAdz19te9DwzDvpBRAfwX26UbggSWqHyNW70FmNYZsAmfwxUCBsPxn7D7uSsOEhkYpA9jtUQ==

initialSize=3
maxActive=100
maxWait=70000
minIdle=1
autoCommit=false

timeBetweenEvictionRunsMillis=60000
minEvictableIdleTimeMillis=30000
validationQuery=SELECT 1
testWhileIdle=true
testOnBorrow=false
testOnReturn=false
poolPreparedStatements=true
maxPoolPreparedStatementPerConnectionSize=20