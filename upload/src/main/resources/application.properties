logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.org.springframework.boot=ERROR
logging.level.org.springframework.web=ERROR
logging.level.org.springframework.boot.web=ERROR
logging.level.com.ctg.itrdc.cache=ERROR
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n

logging.file.name=${LOG_HOME:~/javalog}/upload.log
logging.file.max-size=10MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz

#·¢²¼°æ±¾
version=upload_deploy_2023-01-30 11:00