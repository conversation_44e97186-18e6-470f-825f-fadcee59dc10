package com.itco.upload.module;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.TpConfigCenter;
import com.itco.entity.common.TpModule;
import com.itco.entity.common.TpProcess;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.zookeeper.KeeperException;
import org.apache.zookeeper.data.Stat;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

public class Configration {
    static Log log = LogFactory.getLog(Configration.class);
    List<TpConfigCenter> configDbList = null;
    Map<Integer, String> configMap = new HashMap<>();
    ZkClientApi zkClientApi = new ZkClientApi();
    static boolean eExit = false;

    String localConfigPath = null;
    String zkConfigHome = null;
    String processPath = null;

    String paramName;
    String paramMode;
    List<TpConfigCenter> configZkList;

    int level = 0;

   /* public boolean initTableSql() {
        String SQL_200 = "select module_id,module_name,parent_module_id,module_code,module_type,order_id,startup_priority,pro_path,start_command,stop_command,max_process_num from dcoos_tp_module";
        String SQL_201 = "select process_id,module_id,host_id,status,billing_line_id,order_id,module_code,system_process_id,process_name,update_date from DCOOS_TP_PROCESS";
        String SQL_202 = "select attr_id,en_name,ch_name,data_type,comments from tpr_resource_attr";
        String SQL_231 = "select id,event_type_id,attr_id,en_name,order_id,comments from tpr_rule_filter";
        String SQL_240 = "select TABLE_ID,EN_NAME,CH_NAME,KEY_ATTR_NAME,REDIS_DB,REDIS_FORMAT,REDIS_SEPARATOR,TYPE,UPLOAD_SQL,FILE_PATH,FILE_FOAMRT,STATUS_CD,CREATE_STAFF,UPDATE_STAFF,CREATE_DATE,UPDATE_DATE,STATUS_DATE from tpr_table order by TABLE_ID";
        String SQL_241 = "select TABLE_ATTR_ID,TABLE_ID,ATTR_ID,POSI,ORDER_ID from tpr_table_format_item order by TABLE_ID,ORDER_ID";
        String SQL_242 = "select attr_id,en_name,ch_name,data_type,comments from tpr_table_attr";
        String SQL_243 = "select table_id,index_group_id,priority,field_attr_id from tpr_table_index order by table_id,index_group_id,priority";
        String SQL_250 = "select point_id,module_id,host_id,billing_line_id,point_type,point_value,create_date from break_point";
        String SQL_251 = "select table_id,event_type_id,latn_id,ticket_type,if_deduct,billing_cycle_id,table_name,trial_table_name,table_format_id,comments from tpl_indb_table";
        String SQL_252 = "select table_format_id,table_format_name,comments from tpl_indb_table_format";
        String SQL_253 = "select table_format_id,indb_field_id,source_attr_id,default_value,comments from tpl_indb_table_format_item";
        String SQL_255 = "select function_id,module_id,en_name,ch_name,result_type,src_path,lib_path from tpr_function";
        String SQL_256 = "select package_id,path,en_name,ch_name,module_id from tpr_function_package";

        configMap.put(200, SQL_200);
        configMap.put(201, SQL_201);
        configMap.put(202, SQL_202);
        configMap.put(231, SQL_231);
        configMap.put(240, SQL_240);
        configMap.put(241, SQL_241);
        configMap.put(242, SQL_242);
        configMap.put(243, SQL_243);
        configMap.put(250, SQL_250);
        configMap.put(251, SQL_251);
        configMap.put(252, SQL_252);
        configMap.put(253, SQL_253);
        configMap.put(255, SQL_255);
        configMap.put(256, SQL_256);

        return true;
    }*/

    public boolean init() {
        // 初始化上载表语句。
        //initTableSql();

        String sqlConfig = "select id,en_name,ch_name,type,table_sql,path,state,comments from tp_config_center where state = 'EXP' order by id";

        // 初始化zookeeper
        zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("upload");
        if (!zkClientApi.init()) {
            seteExit(true);
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());

        if (paramMode.equals("clear") || paramMode.equals("show") || paramMode.equals("aclshow") || paramMode.equals("acl")) {
            return true;
        }

        DbPool.setConfLoadMode(1);
        DbPool.setZkClientApi(zkClientApi);
        DbPool.setDruidMonitorEnable(false);
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            // 连接数据库，读取配置
            configDbList = DBUtils.queryList(TpConfigCenter.class, connTmp, sqlConfig);
        } finally {
            DbPool.close(connTmp);
        }


        // 读取环境变量
        localConfigPath = zkClientApi.getUploadHomeConfig();
        log.info("localConfigPath:" + localConfigPath);
        if (localConfigPath == null) {
            log.info("没有配置upload加载的配置文件路径：uploadHomeConfig");
        }

        //  /config保证存在
        zkConfigHome = zkClientApi.getConfigHome();
        if (0 == zkClientApi.iExistPath(zkConfigHome)) {
            zkClientApi.createPath(zkConfigHome, "");
        }

        // 进程注册辅助目录
        if (0 == zkClientApi.iExistPath(zkClientApi.getProcessAssistHome())) {
            zkClientApi.createFullPath(zkClientApi.getProcessAssistHome());
        }

        // 进程锁使用目录
        if (0 == zkClientApi.iExistPath(zkClientApi.getProcessLockHome())) {
            zkClientApi.createFullPath(zkClientApi.getProcessLockHome());
        }

        // 进程启停目录 /process保证存在
        processPath = zkClientApi.getProcessPath();
        if (0 == zkClientApi.iExistPath(processPath)) {
            zkClientApi.createPath(processPath, "");
            if (!initProcess()) {
                log.info("initProcess() faile");
                return false;
            }
        }

        // 配置同步目录
        if (0 == zkClientApi.iExistPath(zkClientApi.getMessageConfig())) {
            zkClientApi.createFullPath(zkClientApi.getMessageConfig());
        }

        // 任务消息目录初始化
        if (0 == zkClientApi.iExistPath(zkClientApi.getMessageTask())) {
            zkClientApi.createFullPath(zkClientApi.getMessageTask());
            if (!initTask()) {
                log.info("initTask() faile");
                return false;
            }
        }

        return true;
    }

    public boolean LoadInitTable() {
        if (!zkClientApi.loadInitTable()) {
            log.info("zkClientApi.loadInitTable() faile");
            return false;
        }
        return true;
    }

    public void close() {
        if (zkClientApi != null) {
            zkClientApi.close();
        }

        DbPool.close();
    }

    /*加载文件配置*/
    public String loadFile(TpConfigCenter config) {
        String record = new String();
        BufferedReader recordReader = null;
        try {
            String filePath = localConfigPath + "/" + config.getEn_name();
            //log.info("filePath:" + filePath);
            recordReader = new BufferedReader(new FileReader(filePath));

            String recordStr = null;
            while ((recordStr = recordReader.readLine()) != null) {
                if (recordStr.equals("")) {
                    continue;
                }
                if (!record.equals("")) {
                    record += "\n" + recordStr;
                } else {
                    record += recordStr;
                }
            }

            String zkPath = config.getPath() + "/" + config.getEn_name();
            if (0 == zkClientApi.iExistPath(config.getPath())) {
                zkClientApi.createFullPath(config.getPath());
            }

            if (0 == zkClientApi.iExistPath(zkPath)) {
                zkClientApi.createPath(zkPath, record);
            } else {
                zkClientApi.setData(zkPath, record);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        } finally {
            try {
                if (recordReader != null) {
                    recordReader.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }

        return record;
    }

    /*加载表配置*/
    public String loadTableList(TpConfigCenter config) {
        String record = new String();
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            List<Map<String, Object>> recordMapList = DBUtils.queryMap(connTmp, config.getTable_sql());
            for (Map<String, Object> recordMap : recordMapList) {
                JSONObject jsonObject = new JSONObject(recordMap);

                if (!record.equals("")) {
                    record += "\n" + JSONObject.toJSONString(jsonObject, SerializerFeature.WriteNullStringAsEmpty);
                } else {
                    record += JSONObject.toJSONString(jsonObject, SerializerFeature.WriteNullStringAsEmpty);
                }
            }
        } finally {
            DbPool.close(connTmp);
        }

        String zkPath = config.getPath() + "/" + config.getEn_name();
        if (0 == zkClientApi.iExistPath(config.getPath())) {
            zkClientApi.createFullPath(config.getPath());
        }

        if (0 == zkClientApi.iExistPath(zkPath)) {
            zkClientApi.createPath(zkPath, record);
        } else {
            zkClientApi.setData(zkPath, record);
        }
        return record;
    }

    /**
     * @param config
     * @return
     */
    public String loadTableMap(TpConfigCenter config) {
        String record = new String();

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            List<Map<String, Object>> recordMapList = DBUtils.queryMap(connTmp, config.getTable_sql());
            for (Map<String, Object> recordMap : recordMapList) {
                JSONObject jsonObject = new JSONObject(recordMap);

                if (!record.equals("")) {
                    record += "\n" + JSONObject.toJSONString(jsonObject, SerializerFeature.WriteNullStringAsEmpty);
                } else {
                    record += JSONObject.toJSONString(jsonObject, SerializerFeature.WriteNullStringAsEmpty);
                }
            }
        } finally {
            DbPool.close(connTmp);
        }

        String zkPath = config.getPath() + "/" + config.getEn_name();
        if (0 == zkClientApi.iExistPath(config.getPath())) {
            zkClientApi.createFullPath(config.getPath());
        }

        if (0 == zkClientApi.iExistPath(zkPath)) {
            zkClientApi.createPath(zkPath, record);
        } else {
            zkClientApi.setData(zkPath, record);
        }
        return record;
    }

    List<TpConfigCenter> readConfigPathByZookeeper() {
        String configJson = zkClientApi.getDataByPath("/config");
        if (configJson == null || configJson.equals("")) {
            return null;
        }
        List<TpConfigCenter> list = new ArrayList<>();

        int iBeginPos = 0, iPos = 0;
        while ((iPos = configJson.indexOf("\n", iBeginPos)) != -1) {
            //zk 模块编号和模块名称获取
            String json = configJson.substring(iBeginPos, iPos);
            iBeginPos = iPos + 1;

            if (json != null) {
                TpConfigCenter tRet = JSON.parseObject(json, TpConfigCenter.class);
                list.add(tRet);
            }
        }

        String json = configJson.substring(iBeginPos);
        if (json != null) {
            TpConfigCenter tRet = JSON.parseObject(json, TpConfigCenter.class);
            list.add(tRet);
        }

        return list.size() > 0 ? list : null;
    }

    public boolean inputParamExists() {
        boolean bRet = false;
        if (paramName.equals("all")) {
            return true;
        }

        // 读取现有的zookeeper上/config的值。
        configZkList = readConfigPathByZookeeper();
        if (configZkList == null) {
            log.info("zookeeper 上/config值为空");
        }

        for (TpConfigCenter tpConfigCenter : configDbList) {
            if (tpConfigCenter.getEn_name().equals(paramName)) {
                bRet = true;
            }
        }

        return bRet;
    }

    /*遍历配置表，加载配置*/
    public boolean set() {

        if (paramName.equals("all")) { //全量上载
            log.info("基础配置开始上载");
            String str = new String();
            for (TpConfigCenter config : configDbList) {
                if (CONFIG_TYPE.FILE.type.equals(config.getType())) {
                    String sRet = loadFile(config);
                    if (sRet == null) {
                        log.info("【" + config.getEn_name() + "】,加载失败\n");
                        return false;
                    }
                    log.info("【" + config.getEn_name() + "】,加载成功\n");
                } else if (CONFIG_TYPE.TABLE.type.equals(config.getType())) {
                    String sRet = loadTableList(config);
                    if (sRet == null) {
                        log.info("【" + config.getEn_name() + "】,加载失败\n");
                        return false;
                    }
                    log.info("【" + config.getEn_name() + "】,加载成功\n");
                } else if (CONFIG_TYPE.MAP.type.equals(config.getType())) {
                    String sRet = loadTableMap(config);
                    if (sRet == null) {
                        log.info("【" + config.getEn_name() + "】,加载失败\n");
                        return false;
                    }
                    log.info("【" + config.getEn_name() + "】,加载成功\n");
                }
                if (!str.equals("")) {
                    str += "\n" + JSON.toJSONString(config, SerializerFeature.WriteMapNullValue);
                } else {
                    str += JSON.toJSONString(config, SerializerFeature.WriteMapNullValue);
                }
            }
            zkClientApi.setData(zkConfigHome, str);

            log.info("基础配置上载完成");
        } else {    // 单个上载
            log.info("基础配置开始上载");
            TpConfigCenter findConfigOne = null;
            for (TpConfigCenter config : configDbList) {
                if (config.getEn_name().equals(paramName)) {
                    //log.info("【" + config.getEn_name() + "】,开始加载");
                    if (CONFIG_TYPE.FILE.type.equals(config.getType())) {
                        String sRet = loadFile(config);
                        if (sRet == null) {
                            log.info("【" + config.getEn_name() + "】,加载失败\n");
                            return false;
                        }
                        log.info("【" + config.getEn_name() + "】,加载成功\n");
                    } else if (CONFIG_TYPE.TABLE.type.equals(config.getType())) {
                        String sRet = loadTableList(config);
                        if (sRet == null) {
                            log.info("【" + config.getEn_name() + "】,加载失败\n");
                            return false;
                        }
                        log.info("【" + config.getEn_name() + "】,加载成功\n");
                    } else if (CONFIG_TYPE.MAP.type.equals(config.getType())) {
                        String sRet = loadTableMap(config);
                        if (sRet == null) {
                            log.info("【" + config.getEn_name() + "】,加载失败\n");
                            return false;
                        }
                        log.info("【" + config.getEn_name() + "】,加载成功\n");
                    }
                    findConfigOne = config;
                    break;
                }
            }

            String str = new String();
            boolean bFlag = false;
            if (configZkList != null) {
                for (TpConfigCenter tpConfigCenter : configZkList) {
                    if (tpConfigCenter.getEn_name().equals(findConfigOne.getEn_name())) {
                        if (!str.equals("")) {
                            str += "\n" + JSON.toJSONString(findConfigOne, SerializerFeature.WriteMapNullValue);
                        } else {
                            str += JSON.toJSONString(findConfigOne, SerializerFeature.WriteMapNullValue);
                        }
                        bFlag = true;
                    } else {
                        if (!str.equals("")) {
                            str += "\n" + JSON.toJSONString(tpConfigCenter, SerializerFeature.WriteMapNullValue);
                        } else {
                            str += JSON.toJSONString(tpConfigCenter, SerializerFeature.WriteMapNullValue);
                        }
                    }
                }
            }

            if (!bFlag) {
                if (!str.equals("")) {
                    str += "\n" + JSON.toJSONString(findConfigOne, SerializerFeature.WriteMapNullValue);
                } else {
                    str += JSON.toJSONString(findConfigOne, SerializerFeature.WriteMapNullValue);
                }
            }
            zkClientApi.setData(zkConfigHome, str);
            log.info("基础配置上载完成");
        }
        return false;
    }

    public String get() {
        String str = new String();
        if (paramName.equals("all")) { //全量上载
            str = getZkALL();
        } else { // 单个查询
            str = getZkByName(paramName);
        }
        return str;
    }

    // 初始化进程注册目录
    public boolean initProcess() {
        Map<String, String> moduleMap = new HashMap<>();

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            String sqlTpModule = "select module_id,module_name,parent_module_id,module_code,module_type,order_id,startup_priority,pro_path,start_command,stop_command,max_process_num from dcoos_tp_module";
            List<TpModule> moduleList = DBUtils.queryList(TpModule.class, connTmp, sqlTpModule);
            if (moduleList == null) {
                log.info("数据库中不存在 dccos_tp_module表数据");
                return false;
            }
            for (TpModule module : moduleList) {
                if (module.getModule_type().equals("MA") ||
                        module.getModule_type().equals("MB") ||
                        module.getModule_type().equals("MC") ||
                        module.getModule_type().equals("ME") ||
                        module.getModule_type().equals("MF") ||
                        module.getModule_type().equals("TC") ||
                        module.getModule_type().equals("PC")) {
                    moduleMap.put(module.getModule_code(), String.valueOf(module.getModule_id()));
                    String path = processPath + "/" + module.getModule_code();
                    String data = module.getModule_code() + "," + getSystemDateStr();
                    if (0 == zkClientApi.iExistPath(path)) {
                        log.info("创建目录:" + path);
                        if (!zkClientApi.createPath(path, data)) {
                            log.info("createPath(" + path + "," + data + ") faile");
                        }
                    } else {
                        log.info("已经存在:" + path);
                    }
                }
            }

            for (TpModule module : moduleList) {
                String data = module.getModule_code() + "," + getSystemDateStr();
                String assistPath = zkClientApi.getProcessAssistHome() + "/" + module.getModule_code();
                if (0 == zkClientApi.iExistPath(assistPath)) {
                    log.info("创建目录:" + assistPath);
                    if (!zkClientApi.createPath(assistPath, data)) {
                        log.info("createPath(" + assistPath + "," + data + ") faile");
                    }
                } else {
                    log.info("已经存在:" + assistPath);
                }
            }

            for (TpModule module : moduleList) {
                String data = module.getModule_code() + "," + getSystemDateStr();
                String assistPath = zkClientApi.getProcessLockHome() + "/" + module.getModule_code();
                if (0 == zkClientApi.iExistPath(assistPath)) {
                    log.info("创建目录:" + assistPath);
                    if (!zkClientApi.createPath(assistPath, data)) {
                        log.info("createPath(" + assistPath + "," + data + ") faile");
                    }
                } else {
                    log.info("已经存在:" + assistPath);
                }
            }
        } finally {
            DbPool.close(connTmp);
        }


        if (!zkClientApi.setData(processPath, JSON.toJSONString(moduleMap))) {
            log.info("setData(" + processPath + "," + JSON.toJSONString(moduleMap) + ") faile");
            return false;
        }

        return true;
    }

    public boolean initTask() {
        Map<Integer, TpModule> moduleIndex = new HashMap<>();

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            String sqlTpModule = "select module_id,module_name,parent_module_id,module_code,module_type,order_id,startup_priority,pro_path,start_command,stop_command,max_process_num from dcoos_tp_module";
            List<TpModule> moduleList = DBUtils.queryList(TpModule.class, connTmp, sqlTpModule);
            if (moduleList == null) {
                log.info("数据库中不存在 dccos_tp_module表数据");
                return false;
            }

            for (TpModule module : moduleList) {
                if (module.getModule_type().equals("MA") ||
                        module.getModule_type().equals("MB") ||
                        module.getModule_type().equals("MC") ||
                        module.getModule_type().equals("TC") ||
                        module.getModule_type().equals("PC")) {
                    moduleIndex.put(module.getModule_id(), module);
                }
            }

            String sqlTpProcess = "select process_id,module_id,host_id,status,billing_line_id,perl,module_code,system_process_id,process_name,update_date,host_name from dcoos_tp_process";
            List<TpProcess> processList = DBUtils.queryList(TpProcess.class, connTmp, sqlTpProcess);
            if (processList == null) {
                log.info("数据库中不存在 dccos_tp_process表数据");
                return false;
            }

            for (TpProcess tpProcess : processList) {
                TpModule tpModule = moduleIndex.get(tpProcess.getModule_id());
                if (tpModule != null) {
                    String path = zkClientApi.getMessageTask() + "/" + tpProcess.getProcess_name();
                    if (0 == zkClientApi.iExistPath(path)) {
                        log.info("创建目录:" + path);
                        if (!zkClientApi.createPath(path, tpProcess.toString())) {
                            log.info("createPath(" + path + "," + "" + ") faile");
                        }
                    } else {
                        log.info("已经存在:" + path);
                    }
                }
            }
        } finally {
            DbPool.close(connTmp);
        }
        return true;
    }

    String getLevel() {
        String str = new String();
        for (int i = 0; i < level; i++) {
            str += "-";
        }
        return str;
    }

    public void deletePath(String path) {
        if (0 == zkClientApi.iExistPath(path)) {
            log.error("目录:" + path + "不存在返回。");
            return;
        }

        level++;
        List<String> childPath = zkClientApi.getChildPath(path);
        if (childPath != null) {
            for (String pathTmp : childPath) {
                if (path.equals("/")) {
                    deletePath(path + pathTmp);
                } else {
                    deletePath(path + "/" + pathTmp);
                }
            }
        }
        log.info("删除目录:" + getLevel() + ">" + path);
        zkClientApi.deletePath(path);
        level--;
        return;
    }

    public boolean create_lock(String path, String count) {
        Long iCount = 1L;
        if (count != null) {
            iCount = Long.parseLong(count);
        }
        //String lockPath = "/process/Lock/TicketIndb/";
        String lockPath = path;
        // 获取锁，加锁
        String lockStr = null;

        List<String> childPath = zkClientApi.getChildPath(lockPath.substring(0, lockPath.length() - 1));
        log.info("已经创建的节点:" + childPath.toString());

        for (Long j = 0L; j < iCount; j++) {
            try {
                lockStr = zkClientApi.lock(lockPath);
                log.info("lockStr:" + lockStr);
            } catch (Exception e) {
                log.info("lockPath:" + lockPath + ",lockStr:" + lockStr + ",j:" + j);
                break;
            } finally {
                //释放锁
                if (lockStr != null) {
                    zkClientApi.unLock(lockStr);
                }
            }
        }

        return true;
    }

    public boolean create_Path(String path) {
        if (0 == zkClientApi.iExistPath(path)) {
            log.error("目录:" + path + "不存在返回。");
            return false;
        }

        zkClientApi.createFullPath(path);
        log.info("创建路径成功(create_Path):" + path);
        return true;
    }

    public boolean create_Eph_Path(String path) {
        if (0 == zkClientApi.iExistPath(path)) {
            log.error("目录:" + path + "不存在返回。");
            return false;
        }

        zkClientApi.createEphemeralPath(path, "");
        log.info("创建路径成功(createEphemeralPath):" + path);
        return true;
    }

    public boolean create_Eph_Seq_Path(String path) {
        String tempPath = path.substring(0, path.lastIndexOf("/"));
        if (0 == zkClientApi.iExistPath(tempPath)) {
            log.error("目录:" + tempPath + "不存在返回。");
            return false;
        }

        zkClientApi.createEphemeralSequentialPath(path, "");
        log.info("创建路径成功(createEphemeralSequentialPath):" + path);
        return true;
    }

    public boolean clearPath(String path) {
        deletePath(path);
        return true;
    }

    public void calcAcl(String path) {
        if (0 == zkClientApi.iExistPath(path)) {
            log.error("目录:" + path + "不存在返回。");
            return;
        }

        level++;
        List<String> childPath = zkClientApi.getChildPath(path);
        if (childPath != null) {
            for (String pathTmp : childPath) {
                if (path.equals("/")) {
                    calcAcl(path + pathTmp);
                } else {
                    calcAcl(path + "/" + pathTmp);
                }
            }
        }
        log.info("设置权限目录:" + getLevel() + ">" + path);
        zkClientApi.setACL(path);
        level--;
        return;
    }

    public boolean setAcl(String path) {
        calcAcl(path);
        return true;
    }

    public void showAcl(String path) {
        if (0 == zkClientApi.iExistPath(path)) {
            log.error("目录:" + path + "不存在返回。");
            return;
        }

        level++;
        List<String> childPath = zkClientApi.getChildPath(path);
        if (childPath != null) {
            for (String pathTmp : childPath) {
                if (path.equals("/")) {
                    showAcl(path + pathTmp);
                } else {
                    showAcl(path + "/" + pathTmp);
                }
            }
        }
        System.out.print(getLevel() + ">" + path + " ,目录权限:");
        zkClientApi.getACL(path);
        level--;
        return;
    }

    public boolean aclShow(String path) {
        showAcl(path);
        return true;
    }

    public boolean showProcess() {
        String processPath = zkClientApi.getProcessPath();

        log.info("configHome:" + zkClientApi.getConfigHome());
        log.info("processPath:" + zkClientApi.getProcessPath());
        log.info("messageConfig:" + zkClientApi.getMessageConfig());
        log.info("messageProcess:" + zkClientApi.getMessageProcess());

        String data = zkClientApi.getDataByPath(processPath);
        if (data == null) {
            return false;
        }
        log.info("process:" + data);
        int i = 1;
        for (String moduleCode : zkClientApi.getChildPath(processPath)) {
            log.info(i + ":" + moduleCode);
            i++;
        }

        return true;
    }

    public boolean print(String path) {
        if (StringUtils.isEmpty(path)) {
            log.error("path:" + path + " ,打印路径不能为空");
            return false;
        }

        int iRet = zkClientApi.iExistPath(path);
        if (iRet != 1) {
            log.error("目录：" + path + "，不存在");
            return false;
        }

        String value = zkClientApi.getDataByPath(path);
        System.out.println("获取路径:" + path + ",value:" + value);

        System.out.println("获取路径:" + path + " ,下的子节点信息");
        // 获取指定路径下的所有子节点和目录内容
        List<String> children = zkClientApi.getChildPath(path);

        String arrow = ">";
        // 遍历子节点和目录内容
        for (String child : children) {
            // 拼接子节点的完整路径
            String fullPath = path + "/" + child;
            String msg = zkClientApi.getDataByPath(fullPath);
            System.out.println("-" + arrow + fullPath + " ,value:" + msg);
            //System.out.println("-" + arrow + msg);

            // 获取子节点的状态信息
           /* List<String> childPath = zkClientApi.getChildPath(fullPath);
            if (childPath != null && childPath.size() > 0) {
                for (String pathTmp : childPath) {
                    // 如果是文件，则直接打印文件路径
                    msg = zkClientApi.getDataByPath(fullPath + "/" + pathTmp);
                    System.out.println("--" + arrow + fullPath + "/" + pathTmp);
                    System.out.println("##" + arrow + msg);
                }
            }*/
        }

        return true;
    }

    public boolean printRecursion(String path) {
        if (StringUtils.isEmpty(path)) {
            log.error("path:" + path + " ,打印路径不能为空");
            return false;
        }

        int iRet = zkClientApi.iExistPath(path);
        if (iRet != 1) {
            log.error("目录：" + path + "，不存在");
            return false;
        }

        System.out.println("获取路径:" + path + " ,下的子节点信息");
        // 获取指定路径下的所有子节点和目录内容
        List<String> children = zkClientApi.getChildPath(path);


        // 遍历子节点和目录内容
        for (String child : children) {
            String arrow = ">";
            // 拼接子节点的完整路径
            String fullPath = path + "/" + child;
            String msg = zkClientApi.getDataByPath(fullPath);
            System.out.println("-" + arrow + fullPath + " ,value:" + msg);

            // 获取子节点的状态信息
            List<String> childPath = zkClientApi.getChildPath(fullPath);

            // 判断是否为目录
            if (childPath != null && childPath.size() > 0) {
                // 如果是目录，则递归调用打印函数
                //System.out.println("目录：" + fullPath);
                printRecursion(fullPath);
            } else {
                // 如果是文件，则直接打印文件路径
               /* msg = zkClientApi.getDataByPath(fullPath);
                System.out.println(arrow + " " + fullPath);
                System.out.println(arrow + " " + msg);*/
            }
        }

        return true;
    }

    public String getZkALL() {
        String str = "\n\n【" + zkConfigHome + "】\n";
        str += zkClientApi.getDataByPath(zkConfigHome);

        for (TpConfigCenter config : configDbList) {
            String zkPath = config.getPath() + "/" + config.getEn_name();
            String value = zkClientApi.getDataByPath(zkPath);

            str += "\n\n【" + config.getEn_name() + "】\n" + value;
        }
        return str;
    }

    public String getZkByName(String name) {
        String str = "\n\n【" + zkConfigHome + "】\n";
        str += zkClientApi.getDataByPath(zkConfigHome);

        for (TpConfigCenter config : configDbList) {
            if (config.getEn_name().equals(name)) {
                String zkPath = config.getPath() + "/" + config.getEn_name();
                String value = zkClientApi.getDataByPath(zkPath);

                str += "\n\n【" + config.getEn_name() + "】\n" + value;
                break;
            }
        }
        return str;
    }

    public static boolean iseExit() {
        return eExit;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamMode() {
        return paramMode;
    }

    public void setParamMode(String paramMode) {
        this.paramMode = paramMode;
    }

    public static void seteExit(boolean eExit) {
        Configration.eExit = eExit;
    }

    public String getSystemDateStr() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String dateStr = df.format(new Date(System.currentTimeMillis()));// new Date()为获取当前系统时间，也可使用当前时间戳

        return dateStr;
    }

    public enum CONFIG_TYPE {
        FILE(1, "file", "文件"),
        TABLE(2, "table", "表to List"),
        MAP(3, "map", "表to Map");

        int id;
        String type;
        String remark;

        CONFIG_TYPE(int id, String type, String remark) {
            this.id = id;
            this.type = type;
            this.remark = remark;
        }
    }
}
