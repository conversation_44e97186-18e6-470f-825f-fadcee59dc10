package com.itco.upload;

import com.itco.framework.Version;
import com.itco.upload.module.Configration;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Base64;

import javax.annotation.PreDestroy;

@Controller
@Component
@Repository
@SpringBootApplication
public class Upload {
    static Log log = LogFactory.getLog(Upload.class);
    static ConfigurableApplicationContext context = null;
    static String sParam = new String();
    static String sMode = new String(); //set get init
    static String sParam_2 = null;
    static Configration configration = new Configration();

    //@Bean
    public static boolean start() {
        log.info("start()");

        configration.setParamName(sParam);
        configration.setParamMode(sMode);
        if (!configration.init()) {
            log.info("configration.init() faile");
            Configration.seteExit(true);
            return false;
        }

        if (sMode.equals("init") && sParam.equals("process")) {
            configration.initProcess();
        } else if (sMode.equals("init") && sParam.equals("task")) {
            configration.initTask();
        } else if (sMode.equals("create_path")) {
            configration.create_Path(sParam);
        } else if (sMode.equals("create_eph_path")) {
            configration.create_Eph_Path(sParam);
        } else if (sMode.equals("create_eph_seq_path")) {
            configration.create_Eph_Seq_Path(sParam);
        } else if (sMode.equals("create_lock")) {
            configration.create_lock(sParam, sParam_2);
        } else if (sMode.equals("clear")) {
            configration.clearPath(sParam);
        } else if (sMode.equals("acl")) {
            configration.setAcl(sParam);
        } else if (sMode.equals("aclshow")) {
            configration.aclShow(sParam);
        } else if (sMode.equals("show") && sParam.equals("process")) {
            configration.showProcess();
        } else if (sMode.equals("printRecursion")) {
            configration.printRecursion(sParam);
        } else if (sMode.equals("print")) {
            configration.print(sParam);
        } else if (sMode.equals("-set") || sMode.equals("-get")) {
            if (!configration.inputParamExists()) {
                log.info("输入的参数名不存在:" + sParam);
                showHelp();
                Configration.seteExit(true);
                return false;
            }
            if (sMode.equals("-set")) {
                configration.set();
                log.info("【执行成功】");
            } else if (sMode.equals("-get")) {
                log.info("\n" + configration.get());
                log.info("【执行成功】");
            }
        }
        return true;
    }

    public static synchronized void close() {
        if (context != null) {
            int exit = SpringApplication.exit(context);
            context = null;
            System.exit(exit);
        }
    }

    public static void showHelp() {
        log.info("启动命令格式：upload.sh  [-set|-get] [参数名称]");
        log.info("全量配置上载：upload.sh -set all");
        log.info("配置文件上载：upload.sh -set ctgMq.properties");
        log.info("配置表上载：  upload.sh -set tpr_rule_filter");
        log.info("初始化基础路径：  upload.sh init process");
        log.info("展示基础路径：  upload.sh show process");
        log.info("打印目录数据：  upload.sh print [path]");
        log.info("递归打印目录数据：  upload.sh printRecursion [path]");
        log.info("清理目录：  upload.sh clear [path]");
        log.info("设置目录权限：  upload.sh acl [path]");
        log.info("加密：  upload.sh -encode ");
        log.info("加密：  upload.sh create_lock /process/Lock/TicketIndb/ 100");
    }

    public static void test() {
        Object value = null;
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            value = df.parse("2021-01-01 00:00:00");
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }
        System.out.printf("value:" + value);
    }

    public static void encode(String userAndpasswd) {
        String usernameAndPassword = userAndpasswd;

        byte digest[] = new byte[0];
        try {
            digest = MessageDigest.getInstance("SHA1").digest(usernameAndPassword.getBytes());
            Base64 base64 = new Base64();
            String encodeToString = base64.encodeToString(digest);

            System.out.println(encodeToString);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }


    @PreDestroy
    public void destroy() {
        Version.print(0);
        //Version.destroy();
    }

    public static void main(String[] args) {
        if (Version.print(args, "upload_code_2022-12-30 11:00", 0)) {
            return;
        }

        for (int i = 0; i < args.length; i++) {
            log.info("i:" + i + "," + args[i]);
        }

        if (args.length == 0) {
            sMode = "-set";
            sParam = "all";
        } else if (args.length == 2) {
            if (args[0].equals("-set") || args[0].equals("-get")) {
                sMode = args[0];
                sParam = args[1];
            } else if ((args[0].equals("init") || args[0].equals("show")) && (args[1].equals("process") || args[1].equals("task"))) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("-encode")) {
                sMode = args[0];
                sParam = args[1];
                encode(sParam);
                return;
            } else if (args[0].equals("create_lock")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("clear")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("acl")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("aclshow")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("print")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("printRecursion")) {
                sMode = args[0];
                sParam = args[1];
            } else {
                log.error("输入无法识别的参数,请参考以下程序启动命令");
                showHelp();
            }
        } else if (args.length == 3) {
            if (args[0].equals("create_lock")) {
                sMode = args[0];
                sParam = args[1];
                sParam_2 = args[2];
            } else if (args[0].equals("create_path")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("create_eph_path")) {
                sMode = args[0];
                sParam = args[1];
            } else if (args[0].equals("create_eph_seq_path")) {
                sMode = args[0];
                sParam = args[1];
            }
        } else if (args[0].equals("-help")) {
            log.error("输入无法识别的参数,请参考以下程序启动命令");
            showHelp();
            return;
        } else {
            log.error("输入无法识别的参数,请参考以下程序启动命令");
            showHelp();
            return;
        }

        context = SpringApplication.run(Upload.class, args);
        if (!Upload.start()) {
            log.error("start 运行失败");
        }
        close();
    }

}
