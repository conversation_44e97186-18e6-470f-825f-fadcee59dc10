package com.itco.dbmonitor.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggerConfiguration;
import org.springframework.boot.logging.LoggingSystem;

import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.boot.logging.LoggingSystem.ROOT_LOGGER_NAME;

/**
 * 日志级别设置服务类
 *
 * <AUTHOR>
 */
public class LoggerLevelSettingService {

    @Autowired
    private LoggingSystem loggingSystem;

    private static final Logger LOGGER = LoggerFactory.getLogger(LoggerLevelSettingService.class);

    public void setRootLoggerLevel(String level) {

        LoggerConfiguration loggerConfiguration = loggingSystem.getLoggerConfiguration(ROOT_LOGGER_NAME);

        if (loggerConfiguration == null) {
            if (LOGGER.isErrorEnabled()) {
                LOGGER.error("no loggerConfiguration with loggerName " + level);
            }
            return;
        }

        if (!supportLevels().contains(level)) {
            if (LOGGER.isErrorEnabled()) {
                LOGGER.error("current Level is not support : " + level);
            }
            return;
        }

        if (!loggerConfiguration.getEffectiveLevel().equals(LogLevel.valueOf(level))) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("setRootLoggerLevel success,old level is  '" + loggerConfiguration.getEffectiveLevel()
                        + "' , new level is '" + level + "'");
            }
            loggingSystem.setLogLevel(ROOT_LOGGER_NAME, LogLevel.valueOf(level));
        }
    }

    private List<String> supportLevels() {
        return loggingSystem.getSupportedLogLevels().stream().map(Enum::name).collect(Collectors.toList());
    }
}