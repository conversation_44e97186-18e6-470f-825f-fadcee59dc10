package com.itco.dbmonitor.utils;


import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HttpUtil {
    public static final String UTF8_CONTENT_TYPE = "application/x-www-form-urlencoded;charset=UTF-8";

    public static final int COMMON_TIME_OUT = 50000;

    public static final int READ_TIME_OUT = 30000;

    private static Logger log = LoggerFactory.getLogger( HttpUtil.class );

    /**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs 参数键值对
     * @return
     * @throws Exception
     *
     */
    public static String httpPostMethodNoReTryWithStr( String url, Map<String, String> pairs, List<Header> headers )
            throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
//            log.info( "method<httpPostMethodNoReTry> request url<" + url + "> pairs<" + pairs + "> start..." );
            request = new HttpPost( url );
            if( CollectionUtils.isNotEmpty( headers ) ) {
                for( Header header : headers ) {
                    request.addHeader( header );
                }
            }
            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for( Map.Entry<String, String> e : pairs.entrySet() ) {
                nvps.add( new BasicNameValuePair( e.getKey(), e.getValue() ) );
            }
            request.setEntity( new UrlEncodedFormEntity( nvps, "UTF-8" ) );
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( COMMON_TIME_OUT )
                    .setConnectTimeout( COMMON_TIME_OUT ).build();// 设置请求和传输超时时间
            request.setConfig( requestConfig );
            // 发送请求
            CloseableHttpResponse response = httpclient.execute( request );
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString( entity );
            statusCode = response.getStatusLine().getStatusCode();
            if( (statusCode + "").charAt( 0 ) != '2' ) { // 返回除2XX以外属于请求不成功范畴
//                log.error( "method<httpPostMethodNoReTry>   request url<" + url + "> pairs<" + pairs + "> msg<Http status="
//                        + statusCode + "> ret<" + ret + "> end..." );
                throw new Exception( "request error" );
            } else {
//                log.info( "method<httpPostMethodNoReTry>   request url<" + url + "> pairs<" + pairs + "> msg<Http status="
//                        + statusCode + "> ret<" + ret + "> end..." );
                return ret;
            }
        } catch( Exception e ) {
            log.error( "method<httpPostMethodNoReTry>  request url<" + url + "> pairs<" + pairs + "> msg<" + e.getMessage() + ">",
                    e );
            throw new Exception( e );
        } finally {
            if( null != request ) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
    }

    /**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs 参数键值对
     * @return
     *
     */
    public static String httpPostMethod( String url, Map<String, String> pairs ) {
        log.info( "method<httpPostMethod> request url<" + url + "> start..." );
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            request = new HttpPost( url );
            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for( Map.Entry<String, String> e : pairs.entrySet() ) {
                nvps.add( new BasicNameValuePair( e.getKey(), e.getValue() ) );
            }
            request.setEntity( new UrlEncodedFormEntity( nvps, "UTF-8" ) );
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( READ_TIME_OUT )
                    .setConnectTimeout( COMMON_TIME_OUT ).build();// 设置请求和传输超时时间
            request.setConfig( requestConfig );
            // 发送请求
            CloseableHttpResponse response = httpclient.execute( request );
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString( entity );
            statusCode = response.getStatusLine().getStatusCode();
            /** 处理对方服务器redirect的情况 返回302/303 再次发送请求到重定向地址 */
            if( statusCode == HttpStatus.SC_MOVED_PERMANENTLY || statusCode == HttpStatus.SC_MOVED_TEMPORARILY ) {
                String locationUrl = response.getLastHeader( "Location" ).getValue();
                log.info( "method<httpPostMethod> msg<redirect. Http Status=" + statusCode + ">" );
                if( StringUtils.isNotBlank( locationUrl ) ) {
                    log.info( "method<httpPostMethod> 重定向到：" + locationUrl );
                    ret = httpPostMethod( locationUrl, pairs ); // 用跳转后的页面重新请求。
                }
            }
            else if( (statusCode + "").charAt( 0 ) != '2' ) { // 返回除2XX以外属于请求不成功范畴
                log.error( "method<httpPostMethod> msg<Http status=" + statusCode + ">" );
                return ret;
            }
        } catch( Exception e ) {
            try {
                e.printStackTrace();
                log.error( "method<httpPostMethod> msg<wait and try conn again...." + e.getMessage() + ">", e );
                Thread.sleep( 20L );
                CloseableHttpResponse response = httpclient.execute( request );
                HttpEntity entity = response.getEntity();
                ret = EntityUtils.toString( entity );
            }
            catch( Exception e1 ) {
                e1.printStackTrace();
                log.error( "method<httpPostMethod> msg<Http request error: returnStatusCode:" + statusCode + " - " + e1.getMessage()
                                + "-" + e1.toString() + ">",
                        e1 );
            }
        } finally {
            if( null != request ) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        log.info( "method<httpPostMethod> request url<" + url + "> end..." );
        return ret;
    }



    public static int httpPostMethodNoReTry(String url, Map<String, String> pairs, String charset) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info("method<httpPostMethodNoReTry> request url<" + url + "> start...");
            System.out.println("method<httpPostMethodNoReTry> request url<" + url + "> start...");
            request = new HttpPost(url);
            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for (Map.Entry<String, String> e : pairs.entrySet()) {
                nvps.add(new BasicNameValuePair(e.getKey(), e.getValue()));
            }
            request.setEntity(new UrlEncodedFormEntity(nvps, charset));
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            request.setConfig(requestConfig);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(request);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            System.out.println(ret);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostMethodNoReTry>   request url<" + url + "> msg<Http status=" + statusCode + "> ret<" + ret + ">");
                throw new Exception("statusCode error");
            }
        } catch (Exception e) {
            log.error("method<httpPostMethodNoReTry>  request url<" + url + "> msg<" + e.getMessage() + ">", e);
            throw new Exception("request error",e);
        } finally {
            if (null != request) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
        log.info("method<httpPostMethodNoReTry> request url<" + url + "> end...");
        return statusCode;
    }

    /**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs 参数键值对
     * @return
     * @throws Exception
     *
     */
    public static int httpPostMethodNoReTry( String url, Map<String, String> pairs ) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info( "method<httpPostMethodNoReTry> request url<" + url + "> start..." );
            request = new HttpPost( url );
            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for( Map.Entry<String, String> e : pairs.entrySet() ) {
                nvps.add( new BasicNameValuePair( e.getKey(), e.getValue() ) );
            }
            request.setEntity( new UrlEncodedFormEntity( nvps, "UTF-8" ) );
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( COMMON_TIME_OUT )
                    .setConnectTimeout( COMMON_TIME_OUT ).build();// 设置请求和传输超时时间
            request.setConfig( requestConfig );
            // 发送请求
            CloseableHttpResponse response = httpclient.execute( request );
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString( entity );
            statusCode = response.getStatusLine().getStatusCode();
            if( (statusCode + "").charAt( 0 ) != '2' ) { // 返回除2XX以外属于请求不成功范畴
                log.error( "method<httpPostMethodNoReTry>   request url<" + url + "> msg<Http status=" + statusCode + "> ret<" + ret
                        + ">" );
                throw new Exception( "请求失败" );
            }
        } catch( Exception e ) {
            log.error( "method<httpPostMethodNoReTry>  request url<" + url + "> msg<" + e.getMessage() + ">", e );
            throw new Exception( e );
        } finally {
            if( null != request ) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
        log.info( "method<httpPostMethodNoReTry> request url<" + url + "> end..." );
        return statusCode;
    }

   /* *//**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs
     *            参数键值对
     * @return
     * @throws Exception
     *
     *//*
    public static int httpPostMethodNoReTry(String url, Map<String, String> pairs, String charset) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info("method<httpPostMethodNoReTry> request url<" + url + "> start...");
            request = new HttpPost(url);
            *//** 请求参数 *//*
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for (Map.Entry<String, String> e : pairs.entrySet()) {
                nvps.add(new BasicNameValuePair(e.getKey(), e.getValue()));
            }
            request.setEntity(new UrlEncodedFormEntity(nvps, charset));
            *//** 超时时间 5s 重试次数 3 *//*
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            request.setConfig(requestConfig);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(request);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostMethodNoReTry>   request url<" + url + "> msg<Http status=" + statusCode + "> ret<" + ret + ">");
                throw new BaseServiceException("请求失败");
            }
        } catch (Exception e) {
            log.error("method<httpPostMethodNoReTry>  request url<" + url + "> msg<" + e.getMessage() + ">", e);
            throw new BaseServiceException( e );
        } finally {
            if (null != request) {
                request.releaseConnection();
            }
        }
        log.info("method<httpPostMethodNoReTry> request url<" + url + "> end...");
        return statusCode;
    }*/

    /**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs 参数键值对
     * @return
     *
     */
    public static String httpPostDownloadMethod( String url, Map<String, String> pairs ) {
        log.info( "method<httpPostMethod> request url<" + url + "> start..." );
        System.out.println("method<httpPostMethod> request url<" + url + "> start...");
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            request = new HttpPost( url );
            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for( Map.Entry<String, String> e : pairs.entrySet() ) {
                nvps.add( new BasicNameValuePair( e.getKey(), e.getValue() ) );
            }
            request.setEntity( new UrlEncodedFormEntity( nvps, "UTF-8" ) );
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( COMMON_TIME_OUT )
                    .setConnectTimeout( READ_TIME_OUT ).build();// 设置请求和传输超时时间
            request.setConfig( requestConfig );
            // 发送请求
            CloseableHttpResponse response = httpclient.execute( request );
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString( entity );
            System.out.println("---------"+ret);
            statusCode = response.getStatusLine().getStatusCode();
            if( (statusCode + "").charAt( 0 ) != '2' ) { // 返回除2XX以外属于请求不成功范畴
                log.error( "method<httpPostMethod> msg<Http status=" + statusCode + ">" );
                System.out.println( "method<httpPostMethod> msg<Http status=" + statusCode + ">");
                return "";
            }
        } catch( Exception e ) {
            try {
                e.printStackTrace();
                log.error( "method<httpPostMethod> msg<wait and try conn again...." + e.getMessage() + ">", e );
                System.out.println("method<httpPostMethod> msg<wait and try conn again...." + e.getMessage() + ">");
                Thread.sleep( 20L );
                CloseableHttpResponse response = httpclient.execute( request );
                HttpEntity entity = response.getEntity();
                ret = EntityUtils.toString( entity );
            }
            catch( Exception e1 ) {
                e1.printStackTrace();
                log.error( "method<httpPostMethod> msg<Http request error: returnStatusCode:" + statusCode + " - " + e1.getMessage()
                                + "-" + e1.toString() + ">",
                        e1 );
                System.out.println("method<httpPostMethod> msg<Http request error: returnStatusCode:" + statusCode + " - " + e1.getMessage()
                        + "-" + e1.toString() + ">");
            }
        } finally {
            if( null != request ) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        log.info( "method<httpPostMethod> request url<" + url + "> end..." );
        return ret;
    }

    /**
     * 功能描述： 以get的方式发送请求
     *
     * @param url
     * @param pairs 参数键值对
     * @return
     * @throws Exception
     *
     */
    public static String httpGetMethodNoReTryWithStr( String url, Map<String, String> pairs ) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpGet request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info( "method<httpGetMethodNoReTryWithStr> request url<" + url + "> pairs<" + pairs + "> start..." );

            /** 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for( Map.Entry<String, String> e : pairs.entrySet() ) {
                nvps.add( new BasicNameValuePair( e.getKey(), e.getValue() ) );
            }
            String params = EntityUtils.toString( new UrlEncodedFormEntity( nvps, Consts.UTF_8 ) );
            request = new HttpGet( url + "?" + params );
            /** 超时时间 5s */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( READ_TIME_OUT )
                    .setConnectTimeout( COMMON_TIME_OUT ).build();// 设置请求和传输超时时间
            request.setConfig( requestConfig );
            // 发送请求
            CloseableHttpResponse response = httpclient.execute( request );
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString( entity );
            statusCode = response.getStatusLine().getStatusCode();
            if( (statusCode + "").charAt( 0 ) != '2' ) { // 返回除2XX以外属于请求不成功范畴
                log.error( "method<httpGetMethodNoReTryWithStr>   request url<" + url + "> pairs<" + pairs + "> msg<Http status="
                        + statusCode + "> ret<" + ret + "> end..." );
                throw new Exception( "request error" );
            }
            else {
                log.info( "method<httpGetMethodNoReTryWithStr>   request url<" + url + "> pairs<" + pairs + "> msg<Http status="
                        + statusCode + "> ret<" + (ret.length() > 100 ? ret.substring( 0, 100 ) : ret) + "> end..." );
                return ret;
            }
        } catch( Exception e ) {
            log.error(
                    "method<httpGetMethodNoReTryWithStr>  request url<" + url + "> pairs<" + pairs + "> msg<" + e.getMessage() + ">",
                    e );
            throw new Exception( e );
        } finally {
            if( null != request ) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
    }

    /**
     * 功能描述： 以post的方式发送请求
     *
     * @param url
     * @param pairs
     *            参数键值对
     * @return 请求成功值
     * @throws Exception 请求失败抛出错误
     *
     */
    public static int httpPostMethodReTry(String url, Map<String, String> pairs, String charset) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info("method<httpPostMethodReTry> request url<" + url + "> start...");
            request = new HttpPost(url);
            /* 请求参数 */
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            for (Map.Entry<String, String> e : pairs.entrySet()) {
                nvps.add(new BasicNameValuePair(e.getKey(), e.getValue()));
            }
            request.setEntity(new UrlEncodedFormEntity(nvps, charset));
            /* 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            request.setConfig(requestConfig);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(request);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostMethodReTry>   request url<" + url + "> msg<Http status=" + statusCode + "> ret<" + ret + ">");
                throw new Exception("请求失败");
            }
        } catch (Exception e) {
            log.error("method<httpPostMethodReTry>  request url<" + url + "> msg<" + e.getMessage() + ">", e);
            throw new Exception( e );
        } finally {
            if (null != request) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
        log.info("method<httpPostMethodReTry> request url<" + url + "> end...");
        return statusCode;
    }

    /**
     * 带有头部的请求
     * @param url
     * @param pairs
     * @param headers:请求头
     * @return
     * @throws Exception
     */
    public static String httpPostMethodWithHeaders(String url, Map<String, String> pairs,Map<String, String> headers) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            log.info("method<httpPostMethodNoReTry> request url<" + url + "> pairs<" + pairs + "> start...");
            request = new HttpPost(url);

            if (null != pairs && !pairs.isEmpty()) {
                /** 请求参数 */
                List<NameValuePair> nvps = new ArrayList<NameValuePair>();
                for (Map.Entry<String, String> e : pairs.entrySet()) {
                    nvps.add(new BasicNameValuePair(e.getKey(), e.getValue()));
                }
                request.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
            }

            if (null != headers && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            request.setConfig(requestConfig);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(request);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostMethodNoReTry>   request url<" + url + "> pairs<" + pairs + "> msg<Http status=" + statusCode + "> ret<"
                        + ret + "> end...");
                throw new Exception( "statusCode error" );
            } else {
                log.info("method<httpPostMethodNoReTry>   request url<" + url + "> pairs<" + pairs + "> msg<Http status=" + statusCode + "> ret<"
                        + ret + "> end...");
                return ret;
            }
        } catch (Exception e) {
            log.error("method<httpPostMethodNoReTry>  request url<" + url + "> pairs<" + pairs + "> msg<" + e.getMessage() + ">", e);
            throw new Exception(e);
        } finally {
            if (null != request) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
    }

    /**
     * 带有头部的请求
     * @param url
     * @param pairs
     * @param headers:请求头
     * @return
     * @throws Exception
     */
    public static String httpPostMethodWithHeadersRetryNoLog(String url, Map<String, String> pairs,Map<String, String> headers) throws Exception {
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost request = null;
        try {
            httpclient = HttpClients.createDefault();
            request = new HttpPost(url);

            if (null != pairs && !pairs.isEmpty()) {
                /** 请求参数 */
                List<NameValuePair> nvps = new ArrayList<NameValuePair>();
                for (Map.Entry<String, String> e : pairs.entrySet()) {
                    nvps.add(new BasicNameValuePair(e.getKey(), e.getValue()));
                }
                request.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
            }

            if (null != headers && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            request.setConfig(requestConfig);
            // 发送请求
            CloseableHttpResponse response = httpclient.execute(request);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                throw new Exception( "statusCode error" );
            } else {
                return ret;
            }
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            if (null != request) {
                request.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
    }

    /**
     * post json 数据
     * @param url
     * @param data
     * @return
     */
    public static String httpPostJsonStr(String url, JSONObject data){
        log.info("op<httpPostJsonStr> request url <"+url+"> start....");
        String result = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost httpPost = null;
        try{
            httpclient = HttpClients.createDefault();
            httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(data.toString(),"utf-8");//解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            /** 超时时间 5s 重试次数 3 */
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(READ_TIME_OUT).build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            HttpResponse response = httpclient.execute(httpPost);
            HttpEntity reponseEntity = response.getEntity();
            result = EntityUtils.toString(reponseEntity);
            statusCode = response.getStatusLine().getStatusCode();
            //System.out.println(statusCode);
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostMethod> msg<Http status=" + statusCode + ">");
                return result;
            }
        }catch (Exception e){
            log.error("op<httpPostJsonStr> msg<Http request error: returnStatusCode:" + statusCode + " - " + e.getMessage() + "-" + e.toString() + ">", e);
            e.printStackTrace();
        }finally {
            if (null != httpPost){
                httpPost.releaseConnection();
            }
            if (null != httpclient) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        log.info("op<httpPostJsonStr> request url<" + url + "> end...");
        return result;

    }
    
    private static final int RETRY_TIMES = 3; //重试次数

    public static boolean httpPostWithTryV_1_0(String url, Map<String ,String> requestData){
        boolean result = false;
        int retry = 0;
        int ret = 0;
        try{
            ret = HttpUtil.httpPostMethodNoReTry(url,requestData);
            if (200 == ret){
                result = true;
            }
        }catch (Exception e){
            log.error("op<httpPostWithTryV_1_0> url<{}> error<{}>", url, e.getMessage());
        }
        while (!result  && retry < RETRY_TIMES){
            retry ++;
            try{
                ret = HttpUtil.httpPostMethodNoReTry(url,requestData);
                if (200 == ret){
                    result = true;
                }
            }catch (Exception e){
                log.error("op<httpPostWithTryV_1_0> url<{}> retry<{}> error<{}>", url, retry, e.getMessage());
            }
        }
        return result;
    }

    public static boolean httpPostWithTryV_2_0(String url, Map<String ,String> requestData){
        boolean result = false;
        int retry = 0;
        String ret = null;
        try{
            ret = HttpUtil.httpPostMethodWithHeaders(url,requestData,null);
            if (StringUtils.isNotBlank(ret) && ret.equalsIgnoreCase("success")){
                result = true;
            }
        }catch (Exception e){
            log.error("op<httpPostWithTryV_2_0> url<{}> error<{}>", url, e.getMessage());
        }
        while (!result  && retry < RETRY_TIMES){
            retry ++;
            try{
                ret = HttpUtil.httpPostMethodWithHeaders(url,requestData,null);
                if (StringUtils.isNotBlank(ret) && ret.equalsIgnoreCase("success")){
                    result = true;
                }
            }catch (Exception e){
                log.error("op<httpPostWithTryV_2_0> url<{}> retry<{}> error<{}>", url, retry, e.getMessage());
            }
        }
        return result;
    }


    public static String httpPostJsonWithNoRetryWithHeader(String url, String jsonStr,Map<String, String> headers) throws Exception{
        String ret = "";
        int statusCode = 0;
        CloseableHttpClient httpclient = null;
        HttpPost httpPost = null;
        try{
            httpclient = HttpClients.createDefault();
            log.info("method<httpPostJsonWithNoRetry> request url<" + url + "> start...");
            httpPost = new HttpPost(url);

            if (null != headers && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.setHeader(entry.getKey(),entry.getValue());
                }
            }

            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity stringEntity = new StringEntity(jsonStr,"UTF-8");
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(COMMON_TIME_OUT).setConnectTimeout(COMMON_TIME_OUT).build();// 设置请求和传输超时时间
            httpPost.setConfig(requestConfig);
            CloseableHttpResponse response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            ret = EntityUtils.toString(entity);
            statusCode = response.getStatusLine().getStatusCode();
            if ((statusCode + "").charAt(0) != '2') { // 返回除2XX以外属于请求不成功范畴
                log.error("method<httpPostJsonWithNoRetry> request url<" + url + "> msg<Http status=" + statusCode + "> ret<"
                        + ret + "> end...");
                throw new Exception("statusCode error");
            } else {
                log.info("method<httpPostJsonWithNoRetry> request url<" + url  + "> msg<Http status=" + statusCode + "> ret<"
                        + ret + "> end...");
                return ret;
            }
        } catch (Exception e) {
            log.error("method<httpPostJsonWithNoRetry> request url<" + url + "> msg<" + e.getMessage() + "> end...", e);
            throw new Exception("request error",e);
        } finally {
            if (null != httpPost) {
                httpPost.releaseConnection();
            }
            if (null != httpclient) {
                httpclient.close();
            }
        }
    }
}