package com.itco.dbmonitor.utils.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;


/**
 * 短信任务表
 */
@Data
@Accessors(chain = true)
@Component
public class SmsTask {

    private Integer taskId;
    private String templateId;
    private String templateValue;
    private String userPhone;
    private String sendStatus;
    private String sendTime;
    private String sendResult;
    private String insertTime;
    private String createBy;

}
