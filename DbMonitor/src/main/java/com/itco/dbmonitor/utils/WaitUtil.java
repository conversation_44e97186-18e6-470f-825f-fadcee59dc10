package com.itco.dbmonitor.utils;

public class WaitUtil {


    /**
     * 睡眠等待N秒
     *
     * @param seconds 秒数
     */
    public static void waitForSeconds(int seconds) {

        try {

            Thread.sleep(seconds * 1000);
        } catch (InterruptedException ex) {

            System.out.println("This is waitForSeconds");
        }
    }

    /**
     * 睡眠等待N毫秒
     *
     * @param milliseconds 毫秒数
     */
    public static void waitForMillis(long milliseconds) {

        try {

            Thread.sleep(milliseconds);
        } catch (InterruptedException ex) {

            System.out.println("This is waitForMillis");
        }
    }

    /**
     * 睡眠等待N天
     *
     * @param days 天数
     */
    public static void waitForDays(long days) {

        try {

            Thread.sleep(days * 1000 * 60 * 60 * 24);
        } catch (InterruptedException ex) {

            System.out.println("This is waitForMillis");
        }
    }
}
