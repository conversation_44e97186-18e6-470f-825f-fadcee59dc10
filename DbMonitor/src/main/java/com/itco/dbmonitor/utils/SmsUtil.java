package com.itco.dbmonitor.utils;

import com.alibaba.fastjson.JSONObject;
import com.itco.dbmonitor.entity.TemplateInfo;
import com.itco.dbmonitor.entity.TpSmsLog;
import com.itco.dbmonitor.utils.domain.ResponseEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.Header;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class SmsUtil {
    static Log log = LogFactory.getLog(SmsUtil.class);

    public static void setSmsMap(Map<String, TemplateInfo> smsMap) {
        SmsUtil.smsMap = smsMap;
    }

    static Map<String, TemplateInfo> smsMap;

    static List<TpSmsLog> logList = new ArrayList<>();

    public static List<TpSmsLog> getLogList() {
        return logList;
    }

    /**
     * 发送短信通用接口
     *
     * @param templateId     短信模版id
     * @param templateValues 短信模板变量值（多个变量值之间用&分隔）
     * @param phones         短信接收手机号（多个手机号之间用&分隔）
     * @return短信发送状态
     */
    public static Object sendSms(String templateId, String templateValues, String phones) {
        logList.clear();
        StringBuilder returnMsg = new StringBuilder();
        Map<String, Object> map = new HashMap<>();
        map.put("smsTemplateId", templateId);
        HashMap<String, String> data = new HashMap<>();
        TemplateInfo templateInfo = smsMap.get(templateId);
        String[] keys = templateInfo.getSmsTemplateKey().split("&");
        String[] values = templateValues.split("&");
        // 确保模版的键和传入值的数量一致
        if (keys.length == values.length) {
            for (int i = 0; i < keys.length; i++) {
                data.put(keys[i], values[i]);
            }
        } else {
            return "传入的模版变量值与该模板变量名数量不一致，请检查传入的templateValues！";
        }
        map.put("data", data);
        map.put("appKey", "1073797193");
        map.put("appSecret", "146c873a23046b866a0dbeb174b36ab8");
        for (String phone : phones.split("&")) {
            map.put("mobile", phone);
            String sendResult = sendSmv(map);
            //String sendResult = "发送成功";
            if (!sendResult.contains("发送成功")) {
                returnMsg.append("手机号：" + phone + "短信发送失败，" + sendResult).append(";");
            } else {
                returnMsg.append("手机号：" + phone + "短信" + sendResult).append(";");
            }
            addSendLogByEntity(templateInfo, templateValues, phone, sendResult);
        }
        log.info(returnMsg.toString());
        return returnMsg;
    }

    /**
     * @return 返回短信发送结果
     */
    private static String sendSmv(Map<String, Object> map) {
        String url = "https://ytx.21cn.com/sendApi/send/smv";
        ArrayList<Header> headers = new ArrayList<>();
        Map<String, String> requestData = new HashMap<>();
        requestData.put("timeStamp", String.valueOf(System.currentTimeMillis()));
        String accessToken = "5C7A61F440B42F46950BAA7485AC071E";
        requestData.put("accessToken", accessToken);
        requestData.put("type", "1");
        try {
            if (map.containsKey("mobile")) {
                requestData.put("mobile", MapUtils.getString(map, "mobile"));
            }
            String appKey = "";
            String appSecret = "";
            if (map.containsKey("appKey")) {
                appKey = MapUtils.getString(map, "appKey");
            }
            if (map.containsKey("appSecret")) {
                appSecret = MapUtils.getString(map, "appSecret");
            }
            if (map.containsKey("smsTemplateId")) {
                requestData.put("smsTemplateId", MapUtils.getString(map, "smsTemplateId"));
            }
            requestData.put("requestId", System.currentTimeMillis() + "");
            if (map.containsKey("data")) {
                requestData.put("data", JSONObject.toJSONString(MapUtils.getObject(map, "data")));
            }
            String version = "2.0v";
            String format = "json";
            String clientType = "10001";
            // log.info(requestData.toString());
            RequestParasUtil.setParas(requestData, appKey, appSecret, version, format, clientType);
            // log.info(requestData.toString());
            String response = HttpUtil.httpPostMethodNoReTryWithStr(url, requestData, headers);
//            String response = "{\"code\":10000,\"msg\":\"send_ok\",\"requestId\":\"1703473485562\",\"timeStamp\":\"1703473488781\",\"appKey\":\"1073797193\",\"recordId\":\"i107314Psi3eIHXk\"}";
            log.info("response:" + response);
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer responseCode = jsonObject.getInteger("code"); //返回的状态码
            return ResponseEnum.getDescriptionByCode(responseCode);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("发送短信接口报错" + e.toString());
            String errorMessage = e.toString();
            if (errorMessage.length() > 100) {
                errorMessage = errorMessage.substring(0, 100) + "...";
            }
            return "发送短信接口报错：" + errorMessage;
        }
    }


    /**
     * 记录短信发送记录
     */
    private static void addSendLog(TemplateInfo templateInfo, String templateValues, String phone, String sendResult) {
        Map<String, Object> logMap = new HashMap<>();
        logMap.put("sendPhone", phone);
        String templateText = templateInfo.getSmsTemplateText();
        String[] keys = templateInfo.getSmsTemplateKey().split("&");
        String[] values = templateValues.split("&");
        for (int i = 0; i < keys.length; i++) {
            templateText = templateText.replace("@_" + keys[i] + "_@", values[i]);
        }
        logMap.put("sendContent", "【统一计费帐务系统】" + templateText);
        if (!"发送成功".equals(sendResult)) {
            logMap.put("sendStatus", "发送失败");
            logMap.put("sendFailReason", sendResult);
            logMap.put("retryCount", 0);
        } else {
            logMap.put("sendStatus", sendResult);
        }
        logMap.put("templateId", templateInfo.getSmsTemplateId());
        logMap.put("templateValues", templateValues);
        //smsMapper.addSmsLog(logMap);
    }

    /**
     * 记录短信发送记录
     */
    private static void addSendLogByEntity(TemplateInfo templateInfo, String templateValues, String phone, String sendResult) {
        TpSmsLog tpSmsLog = new TpSmsLog();
        tpSmsLog.setSmsSendPhone(phone);
        tpSmsLog.setSmsSendTime(new Timestamp(System.currentTimeMillis()));

        String templateText = templateInfo.getSmsTemplateText();
        String[] keys = templateInfo.getSmsTemplateKey().split("&");
        String[] values = templateValues.split("&");
        for (int i = 0; i < keys.length; i++) {
            templateText = templateText.replace("@_" + keys[i] + "_@", values[i]);
        }
        tpSmsLog.setSmsSendContent("【统一计费帐务系统】" + templateText);
        if (!"发送成功".equals(sendResult)) {
            tpSmsLog.setSmsSendStatus("发送失败");
            tpSmsLog.setSmsSendFailReason(sendResult);
            tpSmsLog.setSmsRetryCount(0);
        } else {
            tpSmsLog.setSmsSendStatus(sendResult);
        }
        tpSmsLog.setSmsTemplateId(templateInfo.getSmsTemplateId());
        tpSmsLog.setSmsTemplateValue(templateValues);
        logList.add(tpSmsLog);
    }
}
