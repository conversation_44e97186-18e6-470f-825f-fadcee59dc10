package com.itco.dbmonitor.utils.domain;

/**
 * 短信发送结果返回码
 */
public enum ResponseEnum {

    SEND_OK(10000, "send_ok", "发送成功"),
    PARAM_ILLEGAL(-1001, "param_illegal", "参数校验错误"),
    PARAM_LACK(-1002, "param_lack", "缺少参数"),
    INVALID_REQUEST(-1003, "invalid_request", "无效请求"),
    ERROR_SIGN(-1004, "error_sign", "签名错误"),
    ERROR_PERMISSION(-1005, "error_permission", "无接口权限"),
    INVOKED_FREQUENT(-1006, "invoked_frequent", "调用过于频繁"),
    SYSTEM_EXCEPTION(-1007, "system_exception", "系统错误"),
    IP_ILLEGAL(-1008, "ip_illegal", "调用方ip不合法"),
    TOKEN_ILLEGAL(-1009, "token_illegal", "调用方token不合法"),
    SMS_TEMPLATE_ILLEGAL(-1010, "sms_template_illegal", "调用方使用的短信模板不存在或不合法"),
    MAIL_TEMPLATE_ILLEGAL(-1011, "mail_template_illegal", "调用方使用的邮件模板不存在或不合法"),
    MOBILE_ILLEGAL(-1012, "mobile_illegal", "手机号不合法"),
    EMAIL_ILLEGAL(-1013, "email_illegal", "邮箱不合法"),
    CONTENT_ILLEGAL(-1014, "content_illegal", "内容未通过反垃圾扫描"),
    SENDER_PHONE_ILLEGAL(-1015, "sender_phone_illegal", "下发端口不合法"),
    APP_KEY_ILLEGAL(-1016, "appKey_illegal", "appKey不合法"),
    CLIENT_TYPE_ILLEGAL(-1017, "clientType_illegal", "调用方客户类型不合法"),
    EXT_ILLEGAL(-1018, "ext_illegal", "同一个模板不能使用不同的扩展码"),
    EXT_OCCUPIED(-1019, "ext_occupied", "扩展码已经被占用"),
    EXT_FAIL(-1020, "ext_fail", "扩展号分配失败"),
    CHANNEL_ILLEGAL(-1021, "channel_illegal", "通道配置有误"),
    MOBILE_OVER_SPEED(-1301, "mobile_over_speed", "模板号码超速");

    private final int code;
    private final String name;
    private final String description;

    ResponseEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(int code) {
        for (ResponseEnum status : ResponseEnum.values()) {
            if (status.getCode() == code) {
                return status.getDescription();
            }
        }
        return "未知错误";
    }
}