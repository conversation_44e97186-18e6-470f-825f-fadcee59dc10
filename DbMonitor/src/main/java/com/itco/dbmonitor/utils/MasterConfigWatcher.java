package com.itco.dbmonitor.utils;

import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dbmonitor.thread.PlatFormMonitor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;


public class MasterConfigWatcher implements Watcher {
    static Log log = LogFactory.getLog(MasterConfigWatcher.class);
    static ZkClientApi zkClientApi = null;
    static String druidPath = "/config/file/druid.properties";
    String databaseName = "postgres";
    static String currentKey;

    static PlatFormMonitor platFormMonitor = null;


    public static void setZkClientApi(ZkClientApi zkClientApi) {
        MasterConfigWatcher.zkClientApi = zkClientApi;
    }

    public static void setPostgresMonitor(PlatFormMonitor platFormMonitor) {
        MasterConfigWatcher.platFormMonitor = platFormMonitor;
    }

    public static boolean startMonitorMaster() {
        //log.info("重新监听:" + druidPath);
        // 监听自己进程目录，获取消息
        zkClientApi.getData(druidPath, true);
        return true;
    }

    boolean loadDruidPostgres() {
        platFormMonitor.syncDruidProperties();
        return true;
    }

    @Override
    public synchronized void process(WatchedEvent event) {
        Event.KeeperState keeperState = event.getState();
        Event.EventType eventType = event.getType();
        String path = event.getPath();

        if (Event.KeeperState.SyncConnected == keeperState) {
            // 如果当前状态已经连接上了 SyncConnected：连接，AuthFailed：认证失败,Expired:失效过期,
            // ConnectedReadOnly:连接只读,Disconnected:连接失败
            if (Event.EventType.None == eventType) {
                // 如果建立建立成功,让后程序往下走
                log.info("ClientWatcher" + "，zk 建立连接成功!");
            } else if (Event.EventType.NodeCreated == eventType) {
                log.info("ClientWatcher" + "，事件通知,新增node节点" + path);
            } else if (Event.EventType.NodeDataChanged == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被修改....");
                if (!loadDruidPostgres()) {
                    log.error("loadDruidPostgres() failure");
                }

                log.info("重新监听:" + path);
                startMonitorMaster();
            } else if (Event.EventType.NodeDeleted == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "被删除....");
            } else if (Event.EventType.NodeChildrenChanged == eventType) {
                log.info("ClientWatcher" + "，事件通知,当前node节点" + path + "，子节点被修改....");
            }
        }
    }

    public static void setCurrentKey(String currentKey) {
        MasterConfigWatcher.currentKey = currentKey;
    }

    public static String getCurrentKey() {
        return currentKey;
    }
}