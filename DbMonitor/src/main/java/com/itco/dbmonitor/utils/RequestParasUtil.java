package com.itco.dbmonitor.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class RequestParasUtil {
    private static Logger log = LoggerFactory.getLogger(RequestParasUtil.class);

    public RequestParasUtil() {
    }

    /**
     * 参数解密，并按照key=value&key=value格式将数据拆解
     * @param xxTeaText
     * @param appSecret
     * @return
     * @throws Exception
     */
    public static Map<String, String> parseXXTeaParasData(String xxTeaText, String appSecret) throws Exception {
        Map<String,String> requestDataMap = null;
        try {
            String plainParasText = XXTea.decrypt(xxTeaText, "UTF-8", StringUtil.toHex(appSecret.getBytes()));
            requestDataMap = splitParameters(plainParasText);
            return requestDataMap;
        } catch (Exception var4) {
            log.error(var4.getMessage());
            throw new Exception("XXTea-decypt-error");
        }
    }

    private static Map<String, String> splitParameters(String paraStr) {
        Map<String, String> parameters = new HashMap<>();
        if (StringUtils.isNotBlank(paraStr)) {
            String[] array = paraStr.trim().split("&");
            String[] var6 = array;
            int var5 = array.length;

            for(int var4 = 0; var4 < var5; ++var4) {
                String temp = var6[var4];
                if (StringUtils.isNotBlank(temp)) {
                    temp = temp.trim();
                    int index = temp.indexOf("=");
                    if (index > 0) {
                        String key = temp.substring(0, index);
                        String value = temp.substring(index + 1);
                        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                            parameters.put(key.trim(), value.trim());
                        }
                    }
                }
            }
        }

        return parameters;
    }

    /**
     * 加密，获取签字
     * @param plainText
     * @param appSecret
     * @return
     */
    public static String generateHmacSignature(String plainText, String appSecret) {
        String signature = null;
        try {
            long begin = System.currentTimeMillis();
            //log.info("op<generateHmacSignature> plainText<{}>",plainText);
            signature = HMACSHA1.getSignature(plainText, appSecret);
            //log.info("op<generateHmacSignature> cost<{}>", (System.currentTimeMillis() - begin));
        } catch (Exception var5) {
            var5.printStackTrace();
            log.error(var5.getMessage());
        }
        return signature;
    }


    /**
     * 平台通用接口封装方式
     * @param requestData
     * @param appKey
     * @param appSecret
     * @param version
     * @param format
     * @param clientType
     */
    public static void setParas(Map<String, String> requestData, String appKey, String appSecret, String version, String format, String clientType) {
        if (requestData == null) {
            log.error("参数错误");
        } else if (appKey != null && version != null && clientType != null) {
            format = format == null ? "json" : format;
            String cipherParas = generateXXTeaParasData(requestData, appSecret);
            requestData.clear();
            requestData.put("appKey", appKey);
            requestData.put("version", version);
            requestData.put("clientType", clientType);
            requestData.put("format", format);
            requestData.put("paras", cipherParas);
            String plainSig = appKey + clientType + format + version + cipherParas;
            requestData.put("sign", generateHmacSignature(plainSig, appSecret));
        } else {
            log.error("appId or appSecret can not null");
        }
    }

    /**
     * 平台回调第三方接口通用参数封装
     * @param requestData
     * @param appKey
     * @param appSecret
     */
    public static void setParas(Map<String, String> requestData, String appKey, String appSecret) {
        if (requestData == null) {
            log.error("参数错误");
        } else if (null != appKey && null != appSecret) {
            String cipherParas = generateXXTeaParasData(requestData, appSecret);
            requestData.clear();
            requestData.put("appKey", appKey);
            requestData.put("paras", cipherParas);
            String plainSig = appKey + cipherParas;
            requestData.put("sign", generateHmacSignature(plainSig, appSecret));
        } else {
            log.error("appKey or appSecret can not null");
        }
    }

    private static String generateXXTeaParasData(Map<String, String> requestData, String appSecret) {
        try {
            StringBuffer xxTeaText = new StringBuffer();
            if (requestData != null && requestData.size() >= 1) {
                String name;
                String value;
                for(Iterator var4 = requestData.entrySet().iterator(); var4.hasNext(); xxTeaText.append(name + "=" + value + "&")) {
                    Map.Entry<String, String> entry = (Map.Entry)var4.next();
                    name = (String)entry.getKey();
                    value = "";
                    if ("returnURL".equals(name)) {
                        value = URLEncoder.encode((String)entry.getValue(), "utf-8");
                    } else {
                        value = (String)entry.getValue();
                    }
                }

                if (!requestData.containsKey("timeStamp")) {
                    xxTeaText.append("timeStamp=" + getTimeStamp());
                }

                return XXTea.encrypt(xxTeaText.toString(), "UTF-8", StringUtil.toHex(appSecret.getBytes()));
            } else {
                return null;
            }
        } catch (Exception var7) {
            var7.printStackTrace();
            log.error(var7.getMessage());
            return null;
        }
    }

    public static String getTimeStamp() {
        return String.valueOf(System.currentTimeMillis());
    }


    public static void main(String[] args) throws Exception {
        String Text = "ext=00000001&requestId=000001&timeStamp=1615276375057" +
                "&type=2&email=<EMAIL>&accessToken=12312&emailTemplateId=00cz2rO2m4jg&mobile=18028060383" +
                "&";
        byte key[] = "c8cdec93db6e3031055bc345a3c29c4839d554f5".getBytes();
        String cipherText = XXTea.encrypt(Text,"UTF-8", ByteFormat.toHex(key));
        System.out.println("paras:"+cipherText);
        String plainText = "tvWdr6e1c"+"10001"+"json"+"2.0v"+cipherText;
        String appSign = RequestParasUtil.generateHmacSignature(plainText, "c8cdec93db6e3031055bc345a3c29c4839d554f5");
        System.out.println("sign:"+appSign);
        Map<String, String> parasMap =  RequestParasUtil.parseXXTeaParasData("7E23B8474D08CBAEB023E29F0B603E8F0A9318981C239D2EDB89ED377D97FFD7187726B9B8A1DA248701C19858A4C19D","f00880bd8acfde120972704a0b4b5915a81b1996");
        System.out.println(parasMap);
    }
}
