package com.itco.dbmonitor;

import ch.qos.logback.classic.Level;
import com.itco.dbmonitor.log.LogLevelSpring;
import com.itco.dbmonitor.thread.PlatFormMonitor;
import com.itco.dbmonitor.thread.PlatFormTool;
import com.itco.dbmonitor.utils.MasterConfigWatcher;
import com.itco.framework.Version;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.PreDestroy;

@SpringBootApplication
public class DbMonitorApplication {
    static Log log = LogFactory.getLog(DbMonitorApplication.class);
    static ConfigurableApplicationContext context;

    static String stModuleCode = "DbMonitor";
    static String stBillingLineId = "11";
    static String stMode = null;
    static boolean isCluster = false;
    static String cluster = null;
    static int debug = 0;

    public static boolean startNormal() {
        if ("upload".equals(stMode)) {
            PlatFormTool platFormTool = new PlatFormTool();
            platFormTool.setModuleCode(stModuleCode);
            platFormTool.setBillingLineId(stBillingLineId);
            if (!platFormTool.init()) {
                log.error("platFormTool.init() failure");
                return false;
            }
            if (!platFormTool.upload()) {
                log.error("platFormTool.upload() failure");
                return false;
            }
            if (!platFormTool.printMaster()) {
                log.error("platFormTool.printMaster() failure");
                return false;
            }
        } else if ("update".equals(stMode)) {
            PlatFormTool platFormTool = new PlatFormTool();
            platFormTool.setModuleCode(stModuleCode);
            platFormTool.setBillingLineId(stBillingLineId);
            if (!platFormTool.init()) {
                log.error("platFormTool.init() failure");
                return false;
            }
            if (!platFormTool.update()) {
                log.error("platFormTool.update() failure");
                return false;
            }
            if (!platFormTool.printMaster()) {
                log.error("platFormTool.printMaster() failure");
                return false;
            }
        } else if ("clear".equals(stMode)) {
            PlatFormTool platFormTool = new PlatFormTool();
            platFormTool.setModuleCode(stModuleCode);
            platFormTool.setBillingLineId(stBillingLineId);
            if (!platFormTool.init()) {
                log.error("platFormTool.init() failure");
                return false;
            }
            if (!platFormTool.clear()) {
                log.error("platFormTool.clear() failure");
                return false;
            }
            if (!platFormTool.printMaster()) {
                log.error("platFormTool.printMaster() failure");
                return false;
            }
        } else if ("printmaster".equals(stMode)) {
            PlatFormTool platFormTool = new PlatFormTool();
            platFormTool.setModuleCode(stModuleCode);
            platFormTool.setBillingLineId(stBillingLineId);
            if (!platFormTool.init()) {
                log.error("platFormTool.init() failure");
                return false;
            }
            if (!platFormTool.printMaster()) {
                log.error("platFormTool.printMaster() failure");
                return false;
            }
        } else {
            PlatFormMonitor platFormMonitor = new PlatFormMonitor();
            platFormMonitor.setModuleCode(stModuleCode);
            platFormMonitor.setBillingLineId(stBillingLineId);
            platFormMonitor.setDebug(debug);
            if (!platFormMonitor.init()) {
                log.info("platFormMonitor.init() failure");
                return true;
            }

            MasterConfigWatcher.setPostgresMonitor(platFormMonitor);

            Thread thread = new Thread(platFormMonitor);
            thread.start();
        }

        return true;
    }

    // 退出之前需要关闭容器
    public static synchronized void close() {
        if (context != null) {
            int exit = SpringApplication.exit(context);
            context = null;
            System.exit(exit);
        }

        log.info("close(context) ");
    }

    public static void help() {
        log.info("健康检测程序启动: DbMonitor.sh start 11");
        log.info("基础环境初始化: DbMonitor.sh upload");
        log.info("基础环境更新: DbMonitor.sh update");
        log.info("基础环境清理: DbMonitor.sh clear");
        log.info("基础环境清理: DbMonitor.sh print master");
    }

    @PreDestroy
    public void destroy() {
        Version.print();
        Version.destroy();
    }


    /*
     * DbMonitor_code_2024-01-06 11:00
     *
     */
    public static void main(String[] args) {
        if (Version.print(args, "DbMonitor_code_2024-01-06 11:00")) {
            return;
        }

        for (String para : args) {
            if ("-f".equals(para.substring(0, 2))) {    //生产线
                stMode = "single";
                stBillingLineId = para.substring(2);
            } else if ("-s".equals(para.substring(0, 2))) {   //单点 or 集群
                stMode = "cluster";
                isCluster = true;
                if (para.length() > 2) {
                    cluster = para.substring(2);
                }
            } else if ("upload".equals(para)) {
                stMode = para;
            } else if ("update".equals(para)) {
                stMode = para;
            } else if ("clear".equals(para)) {
                stMode = para;
            } else if ("printmaster".equals(para)) {
                stMode = para;
            } else if ("-d".equals(para)) {
                debug = 1;
                if (para.length() > 2) {
                    debug = Integer.parseInt(para.substring(2));
                }
            }
        }

        String moduleCode = System.getenv("MODULE_NAME");
        String billingLineId = System.getenv("BILLING_LINE_ID");
        log.info("billingLineId:" + billingLineId);

        if (moduleCode != null && !moduleCode.equals("")) {
            stModuleCode = moduleCode;
        }
        if (billingLineId != null && !billingLineId.equals("")) {
            stBillingLineId = billingLineId;
        }

        if (stMode == null) {
            help();
            return;
        }

        log.info("stBillingLineId:" + stBillingLineId);
        context = SpringApplication.run(DbMonitorApplication.class, args);
        log.info("module_name:" + stModuleCode);
        log.info("stMode:" + stMode);
        if (debug == 0) {
            LogLevelSpring.levelSwitch("com.itco", Level.WARN.levelStr);
        }
        if (!"single".equals(stMode) && !"cluster".equals(stMode)) {
            LogLevelSpring.levelSwitch("com.itco", Level.INFO.levelStr);
        }
        if (!DbMonitorApplication.startNormal()) {
            log.error("start " + stBillingLineId + " 运行失败");
            close();
        }
        log.info("初始化成功");
    }

}
