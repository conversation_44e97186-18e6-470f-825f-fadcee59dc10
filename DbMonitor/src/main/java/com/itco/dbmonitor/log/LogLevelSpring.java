package com.itco.dbmonitor.log;

import com.alibaba.druid.util.StringUtils;
import com.itco.entity.common.Common;
import com.itco.entity.process.MsgBody;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggerConfiguration;
import org.springframework.boot.logging.LoggingSystem;

import java.util.List;
import java.util.stream.Collectors;


public class LogLevelSpring {
    private static final Log log = LogFactory.getLog(LogLevelSpring.class);

    public static boolean levelSwitch(String name, String level) {
        // 解析level
        if (null == LogLevel.valueOf(level)) {
            System.out.println("不是合法的日志级别：" + level);
            return false;
        }

        System.out.println("修改日志级别 " + name + "," + level + "，开始");

        LoggingSystem loggingSystem = LoggingSystem.get(Thread.currentThread().getContextClassLoader());
        List<LoggerConfiguration> loggerConfigurations = loggingSystem.getLoggerConfigurations();
        for (LoggerConfiguration loggerConfiguration : loggerConfigurations) {
            if (loggerConfiguration.getName().startsWith(name)) {

                List<String> list = loggingSystem.getSupportedLogLevels().stream().map(Enum::name).collect(Collectors.toList());
                if (!list.contains(level)) {
                    log.error("current Level is not support : " + level);
                    return false;
                }
                //System.out.println(loggerConfiguration.getName());
                loggingSystem.setLogLevel(loggerConfiguration.getName(), LogLevel.valueOf(level));
            }
        }
        System.out.println("修改日志级别 " + name + "," + level + "，结束");
        return true;
    }

    public static boolean debugProcess(Common common, MsgBody.DebugBody body) {
        Boolean bRet = true;
        if (StringUtils.isNumber(body.LEVEL)) {
            common.setIDebug(Integer.parseInt(body.LEVEL));
        } else if ("ELEMENT".equals(body.LEVEL)) {
            if (common.getIElement() == 0) {
                common.setIElement(1);
            } else {
                common.setIElement(0);
            }
        } else {

            common.setSDebugLevel(body.LEVEL);
            levelSwitch("com.itco.", body.LEVEL);
        }

        log.warn(common.toString());
        return bRet;
    }
}
