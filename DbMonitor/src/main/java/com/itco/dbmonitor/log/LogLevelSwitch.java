package com.itco.dbmonitor.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import com.alibaba.druid.util.StringUtils;
import com.itco.entity.common.Common;
import com.itco.entity.process.MsgBody;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.slf4j.LoggerFactory;

import java.util.List;

public class LogLevelSwitch {
    static Log log = LogFactory.getLog(LogLevelSwitch.class);

    /*
     * @decription 日志输入切换
     * @param name: 类路径，包路径
     * @param level: 切换后的日志级别
     * @return boolean   是否成功
     * <AUTHOR>
     * @createDate 2022/7/14
     */
    public static boolean levelSwitch(String name, String level) {
        // 解析level
        Level newLevel = Level.toLevel(level);
        if (newLevel == null) {
            System.out.println("不是合法的日志级别：" + level);
            return false;
        }

        System.out.println("修改日志级别 " + name + "," + level + "，开始");
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        List<Logger> loggerList = loggerContext.getLoggerList();
        for (Logger log : loggerList) {
            if (log.getName().startsWith(name)) {

                // 设置新的level
                log.setLevel(newLevel);
            }
        }
        System.out.println("修改日志级别 " + name + "," + level + "，结束");
        return true;
    }

    public static boolean debugProcess(Common common, MsgBody.DebugBody body) {
        Boolean bRet = true;
        if (StringUtils.isNumber(body.LEVEL)) {
            common.setIDebug(Integer.parseInt(body.LEVEL));
        } else if ("ELEMENT".equals(body.LEVEL)) {
            if (common.getIElement() == 0) {
                common.setIElement(1);
            } else {
                common.setIElement(0);
            }
        } else {
            Level level = Level.toLevel(body.LEVEL, null);
            if (level == null) {
                log.error("无效的日志等级:" + body.LEVEL);
                return false;
            }

            common.setSDebugLevel(level.levelStr);
            LogLevelSwitch.levelSwitch("com.itco.", level.levelStr);
        }

        log.warn(common.toString());
        return bRet;
    }
}
