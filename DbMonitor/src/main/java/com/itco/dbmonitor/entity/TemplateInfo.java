package com.itco.dbmonitor.entity;


import lombok.Data;

import java.util.Date;
@Data
public class TemplateInfo {

    private Integer id; //模板表主键id
    private String smsTemplateName; //短信模板名
    private String smsTemplateId; //短信模板id
    private String smsTemplateKey; //短信模板中的变量名（多个之间用&隔开）
    private String smsTemplateKeyExplain; //短信模板中的变量说明
    private String smsTemplateText; //短信模板内容
    private Integer smsMaxRetryLimit; //失败重发机制：最大重试次数限制（如发送不重发，填0）
    private Integer smsRetryTime; //失败重发机制：重试间隔设定（单位：分钟）
    private String smsTemplateLevel; //模版告警级别（高 中 低）
    private Date smsEffectiveTime; //模版生效时间
    private Date smsFailureTime; //模版失效时间
    private Date smsUpdateTime; //模版修改时间
    private Date smsCreateTime; //模版创建时间
    private String smsTemplateType; //模版类型（通知类，告警类，营销类...）
    private String smsTemplateStatus; //模版状态（使用中 已作废）
    private String createBy; //模版创建人
    private String phone;//接收短信人员

    @Override
    public String toString() {
        return "TemplateInfo{" +
                "id=" + id +
                ", smsTemplateName='" + smsTemplateName + '\'' +
                ", smsTemplateId='" + smsTemplateId + '\'' +
                ", smsTemplateKey='" + smsTemplateKey + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }
}
