package com.itco.dbmonitor.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * (TpSmsLog)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-25 09:38:04
 */

@Data
public class TpSmsLog {

    private Integer smsLogId;
    //短信接收方手机号
    private String smsSendPhone;
    //发送短信内容
    private String smsSendContent;
    //发送时间
    private Object smsSendTime;
    //发送状态(发送成功，发送失败)
    private String smsSendStatus;
    //若发送失败，失败原因
    private String smsSendFailReason;
    //使用的短信模板id
    private String smsTemplateId;
    //短信模板中的变量值（多个之间;隔开）
    private String smsTemplateValue;
    //失败重发机制:重新发送的次数
    private Integer smsRetryCount;


}

