package com.itco.dbmonitor.entity;

import lombok.Data;

@Data
public class PostgresCmd {
    // 从库切换主库
    static String promote = "$PGHOME/bin/pg_ctl promote -D $PGDATA";
    // 修改从库指向主库的ip地址
    static String primary_conninfo = "primary_conninfo = 'user=replica host=xxxxxx port=18921 sslmode=disable sslcompression=0 gssencmode=disable target_session_attrs=any'";
    // 修改从库指向主库的ip地址
    static String modify_master = "sed -i 's/xxxx/yyyy/g' $PGDATA/postgresql.auto.conf";
    //  重启数据库
    static String restart_slave = "$PGHOME/bin/pg_ctl -D $PGDATA -l $PGHOME/logfile restart";
    //  重建从库
    static String recreate_slave = "$PGHOME/bin/pg_basebackup -h xxxx -D $PGDATA -p 18921 -U replica -Fp -Xs -Pv -R";

    public static String getPromote() {
        return promote;
    }

    public static String getPrimary_conninfo() {
        return primary_conninfo;
    }

    public static String getModify_master() {
        return modify_master;
    }

    public static String getRestart_slave() {
        return restart_slave;
    }

    public static String getRecreate_slave() {
        return recreate_slave;
    }
}
