package com.itco.dbmonitor.entity;

import lombok.Data;

import java.util.Date;

@Data
public class MonitorInfo {
    String status;//"ok","error"
    Long total_cnt;   //总次数
    Long success_cnt; //成功次数
    Long failure_cnt; //连续失败次数
    Long error_cnt;  //异常次数
    String failure_begin_time;//异常时间
    String update_date;//更新时间
    Date lastTriggerDate;

    public MonitorInfo() {
        status = "ok";
        total_cnt = 0L;
        success_cnt = 0L;
        failure_cnt = 0L;
        error_cnt = 0L;
        failure_begin_time = "";
        update_date = "";
        lastTriggerDate = new Date();
    }
}
