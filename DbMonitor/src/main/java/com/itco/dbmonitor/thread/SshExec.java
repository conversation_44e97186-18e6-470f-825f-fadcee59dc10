package com.itco.dbmonitor.thread;

import com.itco.component.jdbc.DecodePassword;
import com.itco.dbmonitor.database.impl.PostgresMonitor;
import com.itco.dbmonitor.entity.ComponentInfo;
import com.itco.dbmonitor.entity.PostgresCmd;
import com.itco.dbmonitor.entity.SshHostInfo;
import com.itco.dbmonitor.entity.TemplateInfo;
import com.itco.dbmonitor.utils.SmsUtil;
import com.itco.framework.Factory;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;


public class SshExec {
    static Log log = LogFactory.getLog(SshExec.class);
    static Map<String, TemplateInfo> smsMap = null;

    public static void setSmsMap(Map<String, TemplateInfo> smsMap) {
        SshExec.smsMap = smsMap;
    }

    // 登录远程主机执行命令
    public boolean sshLoginExecCmd(SshHostInfo sshHostInfo, String cmd) {
        Session session = null;
        ChannelExec channelExec = null;
        String sshPassword = DecodePassword.decryption(sshHostInfo.getSsh_publickey(), sshHostInfo.getSsh_passwd());
        String fullCmd = "source ~/.bash_profile && " + cmd;

        log.info("主机IP地址：" + sshHostInfo.getSsh_ip() + ",用户名：" + sshHostInfo.getSsh_username() + ",密码：" + sshPassword + ",端口号：" + sshHostInfo.getSsh_port());
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(sshHostInfo.getSsh_username(), sshHostInfo.getSsh_ip(), Integer.parseInt(sshHostInfo.getSsh_port()));
            session.setPassword(sshPassword);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();

            log.info("是否连接：" + session.isConnected());
            if (!session.isConnected()) {
                log.error("连接主机失败:" + sshHostInfo.toString());
                return false;
            }

            log.info("连接成功，准备执行:" + fullCmd);

            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(fullCmd);
            channelExec.connect();
            // 获取命令执行结果的输出流
            InputStream inputStream = channelExec.getInputStream();
            System.out.print("命令执行返回内容:");

            // 打印命令执行结果
            byte[] tmp = new byte[1024];
            while (true) {
                while (inputStream.available() > 0) {
                    int i = inputStream.read(tmp, 0, 1024);
                    if (i < 0) break;
                    System.out.print(new String(tmp, 0, i));
                }
                if (channelExec.isClosed()) {
                    // 当通道关闭时，你可以检查退出状态
                    if (inputStream.available() > 0) {
                        continue; // 确保输出全部读取
                    }
                    break;
                }
                // 添加一小段暂停避免程序过度消耗 CPU 资源
                try {
                    Thread.sleep(1000);
                } catch (Exception ee) {
                    ee.printStackTrace();
                }
            }

            int exitStatus = channelExec.getExitStatus();
            log.info("执行完成，结果：" + (exitStatus == 0 ? "成功" : "失败"));

            return exitStatus == 0 ? true : false;
        } catch (JSchException | IOException e) {
            if (e.getMessage().equals("Auth fail")) {
                log.error(sshHostInfo.getSsh_ip() + " " + sshHostInfo.getSsh_username() + " , 密码错误无法执行命令");
                String msg = sshHostInfo.getSsh_ip() + "&" + sshHostInfo.getSsh_username() + "&" + Factory.getSystemDateStr();
                TemplateInfo templateInfo = smsMap.get("T2024012914461685128794175");
                SmsUtil.sendSms(templateInfo.getSmsTemplateId(), msg, templateInfo.getPhone());
                PostgresMonitor.addSmsLog(SmsUtil.getLogList());
            }
            return false;
        } finally {
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
        }
    }

    // 从库升级为主库
    public boolean sshLoginSwitchSlave2Master(ComponentInfo componentInfo) {
        SshHostInfo sshHostInfo = componentInfo.getSshHostInfo();
        String cmd = PostgresCmd.getPromote();

        try {
            boolean bRet = sshLoginExecCmd(sshHostInfo, cmd);
            if (!bRet) {
                log.error("sshLoginExecCmd(" + cmd + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    // 修改指向的主库
    public boolean sshLoginModifySlave(ComponentInfo componentInfo, String oldIp, String newIp) {
        SshHostInfo sshHostInfo = componentInfo.getSshHostInfo();
        String cmd = PostgresCmd.getModify_master().replace("xxxx", oldIp).replace("yyyy", newIp);

        try {
            boolean bRet = sshLoginExecCmd(sshHostInfo, cmd);
            if (!bRet) {
                log.error("sshLoginExecCmd(" + cmd + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }

            bRet = sshLoginExecCmd(sshHostInfo, PostgresCmd.getRestart_slave());
            if (!bRet) {
                log.error("sshLoginExecCmd(" + PostgresCmd.getRestart_slave() + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    // 把所有从库指向的主库地址改成新主库。
    public boolean sshLoginModifySlave2NewMaster(Map<String, ComponentInfo> info, ComponentInfo oldMaster, ComponentInfo newMaster) {
        String oldIp = oldMaster.getSshHostInfo().getSsh_ip().trim();
        String newIp = newMaster.getSshHostInfo().getSsh_ip().trim();
        for (Map.Entry<String, ComponentInfo> map : info.entrySet()) {
            if ("slave".equals(map.getValue().getCompStatus().getMode())) {
                log.info("从库：" + map.getKey() + " : " + map.getValue().getSshHostInfo().getSsh_ip() + " ,切换主库地址从 " + oldIp + " ，改为：" + newIp);
                boolean bRet = sshLoginModifySlave(map.getValue(), oldIp, newIp);
                if (!bRet) {
                    log.info("从库：" + map.getKey() + " : " + map.getValue().getSshHostInfo().getSsh_ip() + " ,切换主库地址从 " + oldIp + " ，改为：" + newIp + " 。失败");
                    continue;
                }
                log.info("从库：" + map.getKey() + " : " + map.getValue().getSshHostInfo().getSsh_ip() + " ,切换主库地址从 " + oldIp + " ，改为：" + newIp + " 。成功");
            }
        }

        return true;
    }

    // 连接主机重启数据库
    public boolean connectHostRestartComponent(ComponentInfo componentInfo) {
        SshHostInfo sshHostInfo = componentInfo.getSshHostInfo();
        String cmd = PostgresCmd.getRestart_slave();

        try {
            boolean bRet = sshLoginExecCmd(sshHostInfo, cmd);
            if (!bRet) {
                log.error("sshLoginExecCmd(" + cmd + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }

           /* bRet = sshLoginExecCmd(sshHostInfo, sshHostInfo.getSsh_cmd_restart_slave());
            if (!bRet) {
                log.error("sshLoginExecCmd(" + sshHostInfo.getSsh_cmd_restart_slave() + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }*/
            log.info("数据库:" + componentInfo.getDruid_id() + " " + componentInfo.getDatabaseInfo().getUrl() + " ,重启成功");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    // 重建从库
    public boolean createSlave(ComponentInfo master, ComponentInfo slave) {
        SshHostInfo sshHostInfo = slave.getSshHostInfo();
        String cmd = PostgresCmd.getRecreate_slave().replace("xxxx", master.getSshHostInfo().getSsh_ip());

        try {
            boolean bRet = sshLoginExecCmd(sshHostInfo, cmd);
            if (!bRet) {
                log.error("sshLoginExecCmd(" + cmd + ") failure. sshHostInfo:" + sshHostInfo.toString());
                return false;
            }

            log.info("从库:" + slave.getDruid_id() + " 基于主库: " + master.getDruid_id() + " " + " ,重建成功。从库地址:" + slave.getDatabaseInfo().getUrl());
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
