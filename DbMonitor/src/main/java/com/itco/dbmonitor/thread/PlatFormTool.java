package com.itco.dbmonitor.thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dbmonitor.database.impl.PostgresMonitor;
import com.itco.dbmonitor.entity.*;
import com.itco.dbmonitor.utils.MasterConfigWatcher;
import com.itco.entity.common.Common;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

public class PlatFormTool {
    final static Log log = LogFactory.getLog(PlatFormTool.class);
    Common common = new Common(); //公共配置类

    int debug = 0;
    int module_id = 0;
    int process_id = 0;
    String moduleCode;
    String billingLineId;
    String crontab = "50 * * * * *";

    ThresholdConfig thresholdConfig = new ThresholdConfig();
    boolean bSyncDruid = false;

    Map<String, ComponentInfo> infoMap = new ConcurrentHashMap<>();
    Map<String, PostgresMonitor> postgresMap = new ConcurrentHashMap<>();

    ZkClientApi zkClientApi = new ZkClientApi();
    SshExec sshExec = new SshExec();

    String databaseName = "postgres";
    String druidProperties = "druid.properties";
    String sshInfoProperties = "sshInfo.properties";
    String dbMonitorProperties = "DbMonitor.properties";

    String dbmonitorDatabase;
    private String slave;

    boolean loadZookeeper() {
        if (bSyncDruid) {
            return true;
        }

        zkClientApi.setVersion(common.getVersion());
        zkClientApi.setModuleCode(moduleCode);
        zkClientApi.setBillingLineId(billingLineId);
        if (!zkClientApi.init()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        if (!zkClientApi.register(true)) {
            log.info(zkClientApi.getModuleCode() + " 模块的进程注册失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        // 读取Common.properties
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        module_id = Integer.parseInt(zkClientApi.getModuleID());
        process_id = Integer.parseInt(zkClientApi.getProcessID());

        Factory.setModule_id(module_id);
        Factory.setProcess_id(process_id);

        common.setIDebug(debug);
        log.info(common.toString());
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());

        MasterConfigWatcher.setZkClientApi(zkClientApi);
        return true;
    }

    boolean loadMonitorProperties() {
        Properties properties = zkClientApi.getPropertiesFromZK(dbMonitorProperties);
        String strTmp = properties.getProperty("failure_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            thresholdConfig.setFailure_cnt(Integer.parseInt(strTmp));
        }

        strTmp = properties.getProperty("crontab_" + String.valueOf(process_id));
        if (!StringUtils.isEmpty(strTmp)) {
            crontab = strTmp;
        }

        log.info("crontab:" + crontab);
        return true;
    }


    boolean initDatabase() {
        dbmonitorDatabase = zkClientApi.getDbmonitorDatabase() + "/" + databaseName;

        if (StringUtils.isEmpty(dbmonitorDatabase)) {
            log.error("dbmonitor.database:" + dbmonitorDatabase + " ,健康监控组件目录配置为空");
            return false;
        }

        if (0 == zkClientApi.iExistPath(dbmonitorDatabase)) {
            log.info("dbmonitorDatabase:" + dbmonitorDatabase + ",不存在 创建");
            boolean bRetTmp = zkClientApi.createFullPath(dbmonitorDatabase);
            if (!bRetTmp) {
                log.error("dbmonitorDatabase:" + dbmonitorDatabase + ",创建失败。");
                return false;
            }
        }

        return true;
    }

    boolean initPostgres() {
        Map<String, ComponentInfo> infoTmp = new ConcurrentHashMap<>(infoMap);

        infoMap.clear();
        Properties druidProperties = zkClientApi.getPropertiesFromZK(this.druidProperties);
        String strTmp = druidProperties.getProperty("database_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            thresholdConfig.setDatabase_cnt(Integer.parseInt(strTmp));
        }

        Properties sshProperties = zkClientApi.getPropertiesFromZK(sshInfoProperties);
        if (sshProperties == null || sshProperties.size() == 0) {
            log.error("未上载：" + sshInfoProperties);
            return false;
        }

        for (int i = 1; i <= thresholdConfig.getDatabase_cnt(); i++) {
            String strMode = druidProperties.getProperty(databaseName + ".mode." + i);
            String strDriver = druidProperties.getProperty(databaseName + ".driver." + i);
            String strUrl = druidProperties.getProperty(databaseName + ".url." + i);
            String strUsernamee = druidProperties.getProperty(databaseName + ".username." + i);
            String strPublicKey = druidProperties.getProperty(databaseName + ".publicKey." + i);
            String strPassword = druidProperties.getProperty(databaseName + ".password." + i);

            String strSsh_ip = sshProperties.getProperty(databaseName + ".ssh_ip." + i);
            String StrSshUsername = sshProperties.getProperty(databaseName + ".ssh_username." + i);
            String StrSshPublicKey = sshProperties.getProperty(databaseName + ".ssh_publickey." + i);
            String StrSshPasswd = sshProperties.getProperty(databaseName + ".ssh_passwd." + i);
            String strSshPort = sshProperties.getProperty(databaseName + ".ssh_port." + i);

            if (StringUtils.isEmpty(strDriver) || StringUtils.isEmpty(strUrl) || StringUtils.isEmpty(strUsernamee) || StringUtils.isEmpty(strPublicKey) || StringUtils.isEmpty(strPassword)) {
                log.error("database:" + i + ",信息不完整，请核实");
                log.error("strMode:" + strMode + ",strDriver:" + strDriver + ",strUrl:" + strUrl + ",strUsernamee:" + strUsernamee + ",strPublicKey:" + strPublicKey + ",strPassword:" + strPassword);
                return false;
            }

            if (StringUtils.isEmpty(strSsh_ip) || StringUtils.isEmpty(StrSshUsername) || StringUtils.isEmpty(StrSshPasswd) || StringUtils.isEmpty(strSshPort) || StringUtils.isEmpty(StrSshPublicKey)) {
                log.error("ssh:" + i + ",信息不完整，请核实");
                log.error("strSsh_ip:" + strSsh_ip + ",StrSshUsername:" + StrSshUsername + ",StrSshPasswd:" + StrSshPasswd + ",strSshPort:" + strSshPort + ",StrSshPublicKey:" + StrSshPublicKey);
                return false;
            }

            SshHostInfo sshHostInfo = new SshHostInfo();
            sshHostInfo.setSsh_ip(strSsh_ip);
            sshHostInfo.setSsh_username(StrSshUsername);
            sshHostInfo.setSsh_publickey(StrSshPublicKey);
            sshHostInfo.setSsh_passwd(StrSshPasswd);
            sshHostInfo.setSsh_port(strSshPort);

            DataBaseInfo dataBaseInfo = new DataBaseInfo();
            dataBaseInfo.setDruid_id(String.valueOf(i));
            dataBaseInfo.setMode(strMode);
            dataBaseInfo.setDriver(strDriver);
            dataBaseInfo.setUrl(strUrl);
            dataBaseInfo.setUsername(strUsernamee);
            dataBaseInfo.setPublicKey(strPublicKey);
            dataBaseInfo.setPassword(strPassword);

            ComponentInfo componentInfo = new ComponentInfo();
            componentInfo.setDruid_id(String.valueOf(i));

            if (null != infoTmp.get(String.valueOf(i))) {
                // sync 触发的时候，旧的检测结果要保留
                componentInfo.setMonitorInfo(infoTmp.get(String.valueOf(i)).getMonitorInfo());
                componentInfo.setCompStatus(infoTmp.get(String.valueOf(i)).getCompStatus());
            } else {
                MonitorInfo monitorInfo = new MonitorInfo();
                componentInfo.setMonitorInfo(monitorInfo);

                CompStatus compStatus = new CompStatus();
                compStatus.setDruid_id(String.valueOf(i));
                compStatus.setMode(strMode);
                compStatus.setStatus("ok");
                compStatus.setIp(strSsh_ip);
                compStatus.setUpdate_date(Factory.getSystemDateStr());
                componentInfo.setCompStatus(compStatus);
            }

            componentInfo.setSshHostInfo(sshHostInfo);
            componentInfo.setDatabaseInfo(dataBaseInfo);

            infoMap.put(String.valueOf(i), componentInfo);
            log.info("被监控组件编号:" + i);
            log.info(componentInfo.getCompStatus().toString());
            log.info(componentInfo.getDatabaseInfo().toString());
            log.info(componentInfo.getSshHostInfo().toString());
            log.info(componentInfo.getMonitorInfo().toString());
            log.info("\n");

            // 健康监控程序：/database/dbmonitor/
            String dbmonitorLockPath = "/process/Lock/" + zkClientApi.getTpProcess().getModule_code() + "/";
            String lock = zkClientApi.lock(dbmonitorLockPath, 1000);
            String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + i;
            if (1 != zkClientApi.iExistPath(postgresPath)) {
                log.info("创建数据库监控实例:" + postgresPath);
                boolean bRet = zkClientApi.createPath(postgresPath, JSONObject.toJSONString(componentInfo.getCompStatus()));
                if (!bRet) {
                    log.error(postgresPath + ",数据库实例创建失败");
                    return false;
                }
            }
            zkClientApi.unLock(lock);
        }
        return true;
    }

    boolean initDbMonitorInstances() {
        postgresMap.clear();
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            String dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey() + "/" + zkClientApi.getTpProcess().getProcess_name();
            if (0 == zkClientApi.iExistPath(dbInstancePath)) {
                String ephemeralSequentialPath = zkClientApi.createEphemeralPath(dbInstancePath, JSONObject.toJSONString(map.getValue().getMonitorInfo()));
                if (StringUtils.isEmpty(ephemeralSequentialPath)) {
                    log.error("dbInstancePath:" + dbInstancePath + ", 创建DbMonitor监控实例失败");
                    return false;
                }
            }

            PostgresMonitor postgresMonitor = new PostgresMonitor();
            postgresMonitor.setProcess_id(process_id);
            postgresMap.put(map.getKey(), postgresMonitor);
        }

        return true;
    }


    public boolean init() {
        if (!loadZookeeper()) {
            log.error("loadZookeeper() failure");
            return false;
        }

        if (!loadMonitorProperties()) {
            log.error("loadMonitorProperties() failure");
            return false;
        }

        // 初始化 /dbmonitor/database/postgres目录
        if (!initDatabase()) {
            log.error("initDatabase() failure");
            return false;
        }

        // 根据druid.properties里的配置文件数据库信息，初始化 /dbmonitor/database/postgres/下的节点
        /*if (!initPostgres()) {
            log.error("initDatabase() failure");
            return false;
        }*/

        // DbMonitor监控组件，启动时，在每个数据库实例下创建监控节点。
        /*if (!initDbMonitorInstances()) {
            log.error("initDbMonitorInstances() failure");
            return false;
        }*/

        /*if (!updateMasterProperties()) {
            log.error("updateDruidMaster() failure");
            return false;
        }*/

        log.info(thresholdConfig.toString());
        return true;
    }


    public boolean upload() {
        if (!initPostgres()) {
            log.error("initDatabase() failure");
            return false;
        }
        return true;
    }

    public boolean clear() {
        Properties properties = zkClientApi.getPropertiesFromZK(druidProperties);
        String strTmp = properties.getProperty("database_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            thresholdConfig.setDatabase_cnt(Integer.parseInt(strTmp));
        }

        List<String> postgresList = zkClientApi.getChildPath(dbmonitorDatabase);
        for (String dbTmpName : postgresList) {
            log.info("删除zookeeper上的数据库实例节点: " + dbmonitorDatabase + "/" + dbTmpName);
            zkClientApi.deletePath(dbmonitorDatabase + "/" + dbTmpName);
        }

        return true;
    }

    public boolean update() {
        clear();
        upload();
        return true;
    }

    public boolean printMaster() {
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            if ("master".equals(map.getValue().getCompStatus().getMode())) {
                log.info("druid.properties , 主库为:");
                log.info(map.getValue().getDruid_id() + " , " + map.getValue().getDatabaseInfo().getUrl());
            }

            String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey();
            String data = zkClientApi.getDataByPath(postgresPath);
            CompStatus compStatus = JSONObject.parseObject(data, CompStatus.class);
            if ("master".equals(compStatus.getMode())) {
                log.info(postgresPath + " , 主库为:");
                log.info(map.getValue().getDruid_id() + ", " + map.getValue().getDatabaseInfo().getUrl());
            }
        }
        return true;
    }

    ComponentInfo getMasterComponent() {
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            if ("master".equals(map.getValue().getCompStatus().getMode())) {
                return map.getValue();
            }
        }
        return null;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

}

