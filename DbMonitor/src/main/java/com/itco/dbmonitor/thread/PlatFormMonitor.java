package com.itco.dbmonitor.thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dbmonitor.database.impl.PostgresMonitor;
import com.itco.dbmonitor.entity.*;
import com.itco.dbmonitor.utils.MasterConfigWatcher;
import com.itco.dbmonitor.utils.SmsUtil;
import com.itco.entity.common.Common;
import com.itco.framework.Factory;
import lombok.SneakyThrows;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.support.CronSequenceGenerator;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class PlatFormMonitor implements Runnable {
    final static Log log = LogFactory.getLog(PlatFormMonitor.class);
    final static Common common = new Common(); //公共配置类

    int debug = 0;
    int module_id = 0;
    int process_id = 0;
    String moduleCode;
    String billingLineId;
    String crontab = "50 * * * * *";

    ThresholdConfig thresholdConfig = new ThresholdConfig();
    boolean bSyncDruid = false;

    Map<String, ComponentInfo> infoMap = new ConcurrentHashMap<>();
    Map<String, PostgresMonitor> postgresMap = new ConcurrentHashMap<>();
    Map<String, TemplateInfo> smsMap = null;

    ZkClientApi zkClientApi = new ZkClientApi();
    SshExec sshExec = new SshExec();

    String databaseName = "postgres";
    String druidProperties = "druid.properties";
    String sshInfoProperties = "sshInfo.properties";
    String dbMonitorProperties = "DbMonitor.properties";
    String templateName = "tp_sms_template";

    String dbmonitorDatabase;
    private String slave;

    boolean loadZookeeper() {
        if (bSyncDruid) {
            return true;
        }

        zkClientApi.setModuleCode(moduleCode);
        zkClientApi.setBillingLineId(billingLineId);
        if (!zkClientApi.init(new MasterConfigWatcher())) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        if (!zkClientApi.register(false)) {
            log.info(zkClientApi.getModuleCode() + " 模块的进程注册失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }

        // 读取Common.properties
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        module_id = Integer.parseInt(zkClientApi.getModuleID());
        process_id = Integer.parseInt(zkClientApi.getProcessID());

        Factory.setModule_id(module_id);
        Factory.setProcess_id(process_id);

        common.setIDebug(debug);
        log.info(common.toString());
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());

        MasterConfigWatcher.setZkClientApi(zkClientApi);
        return true;
    }

    boolean loadMonitorProperties() {
        Properties properties = zkClientApi.getPropertiesFromZK(dbMonitorProperties);
        String strTmp = properties.getProperty("failure_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            thresholdConfig.setFailure_cnt(Integer.parseInt(strTmp));
        }

        strTmp = properties.getProperty("crontab_" + String.valueOf(process_id));
        if (!StringUtils.isEmpty(strTmp)) {
            crontab = strTmp;
        }

        log.info("crontab:" + crontab);
        return true;
    }

    boolean loadSmsTemplate() {
        if (bSyncDruid) {
            return true;
        }
        List<TemplateInfo> listTmp = zkClientApi.getTableFromZK(templateName, TemplateInfo.class);
        smsMap = listTmp.stream().collect(Collectors.toMap(TemplateInfo::getSmsTemplateId, h -> h));

        smsMap.forEach((key, value) -> System.out.println(key + " : " + value));
        SmsUtil.setSmsMap(smsMap);
        SshExec.setSmsMap(smsMap);
        return true;
    }

    boolean initDatabase() {
        dbmonitorDatabase = zkClientApi.getDbmonitorDatabase() + "/" + databaseName;

        if (StringUtils.isEmpty(dbmonitorDatabase)) {
            log.error("dbmonitor.database:" + dbmonitorDatabase + " ,健康监控组件目录配置为空");
            return false;
        }

        if (0 == zkClientApi.iExistPath(dbmonitorDatabase)) {
            log.info("dbmonitorDatabase:" + dbmonitorDatabase + ",不存在 创建");
            boolean bRetTmp = zkClientApi.createFullPath(dbmonitorDatabase);
            if (!bRetTmp) {
                log.error("dbmonitorDatabase:" + dbmonitorDatabase + ",创建失败。");
                return false;
            }
        }

        return true;
    }

    boolean initPostgres() {
        Map<String, ComponentInfo> infoTmp = new ConcurrentHashMap<>(infoMap);

        infoMap.clear();
        Properties druidProperties = zkClientApi.getPropertiesFromZK(this.druidProperties);
        String strTmp = druidProperties.getProperty("database_cnt");
        if (!StringUtils.isEmpty(strTmp)) {
            thresholdConfig.setDatabase_cnt(Integer.parseInt(strTmp));
        }

        Properties sshProperties = zkClientApi.getPropertiesFromZK(sshInfoProperties);
        if (sshProperties == null || sshProperties.size() == 0) {
            log.error("未上载：" + sshInfoProperties);
            return false;
        }

        for (int i = 1; i <= thresholdConfig.getDatabase_cnt(); i++) {
            String strMode = druidProperties.getProperty(databaseName + ".mode." + i);
            String strDriver = druidProperties.getProperty(databaseName + ".driver." + i);
            String strUrl = druidProperties.getProperty(databaseName + ".url." + i);
            String strUsernamee = druidProperties.getProperty(databaseName + ".username." + i);
            String strPublicKey = druidProperties.getProperty(databaseName + ".publicKey." + i);
            String strPassword = druidProperties.getProperty(databaseName + ".password." + i);
            String strStatus = druidProperties.getProperty(databaseName + ".status." + i);

            String strSsh_ip = sshProperties.getProperty(databaseName + ".ssh_ip." + i);
            String StrSshUsername = sshProperties.getProperty(databaseName + ".ssh_username." + i);
            String StrSshPublicKey = sshProperties.getProperty(databaseName + ".ssh_publickey." + i);
            String StrSshPasswd = sshProperties.getProperty(databaseName + ".ssh_passwd." + i);
            String strSshPort = sshProperties.getProperty(databaseName + ".ssh_port." + i);

            if (StringUtils.isEmpty(strDriver) || StringUtils.isEmpty(strUrl) || StringUtils.isEmpty(strUsernamee) || StringUtils.isEmpty(strPublicKey) || StringUtils.isEmpty(strPassword) || StringUtils.isEmpty(strStatus)) {
                log.error("database:" + i + ",信息不完整，请核实");
                log.error("strMode:" + strMode + ",strDriver:" + strDriver + ",strUrl:" + strUrl + ",strUsernamee:" + strUsernamee + ",strPublicKey:" + strPublicKey + ",strPassword:" + strPassword + ",strStatus:" + strStatus);
                return false;
            }

            if (StringUtils.isEmpty(strSsh_ip) || StringUtils.isEmpty(StrSshUsername) || StringUtils.isEmpty(StrSshPasswd) || StringUtils.isEmpty(strSshPort) || StringUtils.isEmpty(StrSshPublicKey)) {
                log.error("ssh:" + i + ",信息不完整，请核实");
                log.error("strSsh_ip:" + strSsh_ip + ",StrSshUsername:" + StrSshUsername + ",StrSshPasswd:" + StrSshPasswd + ",strSshPort:" + strSshPort + ",StrSshPublicKey:" + StrSshPublicKey);
                return false;
            }

            SshHostInfo sshHostInfo = new SshHostInfo();
            sshHostInfo.setSsh_ip(strSsh_ip);
            sshHostInfo.setSsh_username(StrSshUsername);
            sshHostInfo.setSsh_publickey(StrSshPublicKey);
            sshHostInfo.setSsh_passwd(StrSshPasswd);
            sshHostInfo.setSsh_port(strSshPort);

            DataBaseInfo dataBaseInfo = new DataBaseInfo();
            dataBaseInfo.setDruid_id(String.valueOf(i));
            dataBaseInfo.setMode(strMode);
            dataBaseInfo.setDriver(strDriver);
            dataBaseInfo.setUrl(strUrl);
            dataBaseInfo.setUsername(strUsernamee);
            dataBaseInfo.setPublicKey(strPublicKey);
            dataBaseInfo.setPassword(strPassword);

            ComponentInfo componentInfo = new ComponentInfo();
            componentInfo.setDruid_id(String.valueOf(i));

            if (null != infoTmp.get(String.valueOf(i))) {
                // sync 触发的时候，旧的检测结果要保留
                componentInfo.setMonitorInfo(infoTmp.get(String.valueOf(i)).getMonitorInfo());
                componentInfo.setCompStatus(infoTmp.get(String.valueOf(i)).getCompStatus());
            } else {
                MonitorInfo monitorInfo = new MonitorInfo();
                componentInfo.setMonitorInfo(monitorInfo);

                CompStatus compStatus = new CompStatus();
                compStatus.setDruid_id(String.valueOf(i));
                compStatus.setMode(strMode);
                compStatus.setStatus(strStatus);
                compStatus.setIp(strSsh_ip);
                compStatus.setUpdate_date(Factory.getSystemDateStr());
                componentInfo.setCompStatus(compStatus);
            }

            componentInfo.setSshHostInfo(sshHostInfo);
            componentInfo.setDatabaseInfo(dataBaseInfo);

            infoMap.put(String.valueOf(i), componentInfo);
            log.info("被监控组件编号:" + i);
            log.info(componentInfo.getCompStatus().toString());
            log.info(componentInfo.getDatabaseInfo().toString());
            log.info(componentInfo.getSshHostInfo().toString());
            log.info(componentInfo.getMonitorInfo().toString());
            log.info("\n");

            // 健康监控程序：/database/dbmonitor/
            String dbmonitorLockPath = "/process/Lock/" + zkClientApi.getTpProcess().getModule_code() + "/";
            String lock = zkClientApi.lock(dbmonitorLockPath, 1000);
            String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + i;
            if (1 != zkClientApi.iExistPath(postgresPath)) {
                log.info("创建数据库监控实例:" + postgresPath);
                boolean bRet = zkClientApi.createPath(postgresPath, JSONObject.toJSONString(componentInfo.getCompStatus()));
                if (!bRet) {
                    log.error(postgresPath + ",数据库实例创建失败");
                    return false;
                }
            }
            zkClientApi.unLock(lock);
        }
        return true;
    }

    boolean initDbMonitorInstances() {
        postgresMap.clear();
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            String dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey() + "/" + zkClientApi.getTpProcess().getProcess_name();
            if (0 == zkClientApi.iExistPath(dbInstancePath)) {
                String ephemeralSequentialPath = zkClientApi.createEphemeralPath(dbInstancePath, JSONObject.toJSONString(map.getValue().getMonitorInfo()));
                if (StringUtils.isEmpty(ephemeralSequentialPath)) {
                    log.error("dbInstancePath:" + dbInstancePath + ", 创建DbMonitor监控实例失败");
                    return false;
                }
            }

            PostgresMonitor postgresMonitor = new PostgresMonitor();
            postgresMonitor.setProcess_id(process_id);
            postgresMap.put(map.getKey(), postgresMonitor);
        }

        return true;
    }

    public boolean init() {
        if (!loadZookeeper()) {
            log.error("loadZookeeper() failure");
            return false;
        }

        if (!loadMonitorProperties()) {
            log.error("loadMonitorProperties() failure");
            return false;
        }

        if (!loadSmsTemplate()) {
            log.error("loadSmsTemplate() failure");
            return false;
        }

        // 初始化 /dbmonitor/database/postgres目录
        if (!initDatabase()) {
            log.error("initDatabase() failure");
            return false;
        }

        // 根据druid.properties里的配置文件数据库信息，初始化 /dbmonitor/database/postgres/下的节点
        if (!initPostgres()) {
            log.error("initDatabase() failure");
            return false;
        }

        // DbMonitor监控组件，启动时，在每个数据库实例下创建监控节点。
        if (!initDbMonitorInstances()) {
            log.error("initDbMonitorInstances() failure");
            return false;
        }

        /*if (!updateMasterProperties()) {
            log.error("updateDruidMaster() failure");
            return false;
        }*/

        // 启动完成打印主库
        printMaster();

        log.info(thresholdConfig.toString());

        if (!bSyncDruid) {
            MasterConfigWatcher.startMonitorMaster();
        }
        //监听 /config/file/master.properties，如果发生变化，重新刷新inofMap
        return true;
    }


    boolean syncInfoMap() {
        Properties druidProperties = zkClientApi.getPropertiesFromZK(this.druidProperties);
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            String reload = druidProperties.getProperty(databaseName + ".reload." + map.getKey());

            if ("1".equals(reload)) {
                String strMode = druidProperties.getProperty(databaseName + ".mode." + map.getKey());
                String strStatus = druidProperties.getProperty(databaseName + ".status." + map.getKey());

                map.getValue().getCompStatus().setMode(strMode);
                map.getValue().getCompStatus().setStatus(strStatus);
                map.getValue().getDatabaseInfo().setMode(strMode);
                map.getValue().getMonitorInfo().setStatus(strStatus);
                map.getValue().getMonitorInfo().setFailure_cnt(0L);
                map.getValue().getMonitorInfo().setUpdate_date(Factory.getSystemDateStr());
                log.info("监控程序同步 getCompStatus:" + map.getValue().getCompStatus().toString());
                log.info("监控程序同步 getMonitorInfo:" + map.getValue().getMonitorInfo().toString());

                String dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey() + "/" + zkClientApi.getTpProcess().getProcess_name();
                zkClientApi.setData(dbInstancePath, JSONObject.toJSONString(map.getValue().getMonitorInfo()));
            } else if ("2".equals(reload)) {
               /* String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey();
                String data = zkClientApi.getDataByPath(postgresPath);
                CompStatus compStatus = JSONObject.parseObject(data, CompStatus.class);*/

                String strMode = druidProperties.getProperty(databaseName + ".mode." + map.getKey());
                String strStatus = druidProperties.getProperty(databaseName + ".status." + map.getKey());

                map.getValue().getCompStatus().setMode(strMode);
                map.getValue().getCompStatus().setStatus(strStatus);
                map.getValue().getDatabaseInfo().setMode(strMode);
                map.getValue().getMonitorInfo().setStatus(strStatus);
                map.getValue().getMonitorInfo().setFailure_cnt(0L);

                map.getValue().getMonitorInfo().setUpdate_date(Factory.getSystemDateStr());
                log.info("上载同步 getCompStatus:" + map.getValue().getCompStatus().toString());
                log.info("上载同步 getMonitorInfo:" + map.getValue().getMonitorInfo().toString());

                String dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey() + "/" + zkClientApi.getTpProcess().getProcess_name();
                zkClientApi.setData(dbInstancePath, JSONObject.toJSONString(map.getValue().getMonitorInfo()));
            }
        }

        return true;
    }

    // 重新同步配置
    public boolean syncDruidProperties() {
        bSyncDruid = true;
        if (!syncInfoMap()) {
            log.error("syncInfoMap() failure");
            return false;
        }

        if (!init()) {
            log.error("init() failure");
            return false;
        }
        bSyncDruid = false;
        return true;
    }

    boolean updateDataBaseDump(String key) {
        ComponentInfo componentInfo = infoMap.get(key);
/*        if ("master".equals(componentInfo.getCompStatus().getMode())) {
            componentInfo.getCompStatus().setMode("old master");
            componentInfo.getCompStatus().setUpdate_date(Factory.getSystemDateStr());
            componentInfo.getDatabaseInfo().setMode("old master");
            componentInfo.getDatabaseInfo().setUpdate_date(Factory.getSystemDateStr());
        }
        componentInfo.getCompStatus().setStatus("error");
        componentInfo.getCompStatus().setUpdate_date(Factory.getSystemDateStr());*/

        String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + key;
        zkClientApi.setData(postgresPath, JSONObject.toJSONString(componentInfo.getCompStatus()));
        log.info(postgresPath + ", 写为故障");

        return true;
    }

    boolean updateSlaveSwitchMaster(String key) {
        ComponentInfo componentInfo = infoMap.get(key);
        componentInfo.getCompStatus().setMode("master");
        componentInfo.getCompStatus().setUpdate_date(Factory.getSystemDateStr());
        componentInfo.getDatabaseInfo().setMode("master");
        componentInfo.getDatabaseInfo().setUpdate_date(Factory.getSystemDateStr());
        componentInfo.getMonitorInfo().setUpdate_date(Factory.getSystemDateStr());

        String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + key;
        zkClientApi.setData(postgresPath, JSONObject.toJSONString(componentInfo.getCompStatus()));
        return true;
    }

    boolean updateDruidProperties(String key, boolean flag) {
        String data = "database_cnt=" + infoMap.size() + "\n\n";
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            data += "#database instance " + map.getKey() + "\n";
            if (key.equals(map.getKey()) && flag) {
                data += databaseName + ".reload." + map.getKey() + "=" + "2" + "\n"; //程序更新的状态写 2，1是upload上载的
            }

            data += databaseName + ".mode." + map.getKey() + "=" + map.getValue().getCompStatus().getMode() + "\n";
            data += databaseName + ".driver." + map.getKey() + "=" + map.getValue().getDatabaseInfo().getDriver() + "\n";
            data += databaseName + ".url." + map.getKey() + "=" + map.getValue().getDatabaseInfo().getUrl() + "\n";
            data += databaseName + ".username." + map.getKey() + "=" + map.getValue().getDatabaseInfo().getUsername() + "\n";
            data += databaseName + ".publicKey." + map.getKey() + "=" + map.getValue().getDatabaseInfo().getPublicKey() + "\n";
            data += databaseName + ".password." + map.getKey() + "=" + map.getValue().getDatabaseInfo().getPassword() + "\n";
            data += databaseName + ".status." + map.getKey() + "=" + map.getValue().getCompStatus().getStatus() + "\n\n";
        }

        String druidPath = "/config/file/" + druidProperties;
        zkClientApi.setData(druidPath, data);

        return true;
    }

    // 更新zookeeper中的值
    public boolean updateData2Zookeeper(String path, String value) {
        zkClientApi.setData(path, value);
        return true;
    }

    // 判断是不是所有的节点都数据库为故障
    public boolean judgeComponentAllDump(String postgresPath) {
        int iErrorCnt = 0;
        String process = "";
        List<String> childPath = zkClientApi.getChildPath("/process/DbMonitor");
        for (String s : childPath) {
            String pathTmp = postgresPath + "/" + s;
            String data = zkClientApi.getDataByPath(pathTmp);
            MonitorInfo tmp = JSON.parseObject(data, MonitorInfo.class);
            if ("error".equals(tmp.getStatus())) {
                iErrorCnt++;
            }
            process += "[" + s + "," + tmp.getUpdate_date() + "]";
        }

        log.info("判断是否全部故障: iErrorCnt:" + iErrorCnt + ",childPath.size():" + childPath.size());
        if (iErrorCnt == childPath.size()) {
            log.info("当前进程:" + process_id + ",全部监控程序 " + process + " 都认为:" + postgresPath + " 节点故障");
            return true;
        }

        return false;
    }

    boolean sendMessage(ComponentInfo componentInfo) {
        log.info("发送短信告警(判断数据库故障)：" + componentInfo.getDruid_id() + " , " + componentInfo.getSshHostInfo().getSsh_ip() + " , " + componentInfo.getCompStatus().getMode() + " , " + componentInfo.getCompStatus().getStatus() + " , " + componentInfo.getCompStatus().getUpdate_date());
        String msg = componentInfo.getDruid_id() + "&" + componentInfo.getSshHostInfo().getSsh_ip() + "&" + componentInfo.getCompStatus().getMode() + "&" + componentInfo.getCompStatus().getStatus() + "&" + componentInfo.getCompStatus().getUpdate_date();
        TpSmsTask tpSmsTask = new TpSmsTask();
        tpSmsTask.setSms_template_id("T20240122101825101940061");
        tpSmsTask.setSms_template_value(msg);
        tpSmsTask.setSms_user_phone("18150021002&1895027252");
        tpSmsTask.setSms_send_status("未发送");
        tpSmsTask.setCreate_by("admin");

        //PostgresMonitor.addSms(tpSmsTask);
        /*boolean bRet = postgresMap.get(componentInfo.getDruid_id()).sendMessage(componentInfo, tpSmsTask);
        if (!bRet) {
            log.error("短信告警生成失败，" + tpSmsTask.toString());
            return false;
        }*/

        TemplateInfo templateInfo = smsMap.get("T20240122101825101940061");
        SmsUtil.sendSms(templateInfo.getSmsTemplateId(), msg, templateInfo.getPhone());
        PostgresMonitor.addSmsLog(SmsUtil.getLogList());

        return true;
    }

    //  检查组件健康状况
    int checkComponent(ComponentInfo componentInfo) {
        String key = componentInfo.getDruid_id();
        MonitorInfo monitorInfo = componentInfo.getMonitorInfo();
        String mode = componentInfo.getCompStatus().getMode();

        monitorInfo.setLastTriggerDate(new Date());
        monitorInfo.setFailure_begin_time(Factory.getSystemDateStr());

        //连接检测
        int iRet = postgresMap.get(key).checkConnection(componentInfo);
        if (1 == iRet && "master".equals(componentInfo.getCompStatus().getMode())) {
            //插入更新检测
            iRet = postgresMap.get(key).checkInsert(componentInfo);
        }

        if (iRet == -1) {
            log.info("编号:" + key + ",组件模式:" + mode + ",触发脚本:" + crontab + ",到达。" + ",数据库异常");
            monitorInfo.setFailure_cnt(monitorInfo.getFailure_cnt() + 1);
            monitorInfo.setSuccess_cnt(0L);
        } else if (iRet == 1) {
            log.info("编号:" + key + ",组件模式:" + mode + ",触发脚本:" + crontab + ",检测成功:" + postgresMap.get(key).checkResult());
            monitorInfo.setSuccess_cnt(monitorInfo.getSuccess_cnt() + 1);
            monitorInfo.setFailure_cnt(0L);
        } else if (iRet == -2) {
            log.info("编号:" + key + ",组件模式:" + mode + ",触发脚本:" + crontab + ",到达。" + ",非数据库异常");
            monitorInfo.setError_cnt(monitorInfo.getError_cnt() + 1);
        } else {
            log.info("编号:" + key + ",组件模式:" + mode + ",触发脚本:" + crontab + ",到达。" + ",无法识别的检测结果");
        }

        log.info("编号:" + key + ",组件模式:" + mode + ",组件检测情况: " + monitorInfo.toString());
        monitorInfo.setTotal_cnt(monitorInfo.getTotal_cnt() + 1);

        //log.info(postgresMap.get(key).checkResult());
        return iRet;
    }

    //顺序寻找下一个从库
    int getNextSlave(int currentKey) {
        int nextSlave = -1;
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            if (map.getKey().equals(String.valueOf(currentKey))) {
                log.info("同一台数据库，跳过，currentKey:" + currentKey + ", 遍历:" + map.getKey());
                continue;
            }

            log.info("getNextSlave():" + map.getValue().toString());
            if ("ok".equals(map.getValue().getCompStatus().getStatus()) && "slave".equals(map.getValue().getCompStatus().getMode())) {
                nextSlave = Integer.parseInt(map.getKey());
            }
        }
        return nextSlave;
    }

    boolean monitorInfo(ComponentInfo componentInfo) {
        String key = componentInfo.getDruid_id();
        MonitorInfo monitorInfo = componentInfo.getMonitorInfo();
        String mode = componentInfo.getCompStatus().getMode();

        // 定时未触发，判断数据库异常次数是否超过阀值。
        if (monitorInfo.getFailure_cnt() >= thresholdConfig.getFailure_cnt() && "ok".equals(monitorInfo.getStatus())) {
            // 超过阈值所有的监控节点，更新到zookeeper中 /dbmonitor/database/postgres/postgres001/DbMonitor-10401111
            componentInfo.getCompStatus().setMode("old " + componentInfo.getCompStatus().getMode());
            componentInfo.getCompStatus().setStatus("error");
            componentInfo.getCompStatus().setUpdate_date(Factory.getSystemDateStr());
            monitorInfo.setStatus("error");
            monitorInfo.setUpdate_date(Factory.getSystemDateStr());

            log.info("编号:" + key + ",组件模式:" + mode + ",阈值:" + monitorInfo.getFailure_cnt() + ",failure_cnt:" + thresholdConfig.getFailure_cnt() + "," + ",组件检测情况: " + monitorInfo.toString());

            // 更新该数据库故障状态到zookeeper中
            String dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + key + "/" + zkClientApi.getTpProcess().getProcess_name();
            updateData2Zookeeper(dbInstancePath, JSONObject.toJSONString(monitorInfo));

            // 主库故障，判断是不是所有的节点都故障
            String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + key;
            boolean bRet = judgeComponentAllDump(postgresPath);
            if (bRet) {
                // 发送短信告警
                if (!sendMessage(componentInfo)) {
                    log.error("sendMessage(" + key + ") failure");
                    return false;
                }

                log.info("编号:" + key + ",组件模式:" + mode + ",全部健康检测程序认为它故障 ");
                // 尝试登录，重启数据库。
                bRet = sshExec.connectHostRestartComponent(componentInfo);
                if (bRet) {
                    // 状态恢复。
                    componentInfo.getCompStatus().setMode(componentInfo.getCompStatus().getMode().substring("old ".length()));
                    componentInfo.getCompStatus().setStatus("ok");
                    componentInfo.getCompStatus().setUpdate_date(Factory.getSystemDateStr());

                    //重启成功，重新加入检测。
                    monitorInfo.setStatus("ok");
                    monitorInfo.setFailure_cnt(0L);
                    monitorInfo.setUpdate_date(Factory.getSystemDateStr());

                    dbInstancePath = dbmonitorDatabase + "/" + databaseName + "00" + key + "/" + zkClientApi.getTpProcess().getProcess_name();
                    // 更新状态到监控节点
                    updateData2Zookeeper(dbInstancePath, JSONObject.toJSONString(monitorInfo));

                    postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + key;
                    String data = zkClientApi.getDataByPath(postgresPath);

                    CompStatus compStatus = JSON.parseObject(data, CompStatus.class);
                    compStatus.setStatus("ok");
                    // 更新状态到数据库节点
                    updateData2Zookeeper(postgresPath, JSONObject.toJSONString(compStatus));

                    // 更新 druid.properties ,使其他监控节点知道数据库恢复。可以重新监听。
                    if (!updateDruidProperties(key, true)) {
                        log.error("updateDruidProperties() failure");
                        return false;
                    }

                    // 发送短信告警
                    if (!sendMessage(componentInfo)) {
                        log.error("sendMessage(" + key + ") failure");
                        return false;
                    }
                } else {
                    //无法重启，无法连接。修改为故障
                    if (!updateDataBaseDump(key)) {
                        log.error("updateDataBaseDump(" + key + ") failure");
                        return false;
                    }

                    // 从库跳过
                    if ("slave".equals(componentInfo.getCompStatus().getMode()) || "old slave".equals(componentInfo.getCompStatus().getMode())) {
                        log.info("从库异常发送告警短信，需要系统人员上线处理。");
                        return true;
                    }

                    log.info("主库:" + key + " 故障，状态更新到infoMap和zookeeper成功");

                    //寻找一个从库作为主库。
                    int nextSlave = getNextSlave(Integer.parseInt(key));
                    if (nextSlave == -1) {
                        log.error("没有找到可用的从库，发起告警。");
                        return false;
                    }

                    log.info("主库:" + key + " 故障，找到:" + nextSlave + ",计划升级它为新主库");
                    // 进行数据库主库切换。
                    bRet = sshExec.sshLoginSwitchSlave2Master(infoMap.get(String.valueOf(nextSlave)));
                    if (!bRet) {
                        log.error("slaveSwitchMaster()，数据库从库切主库失败。");
                        return false;
                    }
                    log.info("主库:" + key + " 故障，升级:" + nextSlave + " 为新主库成功");

                    // 更新主库在zookeeper中的模式为master /dbmonitor/database/postgres/postgres00x
                    if (!updateSlaveSwitchMaster(String.valueOf(nextSlave))) {
                        log.error("updateSlaveSwitchMaster(" + String.valueOf(nextSlave) + ") failure");
                        return false;
                    }
                    // 发送短信告警
                    if (!sendMessage(infoMap.get(String.valueOf(nextSlave)))) {
                        log.error("sendMessage(" + key + ") failure");
                        return false;
                    }
                    log.info("主库:" + key + " 故障，升级:" + nextSlave + " 为新主库成功 ,计划更新到druid.properties");

                    // 更新新主库的数据到 druid.properties
                    if (!updateDruidProperties(String.valueOf(nextSlave), true)) {
                        log.error("updateDruidProperties() failure");
                        return false;
                    }

                    // 更改从库指向新主库。
                    bRet = sshExec.sshLoginModifySlave2NewMaster(infoMap, infoMap.get(key), infoMap.get(String.valueOf(nextSlave)));
                    if (!bRet) {
                        log.error("slaveSwitchMaster()，数据库从库指向新主库失败。");
                        return false;
                    }
                }
            } else {
                log.info("编号:" + key + ",组件模式:" + mode + ",健康检测程序未全部认为它故障 ");
            }
        }

        return true;
    }

    void test_ssh_cmd_promote(ComponentInfo componentInfo) {


        sshExec.connectHostRestartComponent(componentInfo);



    }

    void test_pg_select(ComponentInfo componentInfo) {
        sendMessage(componentInfo);

        PostgresMonitor postgresMonitor = new PostgresMonitor();
        postgresMonitor.checkConnection(infoMap.get("2"));
        postgresMonitor.checkInsert(infoMap.get("2"));
    }

    @SneakyThrows
    public void run() {

        //test_ssh_cmd_promote(infoMap.get(String.valueOf(1)));
        //test_pg_select(infoMap.get(String.valueOf(2)));
        do {
            try {
                for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
                    if (isTimeCrontab_tyqw(map.getValue())) {
                        // 定时时间到达，触发
                        checkComponent(map.getValue());
                    } else {
                        // 失败次数监控，是否到达切换条件
                        monitorInfo(map.getValue());
                    }
                }
            } catch (Exception e) {
                log.error(Factory.printStackTraceToString(e));
            }

            Thread.sleep(1000);
        } while (true);

        //exit();
    }


    public boolean isTimeCrontab_tyqw(ComponentInfo componentInfo) {
        MonitorInfo monitorInfo = componentInfo.getMonitorInfo();
        if (!"ok".equals(monitorInfo.getStatus())) {
            // 数据库已经故障，进行监控
            return false;
        }

        boolean validExpression = CronSequenceGenerator.isValidExpression(crontab);
        if (validExpression) {
            // 创建CronSequenceGenerator实例
            CronSequenceGenerator generator = new CronSequenceGenerator(crontab, TimeZone.getTimeZone("Asia/Shanghai"));

            // 获取当前时间
            Date now = new Date();

            // 上一次触发时间
            Date lastTrigger = monitorInfo.getLastTriggerDate();
            // 计算下一次触发时间
            Date nextExecutionTime = generator.next(lastTrigger);
            //log.info("cron:" + cron + "-> nextExecutionTime:" + nextExecutionTime + ",lastTrigger:" + lastTrigger + ",now():" + now);
            //log.info("nextExecutionTime.before(now):" + nextExecutionTime.before(now));
            //log.info("nextExecutionTime.equals(now):" + nextExecutionTime.equals(now));
            // 如果下一次触发时间等于当前时间或者小于当前时间（考虑到毫秒级的时间差），则返回true，表示任务该触发
            return nextExecutionTime.before(now) || nextExecutionTime.equals(now);
        } else {
            log.info("crontab:" + crontab + ",无效的表达式");
        }

        return false;
    }

    public boolean printMaster() {
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            if ("master".equals(map.getValue().getCompStatus().getMode())) {
                log.info("druid.properties , 主库为:");
                log.info(map.getValue().getDruid_id() + " , " + map.getValue().getDatabaseInfo().getUrl());
            }

            String postgresPath = dbmonitorDatabase + "/" + databaseName + "00" + map.getKey();
            String data = zkClientApi.getDataByPath(postgresPath);
            CompStatus compStatus = JSONObject.parseObject(data, CompStatus.class);
            if ("master".equals(compStatus.getMode())) {
                log.info(postgresPath + " , 主库为:");
                log.info(map.getValue().getDruid_id() + ", " + map.getValue().getDatabaseInfo().getUrl());
            }
        }
        return true;
    }

    ComponentInfo getMasterComponent() {
        for (Map.Entry<String, ComponentInfo> map : infoMap.entrySet()) {
            if ("master".equals(map.getValue().getCompStatus().getMode())) {
                return map.getValue();
            }
        }
        return null;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

    public void setDebug(int debug) {
        this.debug = debug;
    }

    boolean exit() {
        // 关闭zookeeper
        zkClientApi.close();

        // 关闭springboot
        Factory.close();
        return true;
    }
}
