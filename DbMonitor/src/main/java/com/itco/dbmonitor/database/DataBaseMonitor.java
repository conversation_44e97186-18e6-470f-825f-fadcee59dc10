package com.itco.dbmonitor.database;

import com.itco.dbmonitor.entity.ComponentInfo;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public abstract class DataBaseMonitor {
    final static Log log = LogFactory.getLog(DataBaseMonitor.class);
    protected int process_id = 0;

    /*
     * @decription 初始化
     * @param null:
     * @return
     * <AUTHOR>
     * @createDate 2024/1/6
     */
    public abstract boolean init();

    /*
     * @decription 检查数据库连接
     * @param : null
     * @return boolean 成功   失败
     * <AUTHOR>
     * @createDate 2024/1/6
     */
    public abstract int checkConnection(ComponentInfo componentInfo);

    /*
     * @decription 检查时候可插入
     * @param : null
     * @return boolean 成功   失败
     * <AUTHOR>
     * @createDate 2024/1/6
     */
    public abstract int checkInsert(ComponentInfo componentInfo);


    public abstract String checkResult();


    public void setProcess_id(int process_id) {
        this.process_id = process_id;
    }
}
