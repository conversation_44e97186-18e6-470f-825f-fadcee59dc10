package com.itco.dbmonitor.database.impl;

import com.alibaba.druid.util.StringUtils;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DecodePassword;
import com.itco.dbmonitor.database.DataBaseMonitor;
import com.itco.dbmonitor.entity.ComponentInfo;
import com.itco.dbmonitor.entity.TpSmsLog;
import com.itco.dbmonitor.entity.TpSmsTask;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.itco.component.jdbc.DbPool.close;

public class PostgresMonitor extends DataBaseMonitor {
    final Log log = LogFactory.getLog(PostgresMonitor.class);

    Connection conn = null;
    static String insertSql = "insert into data.dbmonitor_data(process_id,live_date,comments) values(?,?,?) on conflict (process_id) do update set live_date=?,comments=?";
    static String sendMessagesql = "insert into config.tp_sms_task( sms_template_id, sms_template_value, sms_user_phone, sms_send_status, sms_send_time, sms_send_result, insert_time, create_by) values (?,?,(select string_agg(sms_user_phone, '&') from config.tp_sms_user where sms_user_role in ('运维人员')),?, null, null, date_trunc('second', now()), ?)";
    static String addSmsLogsql = "insert into config.tp_sms_log(sms_send_phone, sms_send_content, sms_send_time, sms_send_status, sms_send_fail_reason, sms_template_id, sms_template_value, sms_retry_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?);";
    static List<TpSmsTask> smsList = new ArrayList<>();
    static List<TpSmsLog> logList = new ArrayList<>();

    int bRetConn = 0;
    int bRetInsert = 0;

    Driver driver = null;

    @Override
    public boolean init() {

        return true;
    }

    @Override
    public int checkConnection(ComponentInfo componentInfo) {
        bRetConn = 0;
        bRetInsert = 0;

        try {
            // jdbc协议:postgresql子协议://主机地址:数据库端口号/要连接的数据库名
            String driverstr = componentInfo.getDatabaseInfo().getDriver();
            String url = componentInfo.getDatabaseInfo().getUrl();
            String username = componentInfo.getDatabaseInfo().getUsername();
            String publicKey = componentInfo.getDatabaseInfo().getPublicKey();
            String password = componentInfo.getDatabaseInfo().getPassword();
            if (StringUtils.isEmpty(url) || StringUtils.isEmpty(username) || StringUtils.isEmpty(password) || StringUtils.isEmpty(password)) {
                log.info("数据库连接信息异常。url:" + url + ",username:" + username + ",password:" + password);
                bRetConn = -2;
                return -2;
            }

            //log.info("key:" + postgresInfo.getDruid_id() + ",组件模式:" + postgresInfo.getMode() + ",地址信息:" + postgresInfo.toString());

            String databasePasswd = DecodePassword.decryption(publicKey, password);

            //Class.forName(driverstr);
            // 1.  创建驱动程序类对象
            driver = new org.postgresql.Driver();
            // 2. 注册驱动程序（可以注册多个驱动程序）
            //DriverManager.registerDriver(driver);

            // 3. 连接数据库，返回连接对象
            conn = DriverManager.getConnection(url, username, databasePasswd);
            if (conn == null) {
                bRetConn = -1;
                return -1;
            }

            int cnt = exeQuery(conn, "SELECT count(1) cnt FROM pg_tables WHERE schemaname = ? and tablename= ? ", "data", "dbmonitor_data");
            if (cnt != 1) {
                if ("master".equals(componentInfo.getCompStatus().getMode())) {
                    String createSql = "create table data.dbmonitor_data(process_id int primary key,live_date varchar(64),comments varchar(200))";
                    boolean bRet = exeUpdate(conn, createSql);
                    if (bRet) {
                        log.info("监控表创建成功：" + createSql);
                        bRetConn = 1;
                        return 1;
                    } else {
                        log.info("监控表创建失败：" + createSql);
                        close(conn, null, null);
                        bRetInsert = -1;
                        return -1;
                    }
                } else {
                    bRetConn = 1;
                    return 1;
                }
            }

            List<Map<String, Object>> maps = DBUtils.queryMap(conn, "select * from data.dbmonitor_data");
            //log.info("maps:" + maps.toString());
            /*if (maps == null || maps.size() == 0) {
                bRetConn = 1;
                return -1;
            }*/

            if (!"master".equals(componentInfo.getCompStatus().getMode())) {
                close(conn, null, null);
                DriverManager.registerDriver(driver);
            }

            bRetConn = 1;
            return 1;
        } catch (SQLException e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            bRetConn = -1;
            return -1;
        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            bRetConn = -2;
            return -2;
        }
    }

    @Override
    public int checkInsert(ComponentInfo componentInfo) {
        boolean bRet = false;
        try {
            // 插入检测
            bRet = exeUpdate(conn, insertSql, process_id, Factory.getSystemDateStr(), "插入", Factory.getSystemDateStr(), "更新");

            // 短信告警发送
            /*if (smsList.size() > 0) {
                Iterator<TpSmsTask> iterator = smsList.iterator();
                while (iterator.hasNext()) {
                    TpSmsTask tpSmsTask = iterator.next();
                    bRet = sendMessage(tpSmsTask);
                    if (!bRet) {
                        log.error("druid_id:" + componentInfo.getDruid_id() + "，短信告警生成失败，" + tpSmsTask.toString());
                    }
                    iterator.remove();  //正确
                }
            }*/

            // 短信告警发送
            if (logList.size() > 0) {
                Iterator<TpSmsLog> iterator = logList.iterator();
                while (iterator.hasNext()) {
                    TpSmsLog tpSmsLog = iterator.next();
                    bRet = writeSmsLog(tpSmsLog);
                    if (!bRet) {
                        log.error("smsTemplateId:" + tpSmsLog.getSmsTemplateId() + "，短信日志生成失败，" + tpSmsLog.toString());
                    }
                    iterator.remove();  //正确
                }
            }

            //log.info("插入结果, bRet:" + bRet);
            close(conn, null, null);
            DriverManager.registerDriver(driver);

            bRetInsert = 1;
            return 1;
        } catch (SQLException e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn, null, null);
            bRetInsert = -1;
            return -1;
        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn, null, null);
            bRetInsert = -2;
            return -2;
        }
    }


    public String checkResult() {
        if (bRetInsert == 0) {
            return "健康检测结果,查询检测:" + switch_msg(bRetConn);
        }
        return "健康检测结果,查询检测:" + switch_msg(bRetConn) + ", 插入更新检测:" + switch_msg(bRetInsert);
    }

    int exeQuery(Connection conn, String sql, Object... obj) throws SQLException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int cnt = 0;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            rs = ps.executeQuery();
            rs.next();
            cnt = rs.getInt(1);
            return cnt;
        } finally {
            ps.close();
        }
    }

    boolean exeUpdate(Connection conn, String sql, Object... obj) throws SQLException {
        PreparedStatement ps = null;
        boolean bRet = true;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            ps.executeUpdate();
            return bRet;
        } finally {
            if (ps != null) {
                ps.close();
            }
        }
    }

    public String switch_msg(int code) {
        String msg = "";
        switch (code) {
            case 1:
                msg = "成功";
                break;
            case 0:
                msg = "未检测";
                break;
            case -1:
                msg = "数据库异常";
                break;
            case -2:
                msg = "非数据库异常";
                break;
            default:
                msg = "无法识别编码";
                break;
        }

        return msg;
    }

    public boolean sendMessage(TpSmsTask tpSmsTask) {
        try {
            boolean bRet = exeUpdate(conn, sendMessagesql, tpSmsTask.getSms_template_id(), tpSmsTask.getSms_template_value(), tpSmsTask.getSms_send_status(), tpSmsTask.getCreate_by());
            if (!bRet) {
                return false;
            }

            return true;
        } catch (SQLException e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            return false;
        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            return false;
        }
    }

    public boolean writeSmsLog(TpSmsLog tpSmsLog) {
        try {
            boolean bRet = exeUpdate(conn, addSmsLogsql, tpSmsLog.getSmsSendPhone(), tpSmsLog.getSmsSendContent(), tpSmsLog.getSmsSendTime(), tpSmsLog.getSmsSendStatus(), tpSmsLog.getSmsSendFailReason(), tpSmsLog.getSmsTemplateId(), tpSmsLog.getSmsTemplateValue(), tpSmsLog.getSmsRetryCount());
            if (!bRet) {
                return false;
            }

            return true;
        } catch (SQLException e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            return false;
        } catch (Exception e) {
            log.error(Factory.printStackTraceToString(e));
            close(conn);
            return false;
        }
    }

    public static boolean addSms(TpSmsTask tpSmsTask) {
        smsList.add(tpSmsTask);
        return true;
    }

    public static boolean addSmsLog(List<TpSmsLog> tpSmsLogList) {
        logList.addAll(tpSmsLogList);
        return true;
    }

}
