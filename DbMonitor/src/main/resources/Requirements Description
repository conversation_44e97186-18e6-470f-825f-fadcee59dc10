【zookeeper目录结构】
/process/DbMonitor/
 	DbMonitor-10401111
 	DbMonitor-10401211
 	DbMonitor-10401311
dbmonitor/database/postgres/
        postgres001(数据库信息01)（主库，在网）
            DbMonitor-10401111(可用)
            DbMonitor-10401211(可用)
            DbMonitor-10401311(可用)
        postgres002(数据库信息02)（从库，在网）
            DbMonitor-10401111(可用)
            DbMonitor-10401211(可用)
            DbMonitor-10401311(可用)
        postgres003(数据库信息03)（从库，在网）
            DbMonitor-10401111(可用)
            DbMonitor-10401211(可用)
            DbMonitor-10401311(可用)

一、提供一个工具程序，新增、修改、删除管理数据库实例的新节点。
二、健康监控程序
1、程序启动去zookeeper（/database/dbmonitor/）注册一个监控实例子节点-》dbmonitor00X()。
2、加载所有需要监控的数据库，数据库信息必须文件存放（防止数据库不可用，无法启动）。
3、程序定时去遍历（/database/dbmonitor/）是否可连接，是否可插入。（长连接和短连接方式可选择）
4、当数据库连接超时，或者数据库写入失败，命名空间满的情况下；超过3次（可配置）就输出告警短信。
	更新zookeeper中（/database/dbmonitor/postgres00X/dbmonitor00X(不可用)）数据库的实例写一行不可用记录。
5、同时判断一下该数据库下的所有健康检测节点是否认为它已经退出。
6、如果都退出就输出短信告警（数据库不可用）
7、判断主机是否可用，可用，重启数据库。不可连接，发起切换流程。
8、从小到大编号，选择一个最小的可用数据库节点，升级为主节点。
9、修改zookeeper组件中当前可用的数据库信息为新主节点。
10、修改zookeeper组件中原主库为离线状态。

三、应用重连数据库机制jar包
1、启动连接zookeeper组件，从zookeeper中获取可用的数据库主库信息。连接数据库。
2、定时每3分钟（可配置）从zookeeper中获取可用的数据库主库信息，是否和上一次有变化。
3、没有变化，结束，有变化发起数据库连接新主库。

【整体内容】
1、健康检测程序。检测多个数据库的健康状况（具备检测多个数据库实例，能区分主从，可用，不可用），维护分布式组件中存放的数据连接信息是当前可用的数据库。输出检测异常的完整日志信息。且具备通知告警功能。
2、通用的数据库故障切换功能。周期性的从分布式获取数据库连接信息，当连接信息变化时重连数据库。所有的切换动作，需要留完整日志信息，且具备通知短信告警功能。
3、所有的应用程序，引入通用的数据库故障切换功能jar包，周期性调用通用程序判断是否切换。
4、订单、话单的接口新增生成文件功能，格式需要按照JSON格式生成，给通用入库可直接入库。
5、通用入库增加订单和话单入库的配置，保证通过接口入库的订单，话单文件，当数据库写入失败时，能通过通用入库补数据。
6、settCenter已经具备双活条件，目前只启动了一个可用区，需要安排时间进行双活启动。1月中旬
7、apiPort已经双活启动，目前总线只配置了一个后端接口。需要安排总线进行双接口配置。1月中旬
8、***************、***************两台sftp服务的主机都在可用区二，需要提申请把***************主机划分到可用区一。找顾老师，要所有主机的划分可用区。
9、zookeeper集群部署迁移到非应用主机上，需要分布到多个可用区上。
10、数据库离线备份的文件，保存最新的3天数据到共享磁盘上。

