logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.org.springframework.boot=ERROR
logging.level.org.springframework.web=ERROR
logging.level.org.springframework.boot.web=ERROR
logging.level.com.ctg.itrdc.cache=ERROR
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.file.name=${LOG_HOME:~/javalog}/SplitOrder.log
logging.file.max-size=50MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
logging.file.max-history=30
logging.file.total-size-cap=100MB


#workFlag=online
#workDir=E:\\test\\
#workFileName=NORMAL_20210301201643_46_3454.1012
#·¢²¼°æ±¾
version=DbMonitor_deploy_2024-01-10 11:00