#log å¼å³
logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.com.ctg.itrdc.cache=ERROR
#ä¸æå°spring boot å¯å¨çæ¥å
logging.level.org.springframework.boot.autoconfigure=ERROR 

logging.level.c.c.i.cache.vjedis.pool.CtgVJedisPool=ERROR
logging.level.c.c.itrdc.cache.monitor.DateFormatUtil=ERROR
logging.level.c.ctg.itrdc.cache.monitor.MonitorWorker=ERROR
logging.level.com.ctg.itrdc.cache.vjedis.pool=ERROR
logging.level.com.ctg.itrdc.cache.monitor.MonitorWorker=ERROR

#è¾åºæ¥å¿æä»¶
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.file.name=${LOG_HOME:~/javalog}/Rating.log
logging.file.max-size=50MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
logging.file.max-history=30
logging.file.total-size-cap=100MB

#è°è¯ä½¿ç¨å¼å³ workFlag:offline ç¦»çº¿æ¨¡å¼  å¶ä»æåµï¼å¨çº¿è°åº¦æ¨¡å¼
workFlag=online
workDir=D:\\Coding\\test\\Rating
workFileName=NORMAL_SERVICE_CONROL_56.1012

#åå¸çæ¬
version=Rating_deploy_2023-05-16 14:00