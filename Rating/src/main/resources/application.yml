spring:
  servlet:
    multipart:
      maxFileSize: 1000MB
      maxRequestSize: 1000MB
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://*************:18801/postgres?useUnicode=true&characterEncoding=UTF-8&useSSL=true&allowMultiQueries=true
      username: root
      password: XbJbYJpw+S3fT1LUyFaop0mLp0+/fcvV9aMY5OyRIzMV8c7DqWGDe461k29kv+W+ki5SBTNspgdMyVd7EGi7UA==
      filters: config,wall,stat
      max-active: 100
      initial-size: 1
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAN+R01OUifmVR9fefSgHQjE/d4g3baPnrJWE2f0OfqzvkzoGCZHjYy6mT0iPcNRtvK2rECI+CQQSAiCp7T9ZcSkCAwEAAQ==
# mybatis
mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.itco.rating.entity



