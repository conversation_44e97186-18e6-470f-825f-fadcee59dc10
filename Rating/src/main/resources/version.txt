--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-01-25 13:30
【修订日期】：2024-01-25
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：60857
【实现功能】：
1. 完善累计功能，累积量结果表新增生失效时间，判断当前账期的话单是否超出合同失效时间，则进入异常单，
   异常类型30020：ticket_start_time 超出合同有效期，无可用累计量
   按月和按合同累计，生失效时间取合同的生失效时间
   按半年期累计，生失效时间取该账期对应所在半年的生失效时间
   按和同年累计，取合同的生效时间，判断当前话单在合同年的哪一个年度，填写该年度的开始结束时间
2. 新增累计source表字段，关联结果表id

【变更文件】：
修改：src/main/java/com/itco/cumulant/constants/GlobalConstants.java
修改：src/main/java/com/itco/cumulant/domain/QuantityResult.java
修改：src/main/java/com/itco/cumulant/domain/QuantitySource.java
修改：src/main/java/com/itco/cumulant/mapper/CumulantMapper.java
修改：src/main/java/com/itco/cumulant/record/BaseDataHandler.java
修改：src/main/java/com/itco/cumulant/record/impl/MemoryDataHandler.java
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java
修改：src/main/java/com/itco/RatingApplication.java
修改：src/main/resources/mapper/CumulantMapper.xml


--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-12-14 09:41
【修订日期】：2023-12-14
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：60753
【实现功能】：
1. 新增按合同年、自然年、半年期累计功能
2. 修改累计逻辑,可自动获取累计结果表中上次的累计结果
3. 可支持批价单点多线程累计

【变更文件】：
新增：src/main/java/com/itco/cumulant/domain/DigitContractInfo.java
修改：src/main/java/com/itco/cumulant/constants/GlobalConstants.java
修改：src/main/java/com/itco/cumulant/record/BaseDataHandler.java
修改：src/main/java/com/itco/cumulant/record/impl/MemoryDataHandler.java
修改：src/main/java/com/itco/module/CalcRecordRating.java
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java
修改：src/main/java/com/itco/rating/dao/RatingProcess.java
修改：src/main/java/com/itco/RatingApplication.java

--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-12-02 19:40
【修订日期】：2023-12-08
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：60661
【实现功能】：
1. 根据需要修改批价精度不再除以100
2. 累积量中对金额的处理不再四舍五入

【变更文件】：
修改：src/main/java/com/itco/cumulant/record/impl/MemoryDataHandler.java
修改：src/main/java/com/itco/rating/util/StringUtil.java

--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-12-02 19:40
【修订日期】：2023-12-02
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：60658
【实现功能】：
1. 累积量功能修复销售品匹配规则

【变更文件】：
修改：src/main/java/com/itco/cumulant/record/BaseDataHandler.java

--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-12-02 14:40
【修订日期】：2023-12-02
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：60480
【实现功能】：
1. 累积量功能增加支持按半年期累积

【变更文件】：
修改：src/main/java/com/itco/cumulant/constants/GlobalConstants.java
修改：src/main/java/com/itco/cumulant/record/BaseDataHandler.java
修改：src/main/java/com/itco/cumulant/record/impl/MemoryDataHandler.java

--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-11-21 20:30
【修订日期】：2023-11-21
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：58879
【实现功能】：
1. 累积量功能增加合同号 contract_code 的判断及支持全匹配
2. 累积量功能增加支持多个销售品编码 sales_code的判断匹配
3. 累积量功能修复charge取值为空的问题

【变更文件】：
修改：src/main/java/com/itco/cumulant/domain/QuantityDataRel.java
修改：src/main/java/com/itco/cumulant/record/BaseDataHandler.java
修改：src/main/java/com/itco/cumulant/record/impl/MemoryDataHandler.java
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java

--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-09-19 09:00
【修订日期】：2023-09-19
【修订人员】：zhangshf
【需求单号】：
【SVN提交前版本】：58801
【实现功能】：
1. 数字生活累积量sale_code替换成sales_code
2. 累积量增加sales_code维度累积
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-08-26 16:00
【修订日期】：2023-08-26
【修订人员】：zhangshf
【需求单号】：
【SVN提交前版本】：55280
【实现功能】：
1. 数字生活累积量功能提交
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-05-16 16:00
【修订日期】：2023-05-16
【修订人员】：yuwh
【需求单号】：
【SVN提交前版本】：55279
【实现功能】：
1. 福建佣金中新增补充分居标识的填写

【变更文件】：
修改：main/java/com/itco/rating/entity/ProcessResultBean.java
修改：main/java/com/itco/rating/dao/impl/RatingProcessImpl.java


【修订要点】：
  暂无

【注意事项】：
  暂无
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-03-20 11:00
【修订日期】：2023-03-20
【修订人员】：fuxingwang
【需求单号】：xxx
【SVN提交前版本】：54947
【实现功能】：
1、福建佣金回退回收改造和试算改造，如果redo_flag是11,13,21,22或者批次号大于0，就使用deal_cycle_id赋值给批价填写的账期。
// 福建佣金，档案封存导致回退回收重跑的单子异常，改造。 2023年3月3日
if (jsonObject.containsKey("redo_flag")) {
    String redo_flag = jsonObject.getString("redo_flag");
    if (("21".equals(redo_flag) || "22".equals(redo_flag)) && feeCycleTmp < Integer.parseInt(billingcycle)) {
        continue;
    }
}

【变更文件】：
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-03-17 11:00
【修订日期】：2023-03-17
【修订人员】：fuxingwang
【需求单号】：xxx
【SVN提交前版本】：54947
【实现功能】：
1、福建佣金回退回收改造和试算改造，如果redo_flag是11,13,21,22或者批次号大于0，就使用deal_cycle_id赋值给批价填写的账期。
// 福建佣金，档案封存导致回退回收重跑的单子异常，改造。 2023年3月3日
if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
    if (jsonObject.containsKey("redo_flag")) {
        String redo_flag = jsonObject.getString("redo_flag");
        int batchIdTmp = Integer.parseInt(jsonObject.getString("cdr_batch_id"));
        if ("11".equals(redo_flag) || "13".equals(redo_flag) || "21".equals(redo_flag) || "22".equals(redo_flag) || batchIdTmp > 0) {
            billingcycle = jsonObject.getString("deal_cycle_id");              //---帐期
        }
    }
}

【变更文件】：
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-03-02 11:00
【修订日期】：2023-03-02
【修订人员】：fuxingwang
【需求单号】：xxx
【SVN提交前版本】：54592
【实现功能】：
1、取消批价要素串数组（a_element_str）的重复填写，element_str已经写了要素串了。
2、数据库连接改成jdbc.properties获取的用户和密码

【变更文件】：
修改：src/main/java/com/itco/rating/dao/impl/RatingProcessImpl.java
修改：src/main/java/com/itco/rating/util/StringUtil.java
修改：src/main/java/com/itco/RatingApplication.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-02-07 11:00
【修订日期】：2023-01-17
【修订人员】：fuxingwang
【需求单号】：需求人员 xiaolc
【SVN提交前版本】：54542
【实现功能】：
1、福建佣金的段落判断，如果因子返回值为空进行特殊处理。
2、福建佣金系统资费的现金流为正参考值如果不满足出资费条件，填写no_payment，不支付原因。

【变更文件】：
修改：main/java/com/itco/rating/dao/impl/RatingProcessImpl.java
修改：main/java/com/itco/rating/util/StringUtil.java
修改：main/resources/version.txt

【修订要点】：
  1、SELECT * FROM UPLOAD_TABLE where key_suffix='Tariff';--规则上载配置
  2、资费表（tariff）上载添加一个字段,concat(charge_party_id,'') charge_party_id;

【注意事项】：
  1、Common.properties配置文件中的system=TPSS,latn_id=591的情况下，才能打开此功能。
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-01-17 11:00
【修订日期】：2023-01-17
【修订人员】：fuxingwang
【需求单号】：需求人员 xiaolc
【SVN提交前版本】：54238
【实现功能】：
福建佣金系统现金流为正功能。
原理是在把资费表的charge_party_id字段，作为配置现金流为正判断的参考值。
当charge_party_id有值时，进行现金流为正判断，若参考值的结果为0则资费不满足，判断是否补结；若参考值结果为1，正常出资费。

【变更文件】： 
修改：main/java/com/itco/rating/dao/impl/RatingProcessImpl.java
修改：main/java/com/itco/rating/service/impl/UploadTableServiceImpl.java
修改：main/java/com/itco/RatingApplication.java
新增：main/resources/version.txt
 
【修订要点】：
  1、SELECT * FROM UPLOAD_TABLE where key_suffix='Tariff';--规则上载配置
  2、资费表（tariff）上载添加一个字段,concat(charge_party_id,'') charge_party_id;

【注意事项】：
  1、Common.properties配置文件中的system=TPSS,latn_id=591的情况下，才能打开此功能。
--------------------------------------------------------------------------------------------------
【发布版本】：Rating_code_2023-01-17 11:00
【修订日期】：2023-01-29
【修订人员】：yuwh
【需求单号】：需求人员 xiaolc
【SVN提交前版本】：54441
【实现功能】：
在批价逻辑左值为空，会出现报错的情况，针对不同的运算规则进行非空判断返回不同的结果（默认右值不为空）。

【变更文件】：
修改：main/java/com/itco/rating/util/StringUtil.java

【修订要点】：
  1、当左右值比较方式为 >  <  =  in 的四种情况下，返回 false
  2、当左右值比较方式为 not in  !=  的两种情况下，返回 true

【注意事项】：
  暂无
--------------------------------------------------------------------------------------------------
