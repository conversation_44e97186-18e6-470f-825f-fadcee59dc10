package com.itco.redis.utils;

import com.ctg.itrdc.cache.vjedis.jedis.BinaryClient;

import java.util.List;

/**
 * list操作
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-02
 * @Time 16:32
 */
public class RedisList<T> extends GeneralKeyFun{
    public RedisList(T key){
        super(key);
    }
    /**
     * 移除并返回列表的头元素
     * @return
     */
    public T lpop(){
        if(key instanceof String){
            return (T) getClient().lpop((String)key);
        }else{
            return (T) getClient().lpop((byte[])key);
        }
    }
    /**
     * 移除并返回列表尾元素。当key不存在时返回null
     * @return
     */
    public T rpop(){
        if(key instanceof String){
            return (T) getClient().rpop((String)key);
        }else{
            return (T) getClient().rpop((byte[])key);
        }
    }

    /**
     * 将一个或多个值 values 插入到列表 key 的表头
     * @param values
     * @return  长度
     */
    public Long lpush(T ... values){
        if(key instanceof String){
            return  getClient().lpush((String)key,(String[])values);
        }else{
            return  getClient().lpush((byte[])key,(byte[][])values);
        }
    }

    /**
     * 将一个或多个值 values 插入到列表 key 的表尾(最右边)
     * @param values
     * @return
     */
    public Long rpush(T ... values){
        if(key instanceof String){
            return  getClient().rpush((String)key,(String[])values);
        }else{
            return  getClient().rpush((byte[])key,(byte[][])values);
        }
    }


    public Long rpushA(String ... values){
            return  getClient().rpush((String)key,(String[])values);
    }

    /**
     * 将值 value 插入到列表 key 的表尾，当且仅当 key 存在并且是一个列表。
     * 和 RPUSH 命令相反，当 key 不存在时， RPUSHX 命令什么也不做。
     * @param value
     * @return
     */
    public Long rpushx(T value){

        if(key instanceof String){
            return  getClient().rpushx((String)key,(String)value);
        }else{
            return  getClient().rpushx((byte[])key,(byte[])value);
        }
    }


    /**
     * 返回列表 key 的长度，若key不存在则返回0
     * @return
     */
    public Long llen(){

        if(key instanceof String){
            return  getClient().llen((String)key);
        }else{
            return  getClient().rpushx((byte[])key);
        }
    }

    /**
     * 返回列表 key 中指定区间内的元素，区间以偏移量 start 和 end 指定。下标从0开始，-1表示最后一个元素
     * @param start
     * @param end
     * @return
     */
    public List<T> lrange(long start, long end){

        if(key instanceof String){
            return (List<T>) getClient().lrange((String) key, start, end);
        }else{
            return (List<T>) getClient().lrange((byte[])key,start,end);
        }
    }

    /**
     * 返回列表 key 中，下标为 index 的元素.
     * 下标从0开始，以 -1 表示列表的最后一个元素，
     * -2 表示列表的倒数第二个元素.
     * @param index
     * @return
     */
    public T lindex(long index){

        if(key instanceof String){
            return (T) getClient().lindex((String) key, index);
        }else{
            return (T) getClient().lindex((byte[])key,index);
        }
    }
    /**
     * 将值 value 插入到列表 key 当中,
     * 位于值 pivot 之前或之后.
     * 当 pivot 不存在于列表 key 时,
     * 不执行任何操作. 当key不存在时, key被视为空列表, 不执行任何操作
     * @param value
     * @param where #{@link BinaryClient.LIST_POSITION}
     * @param pivot
     * @return
     */
    public Long linsert(T value, BinaryClient.LIST_POSITION where,T pivot){

        if(key instanceof String){
            return getClient().linsert((String) key,where,(String)pivot, (String)value);
        }else{
            return getClient().linsert((byte[]) key,where,(byte[])pivot, (byte[])value);
        }
    }

    /**
     * 将值 value 插入到列表 key 的表头，
     * 当且仅当 key 存在并且是一个列表。
     * 和 LPUSH 命令相反，当 key 不存在时，
     * LPUSHX 命令什么也不做。
     * @param value
     * @return
     */
    public Long lpushx(T value){

        if(key instanceof String){
            return getClient().lpushx((String) key, (String)value);
        }else{
            return  getClient().lpushx((byte[])key,(byte[])value);
        }
    }

    /**
     * 根据参数 count 的值，移除列表中与参数 value 相等的元素.
     * count值有以下几种：
     * count > 0 : 从表头开始向表尾搜索，移除与 value 相等的元素，数量为 count.
     * count < 0 : 从表尾开始向表头搜索，移除与 value 相等的元素，数量为 count 的绝对值.
     * count = 0 : 移除表中所有与 value 相等的值
     * @param count
     * @param value
     * @return
     */
    public Long lrem(long count,T value){

        if(key instanceof String){
            return getClient().lrem((String) key,count, (String)value);
        }else{
            return  getClient().lrem((byte[])key,count,(byte[])value);
        }
    }

    /**
     * 将列表 key 下标为 index 的元素的值设置为 value.
     * @param index
     * @param value
     * @return
     */
    public String lset(long index,T value){

        if(key instanceof String){
            return getClient().lset((String) key,index, (String)value);
        }else{
            return getClient().lset((byte[])key,index,(byte[])value);
        }
    }

    /**
     * 对一个列表进行修剪(trim)，就是说，让列表只保留指定区间内的元素，不在指定区间之内的元素都将被删除.
     * 下标从0开始，以 -1 表示列表的最后一个元素， -2 表示列表的倒数第二个元素，以此类推
     * @param start
     * @param end
     * @return
     */
    public String ltrim(long start,long end){
        if(key instanceof String){
            return getClient().ltrim((String) key,start,end);
        }else{
            return getClient().ltrim((byte[])key,start,end);
        }
    }

}
