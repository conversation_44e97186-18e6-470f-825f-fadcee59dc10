package com.itco.redis.utils;

import com.ctg.itrdc.cache.vjedis.ScanResult;
import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Key值操作公共接口
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-02
 * @Time 16:32
 */
public class GeneralKeyFun<T> {
    protected static Logger logger= LoggerFactory.getLogger(GeneralKeyFun.class);
    protected static ThreadLocal<Map<String,VProxyJedis>> local=new ThreadLocal<>();
    protected static ThreadLocal<String> dbLocal=new ThreadLocal<>();
    private static GeneralKeyFun client;
    protected T key;

    public T getKey() {
        return key;
    }

    public void setKey(T key) {
        this.key = key;
    }

    /**
     * 在当前线程使用特定的组
     * @param dbName
     */
    public static final void setDBGroup(String dbName){
        dbLocal.set(dbName);
    }

    /**
     * 在当前线程使用默认组
     */
    public static final void useDefaultGroup(){
        dbLocal.set(RedisUtil.database);
    }
    public static final GeneralKeyFun getFunClient(){
        synchronized (GeneralKeyFun.class) {
            if (client == null) {
                client=new GeneralKeyFun();
            }
        }
        return client;
    }
    protected GeneralKeyFun(){

    }
    protected GeneralKeyFun(T key){
        this.key=key;
    }
    protected static VProxyJedis getClient(){
        if(local.get()==null){
            Map<String,VProxyJedis> newMap=new HashMap<>();
            local.set(newMap);
            Thread nowThread = Thread.currentThread();
            RedisUtil.getExecutorService().execute(()->{
                try {
                    logger.info("等待");
                    nowThread.join();
                    Collection<VProxyJedis> values=newMap.values();
                    values.forEach(VProxyJedis::close);
                    logger.info("关闭主线程");
                } catch (InterruptedException e) {
                    logger.error("等待主线程:{}关闭出现异常{}", nowThread.getName(), e.getLocalizedMessage(), e);
                    if (!nowThread.isAlive()) {
                        Collection<VProxyJedis> values=newMap.values();
                        values.forEach(VProxyJedis::close);
                    }
                }
            });
        }
        if(dbLocal.get()==null){
            dbLocal.set(RedisUtil.database);
        }
        Map<String,VProxyJedis> clientMap=local.get();
        String dbName=dbLocal.get();
        if(clientMap.get(dbName)!=null){
            return clientMap.get(dbName);
        }
        try {
            VProxyJedis client = RedisUtil.getResource(dbName);
            clientMap.put(dbName,client);
            return client;
        }catch (Exception e){
            logger.error(e.getLocalizedMessage(),e);
            throw new RuntimeException(e);
        }
    }
    /**
     * 判断key是否存在
     * @param key
     * @return
     */
    public static Boolean exists(String key){
        return getClient().exists(key);
    }
    public static Boolean exists(byte[] key){
        return getClient().exists(key);
    }

    /**
     * 根据key删除存储的数据
     * @param key   成功删除数
     * @return
     */
    public static  long del(String... key){
        return getClient().del(key);
    }
    public static  long del(byte[]... key){
        return getClient().del(key);
    }

    /**
     * 返回指定key所存储值的类型，类型为 "none", "string", "list", "set", "zset", "hash"，”byte”中的一种
     * 若指定key不存在，则返回"none"
     * @param key
     * @return 储值类型 #{@link DataType}
     */
    public static  String type(String key){
        return getClient().type(key);
    }
    public static  String type(byte[] key){
        return getClient().type(key);
    }

    /**
     * 目前无法使用 会报错  原因未知
     * 重命名oldKey为newKey
     * 若oldKey与newKey相同，则返回错误
     * 若newKey已经存在，则将被覆盖
     * @param oldKey
     * @param newKey
     * @return
     */
    @Deprecated
    public String rename(String oldKey,String newKey){
        return getClient().rename(oldKey,newKey);
    }
    @Deprecated
    public String rename(byte[] oldKey,byte[] newKey){
        return getClient().rename(oldKey,newKey);
    }

    /**
     * 目前无法使用 会报错  原因未知
     * 重命名oldkey为newKey
     * @param oldKey
     * @param newKey
     * @return  resultCode #{@link ResultCode}
     */
    @Deprecated
    public Long renameNx(String oldKey,String newKey){
        return getClient().renamenx(oldKey,newKey);
    }
    @Deprecated
    public Long renameNx(byte[] oldKey,byte[] newKey){
        return getClient().renamenx(oldKey,newKey);
    }

    /**
     * 设置指定kej的生存时间
     * @param key
     * @param seconds   生存时间 秒
     * @return  操作结果 #{@link ResultCode}
     */
    public static  Long expire(String key,int seconds){
        return getClient().expire(key, seconds);
    }
    public static  Long expire(byte[] key,int seconds){
        return getClient().expire(key, seconds);
    }
    /**
     * 设置指定kej的生存时间
     * @param key
     * @param milliSeconds  生存时间 毫秒
     * @return  操作结果 #{@link ResultCode}
     */
    public static  Long pexpire(String key,Long milliSeconds){
        return getClient().pexpire(key,milliSeconds);
    }
    public static  Long pexpire(byte[] key,Long milliSeconds){
        return getClient().pexpire(key,milliSeconds);
    }
    /**
     * 将指定key从易失转为永久
     * @param key
     * @return 操作结果 #{@link ResultCode}
     */
    public static  Long persist(String key){
        return getClient().persist(key);
    }
    public static  Long persist(byte[] key){
        return getClient().persist(key);
    }

    /**
     * 返回指定key剩余的生存时间
     * @param key
     * @return  剩余的生存时间  秒
     */
    public static  Long ttl(String key){
        return getClient().ttl(key);
    }
    public static  Long ttl(byte[] key){
        return getClient().ttl(key);
    }
    /**
     * 返回指定key剩余的生存时间
     * @param key
     * @return  剩余的生存时间  毫秒
     */
    public static  Long pttl(String key){
        return getClient().pttl(key);
    }
    public static  Long pttl(byte[] key){
        return getClient().pttl(key);
    }

    /**
     * 目前无法使用
     * 返回给出key的版本号数值
     * @param key
     * @return
     */
    @Deprecated
    public static  Long version(String key){
        return getClient().version(key);
    }
    @Deprecated
    public static  Long version(byte[] key){
        return getClient().version(key);
    }

    public static ScanResult<String> scan(String key){
        return (ScanResult<String>) getClient().scan(key);
    }

    /**
     * 加分布式锁
     * @param lockName
     * @param lockValue
     * @param lockExpireTime
     * @param timeout
     * @throws InterruptedException
     */
    public static void lock(String lockName,String lockValue,long lockExpireTime,long timeout){
        getClient().lock( lockName, lockValue, lockExpireTime,timeout);
    }
    public static void unLock(String lockName,String lockValue){
        getClient().unlock(lockName,lockValue);
    }
    public static void close(){
        Collection<VProxyJedis> values=local.get().values();
        values.forEach(VProxyJedis::close);
    }
}
