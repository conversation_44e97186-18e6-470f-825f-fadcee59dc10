package com.itco.redis.utils;



/**
 * string类型操作
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-02
 * @Time 16:29
 */
public class RedisStringFun extends GeneralKeyFun{
    private RedisStringFun(){}
    /**
     * 设置给定key的字符串
     * @param key
     * @param value
     * @return  OK:成功
     */
    public static String set(String key,String value){
        return getClient().set(key,value);
    }
    public static String set(byte[] key,byte[] value){
        return getClient().set(key,value);
    }
    /**
     * 返回指定key的值
     * @param key
     * @return  指定key的值 ，不存在返回null
     */
    public static String get(String key){
        return getClient().get(key);
    }
    public static byte[] get(byte[] key){
        return getClient().get(key);
    }

    /**
     * 返回给定key对应值的长度
     * @param key
     * @return
     */
    public static Long strLen(String key){
        return getClient().strlen(key);
    }
    public static Long strLen(byte[] key){
        return getClient().strlen(key);
    }

    /**
     * 向给定key的字符串末尾添加字符串。如果 key 不存在， APPEND 就简单地将给定 key 设为 value
     * @param key
     * @param value
     * @return  操作后的值
     */
    public static Long append(String key,String value){
        return getClient().append(key,value);
    }
    public static Long append(byte[] key,byte[] value){
        return getClient().append(key,value);
    }

    /**
     * 将 key 所储存的值减去减量 decrement。
     * 本操作的值限制在 64 位(bit)有符号数字表示之内。
     * 注意: 如果值包含错误的类型，或字符串类型的值不能表示为数字，那么返回一个错误
     * @param key
     * @param decrement
     * @return  操作后的值
     */
    public static Long decrBy(String key,Long decrement){
        return getClient().decrBy(key,decrement);
    }
    public static Long decrBy(byte[] key,Long decrement){
        return getClient().decrBy(key,decrement);
    }

    /**
     * 将 key 所储存的值减去1。
     * 本操作的值限制在 64 位(bit)有符号数字表示之内。
     * 注意: 如果值包含错误的类型，或字符串类型的值不能表示为数字，那么返回一个错误
     * @param key
     * @return  操作后的值
     */
    public static Long decr(String key){
        return getClient().decr(key);
    }
    public static Long decr(byte[] key){
        return getClient().decr(key);
    }
    /**
     * 将 key 中储存的数字值增一。
     * 本操作的值限制在 64 位(bit)有符号数字表示之内。
     * 注意: 如果值包含错误的类型，或字符串类型的值不能表示为数字，那么返回一个错误
     * @param key
     * @return  操作后的值
     */
    public static Long incr(String key){
        return getClient().incr(key);
    }
    public static Long incr(byte[] key){
        return getClient().incr(key);
    }

    /**
     * 将 key 所储存的值加上增量 increment。
     * 本操作的值限制在 64 位(bit)有符号数字表示之内。
     * 注意: 如果值包含错误的类型，或字符串类型的值不能表示为数字，那么返回一个错误
     * @param key
     * @param increment
     * @return  操作后的值
     */
    public static Long incrBy(String key,Long increment){
        return getClient().incrBy(key,increment);
    }
    public static Long incrBy(byte[] key,Long increment){
        return getClient().incrBy(key,increment);
    }

    /**
     * 返回 key 中字符串值的子字符串，
     * 字符串的截取范围由
     * start 和 end 两个偏移量决定(包括 start 和 end 在内).
     * 下标从0开始，-1 表示最后一个字符， -2 表示倒数第二个
     * @param key
     * @param startOffset
     * @param endOffset
     * @return
     */
    public static String getRange(String key,Long startOffset,Long endOffset){
        return getClient().getrange(key,startOffset,endOffset);
    }
    public static byte[] getRange(byte[] key,Long startOffset,Long endOffset){
        return getClient().getrange(key,startOffset,endOffset);
    }

    /**
     * 将给定 key 的值设为 value ，
     * 并返回 key 的旧值。当key不存在时返回null
     * @param key
     * @param value
     * @return
     */
    public static String getSet(String key,String value){
        return getClient().getSet(key,value);
    }
    public static byte[] getSet(byte[] key,byte[] value){
        return getClient().getSet(key,value);
    }

    /**
     * 将值 value 关联到 key ，
     * 并将 key 的生存时间设为 seconds (以秒为单位).
     * SETEX 是一个原子性(atomic)操作，
     * 关联值和设置生存时间两个动作会在同一时间内完成
     * @param key
     * @param seconds
     * @param value
     * @return     OK : 成功
     */
    public static String setEx(String key,int seconds,String value){
        return getClient().setex(key,seconds,value);
    }
    public static String setEx(byte[] key,int seconds,byte[] value){
        return getClient().setex(key,seconds,value);
    }

    /**
     * 将 key 的值设为 value ，
     * 当且仅当 key 不存在.
     * 若给定的 key 已经存在，则 SETNX 不做任何动作.
     * @param key
     * @param value
     * @return 操作结果 #{@link ResultCode}
     */
    public static Long setNx(String key,String value){
        return getClient().setnx(key,value);
    }
    public static Long setNx(byte[] key,byte[] value){
        return getClient().setnx(key,value);
    }

    /**
     * 用 value 参数覆写(overwrite)给定 key 所储存的字符串值,
     * 从偏移量 offset 开始.
     * 不存在的 key当作空白字符串处理.
     * @param key
     * @param offset
     * @param value
     * @return  操作后的值的长度
     */
    public static Long setRange(String key,long offset,String value){
        return getClient().setrange(key,offset,value);
    }
    public static Long setRange(byte[] key,long offset,byte[] value){
        return getClient().setrange(key,offset,value);
    }
}
