package com.itco.redis.utils;

import com.ctg.itrdc.cache.pool.CtgJedisPoolException;
import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.ctg.itrdc.cache.vjedis.jedis.HostAndPort;
import com.ctg.itrdc.cache.vjedis.jedis.JedisPoolConfig;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPool;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPoolConfig;
import com.itco.framework.calcrecord.CalcRecordManager;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-11-29
 * @Time 15:58
 */
public class RedisUtil {
     static String defaultName="ctgCache.properties";
    static String useFileName="ctgCache.properties";
    private static Map<String,CtgVJedisPool> poolMap=new ConcurrentHashMap();
    private static String resourcePath;
    private static Logger logger= LoggerFactory.getLogger(RedisUtil.class);
    private static CtgVJedisPoolConfig config,config_preproc ;
    private static ExecutorService executorService;
    static String database,databasepreproc;
    private static void init(){
        //读取默认配置
        try {
             Properties defaultProperties = CalcRecordManager.getZkClientApi().getPropertiesFromZK(useFileName);
            //读取自定义配置
             Properties properties = CalcRecordManager.getZkClientApi().getPropertiesFromZK(useFileName);
            //----------------------------------------------------------//
            /*Resource resource = new ClassPathResource(defaultName);
            Properties defaultProperties = PropertiesLoaderUtils.loadProperties(resource) ;
            resource = new ClassPathResource(useFileName);
            Properties properties = PropertiesLoaderUtils.loadProperties(resource) ;*/
            //--------------------------------------------//

            Set<Object> keys = properties.keySet();//返回属性key的集合
            String hosts = properties.getProperty("host");
            if (hosts == null || "".equals(hosts)) {
                hosts = defaultProperties.getProperty("host");
            }
            String[] hostArr = hosts.split(",");
            List<HostAndPort> hostAndPortList = new ArrayList();
            for (String host : hostArr) {
                String[] temp = host.split(":");
                HostAndPort hostAndPort = new HostAndPort(temp[0], Integer.valueOf(temp[1]));
                hostAndPortList.add(hostAndPort);
            }
            config=new CtgVJedisPoolConfig(hostAndPortList);
            config.setMonitorLog(false) ;
            database = properties.getProperty("databaserating");
            logger.info("databaserating="+database);
            if (database == null) {
                database = defaultProperties.getProperty("databaserating");
            }

            String password = properties.getProperty("password");
            if (password == null) {
                password = defaultProperties.getProperty("password");
            }

            String period = properties.getProperty("period");
            if (period == null) {
                period = defaultProperties.getProperty("period");
            }

            String monitorTimeout = properties.getProperty("monitorTimeout");
            if (monitorTimeout == null) {
                monitorTimeout = defaultProperties.getProperty("monitorTimeout");
            }

            String maxIdle = properties.getProperty("maxIdle");
            if (maxIdle == null) {
                maxIdle = defaultProperties.getProperty("maxIdle");
            }
            GenericObjectPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxIdle(Integer.valueOf(maxIdle));
            String maxTotal = properties.getProperty("maxTotal");
            if (maxTotal == null) {
                maxTotal = defaultProperties.getProperty("maxTotal");
            }
            executorService= Executors.newFixedThreadPool(Integer.valueOf(maxTotal));
            poolConfig.setMaxTotal(Integer.valueOf(maxTotal));
            String minIdle = properties.getProperty("minIdle");
            if (minIdle == null) {
                minIdle = defaultProperties.getProperty("minIdle");
            }
            poolConfig.setMinIdle(Integer.valueOf(minIdle));
            String maxWaitMillis = properties.getProperty("maxWaitMillis");
            if (maxWaitMillis == null) {
                maxWaitMillis = defaultProperties.getProperty("maxWaitMillis");
            }
            poolConfig.setMaxWaitMillis(Integer.valueOf(maxWaitMillis));
            config.setDatabase(database).setPassword(password).setPoolConfig(poolConfig).setPeriod(Integer.valueOf(period)).setMonitorTimeout(Integer.valueOf(monitorTimeout));
        } catch (Exception e) {
            logger.error("读取redis配置失败",e);
            throw new RuntimeException(e);
        }
    }
    private static void init_preproc(){
        //读取默认配置
        try {
            Properties defaultProperties = CalcRecordManager.getZkClientApi().getPropertiesFromZK(useFileName);
            //读取自定义配置
            Properties properties = CalcRecordManager.getZkClientApi().getPropertiesFromZK(useFileName);
            Set<Object> keys = properties.keySet();//返回属性key的集合

            String hosts = properties.getProperty("host");
            if (hosts == null || "".equals(hosts)) {
                hosts = defaultProperties.getProperty("host");
            }
            String[] hostArr = hosts.split(",");
            List<HostAndPort> hostAndPortList = new ArrayList();
            for (String host : hostArr) {
                String[] temp = host.split(":");
                HostAndPort hostAndPort = new HostAndPort(temp[0], Integer.valueOf(temp[1]));
                hostAndPortList.add(hostAndPort);
            }
            config_preproc=new CtgVJedisPoolConfig(hostAndPortList);
            config_preproc.setMonitorLog(false) ;
            databasepreproc= properties.getProperty("databasepreproc");
            logger.info("databasepreproc="+databasepreproc);
            if (databasepreproc == null) {
                databasepreproc = defaultProperties.getProperty("databasepreproc");
            }
            String password = properties.getProperty("password");
            if (password == null) {
                password = defaultProperties.getProperty("password");
            }

            String period = properties.getProperty("period");
            if (period == null) {
                period = defaultProperties.getProperty("period");
            }

            String monitorTimeout = properties.getProperty("monitorTimeout");
            if (monitorTimeout == null) {
                monitorTimeout = defaultProperties.getProperty("monitorTimeout");
            }

            String maxIdle = properties.getProperty("maxIdle");
            if (maxIdle == null) {
                maxIdle = defaultProperties.getProperty("maxIdle");
            }
            GenericObjectPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxIdle(Integer.valueOf(maxIdle));
            String maxTotal = properties.getProperty("maxTotal");
            if (maxTotal == null) {
                maxTotal = defaultProperties.getProperty("maxTotal");
            }
            executorService= Executors.newFixedThreadPool(Integer.valueOf(maxTotal));
            poolConfig.setMaxTotal(Integer.valueOf(maxTotal));
            String minIdle = properties.getProperty("minIdle");
            if (minIdle == null) {
                minIdle = defaultProperties.getProperty("minIdle");
            }
            poolConfig.setMinIdle(Integer.valueOf(minIdle));
            String maxWaitMillis = properties.getProperty("maxWaitMillis");
            if (maxWaitMillis == null) {
                maxWaitMillis = defaultProperties.getProperty("maxWaitMillis");
            }
            poolConfig.setMaxWaitMillis(Integer.valueOf(maxWaitMillis));
            config_preproc.setDatabase(databasepreproc).setPassword(password).setPoolConfig(poolConfig).setPeriod(Integer.valueOf(period)).setMonitorTimeout(Integer.valueOf(monitorTimeout));
        } catch (Exception e) {
            logger.error("读取redis配置失败",e);
            throw new RuntimeException(e);
        }
    }

    public static CtgVJedisPool getPool(){
        String dbName;
        init();
        dbName=database;
        if(poolMap.get(dbName)==null) {
            config.setDatabase(dbName);
            CtgVJedisPool pool=new CtgVJedisPool(config);
            poolMap.put(dbName,pool);
        }
        return poolMap.get(dbName);
    }

    public static CtgVJedisPool getPool_preproc(){
        String dbName;
        init_preproc();
        dbName=databasepreproc;
        if(poolMap.get(dbName)==null) {
            config_preproc.setDatabase(dbName);
            CtgVJedisPool pool=new CtgVJedisPool(config_preproc);
            poolMap.put(dbName,pool);
        }
        return poolMap.get(dbName);
    }

    /**
     * 初始化连接池
     * @throws IOException
     */
    public static CtgVJedisPool getPool(String dbName){
        if(database==null){
            init();
        }
        if(dbName==null||"".equals(dbName)){
            dbName=database;
        }
        if(poolMap.get(dbName)==null) {
            config.setDatabase(dbName);
            CtgVJedisPool pool=new CtgVJedisPool(config);
            poolMap.put(dbName,pool);
        }
        return poolMap.get(dbName);
    }

    public static ExecutorService getExecutorService(){
        synchronized (RedisUtil.class) {
            if (executorService == null) {
                init();
            }
        }
        return executorService;
    }

    /**
     * 获取一个连接
     * @return
     * @throws CtgJedisPoolException
     */
    protected static VProxyJedis getResource(String dbName) throws Exception {
        try {
            return getPool(dbName).getResource();
        } catch (Exception e) {
            logger.error("获取redis连接发生异常",e);
            throw e;
        }
    }

    /**
     * 加载配置文件
     * @param path
     * @return
     * @throws IOException
     */
    private static Properties loadProperties(String path) throws IOException {
        Properties properties=new Properties();
        String filePath=getResourcePath()+path;
        logger.info("读取配置文件:{}",filePath);
        if(new File(filePath).exists()) {
            InputStream inputStream = RedisUtil.class.getResourceAsStream(path);
            InputStreamReader reader = new InputStreamReader(inputStream, "UTF-8");
            properties.load(reader);
        }
        return properties;
    }
    private static String getResourcePath(){
        if(resourcePath==null) {
            resourcePath = Thread.currentThread().getContextClassLoader().getResource("").getPath();
            String os = System.getProperty("os.name");
            if (os!=null && os.toLowerCase().startsWith("win")) {
                resourcePath = resourcePath.substring(1);
            }
        }
        return resourcePath;
    }


}