package com.itco.redis.utils;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-02
 * @Time 17:42
 */
public class RedisSortedSet<T> extends GeneralKeyFun{
    public RedisSortedSet(T key){
        super(key);
    }

    /**
     * 将member 元素及其 score 值加入到有序集 key 当中。
     * 如果插入的member已经是有序集的成员，
     * 则更新它的score值，
     * 并通过重新插入这个member来保证它在正确的位置上。
     * score值可以是整数值或双精度浮点数
     * @param score
     * @param member
     * @return
     */
    public Long zadd(double score,T member){
        if(key instanceof String){
            return getClient().zadd((String)key,score,(String)member);
        }else{
            return getClient().zadd((byte[])key,score,(byte[])member);
        }
    }

    /**
     * 将一个或多个 member 元素及其 score 值加入到有序集 key 当中。
     * 如果插入的member已经是有序集的成员，
     * 则更新它的score值，
     * 并通过重新插入这个member来保证它在正确的位置上。score值可以是整数值或双精度浮点数
     * @param scoreMembers
     * @return
     */
    public Long zadd( Map<T,Double> scoreMembers){
        if(key instanceof String){
            return getClient().zadd((String)key,(Map<String,Double>)scoreMembers);
        }else{
            return getClient().zadd((byte[])key,(Map<byte[],Double>)scoreMembers);
        }
    }

    /**
     * 返回有序集 key 的基数（集合元素数量），当key不存在时返回0
     * @return
     */
    public Long zcard(){

        if(key instanceof String){
            return getClient().zcard((String)key);
        }else{
            return getClient().zcard((byte[])key);
        }
    }

    /**
     * 返回有序集 key 中， score 值在 min 和 max 之间(默认包括 score 值等于 min 或 max )的成员的数量
     * @param min
     * @param max
     * @return
     */
    public Long zcount(double min,double max){

        if(key instanceof String){
            return getClient().zcount((String)key,min,max);
        }else{
            return getClient().zcount((byte[])key,min,max);
        }
    }

    /**
     * todo 待测试
     * 为有序集 key 的成员 member 的 score 值加上增量 increment.
     * 当 key不存在，或 member不是 key的成员时， ZINCRBY key increment member 等同于 ZADD key increment member.
     * @param score
     * @param member
     * @return
     */
    public Double zincrby(double score,T member){
        if(key instanceof String){
            return getClient().zincrby((String)key,score,(String)member);
        }else{
            return getClient().zincrby((byte[])key,score,(byte[])member);
        }
    }

    /**
     * 返回有序集 key 中，指定区间内的成员。
     * 其中成员的位置按 score 值递增(从小到大)来排序，
     * 具有相同 score值的成员按字典序(lexicographical order )来排列。
     * @param start
     * @param end
     * @return
     */
    public Set<T> zrange(long start,long end){
        if(key instanceof String){
            return (Set<T>) getClient().zrange((String)key,start,end);
        }else{
            return (Set<T>) getClient().zrange((byte[])key,start,end);
        }
    }

    /**
     * 返回有序集 key 中，指定区间内的成员，其中成员的位置按 score 值递减(从大到小)来排列。
     * @param start
     * @param end
     * @return
     */
    public Set<T> zrevrange(long start,long end){
        if(key instanceof String){
            return (Set<T>) getClient().zrevrange((String)key,start,end);
        }else{
            return (Set<T>) getClient().zrevrange((byte[])key,start,end);
        }
    }

    /**
     * 返回有序集 key 中，
     * 所有 score 值介于 min 和 max 之间(包括等于 min 或 max )的成员.
     * 有序集成员按 score 值递增(从小到大)次序排列
     * @param min
     * @param max
     * @return
     */
    public Set<T> zrangeByScore(double min,double max){

        if(key instanceof String){
            return (Set<T>) getClient().zrangeByScore((String)key,min,max);
        }else{
            return (Set<T>) getClient().zrangeByScore((byte[])key,min,max);
        }
    }

    /**
     * 返回有序集 key 中，所有 score 值介于 min 和 max 之间(包括等于 min 或 max )的成员。
     * 有序集成员按 score值递增(从小到大)次序排列。
     * LIMIT 参数指定返回结果的数量及区间(就像SQL中的 SELECT LIMIT offset, count )，
     * 注意当 offset 很大时，定位 offset
     * 的操作可能需要遍历整个有序集，此过程最坏复杂度为 O(N) 时间。
     * @param min
     * @param max
     * @param offset
     * @param count
     * @return
     */
    public Set<T> zrangeByScore(double min,double max,int offset,int count){
        if(key instanceof String){
            return (Set<T>) getClient().zrangeByScore((String)key,min,max,offset,count);
        }else{
            return (Set<T>) getClient().zrangeByScore((byte[])key,min,max,offset,count);
        }
    }

    /**
     * 移除有序集 key 中的一个或多个成员，不存在的成员将被忽略
     * @param members
     * @return
     */
    public Long zrem(String ... members){
        return  getClient().zrem((String)key,members);
    }
    public Long zrem(byte[] ... members){
        return getClient().zrem((byte[])key,(byte[][])members);
    }

    /**
     * 返回有序集 key 中，成员 member 的 score 值。
     * 如果key不存在或member不是有序集的成员，则返回null
     * @param member
     * @return
     */
    public Double zscore(T member){
        if(key instanceof String){
            return  getClient().zscore((String)key,(String)member);
        }else{
            return getClient().zscore((byte[])key,(byte[])member);
        }
    }
    /**
     * 移除有序集 key 中，指定排名(rank)区间内的所有成员.
     *
     * 下标从0开始，以 -1 表示最后一个成员， -2 表示倒数第二个成员，以此类推
     * @param start
     * @param end
     * @return
     */
    public Long zremrangeByRank(long start,long end){
        if(key instanceof String){
            return  getClient().zremrangeByRank((String)key,start,end);
        }else{
            return getClient().zremrangeByRank((byte[])key,start,end);
        }
    }
    /**
     * 移除有序集 key 中，所有 score 值介于 min 和 max 之间(包括等于 min 或 max )的成员
     * @param start
     * @param end
     * @return
     */
    public Long zremrangeByScore(double start,double end){
        if(key instanceof String){
            return  getClient().zremrangeByScore((String)key,start,end);
        }else{
            return getClient().zremrangeByScore((byte[])key,start,end);
        }
    }

}
