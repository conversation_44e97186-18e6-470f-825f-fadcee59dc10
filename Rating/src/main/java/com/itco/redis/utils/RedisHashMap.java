package com.itco.redis.utils;

import com.ctg.itrdc.cache.vjedis.jedis.ScanParams;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * hash类型操作
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-03
 * @Time 8:54
 */
public class RedisHashMap<T> extends GeneralKeyFun{
    public RedisHashMap(T key){
        super(key);
    }

    /**
     * 删除哈希表 key 中的一个或多个指定域，不存在的域将被忽略
     * @param fields
     * @return
     */
    public Long hdel(String...fields){
        return getClient().hdel((String)key,fields);
    }
    public Long hdel(byte[]...fields){
        return getClient().hdel((byte[])key,fields);
    }
    /**
     * 查看哈希表 key 中，给定域 field 是否存在。如果哈希表不含给定域或key不存在，则返回false
     * @param field
     * @return
     */
    public Boolean hExists(T field){
        if(key instanceof String){
            return getClient().hexists((String)key,(String)field);
        }else{
            return getClient().hexists((byte[])key,(byte[])field);
        }
    }

    /**
     * 返回哈希表 key 中给定域 field 的值。当给定域不存在或是给定 key 不存在时返回null
     * @param field
     * @return
     */
    public T get(T field){
        if(key instanceof String){
            return (T) getClient().hget((String)key,(String)field);
        }else{
            return (T) getClient().hget((byte[])key,(byte[])field);
        }
    }
    /**
     * 返回哈希表 key 中，所有的域和值。当key不存在时返回空哈希表
     * @return
     */
    public Map<T,T> getAll(){
        if(key instanceof String){
            return (Map<T,T> ) getClient().hgetAll((String)key);
        }else{
            return (Map<T,T> ) getClient().hgetAll((byte[])key);
        }
    }
    /**
     * 返回哈希表 key 中的所有域。当 key 不存在时，返回一个空集合
     * @return
     */
    public Set<T> keys(){
        if(key instanceof String){
            return (Set<T> ) getClient().hkeys((String)key);
        }else{
            return (Set<T>) getClient().hkeys((byte[])key);
        }
    }
    /**
     * 返回哈希表 key 中域的数量。当 key 不存在时，返回0
     * @return
     */
    public Long length(){
        if(key instanceof String){
            return  getClient().hlen((String)key);
        }else{
            return  getClient().hlen((byte[])key);
        }
    }
    /**
     * 返回哈希表 key 中，一个或多个给定域的值. 如果给定的域不存在于哈希表，那么返回一个 null 值
     * @param fields
     * @return
     */
    public List<T> get(T...fields){
        if(key instanceof String){
            return (List<T>) getClient().hmget((String)key,(String[])fields);
        }else{
            return (List<T>) getClient().hmget((byte[]) key,(byte[][])fields);
        }
    }
    /**
     * 同时将多个 field-value (域-值)对设置到哈希表 key 中
     * @param hash
     * @return
     */
    public T set(Map<T,T> hash){
        if(key instanceof String){
            return (T) getClient().hmset((String)key,(Map<String,String>)hash);
        }else{
            return (T) getClient().hmset((byte[]) key,(Map<byte[], byte[]>)hash);
        }
    }
    /**
     * 设值
     * @param field
     * @param value
     * @return
     */
    public Long set(T field,T value){

        if(key instanceof String){
            return  getClient().hset((String)key,(String)field,(String)value);
        }else{
            return  getClient().hset((byte[]) key,(byte[]) field,(byte[]) value);
        }
    }

    /**
     * 将哈希表 key 中的域 field 的值设置为 value ，当且仅当域 field 不存在。如果给定域已经存在且没有操作被执行，返回 0
     * @param field
     * @param value
     * @return
     */
    public Long setNx(T field,T value){

        if(key instanceof String){
            return  getClient().hsetnx((String)key,(String)field,(String)value);
        }else{
            return  getClient().hsetnx((byte[]) key,(byte[]) field,(byte[]) value);
        }
    }
    /**
     * 为哈希表 key 中的域 field 的值加上增量 increment.
     * 本操作的值被限制在 64 位(bit)有符号数字表示之内.
     * @param field
     * @param value
     * @return
     */
    public Long incrBy(T field,Long value){

        if(key instanceof String){
            return  getClient().hincrBy((String)key,(String)field,value);
        }else{
            return  getClient().hincrBy((byte[]) key,(byte[]) field,value);
        }
    }

    /**
     * 返回哈希表 key 中所有域的值
     * @return
     */
    public List<T> valS(){
        if(key instanceof String){
            return (List<T>) getClient().hvals((String)key);
        }else{
            return (List<T>) getClient().hvals((byte[]) key);
        }
    }

    /**
     * 游标从0开始，当服务器返回游标0时表示此次迭代结束
     * @param cursor
     * @return  ScanResult<Map.Entry<T, T>>
     */
    public Object scan(T cursor){
        if(key instanceof String){
            return getClient().hscan((String)key,(String)cursor);
        }else{
            return getClient().hscan((byte[]) key,(byte[])cursor);
        }
    }
    /**
     * 游标从0开始，当服务器返回游标0时表示此次迭代结束
     * @param cursor
     * @param params
     * @return  ScanResult<Map.Entry<T,T>>
     */
    public Object scan( T cursor, ScanParams params){
        if(key instanceof String){
            return getClient().hscan((String)key,(String)cursor,params);
        }else{
            return getClient().hscan((byte[]) key,(byte[])cursor,params);
        }
    }
}
