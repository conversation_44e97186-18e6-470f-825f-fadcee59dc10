package com.itco.redis.utils;

import java.util.Set;

/**
 * set操作
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-02
 * @Time 17:15
 */
public class RedisSet<T> extends GeneralKeyFun{
    public RedisSet(T key){
        super(key);
    }

    /**
     * 将一个或多个 members 元素加入到集合 key 当中，
     * 已经存在于集合的 members 元素将被忽略。
     * 返回被添加到集合中的新元素的数量，不包括被忽略的元素
     * @param members
     * @return
     */
    public Long sadd(T ... members){
        if(key instanceof String){
            return getClient().sadd((String)key,(String[])members);
        }else{
            return getClient().sadd((byte[])key,(byte[][])members);
        }
    }

    /**
     * 返回集合 key 的基数(集合中元素的数量)。当key不存在时返回0
     * @return
     */
    public Long scard(){
        if(key instanceof String){
            return getClient().scard((String)key);
        }else{
            return getClient().scard((byte[])key);
        }
    }
    /**
     * 判断 member 元素是否集合 key 的成员。如果 member 元素是集合的成员，返回 true；如果 member 元素不是集合的成员，或 key 不存在，返回 false
     * @param member
     * @return
     */
    public Boolean sismember(T member){
        if(key instanceof String){
            return getClient().sismember((String)key,(String)member);
        }else{
            return getClient().sismember((byte[])key,(byte[])member);
        }
    }

    /**
     * 返回集合 key 中的所有成员
     * @return
     */
    public Set<T> smembers(){
        if(key instanceof String){
            return (Set<T>) getClient().smembers((String)key);
        }else{
            return (Set<T>) getClient().smembers((byte[])key);
        }
    }
    /**
     * 移除集合 key 中的一个或多个 members 元素，
     * 不存在的 members 元素会被忽略。返回被成功移除的元素的数量，不包括被忽略的元素
     * @param members
     * @return  删除的元素数量
     */
    public Long srem(T ... members){
        if(key instanceof String){
            return getClient().srem((String)key,(String[])members);
        }else{
            return getClient().srem((byte[])key,(byte[][])members);
        }
    }

}
