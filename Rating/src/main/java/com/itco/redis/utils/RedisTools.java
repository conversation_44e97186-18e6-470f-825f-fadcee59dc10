package com.itco.redis.utils;

import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RedisTools {

    public static VProxyJedis jedis=null ;

    public static void  initPool(){

    }
    public  static  VProxyJedis  getJedis(String dbName) throws Exception{
        if(jedis==null) {
            CtgVJedisPool pool = RedisUtil.getPool(dbName);
            jedis = pool.getResource();
        }
        return jedis ;
    }

    public static  void close(VProxyJedis  jedis){
        if(jedis!=null)
            jedis.close();
    }

    /**
     * 获取hash中key键对应的map值
     * @param jedis
     * @param key
     * @return
     */
    public static Map<String,String> getHmkey(VProxyJedis jedis,String key){
        Map<String,String> mm=(HashMap)jedis.hgetAll("abcd1");
        return mm ;
    }

    /**
     * 获取hash中key键对应的field的值
     * @param jedis
     * @param key
     * @param field
     * @return
     */
    public static String getHkeyValue(VProxyJedis jedis,String key,String field){
        String vv=jedis.hget(key,field);
        return vv;
    }

    /**
     * 获取hash中key键对应的多个field的值
     * @param jedis
     * @param key
     * @param fields
     * @return
     */
    public static List<String> getHkeysValue(VProxyJedis jedis, String key, String... fields){
        List<String> list1=jedis.hmget(key,fields);
        return list1;
    }



}
