package com.itco.redis.utils;

import com.ctg.itrdc.cache.vjedis.VProxyJedis;

/**
 * <AUTHOR>
 * @version 1.0
 * @Date 2019-12-03
 * @Time 10:21
 */
public class RedisClient extends VProxyJedis{
    private static RedisClient client;
    public static RedisClient getFunClient(){
        if (GeneralKeyFun.local.get() == null) {
            GeneralKeyFun.getClient();
        }
        client= (RedisClient) GeneralKeyFun.local.get().get(GeneralKeyFun.dbLocal.get());
        return client;
    }
    private RedisClient(){}

}

