package com.itco.rating.mapper;

import com.itco.rating.entity.AccuPayOutBean;
import com.itco.rating.entity.AccuUseInfoBean;
import com.itco.rating.entity.UploadDataBean;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface UploadDataMapper {

    @Select("select id,upload_sql as uploadSql,key_suffix as keySuffix,key_field as keyField,data_field as dataField,score_field as scoreField,key_type as keyType,database_name as databaseName from upload_table where is_enabled=1 and upload_type=#{uploadType} order by id")
    List<UploadDataBean> getUploadToCache(int uploadType) ;

    @Select("select object_id,object_type,object_role  from upload_table_ticket_config where event_class=#{eventClass} order by order_id")
    List<Map<String,Object>> listTicketFieldConfig(int eventClass);

    @Select("select t.sys_var_value from sys_config t where t.sys_var='RATING_PROCESS'")
    String getRatingProcessType();

    @Select("select t.sys_var_value from sys_config t where t.sys_var='PROJECT_NAME'")
    String getProjectName();

    @Update("update upload_table set is_enabled=0 where 1=1 and is_enabled=#{isEnabled}")
    int updateUploadTableData(@Param("isEnabled") int isEnabled);

    @Insert("insert into accu_key_tab(key_id,key_value) values(#{key_id},#{value_id})")
    int insertAccuKeyTab(Map<String,Object> map) ;

    @Update("update accu_key_tab set key_value=#{value_id} where key_id=#{key_id}")
    int updateAccuKeyTab(Map<String,Object> map) ;

    @Insert("insert into accu_payout(accu_payout_id,accu_id,payout_type,payout_id,prod_inst_id,obj_id,op_value,remark,status_cd,create_staff,update_staff,create_date,update_date,status_date)  values(#{accuPayoutId},#{accuId},#{payoutType},#{payoutId},#{prodInstId},#{objId},#{opValue},#{remark},#{statusCd},#{createStaff},#{updateStaff},now(),now(),now())")
    int insertAccuPayoutTab(AccuPayOutBean bean) ;

    @Insert("insert into  accu_use_key_tab(accu_use_id_key,accu_use_id_key_value,billcycle_id) values(#{key_id},#{value_id},,#{billcycle_id})")
    int insertAccuUseKeyTab(Map<String,Object> map) ;

    @Insert("insert into prod_inst_accu_use_info(accu_use_id,accu_id,obj_id,offer_inst_id,billing_cycle_id,usage_amount,usage_voice,usage_sms,status_cd)  values(#{accuUseId},#{accuId},#{objId},#{offerInstId},#{billingCycleId},#{usageAmount},#{usageVoice},#{usageSms},#{statusCd})")
    int insertProdInstAccuUseInfo(AccuUseInfoBean bean);

    @Update("update prod_inst_accu_use_info set usage_amount=#{usage_amount},update_date=now()  where accu_use_id=#{accu_use_id}")
    int updateProdInstAccuUseInfo(Map<String,Object> map);

    @Select("${data}")
    List<Map<String,Object>> listParamSqlData(String data);


    @Insert("insert into loadconfig_log(operate_time,deal_rule_active,load_type,is_success,range_id) values(now(),#{dealRuleActive},#{loadType},#{isSuccess},#{rangeId})")
    int insertLoadConfigLog(Map<String,Object> map);

    @Select("select ref_value_id as id,refvalue as data,(select d.value_type from pricing_ref_value d where d.ref_value_id=t.ref_value_id limit 1) value_type  from data.Ref_value_invented t where batch_id=#{batchId}")
    List<Map<String,Object>> listRefValueData(long batchId);

}
