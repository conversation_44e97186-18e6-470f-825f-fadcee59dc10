package com.itco.rating.dao;

import com.alibaba.fastjson.JSONObject;

import com.itco.cumulant.record.BaseDataHandler;
import com.itco.entity.function.FunctionPerl;
import com.itco.rating.entity.ProcessParamBean;
import com.itco.rating.entity.ProcessResultBean;
import java.util.List;
import java.util.Map;


public interface RatingProcess {
    public static final String RefValueTypeString="1" ;    //-  参考值类型-1--常量；
    public static final String RefValueTypeParam="2" ;     //-  参考值类型- 2--内部参数；
    public static final String RefValueTypeObject="3"  ;   //-  参考值类型- 3--参考对象
    public static final String RefValueTypeVar="4"  ;      //-  参考值类型- 4--中间变量
    public static final String RefValueTypeCalc="8" ;      //-- 参考值类型- 8--运算参考值

    public static final String PropertyType="5" ;            //--5--内部属性值
    public static final String ExternPropertyType="4" ;      //--4--外部属性 函数
    public static final String WIDTHTABLEPropertyType="3" ;  //--3--外部属性 宽表
    public static final String CACHEFUNCTIONPropertyType="6" ;  //--6--参考值


    public static final String NoConditionSectionType="1" ;    //---无条件段落类型
    public static final String ConditionSectionType="5" ;     //---有条件段落类型

    public static final String OPERATOR_ADD="2" ;        //----- +  加
    public static final String OPERATOR_MINU="3" ;       //----- -  减
    public static final String OPERATOR_MULTIPLY="4" ;   //----- *  乘
    public static final String OPERATOR_DIVIDE="5" ;     //----- /  除

    public static enum PlanType {   //----定价计划类型
        FIRST("类型1", 10), SECOND("类型2", 20), THREE("类型3", 30) ;
        // 成员变量
        private String name;
        private int index;
        // 构造方法
        private PlanType(String name, int index) {
            this.name = name;
            this.index = index;
        }
        // 普通方法
        public static String getName(int index) {
            for (PlanType c : PlanType.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static PlanType getByValue(int index) {
            for (PlanType c : PlanType.values()) {
                if (c.getIndex() == index) {
                    return c;
                }
            }
            return null;
        }

        // get set 方法
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public int getIndex() {
            return index;
        }
        public void setIndex(int index) {
            this.index = index;
        }
    }


    public void setJsonObject(JSONObject jsonObject) ;

    public String batchProcess(ProcessParamBean processParamBean) throws Exception ;


    public Map<String,String> getPricingPlan(String pricingplanId) throws Exception ;

    public String getPricingPlan(String pricingplanId, String fieldName) throws Exception ;

    public Map<String,String> getEventPricingStrategyData(String eventPricingStrategyId) throws Exception;

    public String getEventPricingStrategyData(String eventPricingStrategyId, String field) throws Exception;

    public Map<String,String> getPricingCombineData(String pricingCombineId) throws Exception;

    public String getPricingCombineData(String pricingCombineId, String field) throws Exception;

    public void dealPricingTariff(ProcessResultBean resultBean, List<String> tariffList, String pricingPlanId) throws Exception ;

    public void dealPricingSection(ProcessResultBean resultBean, String pricingPlanId,String parentSectionId, String eventTypeId) throws Exception;

  //  public Map<String,String> getPricingRefIdValue(String refValueId,ProcessResultBean resultBean) throws Exception ;
    public String getPricingRefIdValue(String refValueId,ProcessResultBean resultBean) throws Exception ;

    public List<String> getPricingCombineWithPlanId(String pricingplanId) throws Exception;

    public List<String> getPricingParentSection(String eventStrategyId) throws Exception;

    public List<String> getChildSection(String parentSectionId) throws Exception;

    public String getPricingSectionData(String pricingSectionId, String field) throws Exception;

    public Map<String,String> getPricingSectionData(String pricingSectionId) throws Exception;

    public List<String> getPricingSectionWithRuleData(String pricingSectionId) throws Exception;

    public String getPricingRuleData(String pricingRuleId, String field) throws Exception ;

    public Map<String,String> getPricingRuleData(String pricingRuleId) throws Exception;

    public Map<String,String> getTariffData(String tariffId) throws Exception;

    public List<String> getPricingSectionWithTariffData(String sectionId) throws Exception;

    public Map<String,String> getPricingRefValueData(String refValueId) throws Exception;

    public  String getPricingRefValueData(String refValueId, String field) throws Exception;

    public Map<String,String> getPricingRefObjectData(String refObjectId) throws Exception;

    public String getPricingRefObjectData(String refObjectId, String field) throws Exception;

    public Map<String,String> getPricingParamDefineData(String pricingParamId) ;

    public List<String> getTariffWithAccuTypeData(String tariffId) ;

    public Map<String,String> getAccuTypeData(String accuTypeId) throws Exception ;

    public List<String> getEnumParamValueWithParamIdData(String pricingParamId) ;

    public Map<String,String> getAccumulationData(String accuId) ;

    public Map<String,String> getAccuPayoutData(String accuPayoutId) ;

    public List<String> getAccuWithAccuPayoutData(String accuId) ;

    public Map<String,String> getProdInstAccuUseInfoData(String accuUseId) ;

    public List<String> getAccuWithAccuUseInfoData(String accuId) ;

    public Map<String,String> getTprResourceAttr(String attrId) throws Exception;

    public String  getTprResourceAttr(String attrId, String field) throws Exception;

    public void getchildsectionId(List<String> allList, String parentSectionId) throws Exception;

    public List<String> getPricingCalcRefValue(String pricingRefvalueId) throws Exception ;

    public String getFuctionValue(String billingcycle, String keyattrvalue, String attr_id) throws Exception ;

    public boolean dealAccuPayout(Map<String, String> map) throws Exception;

    public long dealProdInstAccuUseInfo(Map<String, String> map) throws Exception;

    public boolean dealTariffWithAccuTypeId(Map<String, String> map) throws Exception;

    public String getTariffData(String tariffId, String field) throws Exception;

    public Map<String,String> getRatableEventTypeData(String eventTypeId) throws Exception;

    public String getRatableEventTypeData(String eventTypeId, String field) throws Exception;
    public Map<String,String> getPricingRefIdValue(String refValueId) throws Exception ;
    public String getPricingRefValueById(String refValueId) throws Exception ;
    public boolean uploadToCache(int uploadType,int rangeId,int threadNum) throws Exception;
    public List<String> getVariableRefValue(String sectionId) throws Exception;
    public Map<String,Object>  getPricingPlanWithTicket(List<String> ticketList,String timeStamp) throws Exception ;
    public  Map<Integer, FunctionPerl> getmPerl();
    public List<Map<String,Object>> getRefValueInvented(String batchId) ;
    public List<String> getPricingOfferInstanceWithAppId(String appId) throws Exception ;
    public Map<String,String> getPricingOfferInstance(String offerInstanceId) throws Exception ;
}
