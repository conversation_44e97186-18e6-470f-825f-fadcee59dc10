package com.itco.rating.dao.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.itco.CalcFunction.CalcFunctionLocal;
import com.itco.CalcFunction.CalcVisualFunctionLocal;
import com.itco.entity.common.Result;
import com.itco.entity.function.FunctionPerl;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.rating.dao.RatingProcess;
import com.itco.rating.entity.AccuPayOutBean;
import com.itco.rating.entity.DataResultBean;
import com.itco.rating.entity.ProcessParamBean;
import com.itco.rating.entity.ProcessResultBean;
import com.itco.rating.service.UploadTableService;
import com.itco.rating.util.*;
import com.itco.redis.utils.RedisList;
import com.itco.rulefunction.ctgcache.CShmOpt;
import com.itco.rulefunction.ctgcache.TableService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import static com.itco.RatingApplication.baseDataHandler;

/**
 * 处理批价
 */
@Component
@Repository
public class RatingProcessImpl implements RatingProcess {
    static Log log = LogFactory.getLog(RatingProcessImpl.class);
    @Autowired
    UploadTableService uploadTableService;
    private static RatingProcessImpl ratingProcess;
    String billingcycle, feeCycle, dealCycleId, appId, apiId, requestId, timeStamp, ticketType, eventstrategyId, expCycleId, startTime, planIdAll;
    String planIdBybj, eventstrategyIdBybj;
    public JSONObject jsonObject;

    public void setJsonObject(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
    }

    public VProxyJedis jedis = null, pre_redis = null;
    TableService tableService = null;
    CalcFunctionLocal calcFunction = null;
    // CalcVisualFunctionLocal calcVisualFunctionLocal=null;
    List<AccuPayOutBean> accuPayoutList;
    Map<String, Map> variableMp, refIdValueMp;   //---存放中间变量的值 , 参考值id和值
    List<Long> settleObjectIdList = new ArrayList<>();          //--- settleObjectId集合
    List<Long> settleObjectTypeList = new ArrayList<>();        //--- settleObjectType集合
    List<Long> settleObjectRoleList = new ArrayList<>();        //--- settleObjectRole集合
    String ticketField = null, allCharge = null;
    byte resettleFlag = 0;                                     //---补结标识（0--不补结；1---补结）
    int uploadType;                                          //---1=正常批价；2=试算；3=预计费
    int threadNum;                                          //---进程号
    String batchId = null, eventClass = null;                      //---batchId-试算的批次号
    List<Map<String, Object>> refValueInventList = null;      //--试算预设参考值和值的数据集
    String projectName = null;
    boolean trialPreValueFlag; //--是否有预设值

    @PostConstruct
    public void init() {
        ratingProcess = this;
        ratingProcess.uploadTableService = this.uploadTableService;
    }

    void print(String... params) {
        if (CalcRecordManager.getCommon().getIDebug() == 1) {
            for (int i = 0; i < params.length; i++) {
                System.out.print(params[i]);
            }
            System.out.println("");
        }
    }

    /**
     * 批价处理程序
     *
     * @param processParamBean
     * @return
     * @throws Exception
     */
    @Override
    public String batchProcess(ProcessParamBean processParamBean) throws Exception {
        this.jsonObject = processParamBean.getJsonObject();
        this.jedis = processParamBean.getJredis();
        this.pre_redis = processParamBean.getPre_redis();
        this.tableService = processParamBean.getTableService();
        this.calcFunction = processParamBean.getCalcFunction();
        //    this.calcVisualFunctionLocal=processParamBean.getCalcVisualFunctionLocal() ;
        String result, effDate, expDate, eventPricingStrategyType;
        String errorMsg = null;
        List<String> schemePlans = null;
        resettleFlag = 0;
        double charge = 0d, charge1 = 0d;
        accuPayoutList = new ArrayList<AccuPayOutBean>();
        ProcessResultBean bean = null;
        List<Map<String, Object>> ticketList = null;
        List<String> CombineIds, parentSectionIds;
        List<Long> pricingPlanList = new ArrayList<>();     //---定价计划标识集合
        List<Long> tariffList = new ArrayList<>();          //---资费标识集合
        List<Long> eventStrategyList = new ArrayList<>();   //---策略标识集合
        List<Long> acctItemTypeList = new ArrayList<>();    //---帐目类型标识集合
        List<Integer> feeCycleIdList = new ArrayList<>();
        List<Integer> billingCycleIdList = new ArrayList<>();
        List<Integer> expCycleIdList = new ArrayList<>();
        List<Integer> ifDeductList = new ArrayList<>();
        List<Long> bereauIdList = new ArrayList<>();
        settleObjectIdList = new ArrayList<>();          //--- settleObjectId集合
        settleObjectTypeList = new ArrayList<>();        //--- settleObjectType集合
        settleObjectRoleList = new ArrayList<>();        //--- settleObjectRole集合
        String strategyId, eventTypeId;
        List<Double> tariffCharges = new ArrayList<>();     //---资费1金额集合
        List<Double> tariffCharges1 = new ArrayList<>();     //---资费2金额集合
        Map<String, String> planMp = null;
        DataResultBean dataResultBean;
        String statusCd = null;
        StringBuffer errorMsgStr = new StringBuffer();
        StringBuffer errorMsgStrTemp = null;
        int errorType = 0;       //---错误信息编码
        Map<String, Object> errorMsgMp = new HashMap<String, Object>();
        Map<String, Object> errorMsgMpByPlan = new HashMap<String, Object>();
        List<String> correctList = new ArrayList<>();
        try {
            projectName = MemoryCacheUtils.getData("ProjectName");      //---项目工程代码(TPSS,ISS)
            ticketType = jsonObject.getString("ticket_type");          //---话单类型
            errorMsg = jsonObject.getString("error_msg");              //---错误信息
            errorMsgStr.append(errorMsg);
            if ("1".equals(ticketType)) {           //--异常单和other单 不处理                                          //----异常单,不要处理流程
                writeAbortMessage(jsonObject);   //-----写入异常所需的信息
                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                return result;
            } else if ("2".equals(ticketType)) {
                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                return result;
            }
            eventClass = jsonObject.getString("event_class");          //---区分策略（1-实时或2-周期)
            if (eventClass == null || "".equals(eventClass))
                throw new DBExeption.BadExecution("话单event_class没有配置！{10014}");
            else if (eventClass.equals("1") || eventClass.equals("2")) {
                if ("1".equals(eventClass))        //---实时
                    ticketList = MemoryCacheUtils.getData("ticketList1");    //--实时-匹配的话单字段
                else if ("2".equals(eventClass))    //---周期
                    ticketList = MemoryCacheUtils.getData("ticketList2");    //--周期-匹配的话单字段
            } else {
                throw new DBExeption.BadExecution("话单event_class:" + eventClass + " 配置有误！{10024}");
            }

            if (ticketList == null || ticketList.size() == 0)
                throw new DBExeption.BadExecution("话单event_class:" + eventClass + " upload_table_ticket_config表数据没有加载到内存！{10025}");
            schemePlans = getSchemePlan(ticketList);        //---获取方案配置表中的字段组合值
            if (null == schemePlans || schemePlans.size() == 0)
                throw new DBExeption.BadExecution("upload_table_ticket_config表数据没有加载内存或者配置有问题{10026}");
            if (uploadType == 2) {
                batchId = jsonObject.getString("cdr_batch_id");                //---试算批次号
                if (batchId != null && !"".equals(batchId)) {
                    refValueInventList = getRefValueInvented(batchId);
                }
            }
            String event_type_id = jsonObject.getString("event_type_id");       //---事件类型
            billingcycle = jsonObject.getString("billing_cycle_id");              //---帐期

            // 福建佣金，档案封存导致回退回收重跑的单子异常，改造。 2023年3月3日
            if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                if (jsonObject.containsKey("redo_flag")) {
                    String redo_flag = jsonObject.getString("redo_flag");
                    int batchIdTmp = 0;
                    if (jsonObject.containsKey("cdr_batch_id") && !StringUtils.isEmpty(jsonObject.getString("cdr_batch_id"))) {
                        batchIdTmp = Integer.parseInt(jsonObject.getString("cdr_batch_id"));
                    }
                    if ("11".equals(redo_flag) || "13".equals(redo_flag) || "21".equals(redo_flag) || "22".equals(redo_flag) || batchIdTmp > 0) {
                        if ("1".equals(eventClass)) {   //---实时
                            billingcycle = jsonObject.getString("deal_cycle_id");              //---帐期
                        } else if ("2".equals(eventClass)) {
                            // 周期使用实时的账期月偏移。
                        }
                    }
                }
            }

            if ("DIGIT".equals(CalcRecordManager.getCommon().getSSystem()) && "010".equals(CalcRecordManager.getCommon().getSLatnId())) {
                if(!baseDataHandler.getAccumulation(jsonObject)){
                    writeAbortMessage(jsonObject);   //-----写入异常所需的信息
                    Factory.setTicketTypeAbn(jsonObject, 30020, "ticket_start_time 超出合同有效期，无可用累计量");
                    result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                    return result;
                }
            }

            feeCycle = jsonObject.getString("fee_cycle_id");                     //---帐期
            dealCycleId = jsonObject.getString("deal_cycle_id");
            expCycleId = jsonObject.getString("exp_cycle_id");                    //--失效账期
            requestId = jsonObject.getString("service_control_id");               //---请求序列
            timeStamp = jsonObject.getString("finish_time");                      //---时间戳
            startTime = jsonObject.getString("start_time");                      //---结算用的
            allCharge = jsonObject.getString("charge");                          //---charge
            if ((timeStamp == null || "".equals(timeStamp)) && (startTime == null || "".equals(startTime))) {
                writeAbortMessage(jsonObject);   //-----写入异常所需的信息
                Factory.setTicketTypeAbn(jsonObject, 10027, "话单字段finish_time或start_time是空的！");
                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                return result;
            }

            if (timeStamp == null || "".equals(timeStamp) || "0".equals(timeStamp))
                timeStamp = startTime;
            //-----------------------------------------------------//
            int operationFlag = 0;
            Map<Integer, Object> propMap = new HashMap<Integer, Object>();
            bean = new ProcessResultBean();
            //-----批价处理资费的方式（0-是只出一笔；1是一个策略只出一笔；2是一个定价计划出一笔；3是不限制条件能出几笔算几笔）--//
            operationFlag = calculatePricingPlanType();  //---获取批价处理资费的方式
            print("处理资费方式：" + operationFlag + "，（0-是只出一笔；1是一个策略只出一笔；2是一个定价计划出一笔；3是不限制条件能出几笔算几笔）");
            //------获取定价计划ID-列表--------//
            Map<String, Object> pricingPlanMp = getPricingPlanWithTicket(schemePlans, timeStamp);
            if (pricingPlanMp != null && !pricingPlanMp.isEmpty()) {
                if (pricingPlanMp.get("planIdList") == null) {
                    String error = pricingPlanMp.get("error").toString();
                    // errorType = Integer.parseInt(pricingPlanMp.get("errorType").toString());
                    if (errorMsgStr.length() > 0)
                        errorMsgStr.append(",").append(error);
                    else
                        errorMsgStr.append(error);
                    writeAbortMessage(jsonObject);   //-----写入异常所需的信息----
                    if (errorMsgStr.length() > 0)
                        Factory.setTicketTypeAbn(jsonObject, 10009, errorMsgStr.toString() + ",pricing_offer_instance方案表中匹配不到定价计划,其组合的值：" + schemePlans.toString());
                    else
                        Factory.setTicketTypeAbn(jsonObject, 10009, "pricing_offer_instance方案表中匹配不到定价计划其组合的值：" + schemePlans.toString());

                    result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                    return result;
                }
                List<String> pricingPlanIds = (List<String>) pricingPlanMp.get("planIdList");
                List<String> tmpPricingPlanIds = new ArrayList<>();
                print("定价计划方案列表：" + pricingPlanIds);

                Map<String, Object> planIdMp = (Map<String, Object>) pricingPlanMp.get("planIdForTicket");
                refIdValueMp = new HashMap<>();    //---存放取过的参考值
                if (refValueInventList != null) {
                    refIdValueMp = StringUtil.getRefValueListToMap(refValueInventList);
                }
                int ii = 0;    //---设置初始值
                for (String pricingPlanId : pricingPlanIds) {
                    planMp = getPricingPlan(pricingPlanId);
                    if (null == planMp) {
                        errorMsgStrTemp = new StringBuffer();
                        errorMsgStrTemp.append("定价计划id=").append(pricingPlanId).append(" 不存在！");
                        errorMsgMpByPlan.put(pricingPlanId, errorMsgStrTemp.toString());
                        errorType = 10001;
                        print("定价计划id= " + pricingPlanId + " 不存在！");
                        continue;
                    }
                    statusCd = planMp.get("status_cd");   //---定价计划状态
                    if (uploadType == 2) {  //---试算
                        if ("1400".equals(statusCd)) {   //---注销状态
                            errorMsgStrTemp = new StringBuffer();
                            errorMsgStrTemp.append("定价计划id=").append(pricingPlanId).append(" 状态是注销状态！");
                            errorMsgMpByPlan.put(pricingPlanId, errorMsgStrTemp.toString());
                            errorType = 10010;
                            print("定价计划id= " + pricingPlanId + " 状态是注销状态！");
                            continue;
                        }
                    } else {
                        if (!"1000".equals(statusCd)) {
                            errorMsgStrTemp = new StringBuffer();
                            errorMsgStrTemp.append("定价计划id=").append(pricingPlanId).append(" 状态不是在用状态！");
                            errorMsgMpByPlan.put(pricingPlanId, errorMsgStrTemp.toString());
                            errorType = 10010;
                            print("定价计划id= " + pricingPlanId + " 状态不是在用状态！");
                            continue;
                        }
                    }
                    tmpPricingPlanIds.add(pricingPlanId);
                    ii++;
                }
                if (ii == 0) {  //---所有的定价计划都不符合要求
                    print("所有的定价计划都不符合要求! ");
                    Factory.setTicketTypeAbn(jsonObject, errorType, errorMsgStrTemp.toString());
                    result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                    return result;
                }
                ii = 0;    //---设置初始值
                errorMsgStrTemp = null;

                for (String pricingPlanId : tmpPricingPlanIds) {
                    print("【处理定价计划：" + pricingPlanId + "】");
                    planIdAll = pricingPlanId;
                    ticketField = (String) planIdMp.get(pricingPlanId);
                    //System.out.println("ticketField:" + ticketField);
                    //-------根据定价计划id来查出组合id-------//
                    CombineIds = getPricingCombineWithPlanId(pricingPlanId);
                    print("根据定价计划id来查出组合id：" + CombineIds);
                    if (CombineIds != null) {
                        for (String CombineId : CombineIds) {
                            trialPreValueFlag = false;
                            variableMp = new HashMap<>();
                            if (refValueInventList != null && refValueInventList.size() > 0) {
                                variableMp = StringUtil.getRefValueListToMap(refValueInventList);
                                trialPreValueFlag = true;   //--有预设值
                            }
                            //-------获取策略id
                            eventstrategyId = strategyId = getPricingCombineData(CombineId, "event_pricing_strategy_id");
                            //----获取策略表中的事件类型id-----------//
                            eventTypeId = getEventPricingStrategyData(strategyId, "event_type_id");
                            print("当前策略id:" + eventstrategyId + "，策略表中的事件类型id:" + eventTypeId);
                            if (!event_type_id.equals(eventTypeId)) {       //---事件类型不一样，则跳过不处理
                                /*if (CalcRecordManager.getCommon().getSSystem().equals("ISS")){  //--结算要显示这个异常信息
                                     errorMsgStrTemp = new StringBuffer();
                                     errorMsgStrTemp.append("策略id:").append(strategyId).append(" 事件类型:").append(eventTypeId).append(" 跟话单event_type_id:").append(event_type_id).append("不符!");
                                     errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                     errorType = 10011;
                                }*/
                                //---佣金-太多了暂时禁掉----//
                                //print("策略表中的事件类型id:" + eventTypeId+" 跟话单event_type_id:"+event_type_id + "不一致");
                                continue;
                            }
                            print("策略表中的事件类型id:" + eventTypeId + " 跟话单event_type_id:" + event_type_id + "一致");
                            //------------------区分话单是实时或周期策略----------------------//
                            if (null == eventClass || "".equals(eventClass)) {
                                writeAbortMessage(jsonObject);   //-----写入异常所需的信息
                                if (errorMsgStr.length() > 0)
                                    Factory.setTicketTypeAbn(jsonObject, 10012, errorMsgStr.toString() + ",话单event_class是空的");
                                else
                                    Factory.setTicketTypeAbn(jsonObject, 10012, "话单event_class是空的");

                                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                                return result;
                            }
                            eventPricingStrategyType = getEventPricingStrategyData(strategyId, "event_pricing_strategy_type"); //---100(实时),200(周期)
                            if ("1".equals(eventClass)) {
                                if (!"100".equals(eventPricingStrategyType)) {
                                    errorMsgStrTemp = new StringBuffer();
                                    errorMsgStrTemp.append("策略id:").append(strategyId).append("其策略类型不是实时(100)");
                                    errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                    errorType = 10013;
                                    print("策略id:" + strategyId + "其策略类型不是实时(100)");
                                    continue;
                                }
                            } else if ("2".equals(eventClass)) {
                                if (!"200".equals(eventPricingStrategyType)) {
                                    errorMsgStrTemp = new StringBuffer();
                                    errorMsgStrTemp.append("策略id:").append(strategyId).append(",其策略类型不是周期(200)");
                                    errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                    errorType = 10013;
                                    print("策略id:" + strategyId + ",其策略类型不是周期(200)");
                                    continue;
                                }
                            }
                            //------------------------------------------------//
                            effDate = getEventPricingStrategyData(strategyId, "eff_date"); //---生效时间
                            expDate = getEventPricingStrategyData(strategyId, "exp_date"); //---失效时间
                            effDate = StringUtil.formatDate(effDate);
                            expDate = StringUtil.formatDate(expDate);
                            if ((timeStamp != null && !"".equals(timeStamp)) && (effDate != null && !"".equals(effDate)) && (expDate != null && !"".equals(expDate))) {
                                if (Long.parseLong(timeStamp.substring(0, 8)) < Long.parseLong(effDate.substring(0, 8))) {
                                    errorMsgStrTemp = new StringBuffer();
                                    errorMsgStrTemp.append("话单finish_time小于策略id:").append(strategyId).append("的eff_date");
                                    errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                    errorType = 10014;
                                    print("话单finish_time小于策略id:" + strategyId + "的eff_date");
                                    continue;
                                }
                                if (Long.parseLong(timeStamp.substring(0, 8)) >= Long.parseLong(expDate.substring(0, 8))) {
                                    errorMsgStrTemp = new StringBuffer();
                                    errorMsgStrTemp.append("话单finish_time大于等于策略id:").append(strategyId).append("的exp_date");
                                    errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                    errorType = 10014;
                                    print("话单finish_time大于等于策略id:" + strategyId + "的exp_date");
                                    continue;
                                }
                                if (Long.parseLong(effDate.substring(0, 8)) >= Long.parseLong(expDate.substring(0, 8))) {
                                    errorMsgStrTemp = new StringBuffer();
                                    errorMsgStrTemp.append("策略id:").append(strategyId).append("的eff_date大于等于exp_date");
                                    errorMsgMp.put(eventstrategyId, errorMsgStrTemp.toString());
                                    errorType = 10014;
                                    print("策略id:" + strategyId + "的eff_date大于等于exp_date");
                                    continue;
                                }
                            }
                            print("【处理定价计划->" + pricingPlanId + " 下的定价组合 :" + CombineId + " ,策略标识 :" + strategyId + ", 事件类型: " + eventTypeId + ", event_class: " + eventClass + "】");
                            bean.getEventStrategyStr().append("!" + strategyId);        //---运行过的策略标识（轨迹）

                            print("循环处理段落标识：" + strategyId);
                            //------根据策略ID,获取对应父段落id-------------//
                            parentSectionIds = getPricingParentSection(strategyId);
                            if (parentSectionIds != null) {
                                correctList.add(pricingPlanId + "_" + strategyId);    //---保存有进入段落的定价计划和策略标识
                                for (String parentSectionId : parentSectionIds) {
                                    print("【处理定价计划->" + pricingPlanId + ",处理段落标识:" + parentSectionId + "】");
                                    //-------段落类型id--(1--无条件段落;5--应用判断条件段落)--------//
                                    //------处理段落逻辑---------//
                                    dealPricingSection(bean, pricingPlanId, parentSectionId, event_type_id);
                                }
                            } else {
                                continue;    //--策略找不到父段落,则继续下一个策略
                            }
                            if (operationFlag == 1) {  //----1=>一个策略只出一笔
                                Object obj = bean.getChargeMp().get(strategyId);
                                if (obj != null) {
                                    dataResultBean = (DataResultBean) bean.getChargeMp().get(strategyId);
                                    getCalculateData(dataResultBean, pricingPlanId, ticketField, tariffCharges, tariffCharges1, pricingPlanList, tariffList, eventStrategyList, acctItemTypeList, feeCycleIdList, billingCycleIdList, expCycleIdList, ifDeductList, settleObjectIdList, settleObjectTypeList, settleObjectRoleList);
                                }
                            }

                        }
                    } else {
                        errorMsgStrTemp = new StringBuffer();
                        errorMsgStrTemp.append("定价计划id:").append(pricingPlanId).append(" 找不到组合表");
                        errorMsgMpByPlan.put(pricingPlanId, errorMsgStrTemp.toString());
                        errorType = 10002;
                        print("定价计划id: " + pricingPlanId + " 找不到组合表");
                    }
                    ii++;
                    if (operationFlag == 0) {  //-----0=>整个话单只出一笔
                        Object obj = bean.getChargeMp().get(pricingPlanId);
                        if (obj != null) {
                            dataResultBean = (DataResultBean) bean.getChargeMp().get(pricingPlanId);
                            charge = dataResultBean.getA_charge() == null ? 0 : Double.parseDouble(dataResultBean.getA_charge());
                            charge1 = dataResultBean.getA_charge1() == null ? 0 : Double.parseDouble(dataResultBean.getA_charge1());
                            if (charge == 0 && charge1 == 0) {
                                getCalculateData(dataResultBean, pricingPlanId, ticketField, tariffCharges, tariffCharges1, pricingPlanList, tariffList, eventStrategyList, acctItemTypeList, feeCycleIdList, billingCycleIdList, expCycleIdList, ifDeductList, settleObjectIdList, settleObjectTypeList, settleObjectRoleList);
                                propMap.clear();    //---清除掉之前的定价计划和资费
                                break;
                            } else {
                                propMap.put(PlanType.FIRST.getIndex(), dataResultBean);   //---保存一笔资费信息
                            }
                        }
                    } else if (operationFlag == 2) {   //----2=>一个定价计划只出一笔
                        //------------一条定价计划只产生一笔数据--------------//
                        Object obj = bean.getChargeMp().get(pricingPlanId);
                        if (obj != null) {
                            dataResultBean = (DataResultBean) bean.getChargeMp().get(pricingPlanId);
                            getCalculateData(dataResultBean, pricingPlanId, ticketField, tariffCharges, tariffCharges1, pricingPlanList, tariffList, eventStrategyList, acctItemTypeList, feeCycleIdList, billingCycleIdList, expCycleIdList, ifDeductList, settleObjectIdList, settleObjectTypeList, settleObjectRoleList);
                        }
                    }
                }   ///---定价计划循环层

                if (ii == 0) {  //---所有的定价计划都不符合要求
                    if (errorMsgStrTemp == null)
                        Factory.setTicketTypeAbn(jsonObject, 10063, "所有的定价计划都不符合要求！");
                    else
                        Factory.setTicketTypeAbn(jsonObject, errorType, errorMsgStrTemp.toString());
                    result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                    return result;
                }
            } else {
                writeAbortMessage(jsonObject);   //-----写入异常所需的信息
                if (errorMsgStr.length() > 0)
                    Factory.setTicketTypeAbn(jsonObject, 10009, errorMsgStr.toString() + ",APP组合值:" + schemePlans.toString() + "在方案表(pricing_offer_instance)中匹配不到定价计划");
                else
                    Factory.setTicketTypeAbn(jsonObject, 10009, "APP组合值:" + schemePlans.toString() + "在方案表(pricing_offer_instance)中匹配不到定价计划");

                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                return result;
            }

            if (propMap.size() > 0) {    //---整个话单只出一笔
                dataResultBean = (DataResultBean) propMap.get(PlanType.FIRST.getIndex());
                if (dataResultBean.getA_charge() != null)
                    tariffCharges.add(Double.parseDouble(dataResultBean.getA_charge()));    //---资费1金额 集合
                if (dataResultBean.getA_charge1() != null)
                    tariffCharges1.add(Double.parseDouble(dataResultBean.getA_charge1()));   //---资费2金额 集合
                tariffList.add(Long.parseLong(dataResultBean.getTariffId()));               //---资费标识 集合
                pricingPlanList.add(Long.parseLong(dataResultBean.getPricingPlanId()));      //---定价计划 集合
                acctItemTypeList.add(Long.parseLong(dataResultBean.getAcctItemTypeId()));     //---帐目类型 集合
                eventStrategyList.add(Long.parseLong(dataResultBean.getEventstrategyId()));
                ifDeductList.add(Integer.parseInt(dataResultBean.getIfDeductOldOwe()));
                feeCycleIdList.add(dataResultBean.getFeeCycleId());
                billingCycleIdList.add(dataResultBean.getBillingCycleId());
                expCycleIdList.add(dataResultBean.getExpCycleId());
            }
            if (operationFlag == 3) {      //----有多少笔出多少笔，没有限制------//
                tariffCharges.clear();
                tariffCharges1.clear();
                tariffCharges = bean.getChargeList();    //---资费1 金额
                tariffCharges1 = bean.getCharge1List();   //---资费2 金额
                tariffList = bean.getTariffList();
                pricingPlanList = bean.getPricingPlanList();
                //     pricingPlanList=bean.getPricingPlanList().stream().distinct().collect(Collectors.toList()); //--去重
                acctItemTypeList = bean.getAcctItemTypeList();
                eventStrategyList = bean.getStrategyList();
                feeCycleIdList = bean.getFeeCycleIdList();
                billingCycleIdList = bean.getABillCycleIdList();
                expCycleIdList = bean.getExpCycleIdList();
                ifDeductList = bean.getAIfDeductList();
                settleObjectIdList = bean.getSettleObjectIdList();
                settleObjectTypeList = bean.getSettleObjectTypeList();
                settleObjectRoleList = bean.getSettleObjectRoleList();
                bereauIdList = bean.getBereauIdList();
            }
            if (CalcRecordManager.getCommon().getIElement() == 1) {
                jsonObject.put("element_str", getElementStr(bean));
            }

            if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem())) {
                resolveElement(jsonObject, bean);   //---2022.1.7--计算公式结果字段
            }

            if (tariffCharges.size() == 0 && tariffCharges1.size() == 0) {              //---没有资费， 异常单
                errorMsgStr.append(bean.getErrorTypeMsg());
                dealErrorMsg(errorMsgStr, correctList, errorMsgMp, errorMsgMpByPlan);     //---处理异常信息
                if (bean.getErrorType() != null && bean.getErrorType().intValue() != 0) {
                    errorType = bean.getErrorType().intValue();
                    jsonObject.put("error_type", errorType);
                }
                if ("2".equals(eventClass) && resettleFlag == 1) {   //---补结
                    if (Integer.parseInt(billingcycle) >= Integer.parseInt(expCycleId)) {  //-当前账期大于等于失效账期，则判为异常单
                        print("没有资费但有补结条件，账期:" + billingcycle + " 大于等于 失效账期:" + expCycleId + " ;置为异常单");
                        jsonObject.put("ticket_type", 1);   //-----1---异常单
                        //  errorMsgStr.append(",话单为周期,有补结标志且当前账期大于等于失效账期，则判为异常单{10047}");
                        writeErrorMsg(jsonObject, errorMsgStr.toString());
                        jsonObject.put("error_type", 10047);
                        if (tariffCharges.size() == 0) {
                            tariffCharges.add(Double.parseDouble(jsonObject.getString("charge")));
                            jsonObject.put("charge", StringUtil.LongArray(tariffCharges)[0]);
                        }
                    } else {  //-- 需要继续补结
                        print("没有资费但有补结条件，账期:" + billingcycle + " 小于 失效账期:" + expCycleId + " ;置为预结单");
                        jsonObject.put("ticket_type", 3);   //--3--预结单
                        if (tariffCharges.size() == 0)
                            tariffCharges.add(Double.parseDouble(jsonObject.getString("charge")));
                        jsonObject.put("a_charge", StringUtil.LongArray(tariffCharges));
                        ifDeductList.clear();
                        ifDeductList.add(0);    //---否 （是否立即结算）
                        jsonObject.put("a_if_deduct_old_owe", StringUtil.listToIntArray(ifDeductList));
                        Map<String, String> tariffMp = getTariffData(jsonObject.getString("org_tariff_id"));
                        String belongCycleType = tariffMp.get("belong_cycle_type");              //---周期偏移量类型（1--月；2---季；3--年）
                        bean.getABillCycleIdList().clear();
                        bean.getABillCycleIdList().add(StringUtil.addYearMonthByBelongType(belongCycleType, billingcycle, 1));
                        bean.getFeeCycleIdList().clear();
                        bean.getFeeCycleIdList().add(Integer.parseInt(feeCycle));
                        bean.getExpCycleIdList().clear();
                        bean.getExpCycleIdList().add(Integer.parseInt(expCycleId));
                        eventStrategyList.add(Long.parseLong(eventstrategyIdBybj));            //---策略id
                        tariffList.add(jsonObject.getLong("org_tariff_id"));              //---资费id
                        pricingPlanList.add(Long.parseLong(planIdBybj));                       //---定价计划id
                        acctItemTypeList.add(jsonObject.getLong("acct_item_type_id"));
                        jsonObject.put("a_acct_item_type_id", StringUtil.listToLongArray(acctItemTypeList));
                        jsonObject.put("a_billing_cycle_id", StringUtil.listToIntArray(bean.getABillCycleIdList()));
                        jsonObject.put("a_fee_cycle_id", StringUtil.listToIntArray(bean.getFeeCycleIdList()));
                        jsonObject.put("a_exp_cycle_id", StringUtil.listToIntArray(bean.getExpCycleIdList()));
                        jsonObject.put("a_strategy_id", StringUtil.listToLongArray(eventStrategyList));
                        jsonObject.put("a_tariff_id", StringUtil.listToLongArray(tariffList));                //---资费标识
                        jsonObject.put("a_pricing_plan_id", StringUtil.listToLongArray(pricingPlanList));     //---定价计划
                        settleObjectIdList.add(Long.parseLong(ticketField.split("_")[0]));
                        settleObjectTypeList.add(Long.parseLong(ticketField.split("_")[1]));
                        settleObjectRoleList.add(Long.parseLong(ticketField.split("_")[2]));
                        jsonObject.put("a_settle_obj_id", StringUtil.listToLongArray(settleObjectIdList));
                        jsonObject.put("a_settle_obj_type", StringUtil.listToLongArray(settleObjectTypeList));
                        jsonObject.put("a_settle_role", StringUtil.listToLongArray(settleObjectRoleList));
                        //jsonObject.put("a_element_str", StringUtil.elementStrArray(eventStrategyList, jsonObject.getString("element_str")));
                        jsonObject.put("re_settle_flag", 1);     //---补结标志
                    }
                } else {
                    print("没有资费且没有补结参考值，进入异常单");
                    writeAbortMessage(jsonObject);          //-----写入异常所需的信息
                    dealErrorMsg(errorMsgStr, correctList, errorMsgMp, errorMsgMpByPlan);     //---处理异常信息
                    if (bean.getPricingPlanList() == null)
                        errorMsgStr.append(",没有进入定价计划表及策略组合表{10045}");
                    else {
                        if (bean.getNotConditionStr() != null && bean.getNotConditionStr().length() > 0)
                            errorMsgStr.append(",策略id:" + bean.getNotConditionStr() + "下的段落不成立，请查看运行轨迹{10046}");
                        else {
                            if (errorMsgStr.length() > 0)
                                errorMsgStr.append(",没有生成资费");
                            else
                                errorMsgStr.append("没有生成资费");
                            jsonObject.put("error_type", 10048);
                        }
                    }
                    jsonObject.put("ticket_type", 1);    //--异常
                    writeErrorMsg(jsonObject, errorMsgStr.toString());
                }
            } else {
                jsonObject.put("ticket_type", Integer.parseInt("0"));                 //---0-正常单-3-预结单
                jsonObject.put("a_charge", StringUtil.LongArray(tariffCharges));        //---资费1 金额/100
                jsonObject.put("a_charge1", StringUtil.LongArray(tariffCharges1));        //---资费2 金额/100
                jsonObject.put("a_tariff_id", StringUtil.listToLongArray(tariffList));                //---资费标识
                jsonObject.put("a_pricing_plan_id", StringUtil.listToLongArray(pricingPlanList));     //---定价计划
                jsonObject.put("a_acct_item_type_id", StringUtil.listToLongArray(acctItemTypeList));  //---帐目类型
                jsonObject.put("a_strategy_id", StringUtil.listToLongArray(eventStrategyList));
                jsonObject.put("a_exp_cycle_id", StringUtil.listToIntArray(bean.getExpCycleIdList()));
                jsonObject.put("a_fee_cycle_id", StringUtil.listToIntArray(bean.getFeeCycleIdList()));
                jsonObject.put("a_billing_cycle_id", StringUtil.listToIntArray(bean.getABillCycleIdList()));
                jsonObject.put("a_if_deduct_old_owe", StringUtil.listToIntArray(ifDeductList));
                jsonObject.put("a_settle_obj_id", StringUtil.listToLongArray(settleObjectIdList));
                jsonObject.put("a_settle_obj_type", StringUtil.listToLongArray(settleObjectTypeList));
                jsonObject.put("a_settle_role", StringUtil.listToLongArray(settleObjectRoleList));


                //补充分居标识的填写  a_bereau_id
                if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                    jsonObject.put("a_bereau_id", StringUtil.listToLongArray(bereauIdList));
                }
                //jsonObject.put("a_element_str", StringUtil.elementStrArray(eventStrategyList, jsonObject.getString("element_str")));
            }

            result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            //  log.info(result);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (CalcRecordManager.getCommon().getIElement() > 0) {
                jsonObject.put("element_str", getElementStr(bean));
            }
            writeAbortMessage(jsonObject);   //-----写入异常所需的信息
            dealErrorMsg(errorMsgStr, correctList, errorMsgMp, errorMsgMpByPlan);     //---处理异常信息
            errorMsgStr.append(bean.getErrorTypeMsg());
            errorMsgStr.append(ex.getMessage());
            writeErrorMsg(jsonObject, errorMsgStr.toString());
            result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            return result;
        } finally {
            //------清除变量的数据-------------//
            errorMsgStr.setLength(0);
            cleanTempVar(accuPayoutList, variableMp, refIdValueMp, schemePlans, pricingPlanList, tariffList, eventStrategyList, acctItemTypeList, feeCycleIdList, billingCycleIdList, expCycleIdList, ifDeductList, tariffCharges, tariffCharges1);
            if (bean != null) {
                clearBean(bean, settleObjectIdList, settleObjectTypeList, settleObjectRoleList);   //---清除bean数据
            }
        }
        return result;
    }

    private void cleanTempVar(List<AccuPayOutBean> accuPayoutList, Map<String, Map> variableMp, Map<String, Map> refIdValueMp, List<String> schemePlans, List<Long> pricingPlanList, List<Long> tariffList, List<Long> eventStrategyList, List<Long> acctItemTypeList, List<Integer> feeCycleIdList, List<Integer> billingCycleIdList, List<Integer> expCycleIdList, List<Integer> ifDeductList, List<Double> tariffCharges, List<Double> tariffCharges1) {

        if (accuPayoutList != null)
            accuPayoutList.clear();
        if (variableMp != null)
            variableMp.clear();
        if (refIdValueMp != null)
            refIdValueMp.clear();
        if (schemePlans != null)
            schemePlans.clear();
        if (pricingPlanList != null)
            pricingPlanList.clear();
        if (tariffList != null)
            tariffList.clear();
        if (eventStrategyList != null)
            eventStrategyList.clear();
        if (acctItemTypeList != null)
            acctItemTypeList.clear();
        if (feeCycleIdList != null)
            feeCycleIdList.clear();
        if (billingCycleIdList != null)
            billingCycleIdList.clear();
        if (expCycleIdList != null)
            expCycleIdList.clear();
        if (ifDeductList != null)
            ifDeductList.clear();
        if (tariffCharges != null)
            tariffCharges.clear();
        if (tariffCharges1 != null)
            tariffCharges.clear();
    }

    /**
     * 处理资费过程
     *
     * @param resultBean
     * @param tariffList
     * @param pricingPlanId
     * @throws Exception
     */
    @Override
    public void dealPricingTariff(ProcessResultBean resultBean, List<String> tariffList, String pricingPlanId) throws Exception {
        Map<String, String> dealMap, tariffMp;
        DataResultBean dataResultBean;
        String refVv, fixedRateValueId, scaledRateValueId, tariffType, accuTypeId, acctItemTypeId, ifDeductOldOwe, belongCycleDuration, resettCycleDuration, belongCycleType, replaceFlag, charge_party_id;
        int billingCycleTmp = 0, feeCycleTmp = 0, expCycleTmp = 0;
        double charges = 0;
        dealMap = new HashMap<>();
        dealMap.put("appId", appId);
        dealMap.put("apiId", apiId);

        // 这里特殊处理是，福建综合结算没有填写billing_cycle_id，deal_cycle_id有值，所以如果billing_cycle_id没值，用deal_cycle_id赋值。
        if (billingcycle == null) {
            billingcycle = dealCycleId;
        }

        dealMap.put("billingCycleId", billingcycle);
        dealMap.put("timeStamp", timeStamp);
        if (tariffList != null && tariffList.size() > 0) {  //---有标准资费id
            for (String tariffId : tariffList) {

                //--------获取标准资费信息--------//
                tariffMp = getTariffData(tariffId);
                replaceFlag = tariffMp.get("replace_flag");                       //---是否重算(F,T)
                belongCycleType = tariffMp.get("belong_cycle_type");              //---周期偏移量类型（1--月；2---季；3--年）
                fixedRateValueId = tariffMp.get("fixed_rate_value_id");          //---固定费率取值标识
                scaledRateValueId = tariffMp.get("scaled_rate_value_id");        //---单位费率取值标识
                acctItemTypeId = tariffMp.get("acct_item_type_id");              //---帐目类型标识
                tariffType = tariffMp.get("tariff_type");                        //---资费类型
                accuTypeId = tariffMp.get("accu_type_id");                       //---累积量积分类型
                ifDeductOldOwe = tariffMp.get("if_deduct_old_owe");              //---是否立即结算
                ifDeductOldOwe = (ifDeductOldOwe == null || "".equals(ifDeductOldOwe)) ? "0" : ifDeductOldOwe;
                belongCycleDuration = tariffMp.get("belong_cycle_duration");      //---归属周期偏移量
                resettCycleDuration = tariffMp.get("resett_cycle_duration");      //---补结月份
                charge_party_id = tariffMp.get("charge_party_id");

                print("【处理资费：" + tariffId + " 】固定费率取值标识：" + fixedRateValueId + ",单位费率取值标识：" + scaledRateValueId);

                dealMap.put("accuTypeId", accuTypeId);
                if ("2".equals(tariffType)) {                       //----资费类型为2时，要判断累计量是否超出某值
                    long t1 = System.currentTimeMillis();
                    dealMap = new HashMap<>();
                    dealMap.put("appId", appId);
                    dealMap.put("apiId", apiId);
                    dealMap.put("billingCycleId", billingcycle);
                    dealMap.put("accuTypeId", accuTypeId);
                    dealMap.put("timeStamp", timeStamp);
                    boolean bl = dealTariffWithAccuTypeId(dealMap);    //----累计量大于某值时，不处理资费直接跳出
                    if (bl)
                        continue;
                }
                String tempTime = timeStamp.substring(0, 6);    //---finish_time
                belongCycleDuration = belongCycleDuration == null || "".equals(belongCycleDuration) ? "0" : belongCycleDuration; //--偏移量
                if (fixedRateValueId != null && !"".equals(fixedRateValueId) && !"null".equals(fixedRateValueId) && !"0".equals(fixedRateValueId)) {

                    resultBean.getTariffStr().append("$" + tariffId + "," + fixedRateValueId + "(id)" + "=");
                    if (variableMp.get(fixedRateValueId) == null) {   //---先判断中间变量Mp中没有这个参考值
                        if (refIdValueMp.get(fixedRateValueId) == null)    //---参考值全局变量中没有这个参考值
                            refVv = getPricingRefIdValue(fixedRateValueId, resultBean);      //--根据参考值Id获取相应的值
                        else {
                            refVv = refIdValueMp.get(fixedRateValueId).get("value").toString();
                            resultBean.getTariffStr().append(refVv);
                        }
                    } else {
                        refVv = variableMp.get(fixedRateValueId).get("value").toString();
                        resultBean.getTariffStr().append(variableMp.get(scaledRateValueId).get("trail").toString());
                        resultBean.getTariffStr().append(refVv);
                    }

                    if (refVv != null && !"".equals(refVv)) {
                        print("【处理资费：" + tariffId + " 】固定费率取值标识：" + fixedRateValueId + ",值：" + refVv + ",charge_party_id:" + charge_party_id);

                        // modify Rating_code_2022-01-17 11:00 批价新增现金流为正判断，福建佣金系统需求 xiaolc
                        if (!StringUtils.isEmpty(charge_party_id) && StringUtils.equals("TPSS", CalcRecordManager.getCommon().getSSystem()) && StringUtils.equals("591", CalcRecordManager.getCommon().getSLatnId())) {
                            Object objectTmp = jsonObject.get("charge_2");
                            jsonObject.put("charge_2", refVv);
                            String tmpValue = getPricingRefIdValue(charge_party_id, resultBean);
                            print("资费标识：" + tariffId + "，存在现金流为正参考值：" + charge_party_id + " ,因子结果:" + tmpValue + " (0:现金流为负，走补结。1:现金流为正，正常出资费)");
                            jsonObject.put("charge_2", objectTmp);

                            // 因子返回0 说明现金流为负，不给酬金。
                            if (StringUtils.equals("0", tmpValue)) {
                                print("【处理资费：" + tariffId + " 】固定费率取值标识：" + fixedRateValueId + ",值：" + refVv + "；现金流为正参考值:" + charge_party_id + ",现金流为正参考值不满足条件，资费不生成。");
                                resettleFlag = 1;
                                planIdBybj = planIdAll;
                                eventstrategyIdBybj = eventstrategyId;
                                // xieh 2023年2月2日 要求补充
                                if (jsonObject.get("no_payment") != null) {
                                    jsonObject.put("no_payment", jsonObject.get("no_payment") + ";现金流为正为否(资费标识:" + tariffId + " 现金流为正参考值:" + charge_party_id + ",结果值:0)");
                                } else {
                                    jsonObject.put("no_payment", ";现金流为正为否(资费标识:" + tariffId + " 现金流为正参考值:" + charge_party_id + ",结果值:0)");
                                }
                                continue;
                            }
                        }

                        if ("DIGIT".equals(CalcRecordManager.getCommon().getSSystem()) && "010".equals(CalcRecordManager.getCommon().getSLatnId())) {
                            baseDataHandler.calculateTotal(jsonObject,refVv);
                        }

                        if (eventClass.equals("1")) {   //---实时
                            billingCycleTmp = StringUtil.getBillingByReal(belongCycleType, tempTime, billingcycle, belongCycleDuration);
                            feeCycleTmp = StringUtil.getFeeingCycle(belongCycleType, tempTime, belongCycleDuration);
                        } else if (eventClass.equals("2")) {  //---周期
                            billingCycleTmp = StringUtil.getBillingByCycle(belongCycleType, billingcycle, belongCycleDuration);
                            feeCycleTmp = Integer.parseInt(feeCycle);
                        }

                        // modify Rating_code_2023-03-20 11:00 批价针对福建佣金历时月不回退，进行的判断，费用账期如果小于当前账期不出这个资费
                        if (jsonObject.containsKey("redo_flag") && StringUtils.equals("TPSS", CalcRecordManager.getCommon().getSSystem()) && StringUtils.equals("591", CalcRecordManager.getCommon().getSLatnId())) {
                            String redo_flag = jsonObject.getString("redo_flag");
                            if (("21".equals(redo_flag) || "22".equals(redo_flag)) && feeCycleTmp < Integer.parseInt(billingcycle)) {
                                continue;
                            }
                        }

                        resultBean.getAIfDeductList().add(Integer.parseInt(ifDeductOldOwe));//--a_if_deduct_old_nwe
                        resultBean.getABillCycleIdList().add(billingCycleTmp);   //--a_billing_cycle_id
                        resultBean.getFeeCycleIdList().add(feeCycleTmp);         //--a_fee_cycle_id

                        if (!resultBean.getPricingPlanList().contains(pricingPlanId))
                            resultBean.getPricingPlanList().add(Long.parseLong(pricingPlanId));      //----添加定价计划ID
                        if (!resultBean.getStrategyList().contains(eventstrategyId))
                            resultBean.getStrategyList().add(Long.parseLong(eventstrategyId));       //---添加策略id
                        if (acctItemTypeId != null && !"".equals(acctItemTypeId)) {
                            if (!resultBean.getAcctItemTypeList().contains(acctItemTypeId))
                                resultBean.getAcctItemTypeList().add(Long.parseLong(acctItemTypeId));    //----帐目类型ID
                        }

                        if (!resultBean.getTariffList().contains(tariffId))
                            resultBean.getTariffList().add(Long.parseLong(tariffId));        //----资费ID

                        charges = Double.parseDouble(refVv);
                        if ("F".equals(replaceFlag))
                            charges += Double.parseDouble(allCharge);

                        resultBean.getChargeList().add(charges);            //--添加资费金额
                        if (resultBean.getTariffStr().indexOf("$" + tariffId) < 0) {
                            resultBean.getTariffStr().append("$" + tariffId);     //---添加资费ID
                            resultBean.getChargeList().add(charges);            //--添加资费金额
                        }
                        dataResultBean = new DataResultBean();
                        dataResultBean.setAcctItemTypeId(acctItemTypeId);
                        dataResultBean.setEventstrategyId(eventstrategyId);
                        dataResultBean.setTariffId(tariffId);
                        dataResultBean.setA_charge(StringUtil.formatDouble(charges));   //--取消科学计数法
                        dataResultBean.setIfDeductOldOwe(ifDeductOldOwe);
                        dataResultBean.setFeeCycleId(feeCycleTmp);
                        dataResultBean.setBillingCycleId(billingCycleTmp);
                        if ("1".equals(eventClass)) {  //---实时话单
                            expCycleTmp = StringUtil.getExpCycle(belongCycleType, feeCycleTmp, resettCycleDuration, billingcycle);
                            resultBean.getExpCycleIdList().add(expCycleTmp);  //---a_exp_cycle_id
                            dataResultBean.setExpCycleId(expCycleTmp);       //---a_exp_cycle_id
                        } else {
                            resultBean.getExpCycleIdList().add(Integer.parseInt(expCycleId));  //---a_exp_cycle_id
                            dataResultBean.setExpCycleId(Integer.parseInt(expCycleId));       //---a_exp_cycle_id
                        }

                        resultBean.getChargeMp().put(eventstrategyId, dataResultBean);
                        resultBean.getChargeMp().put(pricingPlanId, dataResultBean);

                        resultBean.getSettleObjectIdList().add(Long.parseLong(ticketField.split("_")[0]));
                        resultBean.getSettleObjectTypeList().add(Long.parseLong(ticketField.split("_")[1]));
                        resultBean.getSettleObjectRoleList().add(Long.parseLong(ticketField.split("_")[2]));

                        // 补充分居标识的填写  a_bereau_id
                        if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                            CShmOpt opt_ORGANIZATION = new CShmOpt("tp_organization", threadNum);
                            String queryStr = "org_id:" + ticketField.split("_")[0];
                            if (!opt_ORGANIZATION.QueryByIndex(queryStr)) {
                                print("查询失败：字段：org_id");
                                return;
                            }
                            if (opt_ORGANIZATION.Size() > 0) {
                                while (opt_ORGANIZATION.Next()) {
                                    Long sRegionId = Long.parseLong(opt_ORGANIZATION.getValue().get("common_region_id").toString());
                                    resultBean.getBereauIdList().add(sRegionId);
                                }
                            }
                        }

                    } else {
                        resultBean.getTariffList().add(Long.parseLong(tariffId));
                        resultBean.getAcctItemTypeList().add(Long.parseLong(acctItemTypeId));
                        resultBean.getPricingPlanList().add(Long.parseLong(pricingPlanId));
                        resultBean.getStrategyList().add(Long.parseLong(eventstrategyId));
                        resultBean.getErrorTypeMsg().append(",参考值:").append(fixedRateValueId).append("其值为空");
                        resultBean.setErrorType(10019);
                    }
                }
                if (scaledRateValueId != null && !"".equals(scaledRateValueId) && !"null".equals(scaledRateValueId) && !"0".equals(scaledRateValueId)) {
                    resultBean.getTariffStr().append("$" + tariffId + "," + scaledRateValueId + "(id)" + "=");
                    if (variableMp.get(scaledRateValueId) == null) {   //---先判断中间变量Mp中没有这个参考值
                        if (refIdValueMp.get(scaledRateValueId) == null)    //---参考值全局变量中没有这个参考值
                        {
                            refVv = getPricingRefIdValue(scaledRateValueId, resultBean);      //--根据参考值Id获取相应的值
                        } else {
                            refVv = refIdValueMp.get(scaledRateValueId).get("value").toString();
                            resultBean.getTariffStr().append(refVv);
                        }
                    } else {
                        refVv = variableMp.get(scaledRateValueId).get("value").toString();
                        resultBean.getTariffStr().append(variableMp.get(scaledRateValueId).get("trail").toString());
                        resultBean.getTariffStr().append(refVv);
                    }
                    print("【处理资费：" + tariffId + " 】单位费率取值标识：" + scaledRateValueId + ",值：" + refVv);

                    if (refVv != null && !"".equals(refVv)) {
                        if (!resultBean.getPricingPlanList().contains(pricingPlanId))
                            resultBean.getPricingPlanList().add(Long.parseLong(pricingPlanId));      //----添加定价计划id
                        if (!resultBean.getStrategyList().contains(eventstrategyId))
                            resultBean.getStrategyList().add(Long.parseLong(eventstrategyId));       //---添加策略id
                        if (!resultBean.getTariffList().contains(tariffId))
                            resultBean.getTariffList().add(Long.parseLong(tariffId));                //----资费ID
                        if (acctItemTypeId != null && !"".equals(acctItemTypeId)) {
                            if (!resultBean.getAcctItemTypeList().contains(acctItemTypeId))
                                resultBean.getAcctItemTypeList().add(Long.parseLong(acctItemTypeId));     //----帐目类型ID
                        }
                        charges = Double.parseDouble(refVv);
                        if ("F".equals(replaceFlag))
                            charges += Double.parseDouble(allCharge);

                        if (!CalcRecordManager.getCommon().getSSystem().equals("ISS"))   //---ISS-福建综合结算
                            resultBean.getChargeList().add(charges);                   //--添加资费1金额
                        else
                            resultBean.getCharge1List().add(Double.parseDouble(refVv));
                        if (resultBean.getTariffStr().indexOf("$" + tariffId) < 0) {
                            resultBean.getTariffStr().append("$" + tariffId);           //---添加资费ID
                            if (!CalcRecordManager.getCommon().getSSystem().equals("ISS"))    //---佣金项目
                                resultBean.getChargeList().add(charges);             //--资费1金额
                            else
                                resultBean.getCharge1List().add(Double.parseDouble(refVv));    //--资费2金额
                        }
                        if (CalcRecordManager.getCommon().getSSystem().equals("ISS")) {     //---福建综合结算
                            dataResultBean = (DataResultBean) resultBean.getChargeMp().get(eventstrategyId);
                            if (dataResultBean != null) {
                                dataResultBean.setA_charge1(refVv);
                            }
                        } else {
                            dataResultBean = new DataResultBean();
                            dataResultBean.setAcctItemTypeId(acctItemTypeId);
                            dataResultBean.setEventstrategyId(eventstrategyId);
                            dataResultBean.setTariffId(tariffId);
                            dataResultBean.setA_charge(StringUtil.formatDouble(charges));
                            dataResultBean.setIfDeductOldOwe(ifDeductOldOwe);
                            dataResultBean.setBillingCycleId(billingCycleTmp);
                            dataResultBean.setFeeCycleId(feeCycleTmp);

                            if (eventClass.equals("1")) {   //---实时
                                billingCycleTmp = Math.max(StringUtil.addYearMonth(tempTime, Integer.parseInt(belongCycleDuration)), Integer.parseInt(billingcycle));
                                feeCycleTmp = StringUtil.addYearMonth(tempTime, Integer.parseInt(belongCycleDuration));
                            } else if (eventClass.equals("2")) {  //---周期
                                billingCycleTmp = StringUtil.addYearMonth(billingcycle, Integer.parseInt(belongCycleDuration));
                                feeCycleTmp = Integer.parseInt(feeCycle);
                            }
                            resultBean.getABillCycleIdList().add(billingCycleTmp);   //--a_billing_cycle_id
                            resultBean.getFeeCycleIdList().add(feeCycleTmp);         //--a_fee_cycle_id

                            if ("1".equals(eventClass)) {  //---实时话单
                                expCycleTmp = Math.max(StringUtil.addYearMonth(String.valueOf(feeCycleTmp), Integer.parseInt(resettCycleDuration)), Integer.parseInt(billingcycle));
                                resultBean.getExpCycleIdList().add(expCycleTmp);  //---a_exp_cycle_id
                                dataResultBean.setExpCycleId(expCycleTmp);       //---a_exp_cycle_id
                            } else {
                                resultBean.getExpCycleIdList().add(Integer.parseInt(expCycleId));  //---a_exp_cycle_id
                                dataResultBean.setExpCycleId(Integer.parseInt(expCycleId));       //---a_exp_cycle_id
                            }
                        }

                        resultBean.getChargeMp().put(eventstrategyId, dataResultBean);
                        resultBean.getChargeMp().put(pricingPlanId, dataResultBean);

                        resultBean.getSettleObjectIdList().add(Long.parseLong(ticketField.split("_")[0]));
                        resultBean.getSettleObjectTypeList().add(Long.parseLong(ticketField.split("_")[1]));
                        resultBean.getSettleObjectRoleList().add(Long.parseLong(ticketField.split("_")[2]));

                        // 补充分居标识的填写  a_bereau_id
                        if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                            CShmOpt opt_ORGANIZATION = new CShmOpt("tp_organization", threadNum);
                            String queryStr = "org_id:" + ticketField.split("_")[0];
                            if (!opt_ORGANIZATION.QueryByIndex(queryStr)) {
                                print("查询失败：字段：org_id");
                                return;
                            }
                            if (opt_ORGANIZATION.Size() > 0) {
                                while (opt_ORGANIZATION.Next()) {
                                    Long sRegionId = Long.parseLong(opt_ORGANIZATION.getValue().get("common_region_id").toString());
                                    resultBean.getBereauIdList().add(sRegionId);
                                }
                            }
                        }
                    } else {
                        resultBean.getTariffList().add(Long.parseLong(tariffId));
                        resultBean.getAcctItemTypeList().add(Long.parseLong(acctItemTypeId));
                        resultBean.getPricingPlanList().add(Long.parseLong(pricingPlanId));
                        resultBean.getStrategyList().add(Long.parseLong(eventstrategyId));
                        resultBean.getErrorTypeMsg().append(",参考值:").append(scaledRateValueId).append("其值为空");
                        resultBean.setErrorType(10019);
                    }
                }
            }
        }
    }


    /**
     * 处理段落 1--无条件段落;5--应用判断条件段落)
     *
     * @param resultBean
     * @param pricingPlanId
     * @param parentSectionId
     * @param eventTypeId
     * @throws Exception
     */
    @Override
    public void dealPricingSection(ProcessResultBean resultBean, String pricingPlanId, String parentSectionId, String eventTypeId) throws Exception {
        List<String> tariffList = null, variabList = null;
        StringBuffer trail = new StringBuffer();
        Map<String, String> mp = null;
        Map<String, String> sectionMps = getPricingSectionData(parentSectionId);
        String sectionTypeId = sectionMps.get("section_type_id");        //---段落类型标识
        String tariff_count = sectionMps.get("tariff_count");
        String sectionEmptyType = sectionMps.get("section_empty_type");  //---3--中间变量
        print("\n\n【定价段落：" + parentSectionId + ",段落类型:" + ("1".equals(sectionTypeId) ? "无条件段落" : "有条件段落") + ",tariff_count:" + tariff_count + ",section_empty_type:" + sectionEmptyType + "】");
        if ("3".equals(sectionEmptyType)) {                               //---取出中间变量的值
            print("【中间变量处理：" + parentSectionId + "】");
            variabList = getVariableRefValue(parentSectionId);
            if (variabList != null) {
                Map<String, String> resultRefValue;
                for (String tempStr : variabList) {
                    print("【中间变量处理：" + parentSectionId + "," + tempStr + "】");
                    String variabRefId = tempStr.split("-")[0];   //---中间变量id
                    String resultRefId = tempStr.split("-")[1];   //---参考值ID
                    if (uploadType == 2 && trialPreValueFlag && !variableMp.isEmpty()) {       //---试算过程
                        mp = variableMp.get(variabRefId);                //---试算页面的预设值
                        if (mp != null) {
                            print("试算页面的预设值，中间变量参考值：" + variabRefId + ",赋值" + mp.toString());
                        }
                    }
                    if (mp == null || mp.isEmpty()) {
                        resultRefValue = getPricingRefIdValue(resultRefId);  //---获取参考值的值
                        trail.append(resultRefId + "(id),").append(resultRefId + "(id)=").append(resultRefValue.get("value"));
                        trail.append(",").append(variabRefId + "(id)=");
                        resultRefValue.put("trail", trail.toString());
                        variableMp.put(variabRefId, resultRefValue);                  //--保存中间变量的值
                        print("中间变量参考值：" + variabRefId + ",赋值" + variableMp.get(variabRefId) != null ? variableMp.get(variabRefId).toString() : "");
                        trail.delete(0, trail.length());                             //---清空
                        resultBean.getVariableStr().append("&").append(parentSectionId).append(",").append(variabRefId).append(",")
                                .append(resultRefValue.get("value"));
                    }
                }
            }
        }

        switch (sectionTypeId) {
            case NoConditionSectionType:   //---无条件段落
                resultBean.getPricingSectionStr().append("@" + parentSectionId + ",true");    //---段落轨迹，true 代表段落成立
                //-----找出段落对应的资费id集合----//
                if (!"0".equals(tariff_count)) {
                    tariffList = getPricingSectionWithTariffData(parentSectionId);
                    if (tariffList != null) {
                        print("\n【处理定价段落：" + parentSectionId + " 的资费,tariff_count:" + tariff_count);
                        dealPricingTariff(resultBean, tariffList, pricingPlanId);         //----处理资费
                    }
                }
                //--------根据父段落来获取子段落id--------------//
                String childNum = getPricingSectionData(parentSectionId, "child_count");
                if (!"0".equals(childNum)) {            //---有子段落
                    List<String> childSectionIds = getChildSection(parentSectionId);
                    print("\n【处理定价段落：" + parentSectionId + " 的子段落,字段的数量:" + childNum + ",childSectionIds.size():" + childSectionIds.size());
                    if (childSectionIds != null && childSectionIds.size() > 0) {
                        for (String sectionId : childSectionIds) {
                            print("【处理定价段落：" + parentSectionId + " 的子段落->" + sectionId);
                            //----------处理段落------------//
                            dealPricingSection(resultBean, pricingPlanId, sectionId, eventTypeId);
                        }
                    }
                }
                break;
            case ConditionSectionType:    //---  有条件段落
                //----先判断条件--如果不满足，不找资费和子段落-------//
                //-----根据段落id--获取判断条件-是否成立--------//
                boolean bl = judgePricingRuleCondition(parentSectionId, resultBean);
                if (bl) {    //---成立就查资费
                    //-----找出段落对应的资费id集合----//
                    resultBean.getPricingSectionStr().append("@" + parentSectionId + ",true");   //---段落轨迹，true 代表段落成立
                    if (!"0".equals(tariff_count)) {
                        tariffList = getPricingSectionWithTariffData(parentSectionId);
                        if (tariffList != null)
                            dealPricingTariff(resultBean, tariffList, pricingPlanId);             //----处理资费
                    }
                    //--------根据父段落来获取子段落id--------------//
                    childNum = getPricingSectionData(parentSectionId, "child_count");
                    if (!"0".equals(childNum)) {   //---有子段落
                        List<String> childSectionIds = getChildSection(parentSectionId);
                        print("【处理定价段落：" + parentSectionId + " 的子段落,字段的数量:" + childNum + ",childSectionIds.size():" + childSectionIds.size());
                        if (childSectionIds != null && childSectionIds.size() > 0) {
                            for (String sectionId : childSectionIds) {
                                print("【处理定价段落：" + parentSectionId + " 的子段落->" + sectionId);
                                //----------处理段落------------//
                                dealPricingSection(resultBean, pricingPlanId, sectionId, eventTypeId);   //---递归调用
                            }
                        }
                    }
                } else {
                    if (resultBean.isHaveResettleBool()) {
                        resettleFlag = 1;          //---有补结因子factor_type=3参考值 且该判断条件不成立-》设置补结标识=1
                        planIdBybj = planIdAll;
                        eventstrategyIdBybj = eventstrategyId;
                    }
                    resultBean.getPricingSectionStr().append("@" + parentSectionId + ",false");    //---段落轨迹，false 代表段落不成立
                    if (resultBean.getNotConditionStr().toString().indexOf(eventstrategyId) < 0)
                        resultBean.getNotConditionStr().append(eventstrategyId + ",");   //---不成立的策略
                }
                break;
        }

    }

    /**
     * 判断该段落下的各种规则条件是否成立(段落里面有一个不同组的判断条件成立，则没有继续进入下一判断条件，直接跳出返回true)
     *
     * @param sectionId
     * @return
     */
    public boolean judgePricingRuleCondition(String sectionId, ProcessResultBean resultBean) throws Exception {
        Map<String, String> leftValueMp, rightValueMp;
        boolean bool = true;
        StringBuffer refValueStr = new StringBuffer();
        resultBean.setHaveResettleBool(false);    //---初始化设为
        String refValueId = null, resultRefValueId = null, operatorId = null, groupId = null;
        boolean bl = true;
        List<Boolean> xlist = new ArrayList<>();
        print("【处理定价段落：" + sectionId + " 的比较条件");

        //-----根据段落id--获取判断条件---------//
        List<String> pricingRuleIds = getPricingSectionWithRuleData(sectionId);
        if (pricingRuleIds != null && pricingRuleIds.size() > 0) {
            print("【处理定价段落：" + sectionId + " 的比较条件组数：" + pricingRuleIds.size());
            String oldGroupId = "";
            for (String pricingRuleId : pricingRuleIds) {

                if (oldGroupId.equals("")) {
                    oldGroupId = getPricingRuleData(pricingRuleId).get("group_id");
                }

                if (!oldGroupId.equals("") && oldGroupId.equals(getPricingRuleData(pricingRuleId).get("group_id")) && !bl) {
                    continue;
                }

                if (!oldGroupId.equals(getPricingRuleData(pricingRuleId).get("group_id"))) {
                    bool = true;
                    for (Boolean bbl : xlist) {
                        bool = bool && bbl;
                    }
                    if (bool) {
                        return true;
                    }
                    xlist.clear();
                }

                refValueStr.setLength(0);
                Map<String, String> mp = getPricingRuleData(pricingRuleId);
                refValueId = mp.get("ref_value_id");                          //-- 参考值id
                if (projectName.trim().toUpperCase().equals("TPSS")) {      //----佣金系统
                    //----参考值的运行轨迹是#参考值id,参考值id 的值,条件成立true或false---------//
                    refValueStr.append("#").append(refValueId);
                } else {
                    //----参考值的运行轨迹是#段落条件id,参考值id,参考值id的值,条件成立true或false---------//
                    refValueStr.append("#").append(pricingRuleId).append(",").append(refValueId);
                }

                resultRefValueId = mp.get("result_ref_value_id");             //--结果值取值标识id
                operatorId = mp.get("operator_id");                           //---条件运算符标识
                groupId = mp.get("group_id");                                 //--分组
                //-----左值---------//
                if (variableMp == null || variableMp.get(refValueId) == null) {   //---先判断中间变量Mp中没有这个参考值
                    if (refIdValueMp.get(refValueId) == null)    //---参考值全局变量中没有这个参考值
                    {
                        leftValueMp = getPricingRefIdValue(refValueId);           //--根据参考值Id 获取相应的值
                        refIdValueMp.put(refValueId, leftValueMp);      //---保存参考值
                    } else
                        leftValueMp = refIdValueMp.get(refValueId);
                } else {
                    leftValueMp = variableMp.get(refValueId);
                }
                leftValueMp.put("refValueId", refValueId);
                refValueStr.append(",").append(leftValueMp.get("value"));
                //-----右值（结果参考值）
                if (refIdValueMp.get(resultRefValueId) == null)            //---参考值全局变量中没有这个参考值
                    rightValueMp = getPricingRefIdValue(resultRefValueId);        //---根据参考值Id获取相应的值
                else
                    rightValueMp = refIdValueMp.get(resultRefValueId);
                rightValueMp.put("refValueId", resultRefValueId);

                //  System.out.println("sectionId="+sectionId+",leftRefValueId==》"+refValueId+",rightRefValueId==>"+resultRefValueId); ;
                bl = StringUtil.compareValue(sectionId, pricingRuleId, leftValueMp, rightValueMp, operatorId);
                //  fuxingwang 2022年12月2日 补结参考值的比较条件不成立，设置为补结单
                if (!bl) {
                    String factor_type = getPricingRefValueData(refValueId, "factor_type");
                    if ("3".equals(factor_type))                             //----3--有补结因子存在
                        resultBean.setHaveResettleBool(true);                //----有补结因子存在,设true
                }
                print("【处理定价段落：" + sectionId + " 比较条件,第 " + groupId + " 组,pricingRuleId：" + pricingRuleId + ",左参考值标识：" + refValueId + "(" + leftValueMp.toString() + ") " + StringUtil.Operators.getName(Integer.parseInt(operatorId)) + "  右参考值标识：" + resultRefValueId + "(" + rightValueMp.toString() + ")" + (bl ? "成立" : "不成立"));

                refValueStr.append(",").append(bl);
                resultBean.getRefValueStr().add(refValueStr.toString());
                xlist.add(bl);
                oldGroupId = groupId;
            }
            if (xlist.size() > 0) {
                bool = true;
                for (Boolean bbl : xlist) {
                    bool = bool && bbl;
                }
                if (bool)
                    return true;
            }
        } else {
            return true;   // ---段落下没有条件就返回 true
        }
        return false;
    }


    /**
     * 根据参考值Id,获取参考值的值
     *
     * @param refValueId
     * @return
     */
    @Override
    public Map<String, String> getPricingRefIdValue(String refValueId) throws Exception {  //----2021
        Map<String, String> mp = new HashMap<>();
        String valueString = "", calcString = "", valueType = null, enName = null, functionType = null;
        Object functionValue = null;
        Map<String, String> refValueMp = getPricingRefValueData(refValueId); //---根据参考值ID,获取对应的整条信息
        String refValueType = refValueMp.get("ref_value_type");            //---参考值类型
        if (null == refValueType || "".equals(refValueType))
            return null;
        mp.put("ref_value_type", refValueType);
        valueType = refValueMp.get("value_type");          //---值类型
        mp.put("value_type", valueType);
        //----参考值类型(RefValueTypeString--常量；RefValueTypeParam--内部参数；RefValueTypeObject--参考对象 ;
        if (RefValueTypeString.equals(refValueType)) {        //---1-常量
            valueString = refValueMp.get("value_string");     //---常量值
            mp.put("value", valueString);
        } else if (RefValueTypeParam.equals(refValueType)) {      //---2-内部参数
            String pricing_param_id = refValueMp.get("pricing_param_id");
            List<String> list1 = getEnumParamValueWithParamIdData(pricing_param_id);  //--根据参数Id,获取枚举值
            if (list1 == null || list1.size() < 1) {
                throw new DBExeption.NoData("参考值id:" + refValueId + "其内部参数pricing_param_id：" + pricing_param_id + "找不到枚举值（pricing_enum_param）{10017}");
            } else {
                valueString = StringUtil.listToString(list1, ',');  //--list转字符串
                mp.put("value", valueString);
            }
        } else if (RefValueTypeObject.equals(refValueType)) {     //---3-参考对象
            String pricingRefObjectId = refValueMp.get("pricing_ref_object_id");
            Map<String, String> objectMp = getPricingRefObjectData(pricingRefObjectId);
            String propertyType = (String) objectMp.get("property_type");                    //---属主属性类型
            String propertyDefineId = (String) objectMp.get("property_define_id");          //--属主属性标识
            String externPropertyString = (String) objectMp.get("extern_property_string");  //--外部属性标识
            if (PropertyType.equals(propertyType)) {          //---PropertyType-属性值类属性（5--内部属性值）
                enName = getTprResourceAttr(propertyDefineId, "en_name");  //---属性英文名称
                valueString = jsonObject.getString(enName);
                if (valueString == null) {
                    throw new DBExeption.NoData("参考值id:" + refValueId + "其参考对象id:" + pricingRefObjectId + "对应话单字段:" + enName + "其值为null{10022}");
                }
                mp.put("value", valueString);
            } else if (ExternPropertyType.equals(propertyType)) {     //---ExternPropertyType-外部属性（4--外部属性 函数）
                if (calcFunction == null) {
                    calcFunction = CalcFunctionLocal.getInstance(threadNum);
                }

                Result result = new Result(false);
                try {
                    result = calcFunction.calcRefValue(Long.parseLong(refValueId), jsonObject);
                } catch (Exception ex) {
                    throw new DBExeption.NoData("{10018}调用函数失败:参考值id=" + refValueId + "参考对象id=" + pricingRefObjectId + ",ex:" + ex.toString());
                }
                if (!result.isBRet()) {
                    throw new DBExeption.NoData("{10018}调用函数失败[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "] error_msg:" + result.getError_msg());
                }
                functionValue = result.get("VALUE");
                //functionType = String.valueOf(result.get("VALUE_TYPE"));  //---1->int,2->long,3->double,4->string,5->date,6->array_int
                mp.put("value", String.valueOf(functionValue));
            } else if (WIDTHTABLEPropertyType.equals(propertyType)) {     //---3--外部属性 宽表
                try {
                    if (tableService == null) {
                        //   log.info("[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "][externPropertyString:"+externPropertyString+"]");
                        tableService = TableService.getInstance(threadNum);
                    }
                    valueString = tableService.getColuValueByattr(externPropertyString, jsonObject);
                    mp.put("value", valueString);
                } catch (Exception e) {
                    e.printStackTrace();
                    //  log.error(e.getCause().getMessage());
                    throw new DBExeption.NoData("{10057}调用宽表失败[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "][externPropertyString:" + externPropertyString + "]");
                }
            } else if (CACHEFUNCTIONPropertyType.equals(propertyType)) {   //---6--缓存因子属性
                Map<String, Object> params = JSONObject.parseObject(jsonObject.toJSONString(), new TypeReference<Map<String, Object>>() {
                });
                Map<String, Object> resultMp = new HashMap<>();
              /*  boolean iRet=calcVisualFunctionLocal.calcFunction(Integer.parseInt(externPropertyString),params,resultMp);
                if(iRet){
                    valueString=resultMp.get("VALUE").toString();
                    mp.put("value", valueString);
                }else{
                    throw new DBExeption.NoData("调用缓存因子参考值id:"+refValueId+" functionId:"+externPropertyString+"{10023}失败原因:"+resultMp.get("executeMsg").toString());
                }*/
            }
        } else if (RefValueTypeCalc.equals(refValueType)) {     //----8-运算参考值处理
            //------获取运算参考值中的计算符和参考值ID
            List<String> calcList = getPricingCalcRefValue(refValueId);
            if (calcList == null || calcList.size() == 0) {
                throw new DBExeption.NoData("{10007}找不到运算参考值:" + refValueId);
            }
            if (calcList.size() == 1) {
                String calcs = calcList.get(0);
                String refVvId = calcs.split("_")[1];  //---参考值ID
                return getPricingRefIdValue(refVvId);
            } else {
                String refValueIdd, refValueVV, operationChar, refValueIddd = "";
                for (int ii = 0; ii < calcList.size(); ii++) {
                    if (ii == 0) {
                        //--------参考值的值
                        refValueIddd = calcList.get(ii).toString().split("_")[1];
                        calcString = getPricingRefValueById(refValueIddd);
                        if (null == calcString || "".equals(calcString)) {
                            // log.info("参考值" + calcList.get(ii).toString().split("_")[1] + ",值找不到!");
                            throw new DBExeption.NoData("参考值id:" + calcList.get(ii).toString().split("_")[1] + ",值:" + calcString + ",其值为空!{10019}");
                        }
                        if (calcString.indexOf("E") >= 0) {     //----科学计数法的字符串-转换--//
                            double temp = StringUtil.getDoubleNumber(calcString);
                            calcString = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(calcString)) {   //---校验字符串是否为正浮点数
                                if ('-' == calcString.charAt(0)) { // 开头为负数，如-1，改为0-1
                                    calcString = 0 + calcString;
                                    calcString = "(" + calcString + ")";
                                } else {
                                    throw new DBExeption.NoData("参考值id：" + refValueIddd + ",其返回值:" + calcString + "不是非负浮点数{10042}");
                                }
                            }
                        }
                    } else {
                        operationChar = calcList.get(ii).toString().split("_")[0];
                        operationChar = operationChar.replaceAll(OPERATOR_ADD, "+");
                        operationChar = operationChar.replaceAll(OPERATOR_MINU, "-");
                        operationChar = operationChar.replaceAll(OPERATOR_MULTIPLY, "*");
                        operationChar = operationChar.replaceAll(OPERATOR_DIVIDE, "/");
                        refValueIdd = calcList.get(ii).toString().split("_")[1];
                        refValueVV = getPricingRefValueById(refValueIdd);
                        if (null == refValueVV || "".equals(refValueVV)) {
                            throw new DBExeption.NoData("{10019}参考值Id:" + refValueIdd + ",值:" + refValueVV + ",其值为空!");
                        }
                        if (refValueVV.indexOf("E") >= 0) {                          //----科学计数法的字符串-转换--//
                            double temp = StringUtil.getDoubleNumber(refValueVV);
                            refValueVV = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(refValueVV)) {   //---校验字符串是否为正浮点数
                                if ('-' == refValueVV.charAt(0)) {// 开头为负数，如-1，改为0-1
                                    refValueVV = 0 + refValueVV;
                                    refValueVV = "(" + refValueVV + ")";
                                }
                                //  throw new DBExeption.NoData("参考值id：" + refValueIddd + "其返回值:" + refValueVV + "不是正浮点数{10042}");
                            }
                        }

                        calcString += operationChar + refValueVV;
                    }
                }
                double resultValue = 0;
                try {
                    resultValue = Calculator.compute(calcString + "=");   //-----计算运算参考值
                } catch (Exception ex) {
                    throw new DBExeption.BadExecution("{10020}参考值Id：" + refValueId + "对应计算公式有误:" + calcString);
                }
                String tempValue = StringUtil.formatDouble(resultValue);
                mp.put("value", tempValue);
            }
        } else if (RefValueTypeVar.equals(refValueType)) {     //----4-中间变量
            if (variableMp.get(refValueId) == null) {
                throw new DBExeption.BadExecution("策略id:" + eventstrategyId + "下中间变量id:" + refValueId + "没有被赋值!{10021}");
            }
            //------------2022.8.29----中间变量-------//
            mp.put("value", variableMp.get(refValueId).get("value").toString());
            //------------------------------------//
        }
        print("【参考值标识：" + refValueId + ",值:" + mp.toString() + "】");
        return mp;
    }

    /**
     * 根据参考值Id,获取参考值的值
     *
     * @param refValueId
     * @param resultBean
     * @return
     * @throws Exception
     */
    @Override
    public String getPricingRefIdValue(String refValueId, ProcessResultBean resultBean) throws Exception {  //--2021
        String valueString = "", calcString = "", functionType = null;
        double temp = 0;
        Map<String, String> refValueMp, tempMp;
        if (variableMp == null || variableMp.get(refValueId) == null) {
            if (refIdValueMp.get(refValueId) == null)
                refValueMp = getPricingRefValueData(refValueId); //---根据参考值ID,获取对应的整条信息
            else {
                refValueMp = refIdValueMp.get(refValueId);
                resultBean.getTariffStr().append(refValueMp.get("value"));
                return refValueMp.get("value");
            }
        } else {
            refValueMp = variableMp.get(refValueId);
            if (refValueMp.get("trail") != null && !"null".equals(refValueMp.get("trail")))
                resultBean.getTariffStr().append(refValueMp.get("trail"));
            resultBean.getTariffStr().append(refValueMp.get("value"));
            return refValueMp.get("value");
        }

        String refValueType = refValueMp.get("ref_value_type");    //---参考值类型
        String valueType = refValueMp.get("value_type");          //---值类型
        if (null == refValueType || "".equals(refValueType)) {
            throw new DBExeption.NoData("参考值id:" + refValueId + "参考值类型(ref_value_type)没有配置{10016}");
        }
        tempMp = new HashMap<>();
        tempMp.put("ref_value_type", refValueType);
        tempMp.put("value_type", valueType);
        //----参考值类型(RefValueTypeString--常量；RefValueTypeParam--内部参数；RefValueTypeObject--参考对象 ;
        if (RefValueTypeString.equals(refValueType)) {            //---1-常量
            valueString = refValueMp.get("value_string");        //---常量值
            resultBean.getTariffStr().append(valueString);     //---资费 运行轨迹
            //---------------保存参考值的值-在全局变量中----------//
            tempMp.put("value", valueString);
            if (refIdValueMp.get(refValueId) == null)
                refIdValueMp.put(refValueId, tempMp);
            //------------------------------------------------//
            return valueString;
        } else if (RefValueTypeParam.equals(refValueType)) {      //---2-内部参数
            String pricingParamId = refValueMp.get("pricing_param_id");
            List<String> list1 = getEnumParamValueWithParamIdData(pricingParamId);  //--根据参数Id,获取枚举值
            if (list1 == null || list1.size() < 1) {
                throw new DBExeption.NoData("参考值id:" + refValueId + "内部参数：" + pricingParamId + "找不到枚举值(PRICING_ENUM_PARAM){10017}");
            } else {
                valueString = StringUtil.listToString(list1, ',');  //--list转字符串
                resultBean.getTariffStr().append(valueString);              //---资费 运行轨迹
                //---------------保存参考值的值-在全局变量中----------//
                tempMp.put("value", valueString);
                if (refIdValueMp.get(refValueId) == null)
                    refIdValueMp.put(refValueId, tempMp);
                //------------------------------------------------//
                return valueString;
            }
        } else if (RefValueTypeObject.equals(refValueType)) {                                //---3-参考对象
            String pricingRefObjectId = refValueMp.get("pricing_ref_object_id");
            Map<String, String> objectMp = getPricingRefObjectData(pricingRefObjectId);
            String propertyType = (String) objectMp.get("property_type");                   //---属主属性类型
            String propertyDefineId = (String) objectMp.get("property_define_id");          //--属主属性标识
            String externPropertyString = (String) objectMp.get("extern_property_string");  //--外部属性标识
            if (PropertyType.equals(propertyType)) {   //---PropertyType-属性值类属性 5
                String enName = getTprResourceAttr(propertyDefineId, "en_name");     //---属性英文名称
                String attrValue = jsonObject.getString(enName);
                if (attrValue == null) {
                    throw new DBExeption.NoData("参考值id:" + refValueId + "其参考对象id:" + pricingRefObjectId + "对应话单字段:" + enName + "其值为null{10022}");
                }
                resultBean.getTariffStr().append(attrValue);      //---资费 运行轨迹
                //---------------保存参考值的值-在全局变量中----------//
                tempMp.put("value", attrValue);
                if (refIdValueMp.get(refValueId) == null)
                    refIdValueMp.put(refValueId, tempMp);
                //------------------------------------------------//
                return attrValue;
            } else if (ExternPropertyType.equals(propertyType)) {     //---函数调用 4
                if (calcFunction == null) {
                    calcFunction = CalcFunctionLocal.getInstance(threadNum);
                }

                Result result = new Result(false);
                try {
                    result = calcFunction.calcRefValue(Long.parseLong(refValueId), jsonObject);
                } catch (Exception ex) {
                    throw new DBExeption.NoData("{10018}调用函数失败:参考值id=" + refValueId + "参考对象id=" + pricingRefObjectId + ",ex:" + ex.toString());
                }
                if (!result.isBRet()) {
                    throw new DBExeption.NoData("{10018}调用函数失败[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "] error_msg:" + result.getError_msg());
                }
                valueString = String.valueOf(result.get("VALUE"));
                //functionType = String.valueOf(result.get("VALUE_TYPE"));  //---1->int,2->long,3->double,4->string,5->date,6->array_int
                resultBean.getTariffStr().append(valueString);
                //---------------保存参考值的值-在全局变量中----------//
                tempMp.put("value", valueString);
                if (refIdValueMp.get(refValueId) == null)
                    refIdValueMp.put(refValueId, tempMp);
                //------------------------------------------------//
                return valueString;
            } else if (WIDTHTABLEPropertyType.equals(propertyType)) {     //---宽表调用  3
                //  valueString = tableService.getColuValueByattr(externPropertyString, jsonObject);
                try {
                    if (tableService == null) {
                        tableService = TableService.getInstance(threadNum);
                    }
                    valueString = tableService.getColuValueByattr(externPropertyString, jsonObject);
                } catch (Exception e) {
                    // log.error(e.getCause().getMessage());
                    e.printStackTrace();
                    throw new DBExeption.NoData("{10057}调用宽表失败[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "][externPropertyString:" + externPropertyString + "]");
                }
                resultBean.getTariffStr().append(valueString);   //---资费 运行轨迹
                //---------------保存参考值的值-在全局变量中----------//
                tempMp.put("value", valueString);
                if (refIdValueMp.get(refValueId) == null)
                    refIdValueMp.put(refValueId, tempMp);
                //------------------------------------------------//
                return valueString;
            } else if (CACHEFUNCTIONPropertyType.equals(propertyType)) {   //---6--缓存因子属性
                Map<String, Object> params = JSONObject.parseObject(jsonObject.toJSONString(), new TypeReference<Map<String, Object>>() {
                });
                Map<String, Object> resultMp = new HashMap<>();
                /*boolean iRet=calcVisualFunctionLocal.calcFunction(Integer.parseInt(externPropertyString),params,resultMp);
                if(iRet){
                    valueString=resultMp.get("VALUE").toString();
                    tempMp.put("value", valueString);
                    return valueString ;
                }else{
                    throw new DBExeption.NoData("调用缓存因子,functionId="+externPropertyString+",失败原因="+resultMp.get("executeMsg").toString());
                }*/
            }
        } else if (RefValueTypeCalc.equals(refValueType)) {         //----8-运算参考值处理
            //------获取运算参考值中的计算符和参考值ID
            List<String> calcList = getPricingCalcRefValue(refValueId);
            if (calcList == null || calcList.size() == 0) {
                throw new DBExeption.NoData("{10007}找不到运算参考值=" + refValueId);
            }
            if (calcList.size() == 1) {
                String calcs = calcList.get(0);
                String refVvId = calcs.split("_")[1];         //---参考值ID
                return getPricingRefIdValue(refVvId, resultBean);
            } else {
                String refValueIdd, refValueVV, operationChar, refValueIddd = "";
                for (int ii = 0; ii < calcList.size(); ii++) {             //----获取运行轨迹,如：资费参考值id=X1+X2
                    refValueIddd = calcList.get(ii).toString();
                    if (ii > 0)
                        resultBean.getTariffStr().append(refValueIddd.split("_")[0]);
                    resultBean.getTariffStr().append(refValueIddd.split("_")[1] + "(id)");
                }
                for (int ii = 0; ii < calcList.size(); ii++) {
                    if (ii == 0) {
                        //--------参考值的值
                        refValueIddd = calcList.get(ii).toString().split("_")[1];
                        resultBean.getTariffStr().append(",");                //----获取运行轨迹
                        resultBean.getTariffStr().append(refValueIddd + "(id)" + "=");   //----获取运行轨迹
                        calcString = getPricingRefIdValue(refValueIddd, resultBean);
                        if (null == calcString || "".equals(calcString)) {
                            throw new DBExeption.NoData("{10019}参考值Id:" + refValueIddd + ",值:" + calcString + ",其值为空!");
                        }
                        if (calcString.indexOf("E") >= 0) {     //----科学计数法的字符串-转换--//
                            temp = StringUtil.getDoubleNumber(calcString);
                            calcString = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(calcString)) {   //---校验字符串是否为正浮点数
                                // throw new DBExeption.NoData("参考值id：" + refValueIddd + "其返回值:" + calcString + "不是非负浮点数{10042}");
                                if ('-' == calcString.charAt(0)) {// 开头为负数，如-1，改为0-1
                                    calcString = 0 + calcString;
                                    calcString = "(" + calcString + ")";
                                }

                            }
                        }

                    } else {
                        operationChar = calcList.get(ii).toString().split("_")[0];
                        operationChar = operationChar.replaceAll(OPERATOR_ADD, "+");
                        operationChar = operationChar.replaceAll(OPERATOR_MINU, "-");
                        operationChar = operationChar.replaceAll(OPERATOR_MULTIPLY, "*");
                        operationChar = operationChar.replaceAll(OPERATOR_DIVIDE, "/");
                        refValueIdd = calcList.get(ii).toString().split("_")[1];
                        resultBean.getTariffStr().append(",");                        //----获取运行轨迹
                        resultBean.getTariffStr().append(refValueIdd + "(id)" + "=");     //----获取运行轨迹
                        refValueVV = getPricingRefIdValue(refValueIdd, resultBean);
                        if (null == refValueVV || "".equals(refValueVV)) {
                            throw new DBExeption.NoData("{10019}参考值Id:" + refValueIdd + ",值:" + refValueVV + ",其值为空!");
                        }
                        if (refValueVV.indexOf("E") >= 0) {                          //----科学计数法的字符串-转换--//
                            temp = StringUtil.getDoubleNumber(refValueVV);
                            refValueVV = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(refValueVV)) {   //---校验字符串是否为正浮点数
                                if ('-' == refValueVV.charAt(0)) {// 开头为负数，如-1，改为0-1
                                    refValueVV = 0 + refValueVV;
                                    refValueVV = "(" + refValueVV + ")";
                                }
                                //  throw new DBExeption.NoData("参考值id：" + refValueIddd + "其返回值:" + refValueVV + "不是正浮点数{10042}");
                            }
                        }
                        calcString += operationChar + refValueVV;
                    }
                }
                double resultValue = 0;
                try {
                    resultValue = Calculator.compute(calcString + "=");   //-----计算运算参考值
                } catch (Exception ex) {
                    throw new DBExeption.BadExecution("{10020}计算公式有误:" + calcString + "其参考值Id:" + refValueId);
                }
                BigDecimal b = new BigDecimal(resultValue);
                resultValue = b.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
                String tempValue = StringUtil.formatDouble(resultValue);
                resultBean.getTariffStr().append(",").append(refValueId + "(id)").append("=").append(tempValue);
                //---------------保存参考值的值-在全局变量中----------//
                tempMp.put("value", tempValue);
                if (refIdValueMp.get(refValueId) == null)
                    refIdValueMp.put(refValueId, tempMp);
                //------------------------------------------------//
                return tempValue;
            }
        } else if (RefValueTypeVar.equals(refValueType)) {     //----4-中间变量
            if (variableMp.get(refValueId) == null) {
                throw new DBExeption.BadExecution("{10021}策略id:" + eventstrategyId + "下中间变量id:" + refValueId + "没有被赋值!");
            }
            //------2022.8.29--------获取中间变量的值-------------------//
            return variableMp.get(refValueId).get("value").toString();
            //-------------------------------------------------------//
        }
        return null;
    }


    /**
     * 根据参考值Id,获取参考值的值
     *
     * @param refValueId
     * @return
     * @throws Exception
     */
    @Override
    public String getPricingRefValueById(String refValueId) throws Exception {
        String valueString = "", calcString = "", tempValue = null;
        double temp = 0;
        Map<String, String> refValueMp = getPricingRefValueData(refValueId); //---根据参考值ID,获取对应的整条信息
        if (refValueMp == null)
            return null;
        String refValueType = refValueMp.get("ref_value_type");              //---参考值类型
        if (null == refValueType)
            throw new DBExeption.NoData("参考值id:" + refValueId + "参考值类型(ref_value_type)没有配置{10016}");
        //----参考值类型(RefValueTypeString--常量；RefValueTypeParam--内部参数；RefValueTypeObject--参考对象 ;
        if (RefValueTypeString.equals(refValueType)) {                        //---常量---1
            valueString = refValueMp.get("value_string");                    //---常量值
            return valueString;
        } else if (RefValueTypeParam.equals(refValueType)) {                   //---内部参数---2
            String pricingParamId = refValueMp.get("pricing_param_id");
            List<String> list1 = getEnumParamValueWithParamIdData(pricingParamId);  //--根据参数Id,获取枚举值
            if (list1 == null || list1.size() < 1) {
                throw new DBExeption.NoData("参考值id:" + refValueId + "内部参数:" + pricingParamId + "找不到枚举值(PRICING_ENUM_PARAM){10017}");
            } else {
                valueString = StringUtil.listToString(list1, ',');  //--list转字符串
                return valueString;
            }
        } else if (RefValueTypeObject.equals(refValueType)) {     //---参考对象--3
            String pricingRefObjectId = refValueMp.get("pricing_ref_object_id");
            Map<String, String> objectMp = getPricingRefObjectData(pricingRefObjectId);
            String propertyType = (String) objectMp.get("property_type");                     //---属主属性类型
            String propertyDefineId = (String) objectMp.get("property_define_id");            //--属主属性标识
            String externPropertyString = (String) objectMp.get("extern_property_string");  //--外部属性标识
            if (PropertyType.equals(propertyType)) {                   //---PropertyType-属性值类属性 5
                String en_name = getTprResourceAttr(propertyDefineId, "en_name");  //---属性英文名称
                String attr_value = jsonObject.getString(en_name);
                if (attr_value == null) {
                    throw new DBExeption.NoData("参考对象id=" + pricingRefObjectId + ",话单记录字段" + en_name + "其值为空{10022}");
                }
                return attr_value;
            } else if (ExternPropertyType.equals(propertyType)) {     //---ExternPropertyType-外部属性 函数4
                if (calcFunction == null) {
                    calcFunction = CalcFunctionLocal.getInstance(threadNum);
                }

                Result result = new Result(false);
                try {
                    result = calcFunction.calcRefValue(Long.parseLong(refValueId), jsonObject);
                } catch (Exception ex) {
                    throw new DBExeption.NoData("{10018}调用函数失败:参考值id=" + refValueId + "参考对象id=" + pricingRefObjectId + ",ex:" + ex.toString());
                }
                if (!result.isBRet()) {
                    throw new DBExeption.NoData("{10018}调用函数失败[参考值id：" + refValueId + "][参考对象id:" + pricingRefObjectId + "] error_msg:" + result.getError_msg());
                }
                valueString = String.valueOf(result.get("VALUE"));
                return valueString;
            } else if (WIDTHTABLEPropertyType.equals(propertyType)) {     //---宽表调用  3
                valueString = tableService.getColuValueByattr(externPropertyString, jsonObject);
                return valueString;
            } else if (CACHEFUNCTIONPropertyType.equals(propertyType)) {   //---6--缓存因子属性
                Map<String, Object> params = JSONObject.parseObject(jsonObject.toJSONString(), new TypeReference<Map<String, Object>>() {
                });
                Map<String, Object> resultMp = new HashMap<>();
               /* boolean iRet=calcVisualFunctionLocal.calcFunction(Integer.parseInt(externPropertyString),params,resultMp);
                if(iRet){
                    valueString=resultMp.get("VALUE").toString();
                    return valueString ;
                }else{
                    throw new DBExeption.NoData("调用缓存因子,functionId="+externPropertyString+",失败原因="+resultMp.get("executeMsg").toString());
                }*/
            }
        } else if (RefValueTypeCalc.equals(refValueType)) {     //----8-运算参考值处理
            //------获取运算参考值中的计算符和参考值ID
            List<String> calcList = getPricingCalcRefValue(refValueId);
            if (calcList == null || calcList.size() == 0) {
                throw new DBExeption.NoData("{10007}找不到运算参考值=" + refValueId);
            }
            if (calcList.size() == 1) {
                String calcs = calcList.get(0);
                String refVvId = calcs.split("_")[1];  //---参考值ID
                return getPricingRefValueById(refVvId);
            } else {
                for (int ii = 0; ii < calcList.size(); ii++) {
                    if (ii == 0) {
                        //--------参考值的值
                        calcString = getPricingRefValueById(calcList.get(ii).toString().split("_")[1]);
                        if (null == calcString || "".equals(calcString)) {
                            throw new DBExeption.NoData("{10019}参考值Id:" + calcList.get(ii).toString().split("_")[1] + ",值:" + calcString + ",其值为空!");
                        }
                        if (calcString.indexOf("E") >= 0) {     //----科学计数法的字符串-转换--//
                            temp = StringUtil.getDoubleNumber(calcString);
                            calcString = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(calcString)) {   //---校验字符串是否为正浮点数
                                if ('-' == calcString.charAt(0)) {// 开头为负数，如-1，改为0-1
                                    calcString = 0 + calcString;
                                    calcString = "(" + calcString + ")";
                                }
                                //  throw new DBExeption.NoData("参考值id：" + refValueIddd + "其返回值:" + refValueVV + "不是正浮点数{10042}");
                            }
                        }
                    } else {
                        tempValue = getPricingRefValueById(calcList.get(ii).toString().split("_")[1]);
                        if (null == tempValue || "".equals(tempValue)) {
                            throw new DBExeption.NoData("{10019}参考值Id:" + calcList.get(ii).toString().split("_")[1] + ",值:" + tempValue + ",其值为空!");
                        }
                        if (tempValue.indexOf("E") >= 0) {   //----科学计数法的字符串-转换--//
                            temp = StringUtil.getDoubleNumber(tempValue);
                            tempValue = StringUtil.formatDouble(temp);
                        } else {
                            if (!StringUtil.isPositiveNumeric(tempValue)) {   //---校验字符串是否为正浮点数
                                if ('-' == calcString.charAt(0)) {// 开头为负数，如-1，改为0-1
                                    tempValue = 0 + tempValue;
                                    tempValue = "(" + tempValue + ")";
                                }
                                //  throw new DBExeption.NoData("参考值id：" + refValueIddd + "其返回值:" + refValueVV + "不是正浮点数{10042}");
                            }
                        }
                        calcString += calcList.get(ii).toString().split("_")[0] + tempValue;
                    }
                }
                double resultValue = 0;
                try {
                    resultValue = Calculator.compute(calcString + "=");   //-----计算运算参考值
                } catch (Exception ex) {
                    throw new DBExeption.BadExecution("{10020}计算公式有误:" + calcString + "其参考值Id:" + refValueId);
                }
                tempValue = StringUtil.formatDouble(resultValue);
                return tempValue;
            }
        } else if (RefValueTypeVar.equals(refValueType)) {  //--4-中间变量
            if (variableMp.get(refValueId) == null) {
                throw new DBExeption.BadExecution("策略id:" + eventstrategyId + "下中间变量id:" + refValueId + "没有被赋值!{10021}");
            }
            //------------2022.8.29----中间变量------------------//
            return variableMp.get(refValueId).get("value").toString();
            //-------------------------------------------------//
        }
        return valueString;
    }

    /**
     * 获取方案表中的定价计划
     *
     * @param ticketList
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> getPricingPlanWithTicket(List<String> ticketList, String timeStamp) throws Exception {
        Map<String, Object> resultMap, tmp;
        Map<String, String> offerInstanceMp;
        List<String> list = new ArrayList<>();
        List<String> tempList, planList = new ArrayList<>();
        if (ticketList == null || ticketList.size() == 0)
            return null;
        resultMap = new HashMap<>();
        tmp = new HashMap<>();
        String effDate, expDate, pricingPlanId = null;
        StringBuffer errorMsg = new StringBuffer();
        int errorType = 0;
        for (String ticket : ticketList) {
            tempList = getPricingOfferInstanceWithAppId(ticket);   //---方案表id 列表
            if (tempList != null && tempList.size() > 0) {
                for (String instanceId : tempList) {
                    offerInstanceMp = getPricingOfferInstance(instanceId);
                    pricingPlanId = offerInstanceMp.get("pricing_plan_id");    //---定价计划id
                    effDate = offerInstanceMp.get("eff_date");    //--生效日期
                    expDate = offerInstanceMp.get("exp_date");    //--失效日期
                    if (timeStamp.length() < 8) {
                        throw new DBExeption.NoData("话单finish_time字段的长度小于8位{10055}");
                    }
                    if (Long.parseLong(timeStamp.substring(0, 8)) < Long.parseLong(effDate.substring(0, 8))) {
                        errorMsg.append("定价方案id=").append(instanceId).append(",话单日期小于生效日期");
                        errorType = 10008;
                        print("定价方案id=" + instanceId + ",话单日期小于生效日期" + "  话单日期：" + Long.parseLong(timeStamp.substring(0, 8)) + "生效日期:" + Long.parseLong(effDate.substring(0, 8)));
                        continue;
                    }
                    if (Long.parseLong(timeStamp.substring(0, 8)) >= Long.parseLong(expDate.substring(0, 8))) {
                        errorMsg.append("定价方案id=").append(instanceId).append(",话单日期大等于失效日期");
                        errorType = 10008;
                        print("定价方案id=" + instanceId + ",话单日期大等于失效日期" + "  话单日期：" + Long.parseLong(timeStamp.substring(0, 8)) + "失效日期:" + Long.parseLong(expDate.substring(0, 8)));
                        continue;
                    }
                    if (Long.parseLong(effDate.substring(0, 8)) >= Long.parseLong(expDate.substring(0, 8))) {
                        errorMsg.append("定价方案id=").append(instanceId).append(",生效日期大等于失效日期");
                        errorType = 10008;
                        print("定价方案id=" + instanceId + ",生效日期大等于失效日期" + "  生效日期：" + Long.parseLong(effDate.substring(0, 8)) + "失效日期:" + Long.parseLong(expDate.substring(0, 8)));
                        continue;
                    }
                    tmp.put(pricingPlanId, ticket);
                    planList.add(pricingPlanId);
                }

            }
        }
        if (planList != null && planList.size() > 0) {
            planList = planList.stream().distinct().collect(Collectors.toList());   //---去掉重复定价计划id
            list.addAll(planList);
        }
        if (list != null && list.size() > 0) {
            resultMap.put("planIdList", list);
            resultMap.put("planIdForTicket", tmp);
            /*resultMap.put("error", errorMsg.toString());
            resultMap.put("errorType", errorType);*/
            resultMap.put("error", null);
            resultMap.put("errorType", null);
        } else {
            if (errorMsg.length() > 0) {
                resultMap.put("planIdList", null);
                resultMap.put("error", errorMsg.toString());
                resultMap.put("errorType", errorType);
                return resultMap;
            }
            return null;
        }
        return resultMap;
    }


    /**
     * 根据APP组合ID来获取方案表Id(offer_instance_id)(可能多个，根据优先级来处理）
     *
     * @param appId
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getPricingOfferInstanceWithAppId(String appId) throws Exception {
        List<String> list = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.OfferInstanceApp_KEY_SUFFIX + "_" + appId);
        if (obj == null) {
          /*  log.error("话单APP组合:" + appId + "匹配不到方案表");
            throw new DBExeption.BadExecution("话单APP组合:" + appId + "匹配不到方案表{10028}");*/
            return null;
        }
        list = (List<String>) obj;
        return list;
    }

    /**
     * 根据定价方案表Id来获取定价方案表信息
     *
     * @param offerInstanceId
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, String> getPricingOfferInstance(String offerInstanceId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingOfferInstance_KEY_SUFFIX + "_" + offerInstanceId);
        if (obj == null) {
            log.error("Pricing_Offer_Instance表中找不到方案实例id:" + offerInstanceId);
            throw new DBExeption.BadExecution("{10043}Pricing_Offer_Instance表中找不到方案实例id:" + offerInstanceId);
        }
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据定价计划Id来获取定价计划信息
     *
     * @param pricingplanId
     * @return
     */
    @Override
    public Map<String, String> getPricingPlan(String pricingplanId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingPlan_KEY_SUFFIX + "_" + pricingplanId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据定价计划Id和fieldName来获取fieldName的值
     *
     * @param pricingplanId
     * @param fieldName
     * @return
     */
    @Override
    public String getPricingPlan(String pricingplanId, String fieldName) throws Exception {
        Map map = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingPlan_KEY_SUFFIX + "_" + pricingplanId);
        if (obj == null) {
            log.error("pricing_plan表中找不到pricing_plan_id:" + pricingplanId);
            throw new DBExeption.BadExecution("{10029}pricing_plan表中找不到pricing_plan_id:" + pricingplanId);
        }
        map = (Map<String, String>) obj;
        String vv = (String) map.get(fieldName);
        return vv;
    }

    /**
     * 根据策略ID,来获取对应的策略数据
     *
     * @param eventPricingStrategyId
     * @return
     */
    @Override
    public Map<String, String> getEventPricingStrategyData(String eventPricingStrategyId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.EventPricingStrategy_KEY_SUFFIX + "_" + eventPricingStrategyId);
        if (obj == null) {
            log.error("event_pricing_strategy表中找不到event_pricing_strategy_id:" + eventPricingStrategyId);
            throw new DBExeption.BadExecution("{10030}event_pricing_strategy表中找不到event_pricing_strategy_id:" + eventPricingStrategyId);
        }
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据策略Id,来获取策略对应的字段值
     *
     * @param eventPricingStrategyId
     * @param field
     * @return
     */
    @Override
    public String getEventPricingStrategyData(String eventPricingStrategyId, String field) throws Exception {
        Map<String, String> map;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.EventPricingStrategy_KEY_SUFFIX + "_" + eventPricingStrategyId);
        if (obj == null) {
            log.error("event_pricing_strategy表中找不到event_pricing_strategy_id:" + eventPricingStrategyId);
            throw new DBExeption.BadExecution("{10030}event_pricing_strategy表中找不到event_pricing_strategy_id:" + eventPricingStrategyId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }

    /**
     * 根据定价组合ID,来获取对应的定价组合信息
     *
     * @param pricingCombineId
     * @return
     */
    @Override
    public Map<String, String> getPricingCombineData(String pricingCombineId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingCombine_KEY_SUFFIX + "_" + pricingCombineId);
        if (obj == null) {
            log.error("Pricing_Combine表中找不到Pricing_Combine_id:" + pricingCombineId);
            throw new DBExeption.BadExecution("{10031}Pricing_Combine表中找不到Pricing_Combine_id:" + pricingCombineId);
        }
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据组合ID,来获取其它的字段值
     *
     * @param pricingCombineId
     * @param field
     * @return
     */
    @Override
    public String getPricingCombineData(String pricingCombineId, String field) throws Exception {
        Map<String, String> map;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingCombine_KEY_SUFFIX + "_" + pricingCombineId);
        if (obj == null) {
            log.error("Pricing_Combine表中找不到Pricing_Combine_id:" + pricingCombineId);
            throw new DBExeption.BadExecution("{10031}Pricing_Combine表中找不到Pricing_Combine_id:" + pricingCombineId);
        }
        map = (Map<String, String>) obj;
        vv = map.get(field);
        return vv;
    }

    /**
     * 根据定价计划ID,来获取定价组合ID
     *
     * @param pricingplanId
     * @return
     */
    @Override
    public List<String> getPricingCombineWithPlanId(String pricingplanId) throws Exception {
        List<String> strategyList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingPalnWithCombine_KEY_SUFFIX + "_" + pricingplanId);
        if (obj == null) {
            log.error("根据定价计划Id:" + pricingplanId + "查找不到定价组合！{10032}");
            return null;
        }
        strategyList = (List<String>) obj;
        return strategyList;
    }

    /**
     * 根据策略ID,来获取策略下的父段落
     *
     * @param eventStrategyId
     * @return
     */
    @Override
    public List<String> getPricingParentSection(String eventStrategyId) throws Exception {
        List<String> parentSectionList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.EventStrategyWithSectionId_KEY_SUFFIX + "_" + eventStrategyId);
        if (obj == null) {
            //   log.error("根据策略id(event_Strategy_Id)：" + eventStrategyId + "从段落表中查找不到父段落{10033}");
            return null;
        }
        parentSectionList = (List<String>) obj;
        return parentSectionList;
    }


    /**
     * 根据父段落 来获取 子段落
     *
     * @param parentSectionId
     * @return
     */
    @Override
    public List<String> getChildSection(String parentSectionId) throws Exception {
        List<String> childSectionList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingParentSectionWithChild_KEY_SUFFIX + "_" + parentSectionId);
        if (obj == null) {
            log.error("根据父段落id:" + parentSectionId + "查找不到子段落{10034}");
            return null;
        }
        childSectionList = (List<String>) obj;
        return childSectionList;
    }

    /**
     * 获取 某个段落field的值
     *
     * @param pricingSectionId
     * @param field
     * @return
     */
    @Override
    public String getPricingSectionData(String pricingSectionId, String field) throws Exception {
        Map<String, String> map = null;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingSection_KEY_SUFFIX + "_" + pricingSectionId);
        if (obj == null) {
            log.error("pricing_section表中找不到Pricing_section_id:" + pricingSectionId);
            throw new DBExeption.BadExecution("{10035}pricing_section表中找不到Pricing_section_id:" + pricingSectionId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }


    /**
     * 根据段落ID,来获取对应的段落数据
     *
     * @param pricingSectionId
     * @return
     */
    @Override
    public Map<String, String> getPricingSectionData(String pricingSectionId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingSection_KEY_SUFFIX + "_" + pricingSectionId);
        if (obj == null) {
            log.error("pricing_section表中找不到Pricing_section_id:" + pricingSectionId);
            throw new DBExeption.BadExecution("{10035}pricing_section表中找不到Pricing_section_id:" + pricingSectionId);
        }
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据段落ID,来获取段落对应的定价判断条件ID
     *
     * @param pricingSectionId
     * @return
     */
    @Override
    public List<String> getPricingSectionWithRuleData(String pricingSectionId) throws Exception {
        List<String> list = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingSectionWithRule_KEY_SUFFIX + "_" + pricingSectionId);
        if (obj == null)
            return null;
        list = (List<String>) obj;
        return list;
    }


    /**
     * 根据定价判断条件Id及field名称,获取定价判断条件的field的值
     *
     * @param pricingRuleId
     * @param field
     * @return
     */
    @Override
    public String getPricingRuleData(String pricingRuleId, String field) throws Exception {
        Map<String, String> map = null;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRule_KEY_SUFFIX + "_" + pricingRuleId);
        if (obj == null) {
            log.error("pricing_rule表中找不到Pricing_rule_id:" + pricingRuleId);
            throw new DBExeption.BadExecution("{10036}pricing_rule表中找不到Pricing_rule_id:" + pricingRuleId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }


    /**
     * 根据定价判断条件Id,来获取对应的定价判断条件数据
     *
     * @param pricingRuleId
     * @return
     */
    @Override
    public Map<String, String> getPricingRuleData(String pricingRuleId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRule_KEY_SUFFIX + "_" + pricingRuleId);
        if (obj == null) {
            log.error("pricing_rule表中找不到Pricing_rule_id:" + pricingRuleId);
            throw new DBExeption.BadExecution("{10036}pricing_rule表中找不到Pricing_rule_id:" + pricingRuleId);
        }
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据资费标准ID,来获取资费信息
     *
     * @param tariffId
     * @return
     */
    @Override
    public Map<String, String> getTariffData(String tariffId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.Tariff_KEY_SUFFIX + "_" + tariffId);
        if (obj == null) {
            log.error("tariff表中找不到tariff_id:" + tariffId);
            throw new DBExeption.BadExecution("{10037}tariff表中找不到资费(tariff_id):" + tariffId);
        }
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据资费标准ID和字段名,来获取相应字段的信息
     *
     * @param tariffId
     * @param field
     * @return
     */
    public String getTariffData(String tariffId, String field) throws Exception {
        Map<String, String> map;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.Tariff_KEY_SUFFIX + "_" + tariffId);
        if (obj == null) {
            log.error("tariff表中找不到资费id:" + tariffId);
            throw new DBExeption.BadExecution("{10037}tariff表中找不到资费id(tariff_id):" + tariffId);
        }
        map = (Map<String, String>) obj;
        vv = map.get(field);
        return vv;
    }

    /**
     * 根据资费标准ID ,来获取段落的标识
     *
     * @param tariffId
     * @return
     * @throws Exception
     */
    public Map<String, Object> getAllSectionWithTariffData(String tariffId) throws Exception {
        Map<String, String> map;
        String pricing_section_id = null;
        List<String> list = new ArrayList<String>();
        Map<String, Object> resultMp = new HashMap<String, Object>();
        try {
            pricing_section_id = getTariffData(tariffId, "pricing_section_id");
            if (null == pricing_section_id || "".equals(pricing_section_id) || "null".equals(pricing_section_id))
                return null;
            list.add(pricing_section_id);
            getAllParentSectionId(pricing_section_id, list);
            resultMp.put("sectionId", list);           //----资费对应段落（包括父段落）
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("调用getAllSectionWithTariffData方法异常：" + ex.getMessage());
            throw ex;
        }
        return resultMp;
    }

    /**
     * 递归调用（根据子段落标识来查找所有的父段落）以及返回对应的策略标识
     *
     * @param pricing_section_id
     * @param list
     * @throws Exception
     */
    public void getAllParentSectionId(String pricing_section_id, List<String> list) throws Exception {
        String parent_section_id = getPricingSectionData(pricing_section_id, "parent_section_id");
        if (parent_section_id != null && !"".equals(parent_section_id) && !"null".equals(parent_section_id)) {
            list.add(parent_section_id);
            getAllParentSectionId(parent_section_id, list);
        }
    }

    /**
     * 根据段落ID,来获取资费ID
     *
     * @param sectionId
     * @return
     */
    @Override
    public List<String> getPricingSectionWithTariffData(String sectionId) throws Exception {
        List<String> tariffList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingSectionWithTariff_KEY_SUFFIX + "_" + sectionId);
        if (obj == null) {
            return null;
        }
        tariffList = (List<String>) obj;
        return tariffList;
    }


    /**
     * 根据定价参考值Id,来获取定价参考值信息
     *
     * @param refValueId
     * @return
     */
    @Override
    public Map<String, String> getPricingRefValueData(String refValueId) throws Exception {
        Map<String, String> map = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRefValue_KEY_SUFFIX.trim() + "_" + refValueId.trim());
        if (obj == null) {
            log.error("pricing_ref_value表找不到参考值id:" + refValueId);
            throw new DBExeption.BadExecution("{10038}pricing_ref_value表找不到参考值id:" + refValueId);
        }
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据定价参考值Id及field名称,来获取定价参考值中field值
     *
     * @param refValueId
     * @param field
     * @return
     */
    @Override
    public String getPricingRefValueData(String refValueId, String field) throws Exception {
        Map<String, String> map = null;
        String vv;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRefValue_KEY_SUFFIX + "_" + refValueId);
        if (obj == null) {
            log.error("pricing_ref_value表找不到参考值id:" + refValueId);
            throw new DBExeption.BadExecution("{10038}pricing_ref_value表找不到参考值id:" + refValueId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }

    /**
     * 根据定价参考对象ID,获取对应的定价参考对象信息
     *
     * @param refObjectId
     * @return
     */
    @Override
    public Map<String, String> getPricingRefObjectData(String refObjectId) throws Exception {
        Map<String, String> map = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRefObject_KEY_SUFFIX + "_" + refObjectId);
        if (obj == null) {
            log.error("pricing_ref_object表找不到参考对象id:" + refObjectId);
            throw new DBExeption.BadExecution("{10039}pricing_ref_object表找不到参考对象id:" + refObjectId);
        }
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据定价参考对象ID及field ,获取对应field的值
     *
     * @param refObjectId
     * @param field
     * @return
     */
    @Override
    public String getPricingRefObjectData(String refObjectId, String field) throws Exception {
        Map<String, String> map = null;
        String vv;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingRefObject_KEY_SUFFIX + "_" + refObjectId);
        if (obj == null) {
            log.error("pricing_ref_object表找不到参考对象id:" + refObjectId);
            throw new DBExeption.BadExecution("{10039}pricing_ref_object表找不到参考对象id:" + refObjectId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }


    /**
     * 根据定价内部参数ID来获取其值
     *
     * @param pricingParamId
     * @return
     */
    @Override
    public List<String> getEnumParamValueWithParamIdData(String pricingParamId) {
        List<String> tariffList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.EnumParamValueWithParamId_KEY_SUFFIX + "_" + pricingParamId);
        if (obj == null)
            return null;
        tariffList = (List<String>) obj;
        return tariffList;
    }

    /**
     * 根据内部参数Id,来获取内部参数信息
     *
     * @param pricingParamId
     * @return
     */
    @Override
    public Map<String, String> getPricingParamDefineData(String pricingParamId) {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingParamDefine_KEY_SUFFIX + "_" + pricingParamId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据内部参数Id,来获取内部参数信息
     *
     * @param pricingParamId
     * @return
     */
    public String getPricingParamDefineData(String pricingParamId, String field) {
        Map<String, String> map;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.PricingParamDefine_KEY_SUFFIX + "_" + pricingParamId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }

    /**
     * 根据属性ID,获取其记录信息
     *
     * @param attrId
     * @return
     */
    @Override
    public Map<String, String> getTprResourceAttr(String attrId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.TprResourceAttr_KEY_SUFFIX + "_" + attrId);
        if (obj == null) {
            log.error("tpr_resource_attr表中找不到attr_id:" + attrId);
            throw new DBExeption.BadExecution("{10044}tpr_resource_attr表中找不到attr_id:" + attrId);
        }
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据属性ID和字段名,获取其记录信息
     *
     * @param attrId
     * @param field
     * @return
     */
    @Override
    public String getTprResourceAttr(String attrId, String field) throws Exception {
        Map<String, String> map;
        String vv;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.TprResourceAttr_KEY_SUFFIX + "_" + attrId);
        if (obj == null) {
            log.error("tpr_resource_attr表中找不到attr_id:" + attrId);
            throw new DBExeption.BadExecution("{10044}tpr_resource_attr表中找不到attr_id:" + attrId);
        }
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }


    /**
     * 根据量本类型ID,获取相应的量本类型的信息
     *
     * @param accuTypeId
     * @return
     */
    @Override
    public Map<String, String> getAccuTypeData(String accuTypeId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuType_KEY_SUFFIX + "_" + accuTypeId);
        if (obj == null) {
            log.error("accumulation表中找不到accu_type_id:" + accuTypeId);
            throw new DBExeption.BadExecution("accumulation表中找不到accu_type_id:" + accuTypeId);
        }
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据资费标准ID,获取对应的量本类型ID
     *
     * @param tariffId
     * @return
     */
    @Override
    public List<String> getTariffWithAccuTypeData(String tariffId) {
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.TariffWithAccuType_KEY_SUFFIX + "_" + tariffId);
        if (obj == null)
            return null;
        List<String> tariffList = (List<String>) obj;
        return tariffList;
    }


    /**
     * 根据 累积ID,获取累积数据
     *
     * @param accuId
     * @return
     */
    @Override
    public Map<String, String> getAccumulationData(String accuId) {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.Accumulation_KEY_SUFFIX + "_" + accuId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据量本支出记录标识,获取量本支出记录数据
     *
     * @param accuPayoutId
     * @return
     */
    @Override
    public Map<String, String> getAccuPayoutData(String accuPayoutId) {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuPayout_KEY_SUFFIX + "_" + accuPayoutId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }


    /**
     * 根据量本标识，来获取累积支出ID
     *
     * @param accuId
     * @return
     */
    public List<String> getAccuWithAccuPayoutData(String accuId) {
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuWithAccuPayout_KEY_SUFFIX + "_" + accuId);
        if (obj == null)
            return null;
        List<String> tariffList = (List<String>) obj;
        return tariffList;
    }


    /**
     * 累积使用信息
     *
     * @param accuUseId
     * @return
     */
    public Map<String, String> getProdInstAccuUseInfoData(String accuUseId) {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.ProdInstAccuUseInfo_KEY_SUFFIX + "_" + accuUseId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据量本标识，来获取累积使用信息
     *
     * @param accuId
     * @return
     */
    public List<String> getAccuWithAccuUseInfoData(String accuId) {
        List<String> tariffList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuWithProdInstAccuUseInfo_KEY_SUFFIX + "_" + accuId);
        if (obj == null)
            return null;
        tariffList = (List<String>) obj;
        return tariffList;
    }

    /**
     * 获取子段落
     *
     * @param allList
     * @param parentSectionId
     */
    public void getchildsectionId(List<String> allList, String parentSectionId) throws Exception {
        if (!allList.contains(parentSectionId))
            allList.add(parentSectionId);
        String child_num = getPricingSectionData(parentSectionId, "child_count");
        if (!"0".equals(child_num)) {   //---有子段落
            List<String> childSectionIds = getChildSection(parentSectionId);
            if (childSectionIds != null && childSectionIds.size() > 0) {
                for (String sectionId : childSectionIds) {
                    if (!allList.contains(sectionId))
                        allList.add(sectionId);
                    getchildsectionId(allList, sectionId);
                }
            }
        }
    }

    /**
     * 运算参考值，根据运算参考值ID,获取计算操作符和定价参考值ID
     *
     * @param pricingRefvalueId
     * @return
     * @throws Exception
     */
    public List<String> getPricingCalcRefValue(String pricingRefvalueId) throws Exception {
        List<String> list = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.CalcRefValue_KEY_SUFFIX + "_" + pricingRefvalueId);
        if (obj == null)
            return null;
        list = (List<String>) obj;
        return list;
    }

    /**
     * 调用外部函数接口
     *
     * @param billingcycle
     * @param keyattrvalue
     * @param attr_id
     * @return
     * @throws Exception
     */
    public String getFuctionValue(String billingcycle, String keyattrvalue, String attr_id) throws Exception {
        String keyredis = billingcycle + "_" + keyattrvalue;
        String value = null;
        try {
            value = pre_redis.hget(keyredis, attr_id);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
        }
        return value;
    }


    public boolean dealAccuPayout(Map<String, String> map) throws Exception {
        String appId = map.get("appId");   //----obj_id
        String apiId = map.get("apiId");   //----offer_inst_id
        String accuId = map.get("accuId");
        String timeStamp = map.get("timeStamp");
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        long accuPayOutId = 0L;
        Map<String, Object> accuMap = new HashMap<String, Object>();
        //  String lockValue=NoUtilS.getUUID();
        try {
            //--------新增序列加1 ----------//
            //  GeneralKeyFun.lock(RedisKeySuffix.AccuPayOutIDKey_KEY_SUFFIX, lockValue, NoUtilS.lockExpireTime, NoUtilS.lockTimeout);
            accuPayOutId = jedis.incr(RedisKeySuffix.AccuPayOutIDKey_KEY_SUFFIX);
            accuMap.put("key_id", RedisKeySuffix.AccuPayOutIDKey_KEY_SUFFIX);
            accuMap.put("value_id", accuPayOutId);
            if (accuPayOutId == 1)
                ratingProcess.uploadTableService.insertAccuKeyTab(accuMap);
            else
                ratingProcess.uploadTableService.updateAccuKeyTab(accuMap);
            //-----------------------------------------//
            AccuPayOutBean bean = new AccuPayOutBean();
            bean.setAccuPayoutId(accuPayOutId + "");
            bean.setRequestId(requestId);
            bean.setAccuId(accuId);
            bean.setPayoutType("3");
            bean.setPayoutId(timeStamp);
            bean.setProdInstId(apiId);
            bean.setObjId(appId);
            bean.setOpValue("1");
            bean.setRemark("");
            bean.setStatusCd("1000");
            bean.setCreateStaff("1");
            bean.setUpdateStaff("1");
            bean.setCreateDate(localDateTime.format(dateTimeFormatter));
            bean.setUpdateDate(localDateTime.format(dateTimeFormatter));
            bean.setStatusDate(localDateTime.format(dateTimeFormatter));
            accuPayoutList.add(bean);
        } catch (Exception ex) {
            log.error("调用dealAccuPayout错误:" + ex.getMessage());
            throw new DBExeption.BadExecution("调用dealAccuPayout错误:" + ex.getMessage());
        } finally {
            //  GeneralKeyFun.unLock(RedisKeySuffix.AccuPayOutIDKey_KEY_SUFFIX, lockValue);
        }
        return true;
    }

    /**
     * 处理累积使用信息,返回累积量的值
     *
     * @param map
     * @return
     */
    public long dealProdInstAccuUseInfo(Map<String, String> map) throws Exception {
        String appId = map.get("appId");
        String apiId = map.get("apiId");
        String accuTypeId = map.get("accuTypeId");
        String billingCycleId = map.get("billingCycleId");
        String keyId = appId + "_" + apiId + "_" + accuTypeId;
        List accuIdList = null, accuUserIdList = null;
        String accuId = null;

        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuIdWithCombObjId_KEY_SUFFIX + "_" + keyId);
        if (obj == null) {
            log.error("accumulation表中找不到obj_id_offer_inst_id_accu_type_id的组合值:" + keyId);
            throw new DBExeption.BadExecution("accumulation表中找不到obj_id_offer_inst_id_accu_type_id的组合值:" + keyId);
        }
        accuIdList = (List<String>) obj;
        accuId = accuIdList.get(0).toString();           //----获取accuId

        keyId = appId + "_" + apiId + "_" + accuId + "_" + billingCycleId;  //-- 唯一性 主键
        long accuUseId = 0L;
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        long amount = 0L;
        RedisList redisList;
        String keyVV;


        keyVV = jedis.get(RedisKeySuffix.AccuUseIdWithCombObjId_KEY_SUFFIX + "_" + keyId);
        //-------已经存在主键-keyId------------//
        if (keyVV != null) {
            accuUseId = Long.parseLong(keyVV);  //-----获取accuUseIdredis

            amount = jedis.hincrBy(RedisKeySuffix.ProdInstAccuUseInfo_KEY_SUFFIX + "_" + accuUseId, "usage_amount", 1L);
            jedis.hset(RedisKeySuffix.ProdInstAccuUseInfo_KEY_SUFFIX + "_" + accuUseId, "update_date", localDateTime.format(dateTimeFormatter));
            //-------------更新累积量的值-----------------//
            //     ratingProcess.uploadTableService.updateAccuUseTab(amount,accuUseId) ;   //---更新amount
            //-----------------------------------------//
            return amount;

        } else {
            accuUseId = jedis.incr(RedisKeySuffix.AccuUseIDKey_KEY_SUFFIX);
            jedis.set(RedisKeySuffix.AccuUseIdWithCombObjId_KEY_SUFFIX + "_" + keyId, String.valueOf(accuUseId));
            //---------------------------------//
            Map<String, String> mm = new HashMap();
            mm.put("accu_user_id", accuUseId + "");
            mm.put("accu_id", accuId);
            mm.put("obj_id", apiId);
            mm.put("offer_inst_id", appId);
            mm.put("billing_cycle_id", billingCycleId);
            mm.put("usage_amount", "1");
            mm.put("usage_voice", "0");
            mm.put("usage_sms", "0");
            mm.put("status_cd", "1000");
            mm.put("create_date", localDateTime.format(dateTimeFormatter));
            mm.put("update_date", localDateTime.format(dateTimeFormatter));
            mm.put("status_date", localDateTime.format(dateTimeFormatter));
            jedis.hmset(RedisKeySuffix.ProdInstAccuUseInfo_KEY_SUFFIX + "_" + accuUseId, mm);
            return 1L;
        }


    }

    /**
     * 判断资费ID对应的累积量是否超出范围(小于某值时返回false，否则返回true)
     *
     * @param map
     * @return
     */
    public boolean dealTariffWithAccuTypeId(Map<String, String> map) throws Exception {
        String appId = map.get("appId");
        String apiId = map.get("apiId");
        String accuTypeId = map.get("accuTypeId");
        String keyId = appId + "_" + apiId + "_" + accuTypeId;
        List accuIdList = null;
        String accuVal = null;

        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.AccuIdWithCombObjId_KEY_SUFFIX + "_" + keyId);
        if (obj == null) {
            log.error("cache读取accumulation找到组合key:" + keyId + ",其值为空");
            return false;
        }
        accuIdList = (List<String>) obj;

        String accuId = accuIdList.get(0).toString();    //----获取accuId
        map.put("accuId", accuId);
        Map<String, String> mp = null;
        //    dealAccuPayout(map);   //----插入累积支出记录
        obj = MemoryCacheUtils.getData(RedisKeySuffix.Accumulation_KEY_SUFFIX + "_" + accuId);
        if (obj == null)
            return false;
        mp = (Map<String, String>) obj;
        accuVal = mp.get("accu_val").toString();   //----获取累积量的值
        if (accuVal == null || accuVal.equals(""))
            return false;
        long accuValue = dealProdInstAccuUseInfo(map);  //---计算累积
        if (accuValue > Long.parseLong(accuVal))
            return true;
        else
            return false;

    }


    /**
     * 根据事件类型标识来获取事件类型全部信息
     *
     * @param eventTypeId
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, String> getRatableEventTypeData(String eventTypeId) throws Exception {
        Map<String, String> map;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.RatableEventType_KEY_SUFFIX + "_" + eventTypeId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        return map;
    }

    /**
     * 根据事件类型标识来获取事件类型对应的字段值
     *
     * @param eventTypeId
     * @param field
     * @return
     */
    @Override
    public String getRatableEventTypeData(String eventTypeId, String field) throws Exception {
        Map<String, String> map;
        String vv = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.RatableEventType_KEY_SUFFIX + "_" + eventTypeId);
        if (obj == null)
            return null;
        map = (Map<String, String>) obj;
        vv = (String) map.get(field);
        return vv;
    }


    public void removeKey(VProxyJedis jedis, String key) {
        String lockValue = NoUtilS.getUUID();
        long lockExpireTime = 10 * 1000;
        long timeout = 10 * 1000;
        try {
            if (jedis.exists(key)) {
                try {
                    jedis.lock(key, lockValue, lockExpireTime, timeout);
                    jedis.del(key);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    jedis.unlock(key, lockValue);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    private StringBuffer getStrategys(String eventstrategys, List<Long> strategyList) {
        StringBuffer result = new StringBuffer();
        String[] strategys = eventstrategys.split("!");
        for (String temp : strategys) {
            if (!temp.equals("")) {
                if (strategyList.contains(Long.parseLong(temp)))
                    result.append("!").append(temp).append("," + true);
                else
                    result.append("!").append(temp).append("," + false);
            }
        }
        return result;
    }


    /**
     * 根据段落ID来获取中间变量的值
     *
     * @param sectionId
     * @return
     */
    @Override
    public List<String> getVariableRefValue(String sectionId) throws Exception {
        List<String> tariffList = null;
        Object obj = MemoryCacheUtils.getData(RedisKeySuffix.VariableRefValue_KEY_SUFFIX + "_" + sectionId);
        if (obj == null) {
            return null;
        }
        tariffList = (List<String>) obj;
        return tariffList;
    }

    /**
     * 加载表到内存
     *
     * @param uploadType
     * @return
     * @throws Exception
     */
    public boolean uploadToCache(int uploadType, int rangeId, int threadNum) throws Exception {
        this.uploadType = uploadType;
        this.threadNum = threadNum;
        return ratingProcess.uploadTableService.uploadToCache(uploadType, rangeId);
    }


    /**
     * 话单记录写入异常信息
     *
     * @param jsonObject
     */
    private void writeAbortMessage(JSONObject jsonObject) {
        jsonObject.put("ticket_type", 1); //----异常单,不要处理流程
        jsonObject.put("a_charge", new String[]{});
        jsonObject.put("a_tariff_id", new String[]{});
        jsonObject.put("a_strategy_id", new String[]{});
        jsonObject.put("a_pricing_plan_id", new String[]{});
        jsonObject.put("a_acct_item_type_id", new String[]{});
        jsonObject.put("a_fee_cycle_id", new String[]{});
        jsonObject.put("a_billing_cycle_id", new String[]{});
        jsonObject.put("a_if_deduct_old_owe", new String[]{});
        jsonObject.put("a_settle_obj_id", new String[]{});
        jsonObject.put("a_settle_role", new String[]{});
        jsonObject.put("a_settle_obj_type", new String[]{});
        // 针对进入异常单 金额字段处理 多0 情况
        Long charge = jsonObject.getLong("charge");
        Long eventClass = jsonObject.getLong("event_class");
        if (eventClass == 2) {
            if (null != charge && charge > 0) {
                jsonObject.put("charge", charge / 100);
            }
        }
        if ("DIGIT".equals(CalcRecordManager.getCommon().getSSystem()) && "010".equals(CalcRecordManager.getCommon().getSLatnId())) {
            baseDataHandler.releaseAccumulateLock(jsonObject);
        }
    }

    private int calculatePricingPlanType() throws Exception {
        int flag = 0;  //---flag--定价计划类型为10 ,全部只能生产一笔数据
        String ratingProcessType = (String) MemoryCacheUtils.getData("RatingProcessType");
        switch (ratingProcessType) {
            case "10":           //---整个话单只出一笔
                flag = 0;
                break;
            case "20":          //----一个策略只出一笔
                flag = 1;
                break;
            case "30":         //---一个定价计划只出一笔
                flag = 2;
                break;
            case "40":        //---有几笔出几笔（没有限制）
                flag = 3;
                break;
            default:
                flag = 3;
                break;
        }

        return flag;
    }


    /**
     * 清除bean的数据
     *
     * @param bean
     */
    private void clearBean(ProcessResultBean bean, List<Long> list1, List<Long> list2, List<Long> list3) {
        bean.getAIfDeductList().clear();
        bean.getFeeCycleIdList().clear();
        bean.getStrategyList().clear();
        bean.getTariffList().clear();
        bean.getAcctItemTypeList().clear();
        bean.getChargeList().clear();
        bean.getPricingPlanList().clear();
        //    bean.getEventStrategyMp().clear();
        bean.getChargeMp().clear();
        bean.getABillCycleIdList().clear();
        bean.getSettleObjectIdList().clear();
        bean.getSettleObjectTypeList().clear();
        bean.getSettleObjectRoleList().clear();
        bean.getTariffStr().setLength(0);
        bean.getRefValueStr().clear();
        bean.getPricingSectionStr().setLength(0);
        bean.getEventStrategyStr().setLength(0);
        bean.getErrorTypeMsg().setLength(0);
        list1.clear();
        list2.clear();
        list3.clear();
        bean = null;
    }

    /**
     * 从配置表中获取话单与方案表的匹配字段
     *
     * @param ticketList
     * @return
     * @throws Exception
     */
    private List<String> getSchemePlan(List<Map<String, Object>> ticketList) throws Exception {
        String temp1, temp2, temp3;
        List<String> schemePlans = new ArrayList<>();
        for (Map<String, Object> fieldMp : ticketList) {
            temp1 = fieldMp.get("object_id") == null ? "" : fieldMp.get("object_id").toString().toLowerCase();   //---对应的话单字段的值(object_type,partner_id,api_id)
            temp2 = fieldMp.get("object_type") == null ? "" : fieldMp.get("object_type").toString().toLowerCase();
            temp3 = fieldMp.get("object_role") == null ? "" : fieldMp.get("object_role").toString().toLowerCase();
            if (!"".equals(temp1)) {
                if (!StringUtil.isNumeric(temp1)) {
                    temp1 = jsonObject.getString(temp1) == null ? "" : jsonObject.getString(temp1);
                }
            }
            if (!"".equals(temp2)) {
                if (!StringUtil.isNumeric(temp2)) {
                    temp2 = jsonObject.getString(temp2) == null ? "" : jsonObject.getString(temp2);
                }
            }
            if (!"".equals(temp3)) {
                if (!StringUtil.isNumeric(temp3)) {
                    temp3 = jsonObject.getString(temp3) == null ? "" : jsonObject.getString(temp3);
                }
            }
            if (!(temp1.equals("") && temp2.equals("") && temp3.equals("")))
                schemePlans.add(temp1 + "_" + temp2 + "_" + temp3);
        }
        return schemePlans;
    }

    /**
     * 获取过程轨迹串
     *
     * @param bean
     * @return
     */
    private String getElementStr(ProcessResultBean bean) {
        if (bean == null || bean.getEventStrategyStr() == null || bean.getEventStrategyStr().length() == 0)
            return null;
        String eventStrategys = bean.getEventStrategyStr().toString();
        StringBuffer elementStr;
        elementStr = getStrategys(eventStrategys, bean.getStrategyList());    //---策略id后面加true或false
        elementStr.append(bean.getPricingSectionStr());       //---段落过程轨迹
        elementStr.append(bean.getTariffStr());              //---资费过程轨迹
        elementStr.append(String.join("", bean.getRefValueStr()));           //---参考值判断条件过程轨迹
        elementStr.append(bean.getVariableStr());          //---中间变量过程轨迹
        return elementStr.toString();

    }

    /**
     * 批价计算各个话单值
     *
     * @param obj
     * @param pricingPlanId
     * @param ticketField
     * @param tariffCharges
     * @param pricingPlanList
     * @param tariffList
     * @param eventStrategyList
     * @param acctItemTypeList
     * @param cycleIdList
     * @param ifDeductList
     * @param settleObjectIdList
     * @param settleObjectTypeList
     * @param settleObjectRoleList
     */
    private void getCalculateData(Object obj, String pricingPlanId, String ticketField, List<Double> tariffCharges, List<Long> pricingPlanList, List<Long> tariffList, List<Long> eventStrategyList, List<Long> acctItemTypeList, List<Integer> cycleIdList, List<Integer> ifDeductList, List<Long> settleObjectIdList, List<Long> settleObjectTypeList, List<Long> settleObjectRoleList) {
        String tariffs = obj.toString().trim();
        String tariffId = tariffs.split("\\|")[0];
        double charge = Double.parseDouble(tariffs.split("\\|")[1]);
        long acctItemTypeId = Long.parseLong(tariffs.split("\\|")[2]);
        String eventstrategy = tariffs.split("\\|")[3];
        String ifDeduct = tariffs.split("\\|")[4];
        String cycleId = tariffs.split("\\|")[5];
        tariffCharges.add(charge);                              //---金额 集合
        tariffList.add(Long.parseLong(tariffId));               //---资费标识 集合
        pricingPlanList.add(Long.parseLong(pricingPlanId));     //---定价计划 集合
        acctItemTypeList.add(acctItemTypeId);                   //---帐目类型 集合
        eventStrategyList.add(Long.parseLong(eventstrategy));
        ifDeductList.add(Integer.parseInt(ifDeduct));
        cycleIdList.add(Integer.parseInt(cycleId));
        settleObjectIdList.add(Long.parseLong(ticketField.split("_")[0]));
        settleObjectTypeList.add(Long.parseLong(ticketField.split("_")[1]));
        settleObjectRoleList.add(Long.parseLong(ticketField.split("_")[2]));
    }

    /**
     * @param bean
     * @param pricingPlanId
     * @param ticketField
     * @param aCharges
     * @param aCharges1
     * @param pricingPlanList
     * @param tariffList
     * @param eventStrategyList
     * @param acctItemTypeList
     * @param feeCycleIdList
     * @param billingCycleIdList
     * @param expCycleIdList
     * @param ifDeductList
     * @param settleObjectIdList
     * @param settleObjectTypeList
     * @param settleObjectRoleList
     */
    private void getCalculateData(DataResultBean bean, String pricingPlanId, String ticketField, List<Double> aCharges, List<Double> aCharges1, List<Long> pricingPlanList, List<Long> tariffList, List<Long> eventStrategyList, List<Long> acctItemTypeList, List<Integer> feeCycleIdList, List<Integer> billingCycleIdList, List<Integer> expCycleIdList, List<Integer> ifDeductList, List<Long> settleObjectIdList, List<Long> settleObjectTypeList, List<Long> settleObjectRoleList) {
        String a_charge = bean.getA_charge();
        String a_charge1 = bean.getA_charge1();
        if (a_charge != null)
            aCharges.add(Double.parseDouble(a_charge));    //---资费1 金额  （TPSS）
        if (a_charge1 != null)
            aCharges1.add(Double.parseDouble(a_charge1));    //---资费2 金额 (ISS)

        tariffList.add(Long.parseLong(bean.getTariffId()));   //---资费标识 集合
        pricingPlanList.add(Long.parseLong(pricingPlanId));     //---定价计划 集合
        if (bean.getAcctItemTypeId() != null && !"".equals(bean.getAcctItemTypeId()))
            acctItemTypeList.add(Long.parseLong(bean.getAcctItemTypeId()));       //---帐目类型 集合
        eventStrategyList.add(Long.parseLong(bean.getEventstrategyId()));   //---策略标识 集合
        ifDeductList.add(Integer.parseInt(bean.getIfDeductOldOwe()));       //---是否立即结算
        feeCycleIdList.add(bean.getFeeCycleId());                           //---帐期
        billingCycleIdList.add(bean.getBillingCycleId());
        expCycleIdList.add(bean.getExpCycleId());
        settleObjectIdList.add(Long.parseLong(ticketField.split("_")[0]));
        settleObjectTypeList.add(Long.parseLong(ticketField.split("_")[1]));
        settleObjectRoleList.add(Long.parseLong(ticketField.split("_")[2]));
    }


    @Override
    public List<Map<String, Object>> getRefValueInvented(String batchId) {
        return ratingProcess.uploadTableService.listRefValueData(Long.parseLong(batchId));
    }

    @Override
    public Map<Integer, FunctionPerl> getmPerl() {
        Map<Integer, FunctionPerl> map = new HashMap<>();
        map.putAll(CalcFunctionLocal.getmPerl());
        map.putAll(CalcVisualFunctionLocal.getmPerl());
        CalcFunctionLocal.clearPerl();
        CalcVisualFunctionLocal.clearPerl();
        return map;
    }

    /**
     * 异常信息写入话单中
     *
     * @param jsonObject
     * @param errorMsg
     */
    private void writeErrorMsg(JSONObject jsonObject, String errorMsg) {
        if (errorMsg == null || "".equals(errorMsg))
            return;
        print(errorMsg);
        errorMsg = errorMsg.replaceAll(",,", ",");
        errorMsg = errorMsg.trim().replaceAll("^(,+)", "");
        StringBuffer msg = new StringBuffer();
        String ruleIdRegx = "(\\{\\d+\\})";
        Pattern p = Pattern.compile(ruleIdRegx);
        Matcher m = p.matcher(errorMsg);
        String temp = null;
        int errorType = 0;
        while (m.find()) {
            temp = m.group(0);
            temp = temp.replaceAll("\\{", "");
            temp = temp.replaceAll("\\}", "");
            errorType = Integer.parseInt(temp);
        }
        errorMsg = errorMsg.replaceAll("\\{\\d+\\}", "");
        if (errorMsg.length() > 1000)
            errorMsg = errorMsg.substring(0, 1000);
        if (errorType != 0) {
            //jsonObject.put("error_type", errorType);
            Factory.setTicketType(jsonObject, 1, errorType, errorMsg);
        } else {
            Factory.setTicketType(jsonObject, 1, null, errorMsg);
        }

    }

    /**
     * @param correctList
     * @param errorMsgMp
     */
    private void dealErrorMsg(StringBuffer msg, List<String> correctList, Map<String, Object> errorMsgMp, Map<String, Object> errorMsgMpByPlan) {
        String planId = null, stragetyId = null, value = null;
        if (errorMsgMp != null && !errorMsgMp.isEmpty()) {
            if (correctList != null && correctList.size() > 0) {
                for (String planIds : correctList) {
                    //  planId=planIds.split("_")[0] ;
                    stragetyId = planIds.split("_")[1];
                    for (String key : errorMsgMp.keySet()) {
                        if (stragetyId.equals(key)) {
                            value = errorMsgMp.get(key).toString();
                            msg.append(",").append(value);
                        }
                    }

                }
            } else {    //---没有一个策略进入段落阶段------//
                for (String key : errorMsgMpByPlan.keySet()) {
                    if (errorMsgMp.get(key) != null) {
                        value = errorMsgMp.get(key).toString();
                        msg.append(",").append(value);
                    }
                }
                for (String key : errorMsgMp.keySet()) {
                    if (errorMsgMp.get(key) != null) {
                        value = errorMsgMp.get(key).toString();
                        msg.append(",").append(value);
                    }
                }
            }
        }
    }


    public void resolveElement(JSONObject jsonObject, ProcessResultBean bean) {
        List<Long> list = bean.getTariffList();      //---资费标识列表
        String strs = bean.getTariffStr().toString();   //---资费计算过程轨迹
        if (list == null || list.size() == 0)
            return;
        int npos = 0;
        String id = null, calcStr = null, calcs = null;
        Map<String, String> calcMp = new HashMap<>();
        Map<String, String> procMp;
        if (strs != null && strs.length() > 0) {
            String[] strVV = strs.split("\\$");
            for (String vv : strVV) {
                if (vv != null && vv.length() > 0) {
                    npos = vv.indexOf(",");
                    id = vv.substring(0, npos);
                    calcStr = vv.substring(npos + 1);
                    calcMp.put(id, calcStr);
                }
            }
        }
        String sss = null;
        String[] vvv = new String[list.size()];
        int ii = 0;
        for (Long tariffId : list) {
            procMp = new HashMap<>();
            sss = calcMp.get(String.valueOf(tariffId));
            String[] temps = sss.split(",");
            for (String temp : temps) {
                String[] aa = temp.split("=");
                if (procMp.get(aa[0]) == null)
                    procMp.put(aa[0], aa[1]);
            }

            calcs = temps[0].split("=")[1];
            calcs = resolve(procMp, calcs);
            while (calcs.indexOf("id") > 0) {
                calcs = resolve(procMp, calcs);
            }
            vvv[ii] = calcs;
            ii++;
        }

        jsonObject.put("a_tariff_fomrule", vvv);

    }


    public String resolve(Map<String, String> map, String elements) {
        String calcs = null;
        List<String> list = getElement(elements);
        for (String tt : list) {
            calcs = map.get(tt);
            if (calcs.indexOf("id") > 0)
                elements = elements.replace(tt, "(" + calcs + ")");
            else
                elements = elements.replace(tt, calcs);
        }
        return elements;
    }

    private List<String> getElement(String element) {
        List<String> xlist = new ArrayList<>();
        String ruleIdRegx = "(\\d+\\(id\\))";
        Pattern p = Pattern.compile(ruleIdRegx);
        Matcher m = p.matcher(element);
        while (m.find()) {
            xlist.add(m.group(0));
        }
        return xlist;
    }


}
