package com.itco.rating.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.UUID;

public class NoUtilS {

    public static long lockExpireTime=3*1000 ;
    public static long lockTimeout=3*1000 ;

    public static String getUUID(){
        String uuid = UUID.randomUUID().toString();
        //去掉“-”符号
        return uuid.replaceAll("-", "");
    }
    public static String getHostName(){
        try {
            InetAddress address=InetAddress.getLocalHost();
            return address.getHostName();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return null;
    }
}
