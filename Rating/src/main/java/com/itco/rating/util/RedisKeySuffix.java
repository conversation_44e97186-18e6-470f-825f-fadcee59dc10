package com.itco.rating.util;



/**
 * ctgcache(redis) key前缀
 */
public class RedisKeySuffix {

    //-------累积支出记录表中的accu_payout_id的序列号对应的键名-----------//
    public final static String AccuPayOutIDKey_KEY_SUFFIX="AccuPayOutIdKey" ;

    //-------累积支出记录表中的accu_payout的对应的键名-----------//
    public final static String AccuPayOutKey_KEY_SUFFIX="AccuPayOut" ;

    //-------累积使用信息表中的ACCU_USE_ID的序列号对应的键名-----------//
    public final static String AccuUseIDKey_KEY_SUFFIX="AccuUseIdKey" ;

    //-------根据APP_ID,API_ID,accu_type_id来查找accu_id-----------//
    public final static String AccuIdWithCombObjId_KEY_SUFFIX="AccuIdWithCombObjId" ;

    //-------根据APP_ID,API_ID,accu_id来查找accu_user_id-----------//
    public final static String AccuUseIdWithCombObjId_KEY_SUFFIX="AccuUseIdWithCombObjId" ;

    //--------累积支出记录--------//
    public final static String AccuPayout_KEY_SUFFIX="AccuPayout" ;

    //--------累积使用信息 --------//
    public final static String ProdInstAccuUseInfo_KEY_SUFFIX="ProdInstAccuUseInfo" ;

    //--------累积--------//
    public final static String Accumulation_KEY_SUFFIX="Accumulation" ;

    //--------根据量本标识，来获取累积支出ID --------//
    public final static String AccuWithAccuPayout_KEY_SUFFIX="AccuWithAccuPayout" ;

    //--------根据量本标识，来获取累积使用信息--------//
    public final static String AccuWithProdInstAccuUseInfo_KEY_SUFFIX="AccuWithAccuUseInfo" ;


    //-------定价计划信息-----------//
    public final static String PricingPlan_KEY_SUFFIX="Pricingplan" ;

    //-------定价计划关联信息-----------//
    public final static String PricingPlanRel_KEY_SUFFIX="PricingplanRel" ;

    //-------定价组合信息-----------//
    public final static String PricingCombine_KEY_SUFFIX="PricingCombine" ;

    //-------定价组合关联信息-----------//
    public final static String PricingCombineRel_KEY_SUFFIX="PricingCombineRel" ;

    //---------方案表中 能力标识与结算对象标识的综合key--------------//
    public final static String OfferInstanceApp_KEY_SUFFIX="OfferInstanceAppId" ;


    //---------方案表--------------//
    public final static String PricingOfferInstance_KEY_SUFFIX="PricingOfferInstance" ;


    //-------一个定价计划对应多个组合Id-----------//
    public final static String PricingPalnWithCombine_KEY_SUFFIX="PricingPlanWithCombine" ;

    //----------策略信息-----------------//
    public final static String EventPricingStrategy_KEY_SUFFIX="EventPricingStrategy" ;

    //----------策略对应的多个父段落-----------------//
    public final static String EventStrategyWithSectionId_KEY_SUFFIX="EventStrategyWithParentSectionId" ;

    //----------段落标识-----------------//
    public final static String PricingSection_KEY_SUFFIX="PricingSection" ;

    //----------段落关联信息-----------------//
    public final static String PricingSectionRel_KEY_SUFFIX="PricingSectionRel" ;

    //----------父段落对应的多个子段落-----------------//
    public final static String PricingParentSectionWithChild_KEY_SUFFIX="ParentSectionWithChildId" ;

    //----------定价判断条件-----------------//
    public final static String PricingRule_KEY_SUFFIX="PricingRule" ;

    //----------段落对应的定价判断条件-----------------//
    public final static String PricingSectionWithRule_KEY_SUFFIX="PricingSectionRule" ;

    //----------定价段落类型-----------------//
    public final static String PricingSectionType_KEY_SUFFIX="PricingSectionType" ;

    //----------资费标准-----------------//
    public final static String Tariff_KEY_SUFFIX="Tariff" ;

    //----------资费标准-----------------//
    public final static String PricingSectionWithTariff_KEY_SUFFIX="SectionToTariff" ;

    //----------定价参考值----------------//
    public final static String PricingRefValue_KEY_SUFFIX="PricingRefValue" ;

    //----------定价参考对象----------------//
    public final static String PricingRefObject_KEY_SUFFIX="PricingRefObject" ;

    //----------条件运算符----------------//
    public final static String Operator_KEY_SUFFIX="Operator" ;


    //--------定价参数有限枚举取值定义---------//
    public final static String EnumParamValueWithParamId_KEY_SUFFIX="EnumParamIdValue" ;

    //--------定价参数定义---------//
    public final static String PricingParamDefine_KEY_SUFFIX="PricingParamDefine" ;

    //--------定价包含对象---------//
    public final static String PricingObject_KEY_SUFFIX="PricingObject" ;

    //--------量本的类型--------//
    public final static String AccuType_KEY_SUFFIX="AccuType" ;

    //--------资费与量本的类型--------//
    public final static String TariffWithAccuType_KEY_SUFFIX="TariffAccuType" ;


    //--------属性表信息--------//
    public final static String TprResourceAttr_KEY_SUFFIX="TprResourceAttr" ;


    //------运算参考值----------//
    public final static String CalcRefValue_KEY_SUFFIX="CalcRefValue" ;


    //--------事件类型----------//
    public final static String   RatableEventType_KEY_SUFFIX="RatableEventType";

    //--------根据段落ID来获取中间变量的值----------//
    public final static String   VariableRefValue_KEY_SUFFIX="VariableRefValue";


}
