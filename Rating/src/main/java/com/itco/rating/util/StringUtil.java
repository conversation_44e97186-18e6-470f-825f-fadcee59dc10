package com.itco.rating.util;

import com.itco.framework.calcrecord.CalcRecordManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class StringUtil {
    private final static Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");

    private final static String[] numericType = {"1", "2", "3"};  //----1-整数，2-浮点数，3-长整型
    private final static String stringType = "4", boolType = "5";  //--4(字符串),5(布尔型),6(日期型)

    public static enum ValueType {   //----参考值值类型
        INTEGER("整数", 1), FLOAT("浮点数", 2), LONGINT("长整型", 3), STRING("字符串", 4), BOOL("布尔型", 5), DATE("日期型", 6);
        // 成员变量
        private String name;
        private int index;

        // 构造方法
        private ValueType(String name, int index) {
            this.name = name;
            this.index = index;
        }

        // 普通方法
        public static String getName(int index) {
            for (ValueType c : ValueType.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static ValueType getByValue(int index) {
            for (ValueType c : ValueType.values()) {
                if (c.getIndex() == index) {
                    return c;
                }
            }
            return null;
        }

        // get set 方法
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    public static enum Operators {
        EQUAL("相等", 1), NOTEQUAL("不等", 2), GREATER("大于", 3), LESS("小于", 4), GREATEREQUAL("大等于", 5), LESSEQUAL("小等于", 6), NOTEXISTS("NOT EXISTS", 7), EXISTS("EXISTS", 8), RIGHTLIKE("LIKE xx%", 9), LEFTLIKE("LIKE %XX", 10), LIKE("LIKE %XX%", 11), NOTLIKERIGHT("NOT LIKE xx%", 12);
        // 成员变量
        private String name;
        private int index;

        // 构造方法
        private Operators(String name, int index) {
            this.name = name;
            this.index = index;
        }

        // 普通方法
        public static String getName(int index) {
            for (Operators c : Operators.values()) {
                if (c.getIndex() == index) {
                    return c.name;
                }
            }
            return null;
        }

        public static Operators getByValue(int index) {
            for (Operators c : Operators.values()) {
                if (c.getIndex() == index) {
                    return c;
                }
            }
            return null;
        }

        // get set 方法
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }
    }

    /**
     * 判断是否是数字型（包括整数和正负小数之类）
     *
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        if (str == null)
            return false;
        Pattern pattern = Pattern.compile("-?[0-9]+(\\.[0-9]+)?");
        return pattern.matcher(str).matches();
    }


    /**
     * 正浮点数
     *
     * @param str
     * @return
     */
    public static boolean isPositiveNumeric(String str) {
        if (str == null)
            return false;
        Pattern pattern = Pattern.compile("[0-9]+(\\.[0-9]+)?");
        return pattern.matcher(str.trim()).matches();
    }

    /**
     * 判断一个字符串是否在一个字符串数组当中
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean inTheArray(String[] a, String b) {
        boolean has = false;
        for (int i = 0; i < a.length; i++)
            if (a[i].contains(b))
                has = true;
        return has;
    }

    /**
     * 根据关系符号来比较两个值是否条件成立
     *
     * @param leftValueMp
     * @param rightValueMp
     * @param operation
     * @return
     */
    public static boolean compareValue(String pricingSection_id, String pricingRule_id, Map<String, String> leftValueMp, Map<String, String> rightValueMp, String operation) throws Exception {
        String leftRefValueId = leftValueMp.get("refValueId");
        String leftValueType = leftValueMp.get("value_type");        //--(1-整数，2-浮点数，3-长整型,4-字符串,5-布尔型,6-日期型)
        String leftValue = leftValueMp.get("value");
        String rightRefValueId = rightValueMp.get("refValueId");
        String rightValueType = rightValueMp.get("value_type");
        String rightValue = rightValueMp.get("value");
        String tempLeft, tempRight;
        //   System.out.println("leftRefValueId->" + leftRefValueId+",leftValueType->" + leftValueType + ",leftValue->" + leftValue +",rightRefValueId->"+rightRefValueId+ ",rightValueType->" + rightValueType + ",rightValue->" + rightValue + ",operation->" + operation);
        try {
            boolean leftFlag = inTheArray(numericType, leftValueType);    //----是否是 整数，浮点数，长整型
            boolean rightFlag = inTheArray(numericType, rightValueType);  //----是否是 整数，浮点数，长整型
            double v1 = 0, v2 = 0;
            boolean result = false;
            switch (Operators.getByValue(Integer.parseInt(operation))) {
                case EQUAL:    //---- 1--相等 =
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return false;
                        }
                    }
                    if (leftFlag && rightFlag) {   //--都属于数字型方面 ,左值都是常量; 右边也有可能是内部参数中的枚举型（1,4,7...)
                        if (rightValue.indexOf(",") < 0) {  //---不是枚举型
                            if (leftValue.indexOf(",") > 0) {
                                String[] temp = leftValue.split(",");
                                if (Arrays.asList(temp).contains(rightValue))  //---存在 就相等
                                    return true;
                                else
                                    return false;
                            } else {
                                v1 = Double.parseDouble(leftValue);
                                v2 = Double.parseDouble(rightValue);
                                if (v1 == v2)
                                    return true;
                                else
                                    return false;
                            }

                        } else {  //--字符串转数组
                            String[] temp = rightValue.split(",");
                            if (leftValue.indexOf(",") > 0) {
                                String[] temp1 = leftValue.split(",");
                                Arrays.sort(temp);
                                Arrays.sort(temp1);
                                if (Arrays.equals(temp, temp1))
                                    return true;
                                else
                                    return false;
                            } else {
                                if (Arrays.asList(temp).contains(leftValue))  //---存在 就相等
                                    return true;
                                else
                                    return false;
                            }

                        }
                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样
                            return false;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型
                            if (!leftValueType.equals(rightValueType))
                                return false;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {  //---布尔型
                                    return Boolean.parseBoolean(leftValue) == Boolean.parseBoolean(leftValue);
                                } else {   //---字符串型
                                    if (rightValue.indexOf(",") > 0) {
                                        String[] temp = rightValue.split(",");
                                        if (Arrays.asList(temp).contains(leftValue))
                                            return true;
                                        else
                                            return false;
                                    } else {
                                        int nn = leftValue.compareTo(rightValue);
                                        if (nn == 0)
                                            return true;
                                        else
                                            return false;
                                    }

                                }
                            }
                        }
                    }
                case NOTEQUAL:    //---2-不等 <>
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return true;
                        }
                    }
                    if (leftFlag && rightFlag) {  //--都属于数字型方面
                        if (rightValue.indexOf(",") < 0) {  //---不是枚举值
                            if (leftValue.indexOf(",") > 0) {
                                String[] temp = leftValue.split(",");
                                if (!Arrays.asList(temp).contains(rightValue))
                                    return true;
                                else
                                    return false;
                            } else {
                                v1 = Double.parseDouble(leftValue);
                                v2 = Double.parseDouble(rightValue);
                                if (v1 != v2)
                                    return true;
                                else
                                    return false;
                            }

                        } else {  //--字符串转数组
                            String[] temp = rightValue.split(",");
                            if (leftValue.indexOf(",") > 0) {
                                String[] temp1 = leftValue.split(",");
                                Arrays.sort(temp);
                                Arrays.sort(temp1);
                                if (!Arrays.equals(temp, temp1))
                                    return true;
                                else
                                    return false;
                            } else {
                                if (!Arrays.asList(temp).contains(leftValue))  //---不存在,就是不等
                                    return true;
                                else
                                    return false;
                            }

                        }
                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样  这里有大问题
                            return true;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型
                            if (!leftValueType.equals(rightValueType))
                                return true;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {  //---布尔型
                                    return !(Boolean.parseBoolean(leftValue) == Boolean.parseBoolean(leftValue));
                                } else {   //---字符串型
                                    if (rightValue.indexOf(",") > 0) {  //---枚举值
                                        String[] temp = rightValue.split(",");
                                        if (!Arrays.asList(temp).contains(leftValue))  //--不存在，就是不等
                                            return true;
                                        else
                                            return false;
                                    } else {
                                        int nn = leftValue.compareTo(rightValue); //--0 代表不等
                                        if (nn == 0)
                                            return false;
                                        else
                                            return true;
                                    }
                                }
                            }
                        }
                    }
                case GREATER:    //---3-大于 >
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return false;
                        }
                    }
                    if (leftFlag && rightFlag) {  //--都属于数字型方面
                        if (rightValue.indexOf(",") < 0) {
                            v1 = Double.parseDouble(leftValue);
                            v2 = Double.parseDouble(rightValue);
                            if (v1 > v2)
                                return true;
                            else
                                return false;
                        } else     //---(枚举值不比较大小，直接返回false)
                            return false;

                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样
                            return false;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型
                            if (!leftValueType.equals(rightValueType))
                                return false;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {  //---布尔型,没有比较大小的
                                    return false;
                                } else {   //---字符串型
                                    int nn = leftValue.compareTo(rightValue);
                                    if (nn > 0)
                                        return true;
                                    else
                                        return false;
                                }
                            }
                        }
                    }
                case LESS:    //---4-小于 <
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return false;
                        }
                    }
                    if (leftFlag && rightFlag) {  //--都属于数字型方面
                        if (rightValue.indexOf(",") < 0) {
                            v1 = Double.parseDouble(leftValue);
                            v2 = Double.parseDouble(rightValue);
                            if (v1 < v2)
                                return true;
                            else
                                return false;
                        } else  //---(枚举值不比较大小，直接返回false)
                            return false;
                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样
                            return false;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型和日期型
                            if (!leftValueType.equals(rightValueType))
                                return false;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {  //---布尔型,没有比较大小的
                                    return false;
                                } else {   //---字符串型
                                    if (rightValue.indexOf(",") > 0)   //--枚举值不比较大小
                                        return false;
                                    int nn = leftValue.compareTo(rightValue);
                                    if (nn < 0)
                                        return true;
                                    else
                                        return false;
                                }
                            }
                        }
                    }
                case GREATEREQUAL:    //---5-大等于 >=
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return false;
                        }
                    }
                    if (leftFlag && rightFlag) {  //--都属于数字型方面
                        if (rightValue.indexOf(",") < 0) {
                            v1 = Double.parseDouble(leftValue);
                            v2 = Double.parseDouble(rightValue);
                            if (v1 >= v2)
                                return true;
                            else
                                return false;
                        } else  //---(枚举值不比较大小，直接返回false)
                            return false;
                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样
                            return false;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型
                            if (!leftValueType.equals(rightValueType))
                                return false;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {   //---布尔型,没有比较大小的
                                    return false;
                                } else {   //---字符串型
                                    if (rightValue.indexOf(",") > 0)   //--枚举值不比较大小
                                        return false;
                                    int nn = leftValue.compareTo(rightValue);
                                    if (nn >= 0)
                                        return true;
                                    else
                                        return false;
                                }
                            }
                        }
                    }
                case LESSEQUAL:    //---6-小等于 <=
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return false;
                        }
                    }
                    if (leftFlag && rightFlag) {  //--都属于数字型方面
                        if (rightValue.indexOf(",") < 0) {
                            v1 = Double.parseDouble(leftValue);
                            v2 = Double.parseDouble(rightValue);
                            if (v1 <= v2)
                                return true;
                            else
                                return false;
                        } else  //---(枚举值不比较大小，直接返回false)
                            return false;
                    } else {
                        if (leftFlag != rightFlag)   //---类型不一样
                            return false;
                        else {  //--不属于数字型方面,只能是字符串型,布尔型
                            if (!leftValueType.equals(rightValueType))
                                return false;
                            else {   //---类型一样
                                if (leftValueType.equals(boolType)) {  //---布尔型,没有比较大小的
                                    return false;
                                } else {   //---字符串型
                                    if (rightValue.indexOf(",") > 0)   //--枚举值不比较大小
                                        return false;
                                    int nn = leftValue.compareTo(rightValue);
                                    if (nn <= 0)
                                        return true;
                                    else
                                        return false;
                                }
                            }
                        }
                    }
                case NOTEXISTS:   //---not in
                    if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                        if (StringUtil.isEmpty(leftValue)) {
                            return true;
                        }
                    }
                    tempLeft = "," + leftValue + ",";
                    tempRight = "," + rightValue + ",";
                    result = !tempRight.contains(tempLeft);
                    return result;
                case EXISTS:     //---in
                    if (StringUtil.isEmpty(leftValue)) {
                        return false;
                    }
                    tempLeft = "," + leftValue + ",";
                    tempRight = "," + rightValue + ",";
                    result = tempRight.contains(tempLeft);
                    return result;
                case LEFTLIKE:    //-----like %xxX
                    result = leftLike(leftValue, rightValue);
                    return result;
                case LIKE:      //---like %xx%
                    result = allLike(leftValue, rightValue);
                    return result;
                case RIGHTLIKE:      //------LIKE XXX%
                    result = rightLike(leftValue, rightValue);
                    return result;
                case NOTLIKERIGHT:    //---NOT LIKE XXX%
                    result = !rightLike(leftValue, rightValue);    //---取反
                    return result;
            }
        } catch (Exception ex) {
            throw new DBExeption.NoData("段落标识:" + pricingSection_id + "->条件标识：" + pricingRule_id + "判断条件比较出错(" + "左参考值id:" + leftRefValueId + "左参考值类型:" + leftValueType + "左参考值的值:" + leftValue + ",右参考值id:" + rightRefValueId + "右参考值类型:" + rightValueType + "右参考值的值:" + rightValue + ",比较符号:" + operation + ")");
        }
        return true;
    }

    /**
     * List转字符串
     *
     * @param list
     * @param separator
     * @return
     */
    public static String listToString(List list, char separator) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            sb.append(list.get(i)).append(separator);
        }
        return sb.toString().substring(0, sb.toString().length() - 1);
    }

    /**
     * 空字符串
     */
    public static final String EMPTY_STR = "";

    /**
     * 检测是否为空字符串或null引用.
     *
     * @param str 待检测的字符串
     * @return 是返回true; 否则返回false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.length() <= 0;
    }

    /**
     * 检测是否为空格串或null引用、空字符串.
     *
     * @param str 待检测的字符串
     * @return 是返回true; 否则返回false
     */
    public static boolean isBlank(String str) {
        if (isEmpty(str)) {
            return true;
        }

        int strLen = str.length();
        for (int i = 0; i < strLen; i++) {
            if ((Character.isWhitespace(str.charAt(i)) == false)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 二进制转换为十六进制字符串. 如
     *
     * @param buff 二进制
     * @return 十六进制String
     */
    public static String byte2hex(byte[] buff) {
        if (buff == null || buff.length <= 0) {
            return EMPTY_STR;
        }

        StringBuffer hexStr = new StringBuffer();
        String tmpStr = null;
        for (int i = 0; i < buff.length; i++) {
            tmpStr = (Integer.toHexString(buff[i] & 0XFF));
            if (tmpStr.length() == 1) {
                hexStr.append("0");
            }
            hexStr.append(tmpStr);
        }
        return hexStr.toString().toUpperCase();
    }

    /**
     * 用StringTokenizer分割字符串.
     *
     * @param str 待分割字符串
     * @param sep 分割符
     * @return 分割后的字符串数组
     */
    public static String[] splitTokens(String str, String sep) {
        if (isEmpty(str)) {
            return null;
        }

        StringTokenizer token = new StringTokenizer(str, sep);
        int count = token.countTokens();
        if (count < 1) {
            return null;
        }

        int i = 0;
        String[] output = new String[count];
        while (token.hasMoreTokens()) {
            output[i] = token.nextToken();
            ++i;
        }
        return output;
    }

    /**
     * 去掉字符串中所有的空格、回车、制表符.
     *
     * @param str 输入的字符串
     * @return 去掉字符串中所有空格、回车、制表符的字符串
     */
    public static String trimAll(String str) {
        if (isEmpty(str)) {
            return str;
        }

        StringBuffer buf = new StringBuffer(str);
        int index = 0;
        while (buf.length() > index) {
            char c = buf.charAt(index);
            if (Character.isWhitespace(c) || c == '\t' || c == '\r' || c == '\n') {
                buf.deleteCharAt(index);
            } else {
                ++index;
            }
        }

        return buf.toString();
    }

    /**
     * 去掉字符串中前后的空格、回车、制表符.
     *
     * @param str 输入的字符串
     * @return 去掉前后空格、回车、制表符的字符串
     */
    public static String trim(String str) {
        if (StringUtil.isEmpty(str)) {
            return str;
        }
        StringBuffer buf = new StringBuffer(str);

        // 去掉头部的空格
        int index = 0;
        while (buf.length() > index) {
            char c = buf.charAt(index);
            if (Character.isWhitespace(c) || c == '\t' || c == '\r' || c == '\n') {
                buf.deleteCharAt(index);
            } else {
                break;
            }
        }

        // 去掉尾部的空格
        while (buf.length() > 0) {
            int len = buf.length() - 1;
            char c = buf.charAt(len);
            if (Character.isWhitespace(c) || c == '\t' || c == '\r' || c == '\n') {
                buf.deleteCharAt(len);
            } else {
                break;
            }
        }

        return buf.toString();
    }

    /**
     * 过滤换行、回车、指标符 .
     *
     * @param str
     * @return
     * <AUTHOR> 2015年10月29日 zhengwei
     */
    public static String trim2(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    /**
     * 将字符串首字母转大写.
     *
     * @param str 输入的字符串
     * @return 首字母为大写的字符串
     */
    public static String first2UpperCase(String str) {
        if (isEmpty(str)) {
            return str;
        }

        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 将字符串首字母转小写.
     *
     * @param str 输入的字符串
     * @return 首字母为小写的字符串
     */
    public static String first2LowerCase(String str) {
        if (isEmpty(str)) {
            return str;
        }

        return Character.toLowerCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 将字符串数组连接成字符串.
     * <p>
     * 默认使用","连接. 如果字符穿数组中有null，则使用"null".
     *
     * @param strArr
     * @return
     * @see #join(String[], String)
     */
    public static String join(String[] strArr) {
        return join(strArr, ",");
    }

    /**
     * 将字符串数组用指定的分隔符连接成字符串.
     * <p>
     * 如果字符穿数组中有null，则使用"null".
     *
     * @param strArr
     * @param delimeter
     * @return
     */
    public static String join(String[] strArr, String delimeter) {
        if (strArr == null) {
            return null;
        }

        if (strArr.length == 0) {
            return "";
        }

        StringBuffer buf = new StringBuffer();
        for (int i = 0; i < strArr.length; i++) {
            if (i != 0) {
                buf.append(delimeter);
            }
            buf.append(strArr[i]);
        }

        return buf.toString();
    }

    public static String[] upperCaseAll(String... str) {
        String[] ucStr = new String[str.length];
        for (int i = 0; i < str.length; i++) {
            ucStr[i] = str[i].toUpperCase();
        }
        return ucStr;
    }

    public static Long string2Long(String str) {
        return StringUtils.isBlank(str) ? null : Long.parseLong(str.trim());
    }

    public static Integer string2Integer(String str) {
        return StringUtils.isBlank(str) ? null : Integer.parseInt(str.trim());
    }

    public static Integer continuousStatisticsChar(String str, char csStr) {
        Integer i = 0;
        char[] chars = str.toCharArray();
        for (char _char : chars) {
            if (_char == csStr) {
                i++;
            } else {
                break;
            }
        }
        return i;
    }

    public static int[] StringToInt(String[] arrs) {
        int[] ints = new int[arrs.length];
        for (int i = 0; i < arrs.length; i++) {
            ints[i] = Integer.parseInt(arrs[i]);
        }
        return ints;
    }


    /**
     * 从小到大排序String 中的int元素
     *
     * @param arrs
     * @return
     */
    public static String StringSort(String arrs) {
        int[] eventTypeIds = StringToInt(arrs.split(","));
        Arrays.sort(eventTypeIds);
        String eventTypeIdStr = new String();
        for (int i = 0; i < eventTypeIds.length; i++) {
            eventTypeIdStr = eventTypeIdStr + eventTypeIds[i] + ",";
        }
        eventTypeIdStr = eventTypeIdStr.substring(0, eventTypeIdStr.length() - 1);
        return eventTypeIdStr;
    }

    /**
     * 是否全部为空
     *
     * @return 空 true 非空 false
     */
    public static boolean isAllNull(Object... objs) {
        for (Object obj : objs) {
            if (null != obj && !"".equals(obj))
                return false;
        }
        return true;
    }

    /**
     * 是否存在空值
     *
     * @param objs
     * @return
     */
    public static boolean isSameNull(Object... objs) {
        for (Object obj : objs) {
            if (null == obj || "".equals(obj))
                return true;
        }
        return false;
    }

    /**
     * 是否全部非空
     *
     * @param objs
     * @return
     */
    public static boolean isAllNotNull(Object... objs) {
        for (Object obj : objs) {
            if (null == obj || "".equals(obj))
                return false;
        }
        return true;
    }

    /**
     * 是否存在非空
     */
    public static boolean isNotNull(Object... objs) {
        for (Object obj : objs) {
            if (null != obj && !"".equals(obj))
                return true;
        }
        return false;
    }

    /**
     * 字符拼接
     */
    public static String joint(Object... strs) {
        StringBuffer sb = new StringBuffer();
        for (Object str : strs)
            sb.append(str);
        return sb.toString();
    }

    public static List<Long> idsToLongs(String ids, String regex) {
        Assert.hasLength(ids, "ids为空");
        String[] idArr = ids.split(regex);
        List<Long> lArr = new ArrayList<>();
        for (String id : idArr) {
            lArr.add(Long.parseLong(id));
        }
        return lArr;
    }

    public static List<String> idsToStrList(String ids, String regex) {
        Assert.hasLength(ids, "ids为空");
        String[] idArr = ids.split(regex);
        List<String> lArr = new ArrayList<>();
        for (String id : idArr) {
            lArr.add(id);
        }
        return lArr;
    }

    public static List<Integer> idsToIntegers(String ids, String regex) {
        Assert.hasLength(ids, "ids为空");
        String[] idArr = ids.split(regex);
        List<Integer> lArr = new ArrayList<>();
        for (String id : idArr) {
            lArr.add(Integer.valueOf(id));
        }
        return lArr;
    }
    /*方法二：推荐，速度最快
     * 判断是否为整数
     * @param str 传入的字符串
     * @return 是整数返回true,否则返回false
     */

    public static boolean isInteger(String str) {
        return pattern.matcher(str).matches();
    }

    /**
     * List 转 字符串数组
     *
     * @param list
     * @return
     */
    public static String[] listToStringArray(List<String> list) {
        String[] arr = list.toArray(new String[list.size()]);
        return arr;
    }

    /**
     * List 转 浮点数数组
     *
     * @param list
     * @return
     */
    public static Double[] listToDoubleArray(List<Double> list) {
        Double[] arr = list.toArray(new Double[list.size()]);
        return arr;
    }

    /**
     * List 转 整数数组
     *
     * @param list
     * @return
     */
    public static Integer[] listToIntArray(List<Integer> list) {
        Integer[] arr = list.toArray(new Integer[list.size()]);
        return arr;
    }

    public static Long[] listToLongArray(List<Long> list) {
        Long[] arr = list.toArray(new Long[list.size()]);
        return arr;
    }

    public static Integer[] listToIntArray(Integer i) {
        List<Integer> list = new ArrayList<>();
        list.add(i);
        Integer[] arr = list.toArray(new Integer[list.size()]);
        return arr;
    }

    public static Long[] listToLongArray(Long i) {
        List<Long> list = new ArrayList<>();
        list.add(i);
        Long[] arr = list.toArray(new Long[list.size()]);
        return arr;
    }

    /**
     * List<String> to List<Long>
     *
     * @param inList
     * @return
     */
    public static List<Long> StringToLongLst(List<String> inList) {
        List<Long> iList = new ArrayList<Long>(inList.size());
        try {
            for (int i = 0, j = inList.size(); i < j; i++) {
                iList.add(Long.parseLong(inList.get(i)));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return iList;
    }

    /**
     * List<String> to List<Long>
     *
     * @param inList
     * @return
     */
    public static List<Integer> StringToIntegerLst(List<String> inList) {
        List<Integer> iList = new ArrayList<Integer>(inList.size());
        try {
            for (int i = 0, j = inList.size(); i < j; i++) {
                iList.add(Integer.parseInt(inList.get(i)));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return iList;
    }

    public static List<Long> IntegerToLongLst(List<Integer> inList) {
        List<Long> iList = new ArrayList<Long>(inList.size());
        try {
            for (int i = 0, j = inList.size(); i < j; i++) {
                iList.add(Long.parseLong(inList.get(i).toString()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return iList;
    }

    /**
     * 单位换算： 毫变分了
     *
     * @param list
     * @return
     */
    public static Long[] LongArray(List<Double> list) {
        List<Long> longList = new ArrayList<>();
        long temp = 0;
        for (Double dd : list) {
            // temp = Math.round(dd / 100);      //----单位换算： 毫 变 分    --- 2023/12/05分yuwh修改精度问题
            temp = Math.round(dd);      //----单位换算： 毫 变 分
            longList.add(temp);
        }
        Long[] arr = longList.toArray(new Long[longList.size()]);
        return arr;
    }

    public static Long[] LongArray(Long d) {
        List<Long> longList = new ArrayList<>();
        long temp = 0;
        temp = d;      //----单位换算： 毫 变 分
        longList.add(temp);
        Long[] arr = longList.toArray(new Long[longList.size()]);
        return arr;
    }


    public static String formatDate(String dateStr) {
        if (dateStr == null)
            return null;
        dateStr = dateStr.replaceAll("-", "");
        dateStr = dateStr.replaceAll(" ", "");
        dateStr = dateStr.replaceAll(":", "");
        return dateStr;
    }

    /**
     * 生成策略对应的要素串
     *
     * @param eventStrategyList
     * @param elementStr
     * @return
     */
    public static String[] elementStrArray(List<Long> eventStrategyList, String elementStr) {
        List<String> elementsList = new ArrayList<>();
        if (eventStrategyList == null || eventStrategyList.size() == 0)
            return null;
        else if (eventStrategyList.size() == 1) {
            elementsList.add(elementStr);
            return listToStringArray(elementsList);
        }
        for (Long strategyId : eventStrategyList) {
            elementsList.add(elementStr);
        }
       /* String regex=null;
        Matcher matcher = null;
        int a1=elementStr.indexOf("@", 0) ;
        String temp=elementStr.substring(a1) ;
        for(Long strategyId : eventStrategyList){
            regex="!"+strategyId+"([\\s\\S]*?)(true|false)";
            matcher=Pattern.compile(regex).matcher(elementStr);
            if(matcher.find()){
                elementsList.add(matcher.group()+temp) ;
            }
        }*/
        return listToStringArray(elementsList);
    }

    /**
     * 从List转指定格式的Map
     *
     * @param list
     * @return
     */
    public static Map<String, Map> getRefValueListToMap(List<Map<String, Object>> list) {
        if (list == null)
            return null;
        Map<String, Map> resultMp = new HashMap<>();
        Map<String, String> tempMp = null;
        for (Map<String, Object> mp : list) {
            String id = mp.get("id").toString();
            String value = mp.get("data").toString();
            String value_type = mp.get("value_type").toString();
            tempMp = new HashMap<>();
            tempMp.put("value", value);
            tempMp.put("value_type", value_type);
            resultMp.put(id, tempMp);
        }
        return resultMp;
    }


    /**
     * 年月加减月份
     *
     * @param yearMonth
     * @param month
     * @return
     * @throws Exception
     */
    public static int addYearMonth(String yearMonth, int month) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date dt = sdf.parse(yearMonth);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        //   rightNow.add(Calendar.YEAR,-1);         //日期减1年
        rightNow.add(Calendar.MONTH, month);          //日期加month个月
        //  rightNow.add(Calendar.DAY_OF_YEAR,10);  //日期加10天
        Date dt1 = rightNow.getTime();
        String result = sdf.format(dt1);
        return Integer.parseInt(result);
    }

    public static int addYearMonthByBelongType(String belongCycleType, String yearMonth, int month) throws Exception {
        if (belongCycleType.equals("2"))
            month = 3 * month;
        else if (belongCycleType.equals("3"))
            month = 12 * month;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date dt = sdf.parse(yearMonth);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        //   rightNow.add(Calendar.YEAR,-1);         //日期减1年
        rightNow.add(Calendar.MONTH, month);          //日期加month个月
        //  rightNow.add(Calendar.DAY_OF_YEAR,10);  //日期加10天
        Date dt1 = rightNow.getTime();
        String result = sdf.format(dt1);
        return Integer.parseInt(result);
    }


    /**
     * 实时
     *
     * @param belongCycleType
     * @param finishTime
     * @param billingCycleId
     * @param belongCycleDuration
     * @return
     * @throws Exception
     */
    public static int getBillingByReal(String belongCycleType, String finishTime, String billingCycleId, String belongCycleDuration) throws Exception {
        int resultValue = 0;
        switch (belongCycleType) {
            case "1":
                resultValue = Math.max(StringUtil.addYearMonth(finishTime, Integer.parseInt(belongCycleDuration)), Integer.parseInt(billingCycleId));
                break;
            case "2":
                resultValue = Math.max(StringUtil.addYearMonth(getBelongSeasonMonth(finishTime), Integer.parseInt(belongCycleDuration) * 3), Integer.parseInt(getBelongSeasonMonth(billingCycleId)));
                break;
            case "3":
                resultValue = Math.max(StringUtil.addYearMonth(getBelongYearMonth(finishTime), Integer.parseInt(belongCycleDuration) * 12), Integer.parseInt(getBelongYearMonth(billingCycleId)));
                break;
            default:
                resultValue = Math.max(StringUtil.addYearMonth(finishTime, Integer.parseInt(belongCycleDuration)), Integer.parseInt(billingCycleId));
                break;
        }
        return resultValue;
    }


    public static String getBelongSeasonMonth(String finishTime) {
        String year = finishTime.substring(0, 4);
        String month = finishTime.substring(4, 6);
        if (Integer.parseInt(month) <= 3)
            return year + "03";
        else if (Integer.parseInt(month) >= 4 && Integer.parseInt(month) <= 6)
            return year + "06";
        else if (Integer.parseInt(month) >= 7 && Integer.parseInt(month) <= 9)
            return year + "09";
        else if (Integer.parseInt(month) >= 10 && Integer.parseInt(month) <= 12)
            return year + "12";
        else
            return finishTime;
    }

    public static String getBelongYearMonth(String finishTime) {
        String year = finishTime.substring(0, 4);
        return year + "12";
    }


    public static int getFeeingCycle(String belongCycleType, String finishTime, String belongCycleDuration) throws Exception {
        int resultValue = 0;
        switch (belongCycleType) {
            case "1":
                resultValue = StringUtil.addYearMonth(finishTime, Integer.parseInt(belongCycleDuration));
                break;
            case "2":
                resultValue = StringUtil.addYearMonth(getBelongSeasonMonth(finishTime), Integer.parseInt(belongCycleDuration) * 3);
                break;
            case "3":
                resultValue = StringUtil.addYearMonth(getBelongYearMonth(finishTime), Integer.parseInt(belongCycleDuration) * 12);
                break;
            default:
                resultValue = StringUtil.addYearMonth(finishTime, Integer.parseInt(belongCycleDuration));
                break;
        }
        return resultValue;
    }


    public static int getExpCycle(String belongCycleType, int feeCycleId, String resettCycleDuration, String billingcycle) throws Exception {
        int resultValue = 0;
        switch (belongCycleType) {
            case "1":
                resultValue = Math.max(StringUtil.addYearMonth(String.valueOf(feeCycleId), Integer.parseInt(resettCycleDuration)), Integer.parseInt(billingcycle));
                break;
            case "2":
                resultValue = Math.max(StringUtil.addYearMonth(String.valueOf(feeCycleId), Integer.parseInt(resettCycleDuration) * 3), Integer.parseInt(billingcycle));
                break;
            case "3":
                resultValue = Math.max(StringUtil.addYearMonth(String.valueOf(feeCycleId), Integer.parseInt(resettCycleDuration) * 12), Integer.parseInt(billingcycle));
                break;
            default:
                resultValue = Math.max(StringUtil.addYearMonth(String.valueOf(feeCycleId), Integer.parseInt(resettCycleDuration)), Integer.parseInt(billingcycle));
                break;
        }
        return resultValue;
    }

    /**
     * 周期
     *
     * @param belongCycleType
     * @param billingCycleId
     * @param belongCycleDuration
     * @return
     * @throws Exception
     */
    public static int getBillingByCycle(String belongCycleType, String billingCycleId, String belongCycleDuration) throws Exception {
        int resultValue = 0;
        switch (belongCycleType) {
            case "1":
                resultValue = StringUtil.addYearMonth(billingCycleId, Integer.parseInt(belongCycleDuration));
                break;
            case "2":
                resultValue = StringUtil.addYearMonth(billingCycleId, Integer.parseInt(belongCycleDuration) * 3);
                break;
            case "3":
                resultValue = StringUtil.addYearMonth(billingCycleId, Integer.parseInt(belongCycleDuration) * 12);
                break;
            default:
                resultValue = StringUtil.addYearMonth(billingCycleId, Integer.parseInt(belongCycleDuration));
                break;
        }
        return resultValue;
    }

    /**
     * Double原样输出，取消科学计数法
     *
     * @param d
     * @return
     */
    public static String formatDouble(double d) {
        NumberFormat nf = NumberFormat.getInstance();
        //设置保留多少位小数
        nf.setMaximumFractionDigits(20);
        // 取消科学计数法
        nf.setGroupingUsed(false);
        //返回结果
        return nf.format(d);
    }

    /**
     * 将科学计数法的字符串传入
     *
     * @param str
     * @return 返回double类型
     */
    public static double getDoubleNumber(String str) {
        double number = 0;
        BigDecimal bd = new BigDecimal(str);
        number = Double.parseDouble(bd.toPlainString());
        return number;
    }

    /**
     * LIKE XXX%
     *
     * @param leftValue
     * @param rightValue
     * @return
     */
    public static boolean rightLike(String leftValue, String rightValue) {
        String[] vv = rightValue.split(",");
        boolean result = false;
        for (String temp : vv) {
            if (temp == null || temp.trim().equals(""))
                continue;
            result = leftValue.trim().startsWith(temp.trim());
            if (result)
                return true;
        }
        return false;
    }


    /**
     * like %xxX
     *
     * @param leftValue
     * @param rightValue
     * @return
     */
    public static boolean leftLike(String leftValue, String rightValue) {
        String[] vv = rightValue.split(",");
        boolean result = false;
        for (String temp : vv) {
            if (temp == null || temp.trim().equals(""))
                continue;
            result = leftValue.trim().endsWith(temp);
            if (result)
                return true;
        }
        return false;
    }


    /**
     * like
     *
     * @param leftValue
     * @param rightValue
     * @return
     */
    public static boolean allLike(String leftValue, String rightValue) {
        String[] vv = rightValue.split(",");
        boolean result = false;
        for (String temp : vv) {
            if (temp == null || temp.trim().equals(""))
                continue;
            int index = leftValue.indexOf(temp);
            if (index >= 0)
                return true;
        }
        return false;
    }

    public static String removeDuplicateStr(String strs) {
        String[] str = strs.split(",");
        Set set = new HashSet();
        for (int i = 0; i < str.length; i++) {
            set.add(str[i]);
        }
        str = (String[]) set.toArray(new String[0]);
        return Arrays.toString(str);
    }

}
