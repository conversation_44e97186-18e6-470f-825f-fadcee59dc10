package com.itco.rating.util;



public class RedisMergerUtil {
    //key值前缀
    private static String REDIS_KEY;
    //锁有效期
    public static long lockExpireTime=10*1000;
    //获取锁的超时时长
    public static long lockTimeout=20*1000;

    private static String initGeneral(){
        REDIS_KEY=System.getProperty("redisKeySuffix");
        if(StringUtil.isEmpty(REDIS_KEY)){
            REDIS_KEY="Rating";
        }
        return REDIS_KEY;
    }
    protected static String getRedisKey(){
        if(StringUtil.isEmpty(REDIS_KEY)){
            return initGeneral();
        }
        return REDIS_KEY;
    }
}
