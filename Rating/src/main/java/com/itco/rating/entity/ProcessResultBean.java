package com.itco.rating.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.*;


@Data
public class ProcessResultBean implements Serializable {

    private List<Long> pricingPlanList = new ArrayList<>();         //---定价计划集合
    private List<Long> acctItemTypeList = new ArrayList<>();       //---帐目类型集合
    private List<Double> chargeList = new ArrayList<>();             //---资费1金额集合
    private List<Double> charge1List = new ArrayList<>();             //---资费2金额集合
    private List<Long> strategyList = new ArrayList<>();           //---策略集合
    private List<Long> tariffList = new ArrayList<>();             //---资费集合
    private List<Long> bereauIdList = new ArrayList<>();

    private List<Long> settleObjectIdList = new ArrayList<>();             //---settleObjectId集合
    private List<Long> settleObjectTypeList = new ArrayList<>();           //---settleObjectType集合
    private List<Long> settleObjectRoleList = new ArrayList<>();           //---settleObjectRole集合

    private List<Integer> aBillCycleIdList = new ArrayList<>();
    private List<Integer> feeCycleIdList = new ArrayList<>();
    private List<Integer> expCycleIdList = new ArrayList<>();
    private List<Integer> aIfDeductList = new ArrayList<>();

    private StringBuffer eventStrategyStr = new StringBuffer();    //---策略集合
    private StringBuffer pricingSectionStr = new StringBuffer();   //---段落集合
    private StringBuffer tariffStr = new StringBuffer();           //---标准资费集合
    private Set<String> refValueStr = new HashSet<>();              //---参考值集合
    private StringBuffer variableStr = new StringBuffer();         //---中间变量参考值

    private Map<String, Object> chargeMp = new HashMap<String, Object>();         //---定价计划对应的资费和金额

    //  private Map<String,Object> eventStrategyMp=new HashMap<String,Object>();   //---定价计划对应的策略

    private boolean haveResettleBool = false;    //---是否有补结因子

    private StringBuffer notConditionStr = new StringBuffer();

    private StringBuffer errorTypeMsg = new StringBuffer();      //----异常信息名称
    private Integer errorType = null;                             //----异常信息编码


}
