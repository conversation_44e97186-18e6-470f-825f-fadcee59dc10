package com.itco.rating.service;

import com.itco.rating.entity.AccuPayOutBean;
import com.itco.rating.entity.AccuUseInfoBean;
import com.itco.rating.entity.UploadDataBean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface UploadTableService {
    //-----上载数据到内存的sql语句---------//

    public boolean uploadToCache(int uploadType,int rangeId) throws Exception;

    boolean insertAccuKeyTab(Map<String,Object> map) ;
    boolean updateAccuKeyTab(Map<String,Object> map) ;
    boolean saveAccuUseKeyTab(Map<String,Object> map) ;
    boolean saveAccuPayoutTab(AccuPayOutBean bean) ;
    boolean saveAccuUseTab(AccuUseInfoBean bean) ;
    boolean updateAccuUseTab(long usage_amount, long accu_use_id) ;
    List<Map<String,Object>> listTicketFieldConfig(int eventClass);
    List<Map<String,Object>> listRefValueData(long batchId);

}
