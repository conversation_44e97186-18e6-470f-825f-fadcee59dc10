package com.itco.rating.service.impl;


import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.rating.entity.AccuPayOutBean;
import com.itco.rating.entity.AccuUseInfoBean;
import com.itco.rating.entity.UploadDataBean;
import com.itco.rating.mapper.UploadDataMapper;
import com.itco.rating.service.UploadTableService;
import com.itco.rating.util.MemoryCacheUtils;
import com.itco.rating.util.NoUtilS;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Service
public class UploadTableServiceImpl implements UploadTableService {

    static Log log = LogFactory.getLog(UploadTableServiceImpl.class);

    /*@Autowired
    JdbcTemplate jdbcTemplate ;*/

    @Autowired(required = false)
    UploadDataMapper uploadDataMapper ;

    public VProxyJedis jedis=null ;

  /*   public  VProxyJedis  getJedis() throws Exception{
        if(jedis==null) {
            CtgVJedisPool pool = RedisUtil.getPool();
            jedis = pool.getResource();
        }
        return jedis ;
    }

    public  void close(VProxyJedis  jedis){
        if(jedis!=null)
            jedis.close();
    }*/






    /**
     * 上传数据到本机缓存
     * @return
     */
   /* public boolean uploadToCache() throws Exception{
        //------解析上传到内存的数据表语句（json格式）---------//
        log.info("开始准备加载数据...");
        JSONArray dataArray = JSONArray.parseArray(uploadDataJSON.toString());
        if(dataArray!=null && dataArray.size()>0){
            log.info("加载的表个数="+dataArray.size());
            for(int i=0;i<dataArray.size();i++){
                JSONObject obj = dataArray.getJSONObject(i);
                UploadDataBean bean = JSON.toJavaObject(obj,UploadDataBean.class);
                String datas=bean.getUploadSql().trim() ;             //---要上传数据sql语句
                String keySuffix=bean.getKeySuffix().trim() ;        //---key前缀
                String keyField=bean.getKeyField().trim() ;          //---key对应的字段
                String dataField=bean.getDataField().trim() ;        //--数据值对就在的字段（针对redisList,redisSortedSet)
                String scoreField=bean.getScoreField().trim()  ;              //--针对redisSortedSet
                int keyType=Integer.parseInt(bean.getKeyType().trim())  ;     //--数据类型(1--RedisHashMap ;2--RedisList )
                List<Map<String,Object>> dataList=jdbcTemplate.queryForList(datas) ;
                if(keyType==1){     //---1-RedisHashMap
                    if(dataList!=null && dataList.size()>0){
                        for(Map<String,Object> mp: dataList) {
                            String key_vv=String.valueOf(mp.get(keyField)) ;  //--key的值
                            //--把Map<String,Object>转<String,String>
                            for(Map.Entry mapStr : mp.entrySet()){
                                String key11=mapStr.getKey().toString() ;
                                Object vv11=mapStr.getValue() ;
                                mp.put(key11, String.valueOf(vv11));
                            }
                            try {
                                MemoryCacheUtils.setData(keySuffix + "_" + key_vv, mp, 0);
                            }catch (Exception ex){
                                ex.printStackTrace();
                                log.error(ex.getMessage());
                            }

                        }
                    }
                }else if(keyType==2){   //--2-RedisList
                    if(dataField==null || "".equals(dataField.trim()))
                        return false ;
                    Map<String, String> temp_result = new HashMap<String, String>();
                    if(dataList!=null && dataList.size()>0){
                        for(Map<String,Object> mp: dataList) {
                            String key_vv=String.valueOf(mp.get(keyField)) ;  //--key的值
                            String data_vv=String.valueOf(mp.get(dataField)) ;  //--field的值
                            if(temp_result.containsKey(key_vv)){
                                String temp =  temp_result.get(key_vv).toString() ;
                                data_vv = temp+","+data_vv;
                            }
                            temp_result.put(key_vv, data_vv);
                        }
                        String key,vvv;
                        List<String> dd ;
                        for (Map.Entry<String, String> entry : temp_result.entrySet()) {
                            key=entry.getKey() ;
                            vvv=entry.getValue() ;
                            dd=Arrays.asList(vvv.split(",")) ;
                            MemoryCacheUtils.setData(keySuffix + "_" + key,dd,0) ;
                        }
                    }
                }else if(keyType==3){     //--redisSortedSet
                    if(dataField==null || "".equals(dataField))
                        return false ;
                    if(scoreField==null || "".equals(scoreField.trim()))
                        return false ;
                    String old_key_id="" ;
                    int ii=0;
                    List<String> vv =new ArrayList<String>();
                    Map<String, String> temp_result = new HashMap<String, String>();
                    if(dataList!=null && dataList.size()>0) {
                        for (Map<String, Object> mp : dataList) {
                            ii++;
                            String key_vv = String.valueOf(mp.get(keyField)) ;  //--key的值
                            String data_vv = String.valueOf(mp.get(dataField));  //--field的值
                            if(temp_result.containsKey(key_vv)){
                                String temp =  temp_result.get(key_vv).toString() ;
                                data_vv = temp+","+data_vv;
                            }
                            temp_result.put(key_vv, data_vv);
                        }

                        String key,vvv;
                        List<String> dd ;
                        for (Map.Entry<String, String> entry : temp_result.entrySet()) {
                            key=entry.getKey() ;
                            vvv=entry.getValue() ;
                            dd=Arrays.asList(vvv.split(",")) ;
                            MemoryCacheUtils.setData(keySuffix + "_" + key,dd,0) ;
                        }
                    }
                }
            }
        }
       log.info("加载数据到内存成功！");
       return true ;
    }*/



    public boolean uploadToCache(int uploadType,int rangeId) throws Exception{
        //--------加载序列AccuPayOutId,AccuUseId---序列ID--------/
        log.info("开始准备加载数据...");
        List<Map<String, Object>> ticketList=null;
        List<UploadDataBean> list=null;
        List<Map<String, Object>> dataList=null;
        Map<String, String> temp_result=null;
        String key_vv=null,data_vv=null,temp=null;
        String key=null, vvv=null;
        List<String> tmpList=null;
        Map<String,Object> rangeMp=null;
        String[] conditionKeySuffix=new String[]{"Pricingplan","PricingCombine","PricingPlanWithCombine"} ;
        try {
            MemoryCacheUtils.clearAll();
            String ratingProcessType = uploadDataMapper.getRatingProcessType();   //---批价处理规则
            MemoryCacheUtils.setData("RatingProcessType", ratingProcessType, 0);
            String projectName=uploadDataMapper.getProjectName();           //---项目工程代码
            MemoryCacheUtils.setData("ProjectName", projectName==null?"TPSS":projectName, 0);

            ticketList = uploadDataMapper.listTicketFieldConfig(1);
            if (ticketList != null && ticketList.size() > 0) {
                MemoryCacheUtils.setData("ticketList1", ticketList, 0);  //---定制的话单字段保存到内存
            }
            ticketList = uploadDataMapper.listTicketFieldConfig(2);
            if (ticketList != null && ticketList.size() > 0) {
                MemoryCacheUtils.setData("ticketList2", ticketList, 0);  //---定制的话单字段保存到内存
            }else{
                System.out.println("upload data ,ticketList2 is null");
            }

            list = uploadDataMapper.getUploadToCache(uploadType);
            log.info("加载的表个数=" + list.size());
            String keySuffix, keyField, dataField, scoreField, databaseName, paramSql;
            int keyType, ii = 0;
            if (list != null) {
                for (UploadDataBean bean : list) {
                    int idd=bean.getId() ;
                    paramSql = bean.getUploadSql().trim();            //---要上传数据sql语句
                    databaseName = bean.getDatabaseName();       //---数据库名
                    databaseName = databaseName == null ? "" : databaseName.trim();
                    if (databaseName == null || "".equals(databaseName.trim()))
                        paramSql = paramSql.replaceAll("xxxxx.", "");
                    else
                        paramSql = paramSql.replaceAll("xxxxx", databaseName);
                    keySuffix = bean.getKeySuffix().trim();       //---key前缀
                    keyField = bean.getKeyField().trim();         //---key对应的字段
                    dataField = bean.getDataField().trim();       //--数据值对就在的字段（针对redisList,redisSortedSet)
                    scoreField = bean.getScoreField().trim();     //--针对redisSortedSet
                    keyType = Integer.parseInt(bean.getKeyType().trim());     //--数据类型(1--RedisHashMap ;2--RedisList )
                    //----------试算上载范围限制---定价计划id的限制-----------------//
                    if (uploadType == 2 && rangeId > 0 && stringContainsItemFromList(keySuffix, conditionKeySuffix)) {
                        temp = " pricing_plan_id in (Select pricing_plan_id from plan_range Where range_id=" + rangeId + ")";
                        paramSql = paramSql.replaceAll("1=1", temp);
                    } else if (uploadType == 2 && rangeId > 0 && "EventPricingStrategy".equals(keySuffix)) {
                        temp = " event_pricing_strategy_id in (Select a.event_pricing_strategy_id from pricing_combine a Where pricing_plan_id in (Select pricing_plan_id from plan_range b Where range_id=" + rangeId + "))";
                        paramSql = paramSql.replaceAll("1=1", temp);

                    }
                    if (CalcRecordManager.getCommon().getIDebug() > 0){
                        log.info("id==>"+idd+",sql==>"+paramSql);
                    }
                    dataList = uploadDataMapper.listParamSqlData(paramSql);
                    if (CalcRecordManager.getCommon().getIDebug() > 0){
                        log.info("id==>"+idd+",表记录数："+dataList.size());
                    }
                    if (keyType == 1) {       //---1-RedisHashMap
                        if (dataList != null && dataList.size() > 0) {
                            for (Map<String, Object> mp : dataList) {
                                key_vv = String.valueOf(mp.get(keyField));  //--key的值
                                //--把Map<String,Object>转<String,String>
                                for (Map.Entry mapStr : mp.entrySet()) {
                                    key = mapStr.getKey().toString();
                                    mp.put(key, String.valueOf(mapStr.getValue()));
                                }
                                try {
                                    MemoryCacheUtils.setData(keySuffix + "_" + key_vv, mp, 0);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }

                            }
                        }
                    } else if (keyType == 2) {    //--2-RedisList
                        if (dataField == null || "".equals(dataField.trim()))
                            return false;
                        temp_result = new HashMap<String, String>();
                        if (dataList != null && dataList.size() > 0) {
                            for (Map<String, Object> mp : dataList) {
                                key_vv = String.valueOf(mp.get(keyField));  //--key的值
                                data_vv = String.valueOf(mp.get(dataField));  //--field的值
                                if (temp_result.containsKey(key_vv)) {
                                    temp = temp_result.get(key_vv).toString();
                                    data_vv = temp + "," + data_vv;
                                }
                                temp_result.put(key_vv, data_vv);
                            }

                            for (Map.Entry<String, String> entry : temp_result.entrySet()) {
                                key = entry.getKey();
                                vvv = entry.getValue();
                                tmpList = Arrays.asList(vvv.split(","));
                                MemoryCacheUtils.setData(keySuffix + "_" + key, tmpList, 0);
                            }
                        }
                        temp_result=null;
                    } else if (keyType == 3) {   //--redisSortedSet
                        if (dataField == null || "".equals(dataField))
                            return false;
                        if (scoreField == null || "".equals(scoreField.trim()))
                            return false;
                        temp_result = new HashMap<String, String>();
                        if (dataList != null && dataList.size() > 0) {
                            for (Map<String, Object> mp : dataList) {
                                key_vv = String.valueOf(mp.get(keyField));    //--key的值
                                data_vv = String.valueOf(mp.get(dataField));  //--field的值
                                if (temp_result.containsKey(key_vv)) {
                                    temp = temp_result.get(key_vv).toString();
                                    data_vv = temp + "," + data_vv;
                                }
                                temp_result.put(key_vv, data_vv);
                            }

                            for (Map.Entry<String, String> entry : temp_result.entrySet()) {
                                key = entry.getKey();
                                vvv = entry.getValue();
                                tmpList = Arrays.asList(vvv.split(","));
                                tmpList=tmpList.stream().distinct().collect(Collectors.toList());  //---去重复的元素
                                MemoryCacheUtils.setData(keySuffix + "_" + key, tmpList, 0);
                            }
                        }
                        temp_result=null;
                    }
                }

                rangeMp=new HashMap<>();
                if(uploadType==2 && rangeId>0){     //----上载需增加上载日志记录---//
                    rangeMp.put("dealRuleActive",2014) ;   //--2014：试算定价
                    rangeMp.put("loadType",2);            //---1重启自动上载 2调度指令
                    rangeMp.put("isSuccess",1) ;          //---1成功，0失败
                    rangeMp.put("rangeId",rangeId) ;      //--plan_range表的批次，0表示全量上载
                    uploadDataMapper.insertLoadConfigLog(rangeMp) ;
                }else if(uploadType==2 && rangeId==0){
                    rangeMp.put("dealRuleActive",2014) ;   //--2014：试算定价
                    rangeMp.put("loadType",2);            //---1重启自动上载 2调度指令
                    rangeMp.put("isSuccess",1) ;          //---1成功，0失败
                    rangeMp.put("rangeId",0) ;           //--plan_range表的批次，0表示全量上载
                    uploadDataMapper.insertLoadConfigLog(rangeMp) ;
                }

                log.info("加载数据到内存成功！");
            }
        }catch (Exception ex){
            log.error(ex.getCause().getMessage());
            if(uploadType==2 && rangeId>0){     //----上载需增加上载日志记录---//
                rangeMp=new HashMap<>();
                rangeMp.put("dealRuleActive",2014) ;
                rangeMp.put("loadType",2);
                rangeMp.put("isSuccess",0) ;
                rangeMp.put("rangeId",rangeId) ;
                uploadDataMapper.insertLoadConfigLog(rangeMp) ;
            }
            throw ex;
        }finally {
            //-------------释放内存----------------//
            temp=null;key_vv=null;data_vv=null;key=null;vvv=null;
            if(tmpList!=null) {
                tmpList = null;
            }
            if(temp_result!=null) {
                temp_result = null;
            }
            if(dataList!=null) {
                dataList = null;
            }
            if(list!=null) {
                list = null;
            }
            if(ticketList!=null) {
                ticketList = null;
            }
        }
        return true ;
    }







    /**
     * 删除 key
     * @param key
     */
    public void removeKey(String key){
        String lockValue = NoUtilS.getUUID();
        long lockExpireTime = 10 * 1000;
        long timeout = 10 * 1000;
        try{
            jedis.lock(key, lockValue, lockExpireTime, timeout);
            jedis.del(key);
        }catch(Exception ex){
            ex.printStackTrace();
            log.error(ex.getMessage());
        }finally{
            jedis.unlock(key, lockValue);
        }
    }

    /**
     *保存accu_key的键和值
     * @param map
     * @return
     */
    public boolean insertAccuKeyTab(Map<String,Object> map){
        try{
            uploadDataMapper.insertAccuKeyTab(map);
            return true ;
        }catch (Exception ex){
            ex.printStackTrace();
            log.error(ex.getMessage());
        }
        return false;
    }

    /**
     *保存accu_key的键和值
     * @param map
     * @return
     */
    public boolean updateAccuKeyTab(Map<String,Object> map){
        try{
            uploadDataMapper.updateAccuKeyTab(map);
            return true ;
        }catch (Exception ex){
            ex.printStackTrace();
            log.error(ex.getMessage());
        }
        return false;
    }


    public boolean saveAccuPayoutTab( AccuPayOutBean bean){
        uploadDataMapper.insertAccuPayoutTab(bean);
        return true;
    }

    /**
     * 保存prod_inst_accu_use_info表的accu_use_id 的键id
     * @param map
     * @return
     */
    public boolean saveAccuUseKeyTab(Map<String,Object> map){
        uploadDataMapper.insertAccuUseKeyTab(map);
        return true;
    }

    /**
     * 插入prod_inst_accu_use_info表
     * @param bean
     * @return
     */
    public boolean saveAccuUseTab(AccuUseInfoBean bean){
        uploadDataMapper.insertProdInstAccuUseInfo(bean);
        return true;
    }

    /**
     * 更新prod_inst_accu_use_info表中usage_amount字段
     * @param usage_amount
     * @param accu_use_id
     * @return
     */
    public boolean updateAccuUseTab(long usage_amount,long accu_use_id){
        Map<String,Object> map=new HashMap<String,Object>() ;
        map.put("usage_amount",usage_amount);
        map.put("accu_use_id",accu_use_id);
        try {
            uploadDataMapper.updateProdInstAccuUseInfo(map);
            return true;
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
        return false ;
    }

    public List<Map<String,Object>> listTicketFieldConfig(int eventClass){
        return uploadDataMapper.listTicketFieldConfig(eventClass);
    }


    public  boolean stringContainsItemFromList(String inputStr, String[] items) {
        return Arrays.stream(items).parallel().anyMatch(inputStr::contains);
    }

    public List<Map<String,Object>> listRefValueData(long batchId){
        return uploadDataMapper.listRefValueData(batchId);
    }
}
