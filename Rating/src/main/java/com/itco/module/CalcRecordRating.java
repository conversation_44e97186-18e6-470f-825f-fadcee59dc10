package com.itco.module;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPool;
import com.itco.CalcFunction.CalcFunctionLocal;
import com.itco.CalcFunction.CalcVisualFunctionLocal;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.entity.process.REDO_FLAG;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.framework.Factory;
import com.itco.rating.dao.RatingProcess;
import com.itco.rating.dao.impl.RatingProcessImpl;
import com.itco.rating.entity.ProcessParamBean;
import com.itco.rating.util.StringUtil;
import com.itco.rulefunction.ctgcache.TableService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.util.List;
import java.util.Map;
import static com.itco.RatingApplication.baseDataHandler;

public class CalcRecordRating extends CalcRecordManager {
    static Log log = LogFactory.getLog(CalcRecordRating.class);
    RatingProcess ratingProcess;
    CtgVJedisPool pool = null, pool_preproc = null;
    TableService tableService = null;
    CalcFunctionLocal calcFunction = null;
    CalcVisualFunctionLocal calcVisualFunctionLocal = null;
    int uploadType;

    /*初始化*/
    public boolean OnInit() {    //---uploadType=1
        uploadType = 1;
        log.info("OnInit start ");
        ratingProcess = new RatingProcessImpl();
        log.info("start .....");
        boolean result = initData(getThreadNum(), 1, 0, "OnInit close...");
        return result;
    }

    /*线程启动初始化试算*/
    public boolean OnInitTrial() {      //---uploadType=2
        uploadType = 2;
        log.info("OnInitTrial start ");
        int rangeId = getLoadId();    //---上载的批次号
        ratingProcess = new RatingProcessImpl();
        boolean result = initData(getThreadNum(), 2, rangeId, "OnInitTrial close...");
        return result;
    }

    /*线程启动初始化预计费*/
    public boolean OnInitPre() {      //---uploadType=3
        uploadType = 3;
        log.info("OnInitPre start ");
        ratingProcess = new RatingProcessImpl();
        boolean result = initData(getThreadNum(), 3, 0, "OnInitPre close...");
        return result;
    }

    /*
     * @decription redo_flag判断，操作
     * @param jsonObject: 话单
     * @return int 1：冲正单，不进行批价，2： 重算单，需要批价
     * <AUTHOR>
     * @createDate 2022/8/25
     */
    int dealRecycleRollback(JSONObject jsonObject) {
        // 1.redo_flag：0,1,3,11,13,20,21,22则正常批价
        // 1.redo_flag：2,4，则把a_charge=charge*-1 , a_charge_1=charge_1*-1
        // 补充回退回收冲正单，跳过
        if (jsonObject.containsKey("redo_flag")) {
            String redo_flag = jsonObject.getString("redo_flag");
            // 福建综合结算 冲正单逻辑
            if (REDO_FLAG.CODE_2.getCode().equals(redo_flag) || REDO_FLAG.CODE_4.getCode().equals(redo_flag)) {
                log.info("冲正单，不处理");
                if (jsonObject.containsKey("charge")) {
                    Long charge = jsonObject.getLong("charge") * -1;
                    jsonObject.put("a_charge", StringUtil.LongArray(charge));        //---资费1 金额/100
                }
                if (jsonObject.containsKey("charge_2")) {
                    Long charge_1 = jsonObject.getLong("charge_2") * -1;
                    jsonObject.put("a_charge1", StringUtil.LongArray(charge_1));        //---资费2 金额/100
                }
                if (jsonObject.containsKey("tariff_id")) {
                    Long tariff_id = jsonObject.getLong("tariff_id");
                    jsonObject.put("a_tariff_id", StringUtil.listToLongArray(tariff_id));                //---资费标识
                }
                if (jsonObject.containsKey("pricing_plan_id")) {
                    Long pricing_plan_id = jsonObject.getLong("pricing_plan_id");
                    jsonObject.put("a_pricing_plan_id", StringUtil.listToLongArray(pricing_plan_id));     //---定价计划
                }
                if (jsonObject.containsKey("acct_item_type_id")) {
                    Long acct_item_type_id = jsonObject.getLong("acct_item_type_id");
                    jsonObject.put("a_acct_item_type_id", StringUtil.listToLongArray(acct_item_type_id));  //---帐目类型
                }
                if (jsonObject.containsKey("strategy_id")) {
                    Long strategy_id = jsonObject.getLong("strategy_id");
                    jsonObject.put("a_strategy_id", StringUtil.listToLongArray(strategy_id));
                }
                if (jsonObject.containsKey("exp_cycle_id")) {
                    Integer exp_cycle_id = jsonObject.getInteger("exp_cycle_id");
                    jsonObject.put("a_exp_cycle_id", StringUtil.listToIntArray(exp_cycle_id));
                }
                if (jsonObject.containsKey("fee_cycle_id")) {
                    Integer fee_cycle_id = jsonObject.getInteger("fee_cycle_id");
                    jsonObject.put("a_fee_cycle_id", StringUtil.listToIntArray(fee_cycle_id));
                }
                if (jsonObject.containsKey("billing_cycle_id")) {
                    Integer billing_cycle_id = jsonObject.getInteger("billing_cycle_id");
                    jsonObject.put("a_billing_cycle_id", StringUtil.listToIntArray(billing_cycle_id));
                }
                if (jsonObject.containsKey("settle_obj_id")) {
                    Long settle_obj_id = jsonObject.getLong("settle_obj_id");
                    jsonObject.put("a_settle_obj_id", StringUtil.listToLongArray(settle_obj_id));
                }
                if (jsonObject.containsKey("settle_obj_type")) {
                    Long settle_obj_type = jsonObject.getLong("settle_obj_type");
                    jsonObject.put("a_settle_obj_type", StringUtil.listToLongArray(settle_obj_type));
                }
                if (jsonObject.containsKey("settle_role")) {
                    Long settle_role = jsonObject.getLong("settle_role");
                    jsonObject.put("a_settle_role", StringUtil.listToLongArray(settle_role));
                }
                jsonObject.put("a_if_deduct_old_owe", StringUtil.listToIntArray(1));
                return 1;
            } else if (REDO_FLAG.CODE_1.getCode().equals(redo_flag) || REDO_FLAG.CODE_3.getCode().equals(redo_flag) ||
                    REDO_FLAG.CODE_11.getCode().equals(redo_flag) || REDO_FLAG.CODE_13.getCode().equals(redo_flag) ||
                    REDO_FLAG.CODE_20.getCode().equals(redo_flag) || REDO_FLAG.CODE_21.getCode().equals(redo_flag) ||
                    REDO_FLAG.CODE_22.getCode().equals(redo_flag)) {
                log.info("重算单，进行算费中");
            }
        }
        return 2;
    }

    /*     入参：
     inRecord：输入话单
     outRecord：输出话单
     msg：处理异常时，填写异常信息
     返回值，是否处理成功*/
    public boolean OnTask(String inRecord) {
        String result = "";
        ProcessParamBean processParamBean = null;
        JSONObject jsonObject = null;
        VProxyJedis jredis = null, preproc_redis = null;
        try {
            inRecord = inRecord.trim();
            if (inRecord == null || "".equals(inRecord)) {
                return true;
            }

            jsonObject = JSON.parseObject(inRecord);                            //---解析json字符串
            // 异常单和 other单跳过不处理。
            String ticketType = jsonObject.getString("ticket_type");
            if ("2".equals(ticketType) || "1".equals(ticketType)) {             //---other,error单不处理
                // 周期话单，charge处理，周期的视图里乘以100，如果批价之前已经是异常单，需要除以100
                String eventClassTmp = jsonObject.getString("event_class");
                if ("2".equals(eventClassTmp)) {
                    String chargeTmp = jsonObject.getString("charge");
                    if (StringUtil.isNumeric(chargeTmp)) {
                        Long lChargeTmp = Long.parseLong(chargeTmp);
                        jsonObject.put("charge", lChargeTmp / 100);
                    }
                }

                //  预处理送过来的异常单，如果没有填写错误类型，统一写10060,10061
                String errorType = jsonObject.getString("error_type");
                if ("0".equals(errorType) || null == errorType) {
                    if ("1".equals(ticketType))
                        Factory.setTicketTypeAbn(jsonObject, 10060, "上一流程来的话单是异常单");
                    else
                        Factory.setTicketTypeOther(jsonObject, 10061, "上一流程来的话单是other单");
                }
                result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
            } else {
                if (1 == dealRecycleRollback(jsonObject)) {
                    // redo_flag 冲销单或者无法识别的redo_flag 处理
                    result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
                } else {
                    // 正常批价
                    processParamBean = new ProcessParamBean();
                    processParamBean.setJsonObject(jsonObject);      //---话单
                    processParamBean.setJredis(jredis);              //---redis
                    processParamBean.setPre_redis(preproc_redis);    //---redis
                    processParamBean.setTableService(tableService);  //---tableService
                    processParamBean.setCalcFunction(calcFunction);  //---因子函数calcFunction
                    //processParamBean.setCalcVisualFunctionLocal(calcVisualFunctionLocal);
                    result = ratingProcess.batchProcess(processParamBean);
                }
            }
            setOutRecord(result);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("OnTask(record):" + inRecord);
            if (jsonObject == null) {
                return false;
            }

            jsonObject.put("charge", new String[]{});
            jsonObject.put("tariff_id", new String[]{});
            jsonObject.put("pricing_plan_id", new String[]{});
            jsonObject.put("acct_item_type_id", new String[]{});
            Factory.setTicketTypeAbn(jsonObject, 10062, ex.getMessage().length() > 150 ? ex.getMessage().substring(0, 150) : ex.getMessage());
            result = JSONObject.toJSONString(jsonObject, SerializerFeature.WriteNullStringAsEmpty);

            setOutRecord(result);
        } finally {
            if (jsonObject != null)
                jsonObject.clear();
            if (jredis != null)
                jredis.close();
            if (preproc_redis != null)
                preproc_redis.close();
            if (processParamBean != null)
                processParamBean = null;
        }
        return true;
    }


    /*线程初始化函数
     * 线程每次处理文件之前会执行此函数
     * */
    @Override
    public boolean OnChildInit() {

        return true;
    }

    @Override
    public void reset() {

    }

    /*线程结束函数
     * 线程处理完所有的记录之后，会处理此函数
     * */
    @Override
    public boolean OnChildOver() {


        return true;
    }

    @Override
    public boolean OnOver(Map<String, List<List<KeyValue>>> dataOver) {
        // TODO 数字生活计费系统，新增累积的功能，在这里进行累积量的落地操作，包括累积量总表和累积量明细表。  2023年7月7日
        if ("DIGIT".equals(CalcRecordManager.getCommon().getSSystem()) && "010".equals(CalcRecordManager.getCommon().getSLatnId())) {
            baseDataHandler.updateAccumulation();
        }
        return true;
    }

    @Override
    public Map<Integer, FunctionPerl> getFunctionPerl() {
        return ratingProcess.getmPerl();
    }

    /*模块退出操作
     * 整个模块退出时，需要释放的空间这里操作。
     * */
    @Override
    public boolean OnExit() {
        if (pool != null)
            pool.close();
        if (pool_preproc != null)
            pool_preproc.close();
        if (!isbInitFlag()) {
            log.info("关闭所有资源");
            TableService.closeAll();
            CalcFunctionLocal.close();
        }
        return true;
    }


    private boolean initData(int threadNo, int uploadType, int rangeId, String remark) {
        try {
            if (!isbInitFlag()) {
                ratingProcess.uploadToCache(uploadType, rangeId, threadNo);              //---上传数据到本机cache中
                //     pool = RedisUtil.getPool();                    //-----初始化redis 连接池
                //    pool_preproc = RedisUtil.getPool_preproc();    //----初始化redis 预处理连接池

                // 初始化表接口
                TableService.setZkClientApi(CalcRecordManager.getZkClientApi());
                TableService.setCommon(common);
                if (!TableService.initCfgAst()) {
                    log.error("TableService.initCfgAst() 失败");
                    return false;
                }
                log.info("缓存查询接口初始化成功");

                // 初始话函数接口
                CalcFunctionLocal.setbInitFlag(isbInitFlag());
                CalcFunctionLocal.setCommon(CalcRecordManager.getCommon());
                CalcFunctionLocal.setZkClientApi(CalcRecordManager.getZkClientApi());
                if (!CalcFunctionLocal.initCfg()) {
                    log.error("CalcFunctionLocal.initCfg() 失败");
                    return false;
                }
                log.info("自定义因子接口初始化成功");

                CalcVisualFunctionLocal.setbInitFlag(isbInitFlag());
                CalcVisualFunctionLocal.setZkClientApi(CalcRecordManager.getZkClientApi());
                if (!CalcVisualFunctionLocal.initCfg()) {
                    log.error("CalcVisualFunctionLocal.initCfg() 失败");
                    return false;
                }
                log.info("缓存因子因子调用接口成功");
            }

            calcFunction = CalcFunctionLocal.getInstance(getThreadNum());
            calcVisualFunctionLocal = CalcVisualFunctionLocal.getInstance(getThreadNum());

            log.info("初始化因子调用接口成功：" + getThreadNum());
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            return false;
        }
        log.info(remark);
        return true;
    }

}
