package com.itco.module;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.itco.component.jdbc.DbPool;
import com.itco.component.jdbc.DecodePassword;
import com.itco.component.zookeeper.ZkClientApi;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.Properties;


public class DataSourceConfig {
    private static Log log = LogFactory.getLog(DataSourceConfig.class);

    public static DataSource getInstance() {
        ZkClientApi zkClientApi = new ZkClientApi();
        zkClientApi.setModuleCode("Rating");
        zkClientApi.setBillingLineId("11");
        if (!zkClientApi.init()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return null;
        }

        if (!zkClientApi.loadInitTable()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return null;
        }
        Properties jProp = zkClientApi.getPropertiesFromZK("jdbc.properties");
        zkClientApi.close();
        zkClientApi = null;

        String publicKey = jProp.getProperty("publicKey");
        String password = jProp.getProperty("password");
        int connectTimeOut = 1200000;
        int socketTimeOut = 1200000;

        if (jProp.getProperty("connectTimeOut") != null) {
            connectTimeOut = Integer.parseInt(jProp.getProperty("connectTimeOut"));
        }
        if (jProp.getProperty("socketTimeOut") != null) {
            socketTimeOut = Integer.parseInt(jProp.getProperty("socketTimeOut"));
        }

        String passwd = DecodePassword.decryption(publicKey, password);
        jProp.put(DruidDataSourceFactory.PROP_PASSWORD, passwd);
        //log.info(jProp.toString());
        log.info("url:" + jProp.getProperty("url") + ",username:" + jProp.getProperty("username"));

        DruidDataSource dataSource;
        try {
            dataSource = (DruidDataSource) DruidDataSourceFactory.createDataSource(jProp);
            dataSource.setConnectTimeout(connectTimeOut);
            dataSource.setSocketTimeout(socketTimeOut);

            return dataSource;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
