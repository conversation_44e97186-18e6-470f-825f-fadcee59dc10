package com.itco.cumulant.constants;


import com.itco.cumulant.mapper.CumulantMapper;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;


/**
 * 累积量配置类
 * <AUTHOR>
 */
@Configuration
@Data
public class GlobalConstants {

    // ------------------------ 累计周期 ---------------------------

    /**
     * 按合同
     */
    public static final String QUANTITY_CYCLE_CONTRACT = "contract";

    /**
     * 按自然月
     */
    public static final String QUANTITY_CYCLE_MONTH = "month";

    /**
     * 按合同年
     */
    public static final String QUANTITY_CYCLE_CONTRACT_YEAR = "contractYear";

    /**
     * 按自然年
     */
    public static final String QUANTITY_CYCLE_YEAR = "year";

    /**
     * 按半年期
     */
    public static final String QUANTITY_CYCLE_HALF_YEAR = "halfYear";


    // ------------------------ 累计方式 ---------------------------

    /**
     * 累计
     */
    public static final String QUANTITY_TYPE_COUNTS = "counts";

    /**
     * 按次
     */
    public static final String QUANTITY_TYPE_SUMS = "sums";


    // ---------------------  累积量 mapper ---------------------------

    @Autowired
    private CumulantMapper cumulantMapper;

}
