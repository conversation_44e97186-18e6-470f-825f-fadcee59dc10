package com.itco.cumulant.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.Date;

@Data
@Accessors(chain = true)
public class DigitContractInfo {

    /**
     * 合同编码
     */
    private String contract_code;

    /**
     * 合同开始时间
     */
    private String contract_period_start;

    /**
     * 合同结束时间
     */
    private String contract_period_end;

}
