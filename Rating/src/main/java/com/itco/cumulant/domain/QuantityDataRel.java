package com.itco.cumulant.domain;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 积量关联的数据源范围实体类
 *
 * @Author: Zhangshf
 * @Date: 2023-07-04
 */
@Data
@Accessors(chain = true)
public class QuantityDataRel {

    /**
     * 主键ID
     */
    private long quantity_data_rel_id;

    /**
     * 积量编码
     */
    private String quantity_code;

    /**
     * 服务类型
     */
    private Integer service_type;

    /**
     * 数据推送系统编码
     */
    private String order_data_from_code;

    /**
     * 商品编码
     */
    private String sales_code;

    /**
     * 合同号
     */
    private String contract_code;

    /**
     * 生效日期 （yyyyMM）
     */
    private String eff_date;

    /**
     * 失效日期 （yyyyMM）
     */
    private String exp_date;
}
