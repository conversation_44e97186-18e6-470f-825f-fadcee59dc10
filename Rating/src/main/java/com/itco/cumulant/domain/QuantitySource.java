package com.itco.cumulant.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 累积量明细实体类
 *
 * @Author: Zhangshf
 * @Date: 2023-07-04
 */
@Data
@Accessors(chain = true)
public class QuantitySource {
    /**
     * 主键ID
     */
    private Long quantitySourceId;

    /**
     * 账期
     */
    private Long billingCycleId;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 积量编码
     */
    private String quantityCode;

    /**
     * 服务控制ID
     */
    private Long serviceControlId;

    /**
     * 积量开始
     */
    private BigDecimal quantityStart;

    /**
     * 积量结束
     */
    private BigDecimal quantityEnd;

    /**
     * 积量增量
     */
    private BigDecimal quantityAdd;

    /**
     * 商品编码
     */
    private String salesCode;

    /**
     * 对应 resultId
     */
    private Long quantityResultId;
}
