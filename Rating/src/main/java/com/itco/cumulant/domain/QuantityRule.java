package com.itco.cumulant.domain;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 累积量规则配置实体类
 *
 * @Author: Zhangshf
 * @Date: 2023-07-04
 */
@Data
@Accessors(chain = true)
public class QuantityRule {
    /**
     * 积量编码
     */
    private String quantity_code;

    /**
     * 积量名称
     */
    private String quantity_name;

    /**
     * 累计的字段，tpr_resourc_attr的attr_id
     */
    private String quantity_field;

    /**
     * 积量类型
     */
    private String quantity_type;

    /**
     * 累计前积量填写的字段，tpr_resourc_attr的attr_id
     */
    private String quantity_start;

    /**
     * 累计后积量填写的字段，tpr_resourc_attr的attr_id
     */
    private String quantity_end;

    /**
     * 积量周期，C001 合同 C002 月
     */
    private String quantity_cycle;
}
