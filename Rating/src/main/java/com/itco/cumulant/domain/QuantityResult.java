package com.itco.cumulant.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 累积量汇总实体类
 *
 * @Author: Zhangshf
 * @Date: 2023-07-04
 */
@Data
@Accessors(chain = true)
public class QuantityResult {
    /**
     * 主键ID
     */
    private Long quantityResultId;

    /**
     * 账期
     */
    private Long billingCycleId;

    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 积量编码
     */
    private String quantityCode;

    /**
     * 当前累积量
     */
    private BigDecimal currentQuantity;

    /**
     * 跨月情况下，记录上一条累积量ID
     */
    private Long orgQuantityResultId;

    /**
     * 商品编码
     */
    private String salesCode;

    /**
     * 累计生效时间
     */
    private String quantityStartDay;

    /**
     * 累计失效时间
     */
    private String quantityEndDay;

}
