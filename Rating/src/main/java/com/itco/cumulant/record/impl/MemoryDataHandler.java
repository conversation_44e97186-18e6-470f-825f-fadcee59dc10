package com.itco.cumulant.record.impl;

import com.alibaba.fastjson.JSONObject;
import com.itco.component.jdbc.DbPool;
import com.itco.cumulant.constants.GlobalConstants;
import com.itco.cumulant.domain.DigitContractInfo;
import com.itco.cumulant.domain.QuantityResult;
import com.itco.cumulant.domain.QuantityRule;
import com.itco.cumulant.domain.QuantitySource;
import com.itco.cumulant.mapper.CumulantMapper;
import com.itco.cumulant.record.BaseDataHandler;
import com.itco.rating.mapper.UploadDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import static com.itco.RatingApplication.cumulantMapper;
import static com.itco.cumulant.constants.GlobalConstants.*;

/**
 * 使用内存方式
 */
@Slf4j
public class MemoryDataHandler extends BaseDataHandler {

    private Map<String, ReentrantLock> lockMap;

    private List<QuantityResult> insertResult;
    private List<QuantityResult> updateResult;

    public MemoryDataHandler() {
        this.lockMap =  new ConcurrentHashMap<>();
        this.insertResult = new ArrayList<>();
        this.updateResult = new ArrayList<>();
    }


    /**
     * 填写累计开始值
     * @param inRecordJson
     */
    @Override
    public Boolean getAccumulation(JSONObject inRecordJson) throws Exception {
        List<Map<String,Object>> quantityCodes = super.getQuantityCode(inRecordJson,false);
        if (quantityCodes.isEmpty()) {
            return true;
        }
        onOffLock(quantityCodes,true);
        String billingCycleId = inRecordJson.getString("billing_cycle_id");
        for (Map<String,Object> map : quantityCodes) {
            String quantityCode = (String) map.get("quantity_code");
            String salesCode = (String) map.get("sales_code");
            String contractCode = (String) map.get("contract_code");
            String ticketStartDate = (String)map.get("ticket_start_date");

            if(StringUtils.isEmpty(digitContractMap.get(contractCode))){
                log.info("累积量-当前合同：" + contractCode + " 在合同表找不到结果跳过累计步骤");
                continue;
            }

            QuantityRule rule = super.quantityRuleMap.get(quantityCode);
            String quantityCycle = rule.getQuantity_cycle();
            QuantityResult currentResult = null;
            String key = "";
            String contractPeriodStart = digitContractMap.get(contractCode).getContract_period_start();
            String contractPeriodEnd = digitContractMap.get(contractCode).getContract_period_end();

            if(!timeDetermine(contractPeriodStart, contractPeriodEnd, ticketStartDate)){
                return false;
            }

            if(quantityCycle.equals(QUANTITY_CYCLE_CONTRACT_YEAR)){
                Map<String, Object> quantityMap = returnQuantityTime(ticketStartDate,contractPeriodStart,contractPeriodEnd);
                contractPeriodStart = (String) quantityMap.get("quantityStartDay");
                contractPeriodEnd = (String) quantityMap.get("quantityEndDay");
                key =  getContractYearKey(contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
            }else{
                key =  getKey(contractCode,quantityCode,salesCode);
            }
            if(summaryMap.get(key) == null){
                summaryMap.put(key,new HashMap<>());
            }

            Map<String, QuantityResult> quantityMap = summaryMap.get(key);
            if (quantityMap.get(billingCycleId) == null) {
                switch (quantityCycle){
                    case QUANTITY_CYCLE_CONTRACT:
                        currentResult = getResultByContract(billingCycleId,contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
                        break;
                    case QUANTITY_CYCLE_MONTH:
                        currentResult = getResultByMonthOnly(billingCycleId,contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
                        break;
                    case QUANTITY_CYCLE_HALF_YEAR:
                        currentResult = getResultByHalfMonthYear(billingCycleId,contractCode,quantityCode,salesCode,ticketStartDate);
                        break;
                    case QUANTITY_CYCLE_CONTRACT_YEAR:
                        currentResult = getResultByContractYear(contractCode,quantityCode,salesCode,billingCycleId,contractPeriodStart,contractPeriodEnd);
                        break;
                    case QUANTITY_CYCLE_YEAR:
                        currentResult = getResultByYear(billingCycleId,contractCode,quantityCode,salesCode,ticketStartDate);
                        break;
                }
                currentResult.setQuantityResultId(cumulantMapper.getResultId());
                insertResult.add(currentResult);
                quantityMap.put(billingCycleId,currentResult);
            } else {
                currentResult = quantityMap.get(billingCycleId);
                if (!updateResult.contains(currentResult)) {
                    updateResult.add(currentResult);
                }
            }
            inRecordJson.put(super.tprResourceAttrMap.get(rule.getQuantity_start()), currentResult.getCurrentQuantity());
        }
        return true;
    }



    /**
     * 累积动作
     * @param inRecordJson 话单结构体
     */
    @Override
    public void calculateTotal(JSONObject inRecordJson,String refVv) throws ParseException {
        List<Map<String,Object>> quantityCodes = super.getQuantityCode(inRecordJson,false);
        if (quantityCodes.isEmpty()) {
            return;
        }
        String billingCycleId = inRecordJson.getString("billing_cycle_id");
        Long serviceControlId = inRecordJson.getLong("service_control_id");
        for (Map<String,Object> map : quantityCodes) {
            String quantityCode = (String) map.get("quantity_code");
            String salesCode = (String) map.get("sales_code");
            String contractCode = (String) map.get("contract_code");
            String ticketStartDate = (String)map.get("ticket_start_date");
            if(StringUtils.isEmpty(digitContractMap.get(contractCode))){
                continue;
            }

            QuantityRule rule = super.quantityRuleMap.get(quantityCode);
            String currentKey = "";
            String contractPeriodStart = digitContractMap.get(contractCode).getContract_period_start();
            String contractPeriodEnd = digitContractMap.get(contractCode).getContract_period_end();
            if(rule.getQuantity_cycle().equals(QUANTITY_CYCLE_CONTRACT_YEAR)){
                Map<String, Object> quantityMap = returnQuantityTime(ticketStartDate,contractPeriodStart,contractPeriodEnd);
                contractPeriodStart = (String) quantityMap.get("quantityStartDay");
                contractPeriodEnd = (String) quantityMap.get("quantityEndDay");
                currentKey =  getContractYearKey(contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
            }else{
                currentKey =  getKey(contractCode,quantityCode,salesCode);
            }

            BigDecimal ResultCharge =  super.tprResourceAttrMap.get(rule.getQuantity_field()).equals("charge") ? new BigDecimal(refVv) :
                    inRecordJson.getBigDecimal(super.tprResourceAttrMap.get(rule.getQuantity_field()));
            QuantityResult currentResult = super.summaryMap.get(currentKey).get(billingCycleId);
            BigDecimal startCharge = currentResult.getCurrentQuantity();
            BigDecimal endCharge = super.accumulationCommon(rule, startCharge, ResultCharge);
            currentResult.setCurrentQuantity(endCharge);
            inRecordJson.put(super.tprResourceAttrMap.get(rule.getQuantity_end()), endCharge);
            QuantitySource quantitySource = new QuantitySource()
                    .setBillingCycleId(Long.parseLong(billingCycleId))
                    .setContractCode(contractCode)
                    .setQuantityCode(quantityCode)
                    .setServiceControlId(serviceControlId)
                    .setQuantityStart(startCharge)
                    .setQuantityAdd(QUANTITY_TYPE_COUNTS.equals(rule.getQuantity_type()) ? new BigDecimal(1) : ResultCharge)
                    .setQuantityEnd(endCharge)
                    .setSalesCode(salesCode)
                    .setQuantityResultId(currentResult.getQuantityResultId());
            super.quantitySourceList.add(quantitySource);
        }
        onOffLock(quantityCodes,false);
    }


    /**
     * 异常单释放锁
     * @param inRecordJson
     */
    @Override
    public void releaseAccumulateLock(JSONObject inRecordJson) {
        List<Map<String,Object>> quantityCodes = super.getQuantityCode(inRecordJson,true);
        if (quantityCodes.isEmpty()) {
            return;
        }
        onOffLock(quantityCodes,false);
    }


    public void onOffLock(List<Map<String,Object>> quantityCodes,Boolean onOffFlag){
        for (Map<String,Object> map : quantityCodes) {
            String quantityCode = (String) map.get("quantity_code");
            String salesCode = (String) map.get("sales_code");
            String contractCode = (String) map.get("contract_code");
            String lockKey = getKey(contractCode, quantityCode, salesCode);
            ReentrantLock lock = lockMap.computeIfAbsent(lockKey, k -> new ReentrantLock());
            if(onOffFlag){
                lock.lock();
            }else{
                lock.unlock();
            }
        }
    }

    @Override
    public void updateAccumulation() {
        Connection connTmp = null;
        PreparedStatement psInsert = null;
        PreparedStatement psUpdate = null;
        PreparedStatement psSource = null;
        try {
            connTmp = DbPool.getConn();
            connTmp.setAutoCommit(false);
            // 批量写汇总表
            String insertSql = "insert into data.quantity_result (quantity_result_id,billing_cycle_id,contract_code,quantity_code,current_quantity,org_quantity_result_id,update_time,sales_code,quantity_start_day,quantity_end_day) values(?,?,?,?,?,?,now(),?,?,?)";
            psInsert = connTmp.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
            for (QuantityResult quantityResult : insertResult) {
                psInsert.setObject(1, quantityResult.getQuantityResultId());
                psInsert.setObject(2, quantityResult.getBillingCycleId());
                psInsert.setObject(3, quantityResult.getContractCode());
                psInsert.setObject(4, quantityResult.getQuantityCode());
                psInsert.setObject(5, quantityResult.getCurrentQuantity());
                psInsert.setObject(6, quantityResult.getOrgQuantityResultId());
                psInsert.setObject(7, quantityResult.getSalesCode());
                psInsert.setObject(8, quantityResult.getQuantityStartDay());
                psInsert.setObject(9, quantityResult.getQuantityEndDay());
                psInsert.addBatch();
            }
            psInsert.executeBatch();
            // 6. 更新实体类中的 ID 值
            ResultSet generatedKeys = psInsert.getGeneratedKeys();
            int index = 0;
            while (generatedKeys.next()) {
                long id = generatedKeys.getLong(1);
                insertResult.get(index).setQuantityResultId(id);
                index++;
            }

            // 批量更新汇总表
            String updateSql = "update data.quantity_result set billing_cycle_id = ?,contract_code = ?,quantity_code = ?,current_quantity = ?,org_quantity_result_id = ?," +
                    " sales_code = ?, update_time = now() where quantity_result_id = ?";
            psUpdate = connTmp.prepareStatement(updateSql);
            for (QuantityResult quantityResult : updateResult) {
                psUpdate.setObject(1, quantityResult.getBillingCycleId());
                psUpdate.setObject(2, quantityResult.getContractCode());
                psUpdate.setObject(3, quantityResult.getQuantityCode());
                psUpdate.setObject(4, quantityResult.getCurrentQuantity());
                psUpdate.setObject(5, quantityResult.getOrgQuantityResultId());
                psUpdate.setObject(6, quantityResult.getSalesCode());
                psUpdate.setObject(7, quantityResult.getQuantityResultId());
                psUpdate.addBatch();
            }
            psUpdate.executeBatch();

            // 批量插入明细表
            String sourceSql = "insert into data.quantity_source (billing_cycle_id,contract_code,quantity_code,service_control_id,quantity_start,quantity_end,quantity_add,sales_code,insert_time,quantity_result_id) values(?,?,?,?,?,?,?,?,now(),?)";
            psSource = connTmp.prepareStatement(sourceSql);
            for (QuantitySource quantitySource : super.quantitySourceList) {
                psSource.setObject(1, quantitySource.getBillingCycleId());
                psSource.setObject(2, quantitySource.getContractCode());
                psSource.setObject(3, quantitySource.getQuantityCode());
                psSource.setObject(4, quantitySource.getServiceControlId());
                psSource.setObject(5, quantitySource.getQuantityStart());
                psSource.setObject(6, quantitySource.getQuantityEnd());
                psSource.setObject(7, quantitySource.getQuantityAdd());
                psSource.setObject(8, quantitySource.getSalesCode());
                psSource.setObject(9, quantitySource.getQuantityResultId());
                psSource.addBatch();
            }
            psSource.executeBatch();

            connTmp.commit();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (psInsert != null) {
                try {
                    psInsert.close();
                } catch (SQLException e) {
                    // 处理异常
                }
            }
            if (psUpdate != null) {
                try {
                    psUpdate.close();
                } catch (SQLException e) {
                    // 处理异常
                }
            }
            if (psSource != null) {
                try {
                    psSource.close();
                } catch (SQLException e) {
                    // 处理异常
                }
            }
            if (connTmp != null) {
                DbPool.close(connTmp);
            }

            super.quantitySourceList.clear();
            insertResult.clear();
            updateResult.clear();
        }

    }

    /**
     * 获取合同开始时间月份
     * @return
     */
    public String getStartCycle(String contractCode){
        DigitContractInfo contractInfo = digitContractMap.get(contractCode);
        LocalDate startDate = LocalDate.parse(contractInfo.getContract_period_start(), DateTimeFormatter.ISO_DATE);
        return startDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }


    /**
     * 累计生失效时间判断
     */
    private Boolean timeDetermine(String startTime,String endTime,String ticketTime){
        LocalDate startDate = LocalDate.parse(startTime, DateTimeFormatter.ISO_DATE);
        LocalDate endDate = LocalDate.parse(endTime, DateTimeFormatter.ISO_DATE);
        LocalDate checkDate = LocalDate.parse(timeTypeConversion(ticketTime), DateTimeFormatter.ISO_DATE);
        if (!checkDate.isBefore(startDate) && !checkDate.isAfter(endDate)) {
            return true;
        }
        return false;
    }


    /**
     * 按合同累计
     */
    @Override
    public QuantityResult getResultByContract(String billingCycleId,String contractCode,String quantityCode,String salesCode,String contractPeriodStart,String contractPeriodEnd) {
        String key = getKey(contractCode, quantityCode, salesCode);
        Map<String, QuantityResult> resultMap = super.summaryMap.get(key);
        if (resultMap.isEmpty()) {
            return getResultByMonth(billingCycleId,contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
        } else {
            QuantityResult lastResult = getPreviousMonthDate(key, resultMap, null);
            return new QuantityResult()
                    .setOrgQuantityResultId(lastResult.getQuantityResultId())
                    .setCurrentQuantity(lastResult.getCurrentQuantity())
                    .setQuantityCode(quantityCode)
                    .setContractCode(contractCode)
                    .setSalesCode(salesCode)
                    .setBillingCycleId(Long.parseLong(billingCycleId))
                    .setQuantityStartDay(contractPeriodStart)
                    .setQuantityEndDay(contractPeriodEnd);
        }
    }

    /**
     * 按自然月累计
     */
    @Override
    public QuantityResult getResultByMonthOnly(String billingCycleId,String contractCode,String quantityCode,String salesCode,
                                               String contractPeriodStart,String contractPeriodEnd) {
        return new QuantityResult()
                .setBillingCycleId(Long.parseLong(billingCycleId))
                .setContractCode(contractCode)
                .setQuantityCode(quantityCode)
                .setSalesCode(salesCode)
                .setCurrentQuantity(new BigDecimal(0))
                .setQuantityStartDay(contractPeriodStart)
                .setQuantityEndDay(contractPeriodEnd);
    }

    public QuantityResult getResultByMonth(String billingCycleId,String contractCode,String quantityCode,String salesCode,
                                           String contractPeriodStart,String contractPeriodEnd) {
        return new QuantityResult()
                .setBillingCycleId(Long.parseLong(billingCycleId))
                .setContractCode(contractCode)
                .setQuantityCode(quantityCode)
                .setSalesCode(salesCode)
                .setCurrentQuantity(new BigDecimal(0))
                .setQuantityStartDay(contractPeriodStart)
                .setQuantityEndDay(contractPeriodEnd);
    }


    /**
     * 按合同年进行累计
     */
    @Override
    public QuantityResult getResultByContractYear(String contractCode,String quantityCode,String salesCode,String billingCycleId,
            String contractPeriodStart,String contractPeriodEnd){
        String startCycle = getStartCycle(contractCode);
        String key = getContractYearKey(contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
        Map<String, QuantityResult> resultMap = super.summaryMap.get(key);
        if(resultMap.isEmpty()){
            return getResultByMonth(billingCycleId,contractCode,quantityCode,salesCode,contractPeriodStart,contractPeriodEnd);
        }else{
            QuantityResult lastResult = getPreviousMonthDate(key, resultMap, startCycle);
            return new QuantityResult()
                    .setOrgQuantityResultId(lastResult.getQuantityResultId())
                    .setCurrentQuantity(lastResult.getCurrentQuantity())
                    .setQuantityCode(quantityCode)
                    .setContractCode(contractCode)
                    .setSalesCode(salesCode)
                    .setBillingCycleId(Long.parseLong(billingCycleId))
                    .setQuantityStartDay(contractPeriodStart)
                    .setQuantityEndDay(contractPeriodEnd);
        }
    }


    private Map<String,Object> returnQuantityTime(String ticketStartDate,String contractPeriodStart,String contractPeriodEnd) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = sdf.parse(contractPeriodStart);
        Date endDate = sdf.parse(contractPeriodEnd);
        if(checkExceedOneYear(startDate,endDate)){
            // 找到对应ticket_start_time是属于哪个分支里
            Date ticketDate = sdf.parse(ticketStartDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            while (calendar.getTime().before(endDate)) {
                Date yearStartTime = calendar.getTime();
                calendar.add(Calendar.YEAR, 1);
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                Date yearEndTime = calendar.getTime();
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                // 判断当前 ticket_start_time 是否在当前次的开始和结束时间内
                if ((ticketDate.equals(yearStartTime) || ticketDate.equals(yearEndTime)) ||
                        (ticketDate.after(yearStartTime) && ticketDate.before(yearEndTime))) {
                    contractPeriodStart = sdf.format(yearStartTime);
                    contractPeriodEnd = sdf.format(yearEndTime);
                }
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("quantityStartDay",contractPeriodStart);
        map.put("quantityEndDay",contractPeriodEnd);
        return map;
    }



    /**
     * 判断是否是跨年度合同
     */
    private Boolean checkExceedOneYear(Date startDate,Date endDate) throws ParseException {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        // 获取开始时间和结束时间的年份
        int startYear = startCalendar.get(Calendar.YEAR);
        int endYear = endCalendar.get(Calendar.YEAR);
        // 获取开始时间和结束时间的年中的天数
        int startDayOfYear = startCalendar.get(Calendar.DAY_OF_YEAR);
        int endDayOfYear = endCalendar.get(Calendar.DAY_OF_YEAR);
        return endYear - startYear > 1 || (endYear - startYear == 1 && endDayOfYear > startDayOfYear);
    }


    /**
     * 按半年期进行累计
     */
    @Override
    public QuantityResult getResultByHalfMonthYear(String billingCycleId,String contractCode,String quantityCode,String salesCode,String ticketStartDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(billingCycleId + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = getKey(contractCode, quantityCode, salesCode);
        String quantityStartDay = "";
        String quantityEndDay = "";
        String limitCycle = null;
        if (date.getMonthValue() <= 6) {
            limitCycle = date.getYear() + "01";
            quantityStartDay = date.withDayOfYear(1).format(formatter);
            quantityEndDay = date.withMonth(6).withDayOfMonth(date.withMonth(6).lengthOfMonth()).format(formatter);
        } else {
            limitCycle =  date.getYear() + "07";
            quantityStartDay = date.withMonth(7).withDayOfMonth(1).format(formatter);
            quantityEndDay = date.withDayOfYear(date.lengthOfYear()).format(formatter);
        }
        boolean isHalfFirstMonth = (date.getMonthValue() == 1 || date.getMonthValue() == 7);
        Map<String, QuantityResult> resultMap = super.summaryMap.get(key);
        if(resultMap.isEmpty() || isHalfFirstMonth){
            return getResultByMonth(billingCycleId,contractCode,quantityCode,salesCode,quantityStartDay,quantityEndDay);
        }else{
            QuantityResult lastResult = getPreviousMonthDate(key, resultMap, limitCycle);
            return new QuantityResult()
                    .setOrgQuantityResultId(lastResult.getQuantityResultId())
                    .setCurrentQuantity(lastResult.getCurrentQuantity())
                    .setQuantityCode(quantityCode)
                    .setContractCode(contractCode)
                    .setSalesCode(salesCode)
                    .setBillingCycleId(Long.parseLong(billingCycleId))
                    .setQuantityStartDay(quantityStartDay)
                    .setQuantityEndDay(quantityEndDay);
        }
    }


    /**
     * 按自然年进行累计
     */
    @Override
    public QuantityResult getResultByYear(String billingCycleId,String contractCode,String quantityCode,String salesCode,
                                          String ticketStartDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(billingCycleId + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        String quantityStartDay = date.withDayOfYear(1).format(formatter);
        String quantityEndDay = date.withDayOfYear(date.lengthOfYear()).format(formatter);
        YearMonth yearMonth = YearMonth.parse(billingCycleId, DateTimeFormatter.ofPattern("yyyyMM"));
        String limitCycle = yearMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
        boolean isFirstMonth = yearMonth.getMonthValue() == 1;
        String key = getKey(contractCode, quantityCode, salesCode);
        Map<String, QuantityResult> resultMap = super.summaryMap.get(key);
        if (resultMap.isEmpty() || isFirstMonth) {
            return getResultByMonth(billingCycleId,contractCode,quantityCode,salesCode,quantityStartDay,quantityEndDay);
        } else {
            QuantityResult lastResult = getPreviousMonthDate(key, resultMap, limitCycle);
            return new QuantityResult()
                    .setOrgQuantityResultId(lastResult.getQuantityResultId())
                    .setCurrentQuantity(lastResult.getCurrentQuantity())
                    .setQuantityCode(quantityCode)
                    .setContractCode(contractCode)
                    .setSalesCode(salesCode)
                    .setBillingCycleId(Long.parseLong(billingCycleId))
                    .setQuantityStartDay(quantityStartDay)
                    .setQuantityEndDay(quantityEndDay);
        }
    }


    private QuantityResult getPreviousMonthDate(String key,Map<String, QuantityResult> resultMap,String limitCycle) {
        QuantityResult result = null;
        for (Map.Entry<String, QuantityResult> entry : resultMap.entrySet()) {
            String mapKey = entry.getKey();
            if (limitCycle == null || (mapKey.compareTo(key) < 0 && mapKey.compareTo(limitCycle) >= 0)) {
                if (result == null || mapKey.compareTo(String.valueOf(result.getBillingCycleId())) > 0) {
                    result = entry.getValue();
                }
            }
        }
        return result;
    }


    private String getKey(String contractCode, String quantityCode, String salesCode) {
        return contractCode + "-" + quantityCode + "-" + salesCode;
    }

    private String getContractYearKey(String contractCode,String quantityCode,String salesCode,String quantityStartDay,String quantityEndDay) {
        return contractCode + "-" + quantityCode + "-" + salesCode + "-" + quantityStartDay + "-" + quantityEndDay;
    }

}
