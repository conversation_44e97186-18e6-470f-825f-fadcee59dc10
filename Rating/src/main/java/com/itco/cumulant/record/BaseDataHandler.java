package com.itco.cumulant.record;

import com.alibaba.fastjson.JSONObject;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.cumulant.domain.*;
import com.itco.cumulant.mapper.CumulantMapper;
import com.itco.entity.common.TprResourceAttr;
import com.itco.rating.mapper.UploadDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.itco.cumulant.constants.GlobalConstants.*;

/**
 * 累积量基类
 */
public abstract class BaseDataHandler {

    protected Map<String,DigitContractInfo> digitContractMap;

    protected List<QuantityDataRel> quantityDataRelList;

    protected Map<String, QuantityRule> quantityRuleMap;

    /**
     * 汇总表数据
     */
    protected Map<String,Map<String, QuantityResult>> summaryMap;

    /**
     * 明细表数据
     */
    protected List<QuantitySource> quantitySourceList = new ArrayList<>();

    /**
     * 字段属性配置
     */
    protected Map<String, String> tprResourceAttrMap;

    /**
     * 累计通用
     *
     * @param rule        规则
     * @param startCharge 起初金额
     * @param charge      该条话单金额
     * @return 累计后的金额
     */
    protected BigDecimal accumulationCommon(QuantityRule rule, BigDecimal startCharge, BigDecimal charge) {
        String quantityType = rule.getQuantity_type();
        if (QUANTITY_TYPE_COUNTS.equals(quantityType)) {
            return startCharge.add(BigDecimal.ONE);
        } else if (QUANTITY_TYPE_SUMS.equals(quantityType)) {
            return startCharge.add(charge);
        }
        return new BigDecimal(0);
    }


    /**
     * 上载配置表
     *
     * @return
     */
    public boolean loadTableConfig() {
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            // 上载基量关联表
            quantityDataRelList = DBUtils.queryList(QuantityDataRel.class, connTmp,
                    "SELECT * FROM data.quantity_data_rel");

            // 上载合同配置
            List<DigitContractInfo> digitContractList = DBUtils.queryList(DigitContractInfo.class, connTmp,
                    "select contract_code,contract_period_start,contract_period_end from data.digit_contract_info_full");
            setDigitContractMap(digitContractList);

            // 上载累积量规则配置
            List<QuantityRule> quantityRuleList = DBUtils.queryList(QuantityRule.class, connTmp,
                    "SELECT * FROM data.quantity_rule");
            setQuantityRuleMap(quantityRuleList);

            // 上载字段属性
            List<TprResourceAttr> tprResourceAttrList = DBUtils.queryList(TprResourceAttr.class, connTmp,
                    " select attr_id,lower(en_name) en_name,ch_name,substr(data_type,2) data_type,comments from config.tpr_resource_attr");
            setTprResourceAttrList(tprResourceAttrList);

            // 汇总结果
            List<QuantityResult> quantityResultList = DBUtils.queryListByCamelCase(QuantityResult.class, connTmp,
                    "SELECT * FROM data.quantity_result");
            setQuantityResultMap(quantityResultList);

        } finally {
            DbPool.close(connTmp);
        }
        return true;
    }

    /**
     * 查询累积量
     * 填写 count_start 和 amount_start
     *
     * @param inRecordJson
     */
    public abstract Boolean getAccumulation(JSONObject inRecordJson) throws Exception;

    /**
     * 统计累积量
     * 填写填写count_end和amount_end
     *
     * @param inRecordJson 话单结构体
     */
    public abstract void calculateTotal(JSONObject inRecordJson,String refVv) throws ParseException;

    /**
     * 更新累积量
     */
    public abstract void updateAccumulation();

    /**
     * 根据合同号累计
     */
    public abstract QuantityResult getResultByContract(String billingCycleId,String contractCode,String quantityCode,String salesCode,String contractPeriodStart,String contractPeriodEnd);

    /**
     * 根据月份累计
     *
     * @return
     */
    public abstract QuantityResult getResultByMonthOnly(String billingCycleId,String contractCode,String quantityCode,String salesCode, String contractPeriodStart,String contractPeriodEnd);


    /**
     * 根据合同年累计
     */
    public abstract QuantityResult getResultByContractYear(String contractCode,String quantityCode,String salesCode,String billingCycleId, String contractPeriodStart,String contractPeriodEnd) throws Exception;

    /**
     * 根据半年期累计
     */
    public abstract QuantityResult getResultByHalfMonthYear(String billingCycleId,String contractCode,String quantityCode,String salesCode,String ticketStartDate);

    /**
     * 根据自然年累计
     *
     * @return
     */
    public abstract QuantityResult getResultByYear(String billingCycleId,String contractCode,String quantityCode,String salesCode,String ticketStartDate);


    /**
     * 异常单释放锁
     * @param inRecordJson
     */
    public abstract void releaseAccumulateLock(JSONObject inRecordJson);


    /**
     * 判断累计类型
     * @param inRecordJson 话单结构体
     * @return 返回基量编码数组
     */
    protected List<Map<String,Object>> getQuantityCode(JSONObject inRecordJson,Boolean isIgnore) {
        List<Map<String,Object>> quantityCodes = new ArrayList<>();
        Integer ticketType = inRecordJson.getInteger("ticket_type");
        if (ticketType != 0 && !isIgnore) {
            return quantityCodes;
        }
        String billingCycleIdStr = Optional.ofNullable(inRecordJson.getString("billing_cycle_id")).orElse("");
        String serviceType = Optional.ofNullable(inRecordJson.getString("service_type")).orElse("");
        String orderDataFromCode = Optional.ofNullable(inRecordJson.getString("order_data_from_code")).orElse("");
        String salesCode = Optional.ofNullable(inRecordJson.getString("sales_code")).orElse("");
        String ContractCode = Optional.ofNullable(inRecordJson.getString("contract_code")).orElse("");
        for (QuantityDataRel rel: quantityDataRelList) {
            String[] salesCodeList = rel.getSales_code().split(",");
            if(rel.getService_type().toString().equals(serviceType) && rel.getOrder_data_from_code().equals(orderDataFromCode) &&
                    Arrays.asList(salesCodeList).contains(salesCode) && (rel.getContract_code().equals(ContractCode) || rel.getContract_code().equals("*"))){
                int effDate = Integer.parseInt(rel.getEff_date());
                int expDate = Integer.parseInt(rel.getExp_date());
                int billingCycleId = Integer.parseInt(billingCycleIdStr);
                if (billingCycleId <= expDate && billingCycleId >= effDate) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("quantity_code",rel.getQuantity_code());
                    map.put("sales_code",rel.getSales_code());
                    map.put("ticket_start_date",inRecordJson.getString("ticket_start_date"));
                    map.put("contract_code", rel.getContract_code().equals("*") ? ContractCode : rel.getContract_code());
                    quantityCodes.add(map);
                }
            }
        }
        return quantityCodes;
    }



    private void setQuantityRuleMap(List<QuantityRule> quantityRuleList) {
        quantityRuleMap = new HashMap<>();
        for (QuantityRule quantityRule : quantityRuleList) {
            String key = quantityRule.getQuantity_code();
            quantityRuleMap.put(key, quantityRule);
        }
    }



    private void setDigitContractMap(List<DigitContractInfo> digitContractList) {
        digitContractMap = new HashMap<>();
        for (DigitContractInfo digitInfo : digitContractList) {
            String key = digitInfo.getContract_code();
            digitInfo.setContract_period_start(timeTypeConversion(digitInfo.getContract_period_start()));
            digitInfo.setContract_period_end(timeTypeConversion(digitInfo.getContract_period_end()));
            digitContractMap.put(key, digitInfo);
        }
    }


    public String timeTypeConversion(String inputDate){
        // 尝试使用不同的格式进行解析
        String[] formats = {"yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"};
        for (String format : formats) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                LocalDateTime dateTime = LocalDate.parse(inputDate, formatter).atStartOfDay();
                return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                // 解析失败，尝试下一个格式
            }
        }
        // 所有格式都无法解析，返回null或者做其他处理
        return null;
    }

    private void setTprResourceAttrList(List<TprResourceAttr> tprResourceAttrList) {
        tprResourceAttrMap = new HashMap<>();
        for (TprResourceAttr tprResourceAttr : tprResourceAttrList) {
            tprResourceAttrMap.put(String.valueOf(tprResourceAttr.getAttr_id()), tprResourceAttr.getEn_name().toLowerCase());
        }
    }

    private void setQuantityResultMap(List<QuantityResult> quantityResultList) {
        summaryMap = new HashMap<>();
        if (quantityResultList == null) {
            return;
        }
        for (QuantityResult quantityResult : quantityResultList) {
            String key = "";
            if(quantityResult.getQuantityCode().equals(QUANTITY_CYCLE_CONTRACT_YEAR)) {
                key = getContractYearKey(quantityResult.getContractCode(), quantityResult.getQuantityCode(),
                        quantityResult.getSalesCode(), quantityResult.getQuantityStartDay(), quantityResult.getQuantityEndDay());
            }else{
                key =  getKey(quantityResult.getContractCode(),quantityResult.getQuantityCode(),quantityResult.getSalesCode());
            }
            Map<String, QuantityResult> resultMap = summaryMap.get(key);
            if (resultMap == null) {
                resultMap = new HashMap<>();
                summaryMap.put(key, resultMap);
            }
            resultMap.put(String.valueOf(quantityResult.getBillingCycleId()), quantityResult);
        }
    }

    private String getKey(String contractCode,String quantityCode,String salesCode) {
        return contractCode + "-" + quantityCode + "-" + salesCode;
    }

    private String getContractYearKey(String contractCode,String quantityCode,String salesCode,String quantityStartDay,String quantityEndDay) {
        return contractCode + "-" + quantityCode + "-" + salesCode + "-" + quantityStartDay + "-" + quantityEndDay;
    }


}