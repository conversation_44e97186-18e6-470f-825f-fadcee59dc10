package com.itco;


import com.itco.cumulant.constants.GlobalConstants;
import com.itco.cumulant.mapper.CumulantMapper;
import com.itco.cumulant.record.BaseDataHandler;
import com.itco.cumulant.record.impl.MemoryDataHandler;
import com.itco.framework.Factory;
import com.itco.framework.Version;
import com.itco.framework.process.impl.SettleProcessManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@SpringBootApplication
@EntityScan
@MapperScan("com.itco.rating.mapper")
@MapperScan("com.itco.cumulant.mapper")
public class RatingApplication {
    static Log log = LogFactory.getLog(RatingApplication.class);
    static int debug = 0;
    static int elemet = 0;
    static String calcClassName = null;
    static String moduleCode = null;
    static String billingLineId = null;
    public static BaseDataHandler baseDataHandler;
    public static CumulantMapper cumulantMapper;

    @Resource
    private GlobalConstants globalConstants;

    public RatingApplication(GlobalConstants globalConstants){
        this.cumulantMapper = globalConstants.getCumulantMapper();
    }

	/*@Bean
    @Order(1)
    public DataSource getDataSource() {
        return DataSourceConfig.getInstance();
    }*/

    @Bean
    @Order(2)
    public boolean run() {
        log.info("Rating run start");

        // 消息接收和加载落地文件框架类实例化
        SettleProcessManager settleProcessManager = new SettleProcessManager();
        settleProcessManager.setDebug(debug);
        settleProcessManager.setIsElement(elemet);
        settleProcessManager.setCalcClassName(calcClassName);
        settleProcessManager.setModuleCode(moduleCode);
        settleProcessManager.setBillingLineId(billingLineId);

        if (!settleProcessManager.Init()) {
            log.info("settleProcessManager init false");
            Factory.setbWhileFlag(true);
            return false;
        }

        Thread recordThread = new Thread(settleProcessManager);
        recordThread.start();

        // 初始化累积量功能
        baseDataHandler = new MemoryDataHandler();
        baseDataHandler.loadTableConfig();

        log.info("Rating run close");
        return true;
    }

    @PreDestroy
    public void destroy() {
        Version.print();
        Version.destroy();
    }

    public static void main(String[] args) {
        if (Version.print(args, "Rating_code_2023-03-20 11:00")) {
            return;
        }

        String sModuleCode = new String("Rating");
        String sBillinglineId = null;

        for (String para : args) {
            if (para.startsWith("-f")) {
                sBillinglineId = new String(para.substring(2));
            } else if (para.startsWith("-d")) {
                debug = 1;
                if (para.length() > 2) {
                    debug = Integer.parseInt(para.substring(2));
                }
            } else if (para.startsWith("-e")) {
                elemet = 1;
            } else if (para.equals("trial")) {
                sModuleCode = "tRating";
            } else if (para.equals("pre")) {
                sModuleCode = "pRating";
            }
        }

        String envModuleCode = System.getenv("MODULE_NAME");
        String envBillingLineId = System.getenv("BILLING_LINE_ID");

        if (envModuleCode != null && !envModuleCode.equals("")) {
            sModuleCode = envModuleCode;
        }
        if (envBillingLineId != null && !envBillingLineId.equals("")) {
            sBillinglineId = envBillingLineId;
        }

        calcClassName = "com.itco.module.CalcRecordRating";
        moduleCode = sModuleCode;
        billingLineId = sBillinglineId;

        SpringApplication app = new SpringApplication(RatingApplication.class);
        //app.setWebApplicationType(WebApplicationType.NONE);    //---去掉TomcatWebserver
        ConfigurableApplicationContext context = app.run(args);
        Factory.setContext(context);
        if (Factory.isbWhileFlag()) {
            // 初始化失败的情况退出
            Factory.close();
        }

        log.info("初始化成功");
    }


}
