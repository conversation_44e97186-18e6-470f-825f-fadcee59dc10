
spring:
  servlet:
    multipart:
      maxFileSize: 1000MB
      maxRequestSize: 1000MB
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: jdbc:postgresql://**************:18921/bill_db?currentSchema=config
      username: bill_app
      password: wa70H4k+l9iiUFZVZ90dyHsXmL/1mG5lTQhD/sP7hKv1d2r2P91sKjgmgwb77WpsVCqLKIi0TvMcn/gSvUlIPA==
      filters: config,wall,stat
      max-active: 100
      initial-size: 1
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAPPLFFdMFNyAwHVoV5DD5NK97vCqKkV1OnlpTRIppPtqPHI/SZJOAy4uVtEdPOJZvjsSf4hnkcqilsOg1RgjtKcCAwEAAQ==
# mybatis
mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.itco.rating.entity


logging:
  level:
    root: WARN
    com.itco: INFO
    com.ctg.itrdc.cache: ERROR
    org.apache.zookeeper: WARN
    org.springframework.boot.autoconfigure: ERROR
    c.c.i.cache.vjedis.pool.CtgVJedisPool: ERROR
    c.c.itrdc.cache.monitor: ERROR
    c.ctg.itrdc.cache.monitor: ERROR
  file:
    name: ${LOG_HOME:~/javalog}/Rating.log
    max-size: 1000MB

