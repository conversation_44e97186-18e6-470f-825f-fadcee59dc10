FROM openjdk:8-jre-alpine 
RUN mkdir javabin/
RUN mkdir config/
RUN mkdir javalog/
RUN mkdir javalog/pid/
RUN pwd
COPY ./bin/zk.properties config/
COPY ./bin/script-11/DbIncept.sh javabin/
COPY ./target/DbIncept-0.0.1-SNAPSHOT.jar javabin/
WORKDIR javabin/
RUN chmod 755 *.sh
ENV PATH=/javabin:$PATH
ENV CONFIG /config/
# 修改docker时区为东八区，规避应用程序和北京时间相差8小时问题 
ENV TZ=Asia/Shanghai 
ENV LOG_HOME /javalog/
ENV HOST_ID=11
CMD sh /javabin/DbIncept.sh start 11