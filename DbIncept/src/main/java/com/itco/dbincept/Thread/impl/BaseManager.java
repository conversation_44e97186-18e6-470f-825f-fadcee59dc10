package com.itco.dbincept.Thread.impl;

import com.itco.dbincept.Thread.BatchManager;
import com.itco.dbincept.Thread.BreakManager;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dbincept.Thread.CollectManager;
import com.itco.dbincept.Thread.RollbackManager;
import com.itco.dbincept.entity.TprCollection;
import com.itco.dbincept.entity.TprCollectionItem;
import com.itco.entity.common.Common;
import com.itco.entity.common.ErrorType;
import com.itco.entity.common.TgAlertLog;
import com.itco.framework.Factory;
import com.itco.framework.log.LogAlert;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;

public abstract class BaseManager {
    static Log log = LogFactory.getLog(BaseManager.class);
    //public String type = null;
    protected String processType = null;

    ZkClientApi zkClientApi = null;
    public Common common = null;

    public Map<String, TprCollectionItem> m_CollectionItem = new HashMap<>();
    public TprCollection tprCollection = null;

    public TprCollectionItem tprCollectionItem = null;

    public int billingLineId = 0;
    public int processId = 0;
    public int module_id = 0;
    public int host_id = 0;

    protected int dclCnt = -1;
    protected int iPostCnt = 0;

    protected int iMaxBackTaskCnt = 100;

    int iZookeeperExpiredReconnectCnt = 0;
    int iZookeeperLossReconnectCnt = 0;

    public List<Map<String, Object>> recordList = new ArrayList<>();

    public String readSqlBak = null;

    String billingLineIdPath = null;

    public String getReadSqlBak() {
        return readSqlBak;
    }

    public static BaseManager getInstance(String type) {
        if (type.equals("batch")) {
            return new BatchManager();
        } else if (type.equals("break")) {
            return new BreakManager();
        } else if (type.equals("Collect")) {
            return new CollectManager();
        } else if (type.equals("rollback")) {
            return new RollbackManager();
        } else {
            return null;
        }
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public void setCollection(TprCollection tprCollection, Map<String, TprCollectionItem> m_CollectionItem) {
        this.tprCollection = tprCollection;
        this.m_CollectionItem = m_CollectionItem;
    }

    public TprCollection getTprCollection() {
        return tprCollection;
    }

    public TprCollectionItem getTprCollectionItem() {
        return tprCollectionItem;
    }

    public List<Map<String, Object>> getRecordList() {
        return recordList;
    }

    public int getiPostCnt() {
        return iPostCnt;
    }

    public void setiPostCnt(int iPostCnt) {
        this.iPostCnt = iPostCnt;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public void setiMaxBackTaskCnt(int iMaxBackTaskCnt) {
        this.iMaxBackTaskCnt = iMaxBackTaskCnt;
    }

    public boolean init() {
        DbPool.setZkClientApi(zkClientApi);
        if (!DbPool.init()) {
            log.error("DbPool.init() faile,数据库连接失败！");
            return false;
        }


        billingLineId = tprCollection.getBilling_line_id();
        processId = Integer.parseInt(zkClientApi.getProcessID());
        module_id = zkClientApi.getTpModule().getModule_id();
        host_id = Integer.valueOf(zkClientApi.getProcessID().substring(4, 6));

        if (!subInit()) {
            log.error("subInit() faile");
            return false;
        }
        return true;
    }

    boolean resetRegister(boolean flag, String msg) {
        //zkClientApi.close();
        if (flag) {
            boolean bRet = zkClientApi.init();
            if (bRet) {
                //重新注册，之间修改状态为0 ，非挂起
                if (!zkClientApi.register(false, "0")) {
                    log.error(msg + "，进程重新注册失败");
                    return false;
                } else {
                    log.info(msg + "，进程重新注册成功");
                    log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
                }
            }
        } else {
            //重新注册，之间修改状态为0 ，非挂起
            if (!zkClientApi.register(false, "0")) {
                log.error(msg + "，进程重新注册失败");
                return false;
            } else {
                log.info(msg + "，进程重新注册成功");
                log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
            }
        }
        return true;
    }

    // 判断调度是否存在
    public int isExistsDispatch() {
        int connectionLoss = zkClientApi.isConnectionLoss("/process/Dispatch/Dispatch-10100000");
        if (connectionLoss == 1) {
            return 1;
        } else if (connectionLoss == 0) {
            return 0;
        } else if (connectionLoss == 10) {
            if (zkClientApi.getiConnectionRefusedCnt() == 0 && zkClientApi.getiConnectionLossCnt() == 0 && zkClientApi.getiSectionExpiredCnt() == 0) {
                return 1;
            }
        } else if (connectionLoss < 0) {
            if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_CONNECTION_LOSS) {
                if (zkClientApi.getiConnectionLossCnt() == 1) {
                    TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                    tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                    LogAlert.alert(tgAlertLog);
                }

                if (zkClientApi.getiConnectionLossCnt() % 10 == 1 && iZookeeperLossReconnectCnt < 3) {
                    if (!resetRegister(true, "zookeeper失去连接")) {
                        iZookeeperLossReconnectCnt++;
                    } else {
                        iZookeeperExpiredReconnectCnt = 0;
                        iZookeeperLossReconnectCnt = 0;
                    }
                }
            } else if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_SESSION_EXPIRED) {
                if (zkClientApi.getiSectionExpiredCnt() == 1) {
                    TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                    tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                    LogAlert.alert(tgAlertLog);
                }

                if (zkClientApi.getiSectionExpiredCnt() % 10 == 1 && iZookeeperExpiredReconnectCnt < 3) {
                    // 连续10次超时，就重新连接
                    TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES, Long.parseLong(zkClientApi.getProcessID()));
                    tgAlertLog.setLog_desc("告警类型:" + ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES + "，zookeeper的section连续10次失效");
                    LogAlert.alert(tgAlertLog);

                    if (!resetRegister(true, "zookeeper的section连续10次失效")) {
                        iZookeeperExpiredReconnectCnt++;
                    } else {
                        iZookeeperLossReconnectCnt = 0;
                        iZookeeperExpiredReconnectCnt = 0;
                    }

                }
            }
        }

        return -1;
    }

    public boolean unLockByBillingLineId() {
        //log.info("释放工作路径:" + billingLineIdPath);
        boolean bRet = false;
        if (billingLineIdPath != null) {
            bRet = zkClientApi.deletePath(billingLineIdPath);
            billingLineIdPath = null;
        }

        return bRet;
    }

    public boolean LockByBillingLineId(int billing_line_id) {
        boolean bRet = true;
        String lockPath = "/process/Assist/" + zkClientApi.getTpProcess().getModule_code() + "/";
        String lockStr = null;
        try {
            lockStr = zkClientApi.lock(lockPath);
            //isLockFlag = true;
            String configPath = "/process/Lock/" + zkClientApi.getTpProcess().getModule_code();
            List<String> seqList = zkClientApi.getChildPath(configPath);
            if (!seqList.stream().anyMatch(name -> String.valueOf(billing_line_id).equals(name))) {
                billingLineIdPath = zkClientApi.createEphemeralPath(configPath + "/" + billing_line_id, Factory.getSystemDateStr());
                if (billingLineIdPath == null) {
                    log.error(configPath + "/" + billing_line_id + ",zookeeper 临时目录创建失败");
                    return false;
                }

                //log.info("获取到工作路径:" + billingLineIdPath);
                bRet = false;
            }

        } finally {
            zkClientApi.unLock(lockStr);
        }
        return bRet;
    }

    /**
     * @Description: 子类初始化操作
     * @Param: []
     * @return: boolean
     * @Author: Mr.Fuxingwang
     * @Date: 2021/6/2
     */
    public abstract boolean subInit();

    public abstract boolean runInit();

    /**
     * @Description: 判断是否有需要接收的话单
     * @Param: []
     * @return: int
     * @Author: Mr.Fu
     * @Date: 2021/6/2
     */
    public abstract int isCondition();


    /**
     * 调度任务是否积压判断
     *
     * @return
     */
    public abstract boolean isTaskBackUp();

    /**
     * @Description: 从数据库读取话单
     * @Param: []
     * @return: boolean
     * @Author: Mr.Fu
     * @Date: 2021/6/2
     */
    public abstract boolean db2Cache();

    /**
     * @Description: 更新断点值
     * @Param: []
     * @return: int
     * @Author: Mr.Fu
     * @Date: 2021/6/2
     */
    public abstract boolean updateBreakPoint();

    /**
     * @Description: 获取批次号
     * @Param: []
     * @return: Long
     * @Author: Mr.Fu
     * @Date: 2021/6/2
     */
    public abstract Long getBatchId();
}
