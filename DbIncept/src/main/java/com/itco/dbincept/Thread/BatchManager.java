package com.itco.dbincept.Thread;

import com.itco.dbincept.Thread.impl.BaseManager;
import com.itco.dbincept.entity.BatchTask;
import com.itco.dbincept.jdbc.DbUtilComm;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;


public class BatchManager extends BaseManager {
    static Log log = LogFactory.getLog(BatchManager.class);

    Long lPointValue = new Long(0);
    BatchTask batchTask = null;

    @Override
    public Long getBatchId() {
        return batchTask.getBatch_id();
    }

    @Override
    public boolean subInit() {

        return true;
    }

    @Override
    public boolean runInit() {
        return true;
    }

    @Override
    public boolean isTaskBackUp() {
        return false;
    }

    @Override
    public int isCondition() {
        List<BatchTask> list = null;
        list = DbUtilComm.queryList(BatchTask.class, tprCollection.getScope_sql());
        if (list == null || list.isEmpty()) {
            return 0;
        } else if (list.size() > 0) {
            batchTask = list.get(0);
        }

        if (common.getIDebug() > 0) {
            log.info(String.format("%-2d -> %-7s ,读取批次号:%-6d ", tprCollection.getBilling_line_id(), tprCollection.getType(), batchTask.getBatch_id()));
        }
        return list.size();
    }

    @Override
    public boolean db2Cache() {
        tprCollectionItem = m_CollectionItem.get(batchTask.getMode());
        if (tprCollectionItem == null) {
            log.error("没有找到试算模式:" + batchTask.getMode());
            return false;
        }

        recordList.clear();
        int iRet = DbUtilComm.loadTableRecord(tprCollectionItem.getRead_sql(), tprCollectionItem.getFile_cnt(), recordList, batchTask.getBatch_id());
        if (iRet < 0) {
            log.error("连接数据库失败");
            return false;
        }
        if (common.getIDebug() >= 0) {
            log.info(String.format("%-2d -> %-7s ,读取批次号:%-6d ,记录数:%d", tprCollection.getBilling_line_id(), tprCollection.getType(), batchTask.getBatch_id(), recordList.size()));
        }
        return true;
    }

    @Override
    public boolean updateBreakPoint() {

        String upBkSql = "update trial.trial_batch set state=?,update_time=to_timestamp(?,'yyyy-mm-dd HH24:MI:SS') where batch_id=?";

        //更新采集断点值
        boolean bRet = DbUtilComm.exeUpdate(upBkSql, "00B", Factory.getSystemDateStr(), batchTask.getBatch_id());
        if (!bRet) {
            log.error(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + ",批次表状态更新失败");
            return false;
        }

        log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + ",批次表状态更新成功，batch_id:" + batchTask.getBatch_id() + ",记录数:" + recordList.size());
        return true;
    }
}
