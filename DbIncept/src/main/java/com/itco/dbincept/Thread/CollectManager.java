package com.itco.dbincept.Thread;

import com.itco.dbincept.Thread.impl.BaseManager;
import com.itco.dbincept.entity.BreakPoint;
import com.itco.dbincept.jdbc.DbUtilComm;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;

public class CollectManager extends BaseManager {
    static Log log = LogFactory.getLog(CollectManager.class);

    Long lPointValue = new Long(0);

    Long lLastPointValue = new Long(0);

    String collectCdrIdSequence = null;

    int iState = 0;
    int dclCnt = -1;

    @Override
    public boolean subInit() {
        int iModuleId = module_id;
        collectCdrIdSequence = "collect_cdr_id_seq_" + processId;
        log.info("序列号名称:" + collectCdrIdSequence);
        log.info("module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);

        String bpSql = "select * from break_point where module_id=? and host_id=? and billing_line_id=?";
        List<BreakPoint> breakPointList = DbUtilComm.queryList(BreakPoint.class, bpSql, iModuleId, host_id, billingLineId);
        if (breakPointList == null || breakPointList.size() != 1) {
            log.error("断点表配置不合法:module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);
            return false;
        }
        if (breakPointList.get(0).getPoint_value() != null) {
            lPointValue = breakPointList.get(0).getPoint_value();
        }

        String dataSeq = "select coalesce(count(1),0) cnt from " + tprCollection.getScope_sql() + " where collect_cdr_id >= ?";
        String sRet = DbUtilComm.query(dataSeq, lPointValue);
        int iCnt = Integer.parseInt(sRet);
        if (iCnt > 0) {
            log.info("【源头表数据异常】");
            log.info("【异常原因】:源头表有COLLECT_CDR_ID值 >=" + lPointValue + " 断点值的记录!");
            log.info("【当前断点值】:" + lPointValue);
            log.info("【异常记录条数】:" + iCnt);

            log.info("请核对源头表数据，或在数据用户[data]下执行如下数据库语句进行处理!");
            log.info("update " + tprCollection.getScope_sql() + " set collect_cdr_id=0 where collect_cdr_id >= " + lPointValue);
            return false;
        }

        String createSeq = "create sequence if not exists " + collectCdrIdSequence + " INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1  CACHE 1";
        if (!DbUtilComm.execute(createSeq)) {
            log.error("DbUtilComm.execute(" + createSeq + ") 失败");
            return false;
        }

        String alterSeq = "alter sequence " + collectCdrIdSequence + " restart with " + lPointValue;
        if (!DbUtilComm.execute(alterSeq)) {
            log.error("DbUtilComm.execute(" + alterSeq + ") 失败");
            return false;
        }

        tprCollectionItem = m_CollectionItem.get(tprCollection.getType());
        log.info(String.format("%-2d -> %-7s ,断点表断点值:%-9d ", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue));

        return true;
    }

    @Override
    public boolean runInit() {
        int iModuleId = module_id;
        collectCdrIdSequence = "collect_cdr_id_seq_" + processId;

        String bpSql = "select * from break_point where module_id=? and host_id=? and billing_line_id=?";
        List<BreakPoint> breakPointList = DbUtilComm.queryList(BreakPoint.class, bpSql, iModuleId, host_id, billingLineId);
        if (breakPointList == null || breakPointList.size() != 1) {
            log.error("断点表配置不合法:module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);
            return false;
        }
        if (breakPointList.get(0).getPoint_value() != null) {
            lPointValue = breakPointList.get(0).getPoint_value();
        }

        String createSeq = "create sequence if not exists " + collectCdrIdSequence + " INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1  CACHE 1";
        if (!DbUtilComm.execute(createSeq)) {
            log.error("DbUtilComm.execute(" + createSeq + ") 失败");
            return false;
        }

        String alterSeq = "alter sequence " + collectCdrIdSequence + " restart with " + lPointValue;
        if (!DbUtilComm.execute(alterSeq)) {
            log.error("DbUtilComm.execute(" + alterSeq + ") 失败");
            return false;
        }

        return true;
    }

    @Override
    public boolean isTaskBackUp() {
        if (dclCnt != -1 && ((dclCnt + iPostCnt) < (iMaxBackTaskCnt - 100))) {
            //log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() +",最大积压任务数量:" + iBackUpCnt + ",当前积压数量:" + (dclCnt + iPostCnt) + ",dclCnt:" + dclCnt + ",iPostCnt:" + iPostCnt);
            return false;
        }

        dclCnt = 0;
        iPostCnt = 0;
        String sql = "select count(1) from dcoos_tp_task_record where state in ('OCL','DCL') and type = ? ";

        if ("FILE".equals(common.getRecordTransferMode())) {
            sql += " and host_id = " + host_id;
        }

        String taskCnt = DbUtilComm.query(sql, processType);
        if (taskCnt != null && (!"".equals(taskCnt))) {
            dclCnt = Integer.parseInt(taskCnt);
        }

        if (common.getIDebug() > 0) {
            //log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + ",最大积压任务数量:" + iMaxBackTaskCnt + ",当前积压数量:" + dclCnt + ",taskCnt:" + taskCnt + ",processType:" + processType);
            log.info(String.format("%-2d -> %-7s ,最大积压任务数量:%-3d ,当前积压数量:%-3d ,taskCnt:%-3s ,processType:%-6s ,host_id:%-2d", tprCollection.getBilling_line_id(), tprCollection.getType(), iMaxBackTaskCnt,dclCnt,taskCnt,processType,host_id));
        }
        if (dclCnt > iMaxBackTaskCnt) {
            return true;
        }

        return false;
    }

    @Override
    public int isCondition() {
        iState = 0;
        String sql = "select coalesce(count(1),0) cnt from " + tprCollection.getScope_sql() + " where collect_cdr_id >= " + lPointValue;
        String cnt = DbUtilComm.query(sql);
        if (cnt == null) {
            return 0;
        } else if ("".equals(cnt)) {
            return 0;
        }

        Long lcnt = Long.parseLong(cnt);
        if (lcnt > 0) {
            if (common.getIDebug() >= 0) {
                log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + "collect_cdr_id > " + lPointValue + ",未处理记录数 lcnt:" + lcnt);
            }
            return 1;
        }

        sql = "select coalesce(count(1),0) cnt from " + tprCollection.getScope_sql() + " where collect_cdr_id = 0 ";
        cnt = DbUtilComm.query(sql);
        if (cnt == null) {
            return 0;
        } else if ("".equals(cnt)) {
            return 0;
        }

        lcnt = Long.parseLong(cnt);
        if (lcnt > 0) {
            iState = 1;
            if (common.getIDebug() >= 0) {
                log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + ",collect_cdr_id = 0 未处理记录数 lcnt:" + lcnt);
            }
            return 1;
        }
        if (common.getIDebug() > 0) {
            log.info(String.format("%-2d -> %-7s ,断点表断点值:%-9d ,源头表 collect_cdr_id:0         ,无记录", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue));
        }
        return 0;
    }


    @Override
    public boolean db2Cache() {
        tprCollectionItem = m_CollectionItem.get(tprCollection.getType());
        if (tprCollectionItem == null) {
            log.error("没有找到处理模式:" + tprCollection.toString());
            return false;
        }
        if (iState == 1) {
            String sql = "update " + tprCollection.getScope_sql() + " set collect_cdr_id = nextval('" + collectCdrIdSequence + "') where collect_cdr_id = 0";
            if (!DbUtilComm.exeUpdate(sql)) {
                log.error("DbUtilComm.exeUpdate() 失败" + sql);
                return false;
            }
        }

        recordList.clear();
        Long endPointValue = lPointValue + tprCollectionItem.getFile_cnt();
        String readSql = "select * from " + tprCollection.getScope_sql() + "  where collect_cdr_id >= ? and collect_cdr_id < ?";
        readSqlBak = "select * from " + tprCollection.getScope_sql() + "  where collect_cdr_id >= " + lPointValue + " and collect_cdr_id < " + endPointValue;

        if (common.getIDebug() > 0) {
            log.info(readSql);
            log.info(String.format("%-2d -> %-7s ,当前读取起始断点值:%-9d ,结束断点值:%-9d", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue, endPointValue));
        }
        int iRet = DbUtilComm.loadTableRecord(readSql, tprCollectionItem.getFile_cnt(), recordList, lPointValue, endPointValue);
        if (iRet < 0) {
            log.error("读取数据库失败");
            return false;
        }

        lLastPointValue = lPointValue + recordList.size();

        if (common.getIDebug() >= 0) {
            log.info(String.format("%-2d -> %-7s ,当前断点值:%-9d ,读取记录数:%d", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue, recordList.size()));
        }
        return true;
    }

    @Override
    public boolean updateBreakPoint() {
        if (lLastPointValue > lPointValue) {
            lPointValue = lLastPointValue;
            String upBkSql = "update break_point set point_value=?,create_date=? where module_id=? and host_id=? and billing_line_id=? and point_type='0'";

            //更新采集断点值
            boolean bRet = DbUtilComm.exeUpdate(upBkSql, lLastPointValue, Factory.getSystemDateStr(), module_id, host_id, billingLineId);
            if (!bRet) {
                log.error("断点表更新失败");
                return false;
            }

            if (common.getIDebug() > 0) {
                log.info(String.format("%-2d -> %-7s ,process_id:%-8d ,point_value:%-9d ,断点表更新成功.", tprCollection.getBilling_line_id(), tprCollection.getType(), processId, lLastPointValue));
            }
        }
        return true;
    }

    @Override
    public Long getBatchId() {
        return 0L;
    }
}
