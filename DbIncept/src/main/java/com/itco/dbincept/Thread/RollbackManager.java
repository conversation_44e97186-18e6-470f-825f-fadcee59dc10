package com.itco.dbincept.Thread;

import com.itco.dbincept.Thread.impl.BaseManager;
import com.itco.dbincept.entity.RollbackTask;
import com.itco.dbincept.jdbc.DbUtilComm;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;


public class RollbackManager extends BaseManager {
    static Log log = LogFactory.getLog(RollbackManager.class);

    Long lPointValue = new Long(0);
    RollbackTask rollbackTask = null;

    @Override
    public Long getBatchId() {
        return rollbackTask.getBatch_id();
    }

    @Override
    public boolean subInit() {

        return true;
    }

    @Override
    public boolean runInit() {
        return true;
    }

    @Override
    public boolean isTaskBackUp() {
        return false;
    }

    @Override
    public int isCondition() {
        List<RollbackTask> list = null;
        //log.info("scope_sql:" + tprCollection.getScope_sql());
        list = DbUtilComm.queryList(RollbackTask.class, tprCollection.getScope_sql());
        if (list == null) {
            log.info("读取批次表，list.size() is null");
            return 0;
        } else if (list.size() > 0) {
            rollbackTask = list.get(0);
        }

        if (common.getIDebug() > 0) {
            log.info(String.format("%-2d -> %-7s ,批次号:%-6d ,读取批次号 ,list.size():%d", tprCollection.getBilling_line_id(), tprCollection.getType(), rollbackTask.getBatch_id(), list.size()));
        }

        return list.size();
    }

    @Override
    public boolean db2Cache() {
        tprCollectionItem = m_CollectionItem.get(tprCollection.getType());
        if (tprCollectionItem == null) {
            log.error("没有找到处理模式:" + tprCollection.toString());
            return false;
        }

        recordList.clear();
        int iRet = DbUtilComm.loadTableRecord(tprCollectionItem.getRead_sql(), tprCollectionItem.getFile_cnt(), recordList, rollbackTask.getBatch_id());
        if (iRet < 0) {
            log.error("连接数据库失败");
            return false;
        }

        if (common.getIDebug() >= 0) {
            log.info(String.format("%-2d -> %-7s ,批次号:%-6d ,读取批次号 ,读取记录数:%d", tprCollection.getBilling_line_id(), tprCollection.getType(), rollbackTask.getBatch_id(), recordList.size()));
        }
        return true;
    }

    @Override
    public boolean updateBreakPoint() {
        String upBkSql = null;
        if ("TPSS".equals(common.getSSystem()) && "591".equals(common.getSLatnId())) {
            upBkSql = "update tpss_data.recycle_rollback_task set state='I06',state_record=state_record||'[I06:'||to_char(now(),'yyyy-mm-dd HH24:MI:SS')||']',state_date=now() where batch_id=?";
        } else {
            upBkSql = "update data.recycle_rollback_task set state='I06',state_record=state_record||'[I06:'||to_char(now(),'yyyy-mm-dd HH24:MI:SS')||']',state_date=now() where batch_id=?";
        }

        //更新采集断点值
        boolean bRet = DbUtilComm.exeUpdate(upBkSql, rollbackTask.getBatch_id());
        if (!bRet) {
            log.error(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + "批次表状态更新失败");
            return false;
        }

        log.info(tprCollection.getBilling_line_id() + " -> " + tprCollection.getType() + "批次表状态更新成功，batch_id:" + rollbackTask.getBatch_id() + ",记录数:" + recordList.size());
        return true;
    }
}
