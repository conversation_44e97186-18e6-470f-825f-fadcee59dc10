package com.itco.dbincept.Thread;

import com.itco.dbincept.Thread.impl.BaseManager;
import com.itco.dbincept.entity.BreakPoint;
import com.itco.dbincept.jdbc.DbUtilComm;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Map;


public class BreakManager extends BaseManager {
    static Log log = LogFactory.getLog(BreakManager.class);

    Long lPointValue = new Long(0);
    // Long lMaxPointValue = new Long(0);
    Long lLastPointValue = new Long(0);


    @Override
    public Long getBatchId() {
        return 0L;
    }

    @Override
    public boolean subInit() {
        int iModuleId = module_id;
        log.info("module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);

        String bpSql = "select * from break_point where module_id=? and host_id=? and billing_line_id=?";
        List<BreakPoint> breakPointList = DbUtilComm.queryList(BreakPoint.class, bpSql, iModuleId, host_id, billingLineId);
        if (breakPointList == null || breakPointList.size() != 1) {
            log.error("断点表配置不合法:module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);
            return false;
        }
        if (breakPointList.get(0).getPoint_value() != null) {
            lPointValue = breakPointList.get(0).getPoint_value();
        }

        tprCollectionItem = m_CollectionItem.get(tprCollection.getType());
        log.info(String.format("  %-2d -> %-7s ,断点表断点值:%-9d ", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue));
        return true;
    }

    @Override
    public boolean runInit() {
        int iModuleId = module_id;

        String bpSql = "select * from break_point where module_id=? and host_id=? and billing_line_id=?";
        List<BreakPoint> breakPointList = DbUtilComm.queryList(BreakPoint.class, bpSql, iModuleId, host_id, billingLineId);
        if (breakPointList == null || breakPointList.size() != 1) {
            log.error("断点表配置不合法:module_id:" + iModuleId + ",host_id:" + host_id + ",billing_line_id:" + billingLineId);
            return false;
        }
        if (breakPointList.get(0).getPoint_value() != null) {
            lPointValue = breakPointList.get(0).getPoint_value();
        }

        return true;
    }

    @Override
    public boolean isTaskBackUp() {
        if (dclCnt != -1 && ((dclCnt + iPostCnt) < (iMaxBackTaskCnt - 100))) {
            //log.info("最大积压任务数量:" + iBackUpCnt + ",当前积压数量:" + (dclCnt + iPostCnt) + ",dclCnt:" + dclCnt + ",iPostCnt:" + iPostCnt);
            return false;
        }

        dclCnt = 0;
        iPostCnt = 0;
        String sql = "select count(1) from dcoos_tp_task_record where state in ('OCL','DCL') and type = ? ";

        if ("FILE".equals(common.getRecordTransferMode())) {
            sql += " and host_id = " + host_id;
        }

        //log.info("判断任务表积压数量:" + sql);
        String taskCnt = DbUtilComm.query(sql, processType);
        if (taskCnt != null && (!"".equals(taskCnt))) {
            dclCnt = Integer.parseInt(taskCnt);
        }

        if (common.getIDebug() > 0) {
            log.info(String.format("  %-2d -> %-7s ,最大积压任务数量:%-3d ,当前积压数量:%-3d ,taskCnt:%-3s ,processType:%-6s ,host_id:%-2d", tprCollection.getBilling_line_id(), tprCollection.getType(), iMaxBackTaskCnt,dclCnt,taskCnt,processType,host_id));
        }
        if (dclCnt > iMaxBackTaskCnt) {
            return true;
        }
        return false;
    }

    @Override
    public int isCondition() {

        String maxPointValue = DbUtilComm.query(tprCollection.getScope_sql(), lPointValue);
        if (maxPointValue == null) {
            return 0;
        } else if ("".equals(maxPointValue)) {
            return 0;
        }
        int iRet = 0;
        Long lMaxPointValue = Long.parseLong(maxPointValue);

        if (lMaxPointValue > lPointValue) {
            iRet = 1;
        }
        if (common.getIDebug() > 0) {
            log.info(String.format("  %-2d -> %-7s ,断点表断点值:%-9d ,源头表 lMaxPointValue:%-9d ,%s", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue, lMaxPointValue, (iRet > 0 ? "有数据" : "无数据")));
        }
        return iRet;
    }

    @Override
    public boolean db2Cache() {
        tprCollectionItem = m_CollectionItem.get(tprCollection.getType());
        if (tprCollectionItem == null) {
            log.error("没有找到处理模式:" + tprCollectionItem.toString());
            return false;
        }

        recordList.clear();
        Long endPointValue = lPointValue + tprCollectionItem.getFile_cnt();
        String sql = tprCollectionItem.getRead_sql();
        String tmp = sql.substring(0, sql.indexOf("?"));
        String tmp1 = sql.substring(sql.indexOf("?") + 1, sql.lastIndexOf("?"));
        String tmp2 = sql.substring(sql.lastIndexOf("?") + 1);

        readSqlBak = tmp + lPointValue + tmp1 + endPointValue + tmp2;

        if (common.getIDebug() > 0) {
            log.info(readSqlBak);
            log.info(String.format("  %-2d -> %-7s ,当前读取起始断点值:%-9d ,结束断点值:%-9d", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue, endPointValue));
        }
        int iRet = DbUtilComm.loadTableRecord(tprCollectionItem.getRead_sql(), tprCollectionItem.getFile_cnt(), recordList, lPointValue, endPointValue);
        if (iRet < 0) {
            log.error("读取数据库失败");
            return false;
        }

        for (Map<String, Object> map : recordList) {
            String maxPointValue = map.get(tprCollectionItem.getMode()).toString();
            Long lMaxPointValue = Long.parseLong(maxPointValue);
            if (lMaxPointValue > lLastPointValue) {
                lLastPointValue = lMaxPointValue;
            }
        }

        if (common.getIDebug() >= 0) {
            log.info(String.format("  %-2d -> %-7s ,当前断点值:%-9d ,读取记录数:%d", tprCollection.getBilling_line_id(), tprCollection.getType(), lPointValue, recordList.size()));
        }
        return true;
    }

    @Override
    public boolean updateBreakPoint() {
        if (recordList.size() == 0) {
            lLastPointValue = lPointValue + tprCollectionItem.getFile_cnt();
        }

        lPointValue = lLastPointValue;
        String upBkSql = "update break_point set point_value=?,create_date=? where module_id=? and host_id=? and billing_line_id=? and point_type='0'";

        //更新采集断点值
        boolean bRet = DbUtilComm.exeUpdate(upBkSql, lLastPointValue, Factory.getSystemDateStr(), module_id, host_id, billingLineId);
        if (!bRet) {
            log.error("断点表更新失败");
            return false;
        }

        if (common.getIDebug() > 0) {
            log.info(String.format("  %-2d -> %-7s ,process_id:%-8d ,point_value:%-9d ,断点表更新成功.", tprCollection.getBilling_line_id(), tprCollection.getType(), processId, lLastPointValue));
        }
        return true;
    }
}
