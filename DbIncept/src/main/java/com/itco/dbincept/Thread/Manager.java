package com.itco.dbincept.Thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dbincept.DbInceptApplication;
import com.itco.dbincept.Thread.impl.BaseManager;
import com.itco.dbincept.entity.CollectionLog;
import com.itco.dbincept.entity.MessageProcess;
import com.itco.dbincept.entity.TprCollection;
import com.itco.dbincept.entity.TprCollectionItem;
import com.itco.dbincept.jdbc.DbUtilComm;
import com.itco.entity.common.Common;
import com.itco.entity.process.E_RecordTransferMode;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.message.TaskMessageFactory;
import com.itco.framework.record.TransferFactory;
import com.itco.framework.record.impl.settle.TransferSettle;
import lombok.SneakyThrows;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

public class Manager implements Runnable {
    static Log log = LogFactory.getLog(Manager.class);
    static boolean eExit = false;

    TaskMessage taskMessage = null;
    TransferSettle transfer = null;

    TransferFactory transferFactory = new TransferFactory();
    TaskMessageFactory taskMessageFactory = new TaskMessageFactory();

    ZkClientApi zkClientApi = null;

    Map<TprCollection, Map<String, TprCollectionItem>> mCollection = new LinkedHashMap<>();

    String billingLineId = null;
    String moduleCode = null;
    String processId = null;
    int module_id = 0;
    int host_id = 0;
    String fileName = null;
    BaseManager baseManager = null;

    Common common = new Common(); //公共配置类
    Long lNo = 1L;
    String homePath = "";
    boolean gTpProcessFlag = true;   //进程状态反馈给调度，控制标识
    SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式

    int iSurplus = 0;

    boolean isCluster = false;
    String clusterId = null;
    List<String> cluster = null;
    int iMaxBackTaskCnt = 100;

    boolean register() {
        zkClientApi = new ZkClientApi();
        zkClientApi.setVersion(common.getVersion());
        zkClientApi.setBillingLineId(billingLineId);
        zkClientApi.setModuleCode(moduleCode);
        if (!zkClientApi.init()) {
            log.error(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return false;
        }
        if (!zkClientApi.register(false)) {
            log.error("注册失败");
            return false;
        }
        processId = zkClientApi.getProcessID();
        module_id = zkClientApi.getTpModule().getModule_id();
        host_id = Integer.valueOf(processId.substring(4, 6));

        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        log.info(common.toString());
        return true;
    }

    @SneakyThrows
    boolean readConfig() {
        List<TprCollection> listCollect = zkClientApi.getTableFromZK("tpr_collection", TprCollection.class);
        List<TprCollectionItem> listCollectItem = zkClientApi.getTableFromZK("tpr_collection_item", TprCollectionItem.class);
        Properties moduleProps = zkClientApi.getPropertiesFromZK(moduleCode + ".properties");

        if (null != moduleProps.getProperty("workFlag")) {
            String flag = moduleProps.getProperty("workFlag");
            if (flag != null && flag.equals("offline")) {
                common.setWorkFlag(true);
                common.setOffworkPath(moduleProps.getProperty("workDir"));
                common.setOffWorkFileName(moduleProps.getProperty("workFileName"));
                log.info("离线模式：" + common.getOffworkPath() + "\\" + common.getOffWorkFileName());
            }
        }

        if (null != moduleProps.getProperty("max_back_task_cnt")) {
            iMaxBackTaskCnt = Integer.parseInt(moduleProps.getProperty("max_back_task_cnt"));
        }

        if (listCollectItem == null || listCollect == null) {
            log.error("tpr_collection、tpr_collection_item表没有配置试算规则");
            return false;
        }

        if (isCluster) {
            String tmpCluster = moduleProps.getProperty(clusterId + "_cluster");
            if (StringUtils.isEmpty(tmpCluster)) {
                tmpCluster = moduleProps.getProperty("cluster");
            }

            if (!StringUtils.isEmpty(tmpCluster)) {
                cluster = Arrays.asList(tmpCluster.split(","));
            } else {
                log.error("集群模式，未配置集群对应的billing_line_id，例如：DbIncept.properties文件中添加 cluster=11,12,21");
                return false;
            }
        } else {
            cluster = new ArrayList<>();
            cluster.add(billingLineId);
        }

        log.info("max_back_task_cnt:" + iMaxBackTaskCnt);
        log.info(isCluster ? "集群模式 -> " + cluster.toString() : "单点模式 -> " + billingLineId);

        if (common.isWorkFlag()) {
            // 离线模式
            Resource resource = new ClassPathResource("/application.properties");
            if (resource.exists()) {
                Properties propApp = PropertiesLoaderUtils.loadProperties(resource);
                String tmpWorkFlag = propApp.getProperty("workFlag");
                String tmpWorkDir = propApp.getProperty("workDir");
                if ("true".equals(tmpWorkFlag)) {
                    common.setWorkFlag(true);
                    common.setOffworkPath(moduleProps.getProperty("workDir"));
                    common.setOffWorkFileName(moduleProps.getProperty("workFileName"));
                }
                if (!StringUtils.isEmpty(tmpWorkDir)) {
                    homePath = tmpWorkDir;
                }
            }
        } else {
            if ("FILE".equals(common.getRecordTransferMode())) {
                homePath = System.getenv("HOME");
                if (StringUtils.isEmpty(homePath)) {
                    log.error("主机环境变量HOME路径未设置或者为空");
                    homePath = "/home/<USER>/";
                }
            }
        }

        for (TprCollection collect : listCollect) {
            if (cluster.stream().anyMatch(name -> String.valueOf(collect.getBilling_line_id()).equals(name))) {
                Map<String, TprCollectionItem> tmpMap = new HashMap<>();
                for (TprCollectionItem item : listCollectItem) {
                    if (collect.getBilling_line_id() == item.getBilling_line_id()) {
                        if ("batch".equals(collect.getType())) {
                            tmpMap.put(item.getMode(), item);
                        } else {
                            tmpMap.put(collect.getType(), item);
                        }
                    }
                }
                mCollection.put(collect, tmpMap);
            } else {
                log.info(collect.getBilling_line_id() + "，在配置中不存在，跳过");
            }
        }

        for (Map.Entry<TprCollection, Map<String, TprCollectionItem>> iterator : mCollection.entrySet()) {
            if (iterator.getValue().size() == 0) {
                log.error("未配置tpr_collection表 、tpr_collection_item表的billing_line_id:" + iterator.getKey().getBilling_line_id() + " 的记录");
                return false;
            }
            log.info(iterator.getKey() + "+" + iterator.getValue());
        }

        return true;
    }

    boolean taskMqInit() {
        taskMessage = taskMessageFactory.getTaskMessageInstanceByMode(common.getTaskTransferMode());
        if (taskMessage == null) {
            log.error("taskMqInit() 失败");
            return false;
        }

        if (common.getIDbIncept_mode() == 0) {  //模式0 无需接收MQ消息
            taskMessage.setMode(1); //无消费者模式
        }
        taskMessage.setZkClientApi(zkClientApi);
        taskMessage.setCommon(common);

        if (!taskMessage.init()) {
            return false;
        }

        if (common.getIDbIncept_mode() == 1) {  //模式1 才有接收MQ消息
            // 启用MQ消息接收
            if (!taskMessage.startReceivedMessage()) {
                log.error("taskMessage.startReceivedMessage() faile");
                Factory.setbWhileFlag(true);
                return false;
            }
        }

        return true;
    }

    public boolean init() {
        // zookeeper 初始化
        if (!register()) {
            return false;
        }

        // 读取zk configp配置
        if (!readConfig()) {
            log.error("读取配置失败");
            return false;
        }

        // task 初始化
        if (!taskMqInit()) {
            log.error("taskMqInit() 初始化失败");
            return false;
        }

        transfer = transferFactory.getTransferSettleInstanceByMode(common.getRecordTransferMode());
        if (transfer == null) {
            log.error("transferRecord 实例化失败");
            return false;
        }

        transfer.setZkClientApi(zkClientApi);
        transfer.setCommon(common);
        if (!transfer.init()) {
            log.error("transferRecord.init(p) 失败");
            return false;
        }

        // 进程申请接收任务。
        if (!processReadyWork()) {
            log.error(processId + " ,进程准备接收任务失败。");
            return false;
        }
        log.info(processId + " ,进程准备接收任务成功。");


        return true;
    }

    protected boolean processReadyWork() {
        if (!zkClientApi.readyWork()) {
            log.error("zkClientApi.readyWork() faile()");
            return false;
        }

        // 调度存在的情况下，进行发送消息
        if (1 == zkClientApi.iExistPath("/process/Dispatch/Dispatch-10100000")) {
            MsgBody.StartStopProcessBody body = new MsgBody.StartStopProcessBody();
            body.COMMAND_ID = getSystemDateStr() + "_1";
            body.TASK_TYPE = MsgBody.TASK_TYPE.T_SS_PROCESS.getType();
            body.MSG_TYPE = "REQUEST";
            body.PROCESS_ID = zkClientApi.getProcessID();
            body.REQUEST_PROCESS_ID = zkClientApi.getProcessID();
            body.STATE = "";
            body.ACTION = "START";
            body.ACTION_DATE = "NOW";
            body.CREATE_DATE = getSystemDateStr();
            common.setVersion("[" + common.getBill_public_version() + "][" + common.getDeploy_version() + "][" + common.getCode_version() + "]");
            body.VERSION = common.getVersion();
            String json = JSON.toJSONString(body);
            log.info("申请修改进程状态为，非挂起状态，json:" + json);
            taskMessage.sendMessage(zkClientApi.getDspchProcessID(), json);
        }

        return true;
    }

    boolean sendTpProcessState(String state) {
        MessageProcess process = new MessageProcess();
        process.setTASK_TYPE("PROCESS");
        process.setSTATUS(state);
        process.setPROCESS_ID(Integer.parseInt(processId));
        process.setUPDATE_DATE(getSystemDateStr());

        String json = JSONObject.toJSONString(process);
        taskMessage.sendMessage(zkClientApi.getDspchProcessID(), json);
        if (common.getIDebug() > 0) {
            log.info("采集进程状态上报调度，json:" + json);
        }
        return true;
    }

    boolean sendRecordFile2Dispatch(MsgBody.DbInceptBody task) {
        String json = JSONObject.toJSONString(task);
        taskMessage.sendMessage(zkClientApi.getDspchProcessID(), json);
        log.info("采集文件上报消息发送成功，json:" + json);
        baseManager.setiPostCnt(baseManager.getiPostCnt() + 1);
        return true;
    }

    boolean sendQuestTaskCount2Dispatch(MsgBody.DbInceptBody task) {
        String json = JSONObject.toJSONString(task);
        taskMessage.sendMessage(zkClientApi.getDspchProcessID(), json);
        log.info("采集请求可用的任务数量，json:" + json);
        return true;
    }

    boolean writefileSendMq(TprCollectionItem collectionItem, List<String> recordList, Long lCurrTime, String flow) {
        fileName = collectionItem.getFile_prefix() + processId + "_" + df.format(new Date()) + "_" + lNo++ + "_" + recordList.size();
        String writePath = fileName;
        if (common.getRecordTransferMode().equals("FILE")) {
            writePath = homePath + collectionItem.getTarget_path() + "/" + fileName;
        } else if (common.getRecordTransferMode().equals("HDFS")) {
            writePath = collectionItem.getTarget_path() + "/" + fileName;
        }
        log.info("写入路径：" + writePath);

        boolean readWrite = transfer.outputRecord(writePath, recordList);
        if (!readWrite) {
            log.error("输出话单文件：" + writePath + " ，失败");
            return false;
        }

        Long lTime = System.currentTimeMillis() - lCurrTime;
        int iPerl = (int) (baseManager.getRecordList().size() * 1000 / (lTime > 0 ? lTime : 1));

        boolean b = insertCollectionLog(recordList.size(), lCurrTime, String.valueOf(iPerl), readWrite);
        if (!b) {
            log.error("插入collectionLog表失败");
        }

        if (common.getRecordTransferMode().equals(E_RecordTransferMode.CTG_MQ.getName())) {
            log.info("话单流转：" + E_RecordTransferMode.CTG_MQ.getName() + " ,不发消息给调度");
        } else {
            // 发送消息给调度
            MsgBody.DbInceptBody msg = new MsgBody.DbInceptBody();
            msg.BATCH_ID = String.valueOf(baseManager.getBatchId());
            msg.HOST_ID = String.valueOf(host_id);
            msg.MODULE_ID = processId.substring(0, 4);
            msg.PROCESS_ID = processId;
            msg.FILE_NAME = fileName;
            if ("CTG_CACHE".equals(common.getRecordTransferMode())) {
                msg.LOCAL_PATH = common.getRecordCacheHashkey();
            } else {
                msg.LOCAL_PATH = baseManager.getTprCollectionItem().getTarget_path();
            }

            msg.RECORD_CNT = String.valueOf(recordList.size());
            msg.FIRST = flow;
            msg.CREATE_DATE = getSystemDateStr();
            msg.PERL = String.valueOf(iPerl);
            msg.MSG_TYPE = "REQUEST";
            msg.TYPE = baseManager.getTprCollectionItem().getType();
            msg.TASK_TYPE = MsgBody.TASK_TYPE.T_RECORD.getType();

            sendRecordFile2Dispatch(msg);
        }
        return true;
    }

    boolean resetRegister(boolean flag, String msg) {
        //zkClientApi.close();
        if (flag) {
            boolean bRet = zkClientApi.init();
            if (bRet) {
                //重新注册，之间修改状态为0 ，非挂起
                if (!zkClientApi.register(false, "0")) {
                    log.error(msg + "，进程重新注册失败");
                    return false;
                } else {
                    log.info(msg + "，进程重新注册成功");
                    log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
                }
            }
        } else {
            //重新注册，之间修改状态为0 ，非挂起
            if (!zkClientApi.register(false, "0")) {
                log.error(msg + "，进程重新注册失败");
                return false;
            } else {
                log.info(msg + "，进程重新注册成功");
                log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
            }
        }
        return true;
    }

    //单点模式
    boolean db2fileNormal() throws InterruptedException {
        TprCollection tprCollection = null;
        Map<String, TprCollectionItem> m_CollectionItem = null;
        for (Map.Entry<TprCollection, Map<String, TprCollectionItem>> iterator : mCollection.entrySet()) {
            if (iterator.getKey().getBilling_line_id() == Integer.parseInt(billingLineId)) {
                tprCollection = iterator.getKey();
                m_CollectionItem = iterator.getValue();
                break;
            }
        }

        String typeTmp = zkClientApi.getTpProcess().getType();
        baseManager = BaseManager.getInstance(tprCollection.getType());
        baseManager.setCollection(tprCollection, m_CollectionItem);
        baseManager.setZkClientApi(zkClientApi);
        baseManager.setCommon(common);
        baseManager.setProcessType(typeTmp);
        if (!baseManager.init()) {
            log.error("init() faile");
            return false;
        }

        while (!eExit) {
            //  判断调度是否存在
            int existsDispatch = baseManager.isExistsDispatch();
            if (existsDispatch == 0) {
                Thread.sleep(8000);
                log.error("调度进程未启动，不进行话单接收工作，等待调度模块启动");
                continue;
            } else if (existsDispatch == -1) {
                Thread.sleep(8000);
                log.error("zookeeper连接异常。。。");
                continue;
            }

            if (common.getIDbIncept_mode() == 0) {
                // 模式0：通过查询任务表，进行最大任务数限制
                if (baseManager.isTaskBackUp()) {
                    Thread.sleep(baseManager.getTprCollection().getThread_sleep());
                    continue;
                }
            } else if (common.getIDbIncept_mode() == 1) {
                //  模式1：通过发消息给调度，获取已使用任务数来限制最大任务数
                //  判断话单是否积压过多
                if (iSurplus <= 0) {
                    int iRet = getTaskCount();
                    if (iRet == 1) {
                        iSurplus = 30; // 每次请求，可用获取100个任务
                    } else if (iRet == -1) {
                        log.error("向调度请求可用任务数30秒无响应，失败。");
                        return false;
                    } else {
                        try {
                            Thread.sleep(baseManager.getTprCollection().getThread_sleep());
                        } catch (InterruptedException e) {
                            log.error(e);
                        }
                        continue;
                    }
                }
            }

            // 判断是否有需要接收的话单
            int iRet = baseManager.isCondition();
            if (iRet == 0) {
                if (gTpProcessFlag) {
                    sendTpProcessState("TB"); //发送进程空闲
                    gTpProcessFlag = false;
                }
                Thread.sleep(baseManager.getTprCollection().getThread_sleep());
                continue;
            } else if (iRet > 0) {
                if (!gTpProcessFlag) {
                    sendTpProcessState("TE");  //发送进程处理中
                    gTpProcessFlag = true;
                }
            }

            Long lCurrTime = System.currentTimeMillis();
            // 接收话单
            if (!baseManager.db2Cache()) {
                log.error("db2Cache() faile");
                return false;
            }

            if (baseManager.getRecordList().size() > 0) {
                TprCollectionItem collectionItem = baseManager.getTprCollectionItem();
                // 写话单文件
                List<String> recordList = new ArrayList<>();
                int iFor = 0;
                for (Map<String, Object> mapTmp : baseManager.getRecordList()) {
                    JSONObject jsonObject = new JSONObject(mapTmp);
                    recordList.add(JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue));

                    if (recordList.size() == collectionItem.getFile_cnt() || (iFor * collectionItem.getFile_cnt() + recordList.size()) == baseManager.getRecordList().size()) {
                        iFor++;
                        if (!writefileSendMq(collectionItem, recordList, lCurrTime, baseManager.getTprCollection().getFlow())) {
                            return false;
                        }
                        recordList.clear();
                    }
                }
            }

            // 更新断点值
            if (!baseManager.updateBreakPoint()) {
                log.error("updateBreakPoint() faile()");
                return false;
            }

            if (baseManager.getRecordList().size() < baseManager.getTprCollectionItem().getFile_cnt() && "Collect".equals(baseManager.getTprCollection().getType())) {
                Thread.sleep(baseManager.getTprCollection().getThread_sleep());
            }
        }
        return true;
    }

    @SneakyThrows
    int isWorkingByCondition() {
        //  判断调度是否存在
        int existsDispatch = baseManager.isExistsDispatch();
        if (existsDispatch == 0) {
            Thread.sleep(8000);
            log.error("调度进程未启动，不进行话单接收工作，等待调度模块启动");
            return 0;
        } else if (existsDispatch == -1) {
            Thread.sleep(8000);
            log.error("zookeeper连接异常。。。");
            return 0;
        }

        if (common.getIDbIncept_mode() == 0) {
            // 模式0：通过查询任务表，进行最大任务数限制
            if (baseManager.isTaskBackUp()) {
                return 0;
            }
        } else if (common.getIDbIncept_mode() == 1) {
            //  模式1：通过发消息给调度，获取已使用任务数来限制最大任务数
            //  判断话单是否积压过多
            if (iSurplus <= 0) {
                int iRet = getTaskCount();
                if (iRet == 1) {
                    iSurplus = 30; // 每次请求，可用获取100个任务
                } else if (iRet == -1) {
                    log.error("向调度请求可用任务数30秒无响应，失败。");
                    return -1;
                } else {
                    return 0;
                }
            }
        }
        return 1;
    }

    //集群模式
    boolean db2fileNormalCluster() throws InterruptedException {
        List<BaseManager> listManager = new ArrayList<>();
        String typeTmp = zkClientApi.getTpProcess().getType();

        for (Map.Entry<TprCollection, Map<String, TprCollectionItem>> iterator : mCollection.entrySet()) {
            BaseManager tmpManager = BaseManager.getInstance(iterator.getKey().getType());
            tmpManager.setCollection(iterator.getKey(), iterator.getValue());
            tmpManager.setZkClientApi(zkClientApi);
            tmpManager.setCommon(common);
            tmpManager.setProcessType(typeTmp);
            tmpManager.setiMaxBackTaskCnt(iMaxBackTaskCnt);
            if (!tmpManager.init()) {
                log.error(iterator.getKey().getBilling_line_id() + " init() faile");
                return false;
            }
            listManager.add(tmpManager);
        }

        log.info(isCluster ? "集群模式 -> " + cluster.toString() : "单点模式 -> " + billingLineId);

        while (!eExit) {
            boolean dataCnt = false;
            for (int jFor = 0; jFor < listManager.size(); jFor++) {
                baseManager = listManager.get(jFor);

                int iRet = isWorkingByCondition();
                if (iRet == 0) {
                    Thread.sleep(baseManager.getTprCollection().getThread_sleep());
                    continue;
                } else if (iRet == -1) {
                    log.error("isWorkingByCondition() faile!!!");
                    return false;
                }

                // 判断是否已经有人正在做这个billing_line_id的出库。
                if (!baseManager.LockByBillingLineId(baseManager.getTprCollection().getBilling_line_id())) {
                    if (!baseManager.runInit()) {
                        log.error("baseManager.runInit() faile!!!");
                        return false;
                    }

                    // 判断是否有需要接收的话单
                    iRet = baseManager.isCondition();
                    if (iRet == 0) {
                        if (gTpProcessFlag) {
                            sendTpProcessState("TB"); //发送进程空闲
                            gTpProcessFlag = false;
                        }
                    } else if (iRet > 0) {
                        if (!gTpProcessFlag) {
                            sendTpProcessState("TE");  //发送进程处理中
                            gTpProcessFlag = true;
                        }

                        Long lCurrTime = System.currentTimeMillis();
                        // 接收话单
                        if (!baseManager.db2Cache()) {
                            log.error("db2Cache() faile");
                            return false;
                        }

                        if (baseManager.getRecordList().size() > 0) {
                            TprCollectionItem collectionItem = baseManager.getTprCollectionItem();
                            // 写话单文件
                            List<String> recordList = new ArrayList<>();
                            int iFor = 0;
                            for (Map<String, Object> mapTmp : baseManager.getRecordList()) {
                                JSONObject jsonObject = new JSONObject(mapTmp);
                                recordList.add(JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue));

                                if (recordList.size() == collectionItem.getFile_cnt() || (iFor * collectionItem.getFile_cnt() + recordList.size()) == baseManager.getRecordList().size()) {
                                    iFor++;
                                    if (!writefileSendMq(collectionItem, recordList, lCurrTime, baseManager.getTprCollection().getFlow())) {
                                        return false;
                                    }
                                    recordList.clear();
                                }
                            }
                        }

                        // 更新断点值
                        if (!baseManager.updateBreakPoint()) {
                            log.error("updateBreakPoint() faile()");
                            return false;
                        }

                        dataCnt = true;
                    }

                    // 要客产品要求，定时读取任务，上游系统实时送数据，我们如果实时接收，导致每个任务订单数太少，总的任务数太多。
                    /*if (baseManager.getRecordList().size() < baseManager.getTprCollectionItem().getFile_cnt() && "Collect".equals(baseManager.getTprCollection().getType())) {
                        Thread.sleep(baseManager.getTprCollection().getThread_sleep());
                    }*/
                    baseManager.unLockByBillingLineId();
                }
            }
            if (!dataCnt) {
                Thread.sleep(8000);
            }

            if (common.getIDebug() > 0) {
                log.info("");
            }
        }
        return true;
    }

    @SneakyThrows
    @Override
    public void run() {
        if (!db2fileNormalCluster()) {
            log.error("db2fileNormalCluster() faile");
        }
        onExit();
    }

    void onExit() {
        log.info("Manager Thread exit");
        taskMessage.close();
        zkClientApi.close();
        //DBUtils.close(null, conn);
        DbInceptApplication.close();
    }


    public void setBillingLineId(String billingLineId) {
        this.billingLineId = billingLineId;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public void setCluster(boolean isCluster, String cluster_id) {
        this.isCluster = isCluster;
        this.clusterId = cluster_id;
    }

    public void setiDebug(int debug) {
        common.setIDebug(debug);
    }

    public String getSystemDateStr() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(System.currentTimeMillis());
        return simpleDateFormat.format(date);
    }

    //插入日志，保存采集的信息
    boolean insertCollectionLog(Integer recordSize, Long lCurrTime, String iPerl, boolean state) {

        CollectionLog collectionLog = new CollectionLog()
                .setProcess_id(processId)
                .setFile_name(fileName)
                .setRecord_cnt(String.valueOf(recordSize))
                .setBak_sql(baseManager.getReadSqlBak())
                .setPerl(iPerl)
                .setState(String.valueOf(state))
                .setTarget_path(baseManager.getTprCollectionItem().getTarget_path())
                .setCreate_date(getCovertDateStr(lCurrTime));

        String inLogSql = "insert into collection_log (process_id,file_name,record_cnt,sql,perl,state,create_date,target_path) values (?,?,?,?,?,?,?,?)";
        Connection connTmp = null;
        boolean bRet = false;
        try {
            connTmp = DbPool.getConn();
            bRet = DbUtilComm.exeUpdate(connTmp, inLogSql, collectionLog.getProcess_id(), collectionLog.getFile_name(), collectionLog.getRecord_cnt(), collectionLog.getBak_sql(), collectionLog.getPerl(), collectionLog.getState(), collectionLog.getCreate_date(), collectionLog.getTarget_path());
        } finally {
            DbPool.close(connTmp);
        }
        return bRet;
    }

    private String getCovertDateStr(Long lTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(lTime);
    }


    // 获取可用任务数量
    private int getTaskCount() {
        int bTmpFlag = 0;
        int tmpNo = 0;
        String barchId = processId + "-" + getSystemDateStr();
        MsgBody.DbInceptBody sendBody = new MsgBody.DbInceptBody();
        sendBody.BATCH_ID = barchId;
        sendBody.HOST_ID = String.valueOf(host_id);
        sendBody.MODULE_ID = processId.substring(0, 4);
        sendBody.PROCESS_ID = processId;
        sendBody.FILE_NAME = null;
        sendBody.LOCAL_PATH = null;

        sendBody.RECORD_CNT = "0";
        sendBody.FIRST = baseManager.getTprCollection().getFlow();
        sendBody.CREATE_DATE = getSystemDateStr();
        sendBody.PERL = null;
        sendBody.MSG_TYPE = "REQUEST";
        sendBody.TYPE = baseManager.getTprCollectionItem().getType();
        sendBody.TASK_TYPE = MsgBody.TASK_TYPE.T_DBINCEPT.getType();

        sendQuestTaskCount2Dispatch(sendBody);

        while (true) {
            try {
                String tmp = taskMessage.pollDbIncept();
                if (tmp != null) {
                    MsgBody.DbInceptBody rBody = JSONObject.parseObject(tmp, MsgBody.DbInceptBody.class);
                    if (StringUtils.equals(barchId, rBody.BATCH_ID) && StringUtils.equals("RESPONSE", rBody.MSG_TYPE)) {
                        log.info("处理消息：" + tmp);
                        int count = Integer.parseInt(rBody.RECORD_CNT);
                        int perl = Integer.parseInt(rBody.PERL);
                        log.info("获取可用任务数,count:" + count + ",perl:" + perl);
                        if (perl > 0) {
                            bTmpFlag = 1;
                        }
                        break;
                    } else {
                        log.info("历史消息跳过：" + tmp);
                        continue;
                    }
                }

                tmpNo++;
                if (tmpNo == 30) {
                    bTmpFlag = -1;
                    log.info("获取可用任务数，等待调度回复。30秒超时");
                    break;
                }
                Thread.sleep(1000);
                log.info("获取可用任务数，等待调度回复。");
            } catch (InterruptedException e) {
                log.error(e);
            }
        }

        return bTmpFlag;
    }
}
