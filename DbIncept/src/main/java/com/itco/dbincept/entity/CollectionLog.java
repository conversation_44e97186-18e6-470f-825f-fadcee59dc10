package com.itco.dbincept.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CollectionLog {

    /**
     * 进程ID
     */
    private String process_id;

    /**
     * 文件名
     */
    private String file_name;

    /**
     * 读取数据的sql
     */
    private String bak_sql;

    /**
     * 读取到的记录数
     */
    private String record_cnt;

    /**
     * 性能
     */
    private String perl;

    /**
     * 缓存(文件）写入是否成功
     */
    private String state;

    /**
     * 创建时间
     */
    private String create_date;

    /**
     * 目标路径
     */
    private String target_path;
}
