package com.itco.dbincept.entity;

public class BatchTask {
    Long batch_id;
    String create_time;
    String operators_id;
    int billing_cycle_id;
    String state;
    int counts;
    String update_time;
    String mode;

    @Override
    public String toString() {
        return "BatchTask{" +
                "batch_id=" + batch_id +
                ", create_time='" + create_time + '\'' +
                ", operators_id='" + operators_id + '\'' +
                ", billing_cycle_id=" + billing_cycle_id +
                ", state='" + state + '\'' +
                ", counts=" + counts +
                ", update_time='" + update_time + '\'' +
                ", mode='" + mode + '\'' +
                '}';
    }

    public Long getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(Long batch_id) {
        this.batch_id = batch_id;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getOperators_id() {
        return operators_id;
    }

    public void setOperators_id(String operators_id) {
        this.operators_id = operators_id;
    }

    public int getBilling_cycle_id() {
        return billing_cycle_id;
    }

    public void setBilling_cycle_id(int billing_cycle_id) {
        this.billing_cycle_id = billing_cycle_id;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getCounts() {
        return counts;
    }

    public void setCounts(int counts) {
        this.counts = counts;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
}
