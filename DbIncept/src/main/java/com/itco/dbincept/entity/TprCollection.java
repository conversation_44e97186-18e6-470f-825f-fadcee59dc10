package com.itco.dbincept.entity;

public class TprCollection {
    int billing_line_id;
    String type;
    String scope_sql;
    int thread_sleep;
    int module_id;
    String flow;

    public int getBilling_line_id() {
        return billing_line_id;
    }

    public void setBilling_line_id(int billing_line_id) {
        this.billing_line_id = billing_line_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getScope_sql() {
        return scope_sql;
    }

    public void setScope_sql(String scope_sql) {
        this.scope_sql = scope_sql;
    }

    public int getThread_sleep() {
        return thread_sleep;
    }

    public void setThread_sleep(int thread_sleep) {
        this.thread_sleep = thread_sleep;
    }

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public String getFlow() {
        return flow;
    }

    public void setFlow(String flow) {
        this.flow = flow;
    }

    @Override
    public String toString() {
        return "TprCollection{" +
                "billing_line_id=" + billing_line_id +
                ", type='" + type + '\'' +
                ", thread_sleep=" + thread_sleep +
                ", module_id=" + module_id +
                '}';
    }
}
