package com.itco.dbincept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: MessageTask
 * @Description: 接收完文件发送消息给调度
 * <AUTHOR> @Date 2021/6/2
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class MessageTask {
    @J<PERSON><PERSON><PERSON>(name="BATCH_ID")
    String BATCH_ID;
    @J<PERSON><PERSON>ield(name="HOST_ID")
    String HOST_ID;
    @J<PERSON><PERSON>ield(name="PROCESS_ID")
    String PROCESS_ID;
    @J<PERSON>NField(name="MODULE_ID")
    String MODULE_ID;
    @J<PERSON><PERSON>ield(name="FILE_NAME")
    String FILE_NAME;
    @J<PERSON><PERSON>ield(name="LOCAL_PATH")
    String LOCAL_PATH;
    @J<PERSON>NField(name="RECORD_CNT")
    String RECORD_CNT;
    @<PERSON>SO<PERSON>ield(name="FIRST")
    String FIRST;
    @J<PERSON><PERSON>ield(name="TYPE")
    String TYPE;
    @JSONField(name="CREATE_DATE")
    String CREATE_DATE;
    @J<PERSON><PERSON><PERSON>(name="PERL")
    String PERL;

    @Override
    public String toString() {
        return "MessageTask{" +
                "BATCH_ID=" + BATCH_ID +
                ", HOST_ID=" + HOST_ID +
                ", PROCESS_ID=" + PROCESS_ID +
                ", FILE_NAME='" + FILE_NAME + '\'' +
                ", LOCAL_PATH='" + LOCAL_PATH + '\'' +
                ", RECORD_CNT=" + RECORD_CNT +
                ", FIRST='" + FIRST + '\'' +
                ", CREATE_DATE='" + CREATE_DATE + '\'' +
                ", PERL=" + PERL +
                '}';
    }
}
