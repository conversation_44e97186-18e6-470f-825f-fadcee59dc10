package com.itco.dbincept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: MessageProcess
 * @Description: 发送进程状态给掉地
 * <AUTHOR>
 * @Date 2021/6/2
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class MessageProcess {
    @J<PERSON><PERSON><PERSON>(name="TASK_TYPE")
    String TASK_TYPE;
    @J<PERSON><PERSON><PERSON>(name="PROCESS_ID")
    int PROCESS_ID;
    @<PERSON><PERSON><PERSON><PERSON>(name="STATUS")
    String STATUS;
    @J<PERSON><PERSON>ield(name="UPDATE_DATE")
    String UPDATE_DATE;
    @J<PERSON><PERSON>ield(name="VERSION")
    String VERSION;

    @Override
    public String toString() {
        return "MessageProcess{" +
                "TASK_TYPE='" + TASK_TYPE + '\'' +
                ", PROCESS_ID=" + PROCESS_ID +
                ", STATUS='" + STATUS + '\'' +
                ", UPDATE_DATE='" + UPDATE_DATE + '\'' +
                ", VERSION='" + VERSION + '\'' +
                '}';
    }
}
