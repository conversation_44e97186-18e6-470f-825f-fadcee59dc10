package com.itco.dbincept.entity;

public class TprCollectionItem {
    int id;
    int billing_line_id;
    String mode;
    String read_sql;
    String file_prefix;
    int file_cnt;
    String record_format;
    String target_path;
    String type;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getBilling_line_id() {
        return billing_line_id;
    }

    public void setBilling_line_id(int billing_line_id) {
        this.billing_line_id = billing_line_id;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getRead_sql() {
        return read_sql;
    }

    public void setRead_sql(String read_sql) {
        this.read_sql = read_sql;
    }

    public String getFile_prefix() {
        return file_prefix;
    }

    public void setFile_prefix(String file_prefix) {
        this.file_prefix = file_prefix;
    }

    public int getFile_cnt() {
        return file_cnt;
    }

    public void setFile_cnt(int file_cnt) {
        this.file_cnt = file_cnt;
    }

    public String getRecord_format() {
        return record_format;
    }

    public void setRecord_format(String record_format) {
        this.record_format = record_format;
    }

    public String getTarget_path() {
        return target_path;
    }

    public void setTarget_path(String target_path) {
        this.target_path = target_path;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "TprCollectionItem{" +
                "id=" + id +
                ", billing_line_id=" + billing_line_id +
                ", mode='" + mode + '\'' +
                ", file_prefix='" + file_prefix + '\'' +
                ", file_cnt=" + file_cnt +
                ", record_format='" + record_format + '\'' +
                ", target_path='" + target_path + '\'' +
                '}';
    }
}
