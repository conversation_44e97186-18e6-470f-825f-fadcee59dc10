package com.itco.dbincept.jdbc;

import com.itco.component.jdbc.DbPool;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.lang.reflect.Field;
import java.sql.*;
import java.util.*;

import static com.itco.component.jdbc.DbPool.close;


/**
 * @ClassName: DbUtilComm
 * @Description: 数据库操作类
 * <AUTHOR>
 * @Date 2021/6/2
 * @Version 1.0
 */
public class DbUtilComm {
    static Log log = LogFactory.getLog(DbUtilComm.class);


    /**
     * @Description:
     * @Param: [t, conn, sql, params]
     * @return: java.util.List<T>
     * @Author: Mr.<PERSON>
     * @Date: 2021/6/2
     */
    public static <T> List<T> queryList(Class<T> t, String sql, Object... params) {
        Connection connTmp = DbPool.getConn();
        if (connTmp == null) {
            log.error("连接数据库失败");
            return null;
        }

        List<T> list = new ArrayList<>();
        T obj = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            ps = connTmp.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            ResultSetMetaData rsmd = rs.getMetaData();
            // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
            Map<String, Object> map = new HashMap<>();
            // 遍历结果集
            while (rs.next()) {
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = rsmd.getColumnLabel(i + 1);
                    //获取列类型的int表示形式，以及列类型名称
                    //System.out.println("列名："+rsmd.getColumnName(i+1)+",列类型:"+rsmd.getColumnType(i + 1)+"----"+rsmd.getColumnTypeName(i+1));
                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname, value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    // 获取map集合键集(列名集合)
                    Set<String> columnNames = map.keySet();
                    // 创建对象
                    obj = t.newInstance();//new Student() //java.lang.Object
                    for (String column : columnNames) {
                        // 根据键获取值
                        Object value = map.get(column);

                        //当数据对象不为空时，才注入数据到属性中
                        if (Objects.nonNull(value)) {
                            // 获取属性对象
                            Field f = t.getDeclaredField(column);

                            // 设置属性为可访问状态
                            f.setAccessible(true);
                            // 为属性设置
                            f.set(obj, value);
                        }
                    }
                    list.add(obj);
                }
            }
            return list;
        } catch (SQLException e) {
            log.error("queryList SQLException:" + printStackTraceToString(e));
        } catch (InstantiationException e) {
            log.error("queryList InstantiationException:" + printStackTraceToString(e));
        } catch (IllegalAccessException e) {
            log.error("queryList IllegalAccessException:" + printStackTraceToString(e));
        } catch (NoSuchFieldException e) {
            log.error(sql);
            log.error("queryList NoSuchFieldException:" + printStackTraceToString(e));
        } catch (SecurityException e) {
            log.error("queryList SecurityException:" + printStackTraceToString(e));
        } finally {
            close(connTmp, ps, rs);
        }
        return null;
    }

    /**
     * @Description:
     * @Param: [conn, sql, cnt, list, params]
     * @return: int
     * @Author: Mr.Fuxingwang
     * @Date: 2021/6/2
     */
    public static int loadTableRecord(String sql, int cnt, List<Map<String, Object>> list, Object... params) {
        Connection connTmp = DbPool.getConn();
        if (connTmp == null) {
            log.error("连接数据库失败");
            return -1;
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        ResultSetMetaData rsmd = null;
        try {
            ps = connTmp.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            rs = ps.executeQuery();
            // 获取插叙结果集中的元数据(获取列类型，数量以及长度等信息)
            rsmd = rs.getMetaData();


            if (list == null) {
                list = new ArrayList<>();
            } else {
                list.clear();
            }

            // 遍历结果集
            while (rs.next()) {
                // 声明一个map集合，用于临时存储查询到的一条数据（key：列名；value：列值）
                Map<String, Object> map = new HashMap<>();
                // 防止缓存上一条数据
                map.clear();
                // 遍历所有的列
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名
                    String cname = rsmd.getColumnLabel(i + 1);

                    // 获取列值
                    Object value = rs.getObject(cname);
                    // 将列明与列值存储到map中
                    map.put(cname.toLowerCase(), value);
                }
                // 利用反射将map中的数据注入到Java对象中，并将对象存入集合
                if (!map.isEmpty()) {
                    list.add(map);
                }
            }
        } catch (SQLException e) {
            log.error("loadTableRecord SQLException:" + printStackTraceToString(e));
            return -1;
        } finally {
            close(connTmp, ps, rs);
        }

        return list.size();
    }

    /**
     * @Description: 执行update sql
     * @Param: [conn, sql, obj]
     * @return: boolean
     * @Author: Mr.Fuxingwang
     * @Date: 2021/6/3
     */
    public static boolean exeUpdate(String sql, Object... obj) {
        Connection connTmp = DbPool.getConn();
        if (connTmp == null) {
            log.error("连接数据库失败");
            return false;
        }
        PreparedStatement ps = null;
        boolean bRet = false;
        try {
            ps = connTmp.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            bRet = ps.executeUpdate() > 0;
            ps.close();
            return bRet;
        } catch (SQLException e) {
            log.error("exeUpdate :" + printStackTraceToString(e));
        } finally {
            close(connTmp, ps, null);
        }
        return false;
    }

    /**
     * 封装通用的更新操作，对所有更新(INSERT,UPDATE，DELETE)有关的操作都能通过该方法实现
     *
     * @param sql
     * @return
     */
    public static boolean exeUpdate(Connection conn, String sql, Object... obj) {
        PreparedStatement ps = null;
        boolean bRet = false;
        try {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            bRet = ps.executeUpdate() > 0;
            ps.close();
            return bRet;
        } catch (SQLException e) {
            log.error("exeUpdate SQLException:" + printStackTraceToString(e));
        } finally {
            close(ps, null);
        }
        return false;
    }

    public static boolean execute(String sql, Object... obj) {
        Connection connTmp = DbPool.getConn();
        if (connTmp == null) {
            log.error("连接数据库失败");
            return false;
        }
        PreparedStatement ps = null;
        boolean bRet = false;
        try {
            ps = connTmp.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            ps.execute();
            ps.close();
            return true;
        } catch (SQLException e) {
            log.error("exeUpdate execute:" + printStackTraceToString(e));
        } finally {
            close(connTmp, ps, null);
        }
        return false;
    }

    public static String query(String sql, Object... obj) {
        Connection connTmp = DbPool.getConn();
        if (connTmp == null) {
            log.error("连接数据库失败");
            return null;
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sRet = new String("");
        try {
            ps = connTmp.prepareStatement(sql);
            for (int i = 0; i < obj.length; i++) {
                ps.setObject(i + 1, obj[i]);
            }
            rs = ps.executeQuery();
            if (rs.next()) {
                sRet = rs.getObject(1).toString();
            }
            return sRet;
        } catch (SQLException e) {
            log.error("query execute:" + printStackTraceToString(e));
        } finally {
            close(connTmp, ps, rs);
        }
        return null;
    }

    public static String printStackTraceToString(Exception e) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(byteArrayOutputStream));
        if (byteArrayOutputStream != null) {
            String exception = byteArrayOutputStream.toString();
            /*if (exception.length() <= 800) {
                return exception;
            } else {
                return exception.substring(0, 800);
            }*/
            return exception;
        }
        return null;
    }
}
