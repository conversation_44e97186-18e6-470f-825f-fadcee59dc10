package com.itco.dbincept;

import com.itco.dbincept.Thread.Manager;
import com.itco.framework.Version;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.PreDestroy;

@SpringBootApplication
public class DbInceptApplication {
    static Log log = LogFactory.getLog(DbInceptApplication.class);
    static ConfigurableApplicationContext context;

    static String stModuleCode = "DbIncept";
    static String stBillingLineId = null;
    static String stMode = null;
    static boolean isCluster = false;
    static String cluster = null;
    static int debug = 0;

    public static boolean startNormal() {
        Manager manager = new Manager();
        manager.setBillingLineId(stBillingLineId);
        manager.setModuleCode(stModuleCode);
        manager.setCluster(isCluster, cluster);
        manager.setiDebug(debug);

        // 初始化
        if (!manager.init()) {
            log.error("managerThread.init() 失败");
            return false;
        }

        // 创建线程启动
        Thread thread = new Thread(manager);
        thread.start();
        return true;
    }

    // 退出之前需要关闭容器
    public static synchronized void close() {
        if (context != null) {
            int exit = SpringApplication.exit(context);
            context = null;
            System.exit(exit);
        }

        log.info("close(context) ");
    }

    public static void help() {
        log.info("接收: DbIncept.sh start 11");
        log.info("试算: tDbIncept.sh start [81/83]");
    }

    @PreDestroy
    public void destroy() {
        Version.print();
        Version.destroy();
    }

    /*
     * DbIncept_code_2022-01-10 11:00 -> Collect模式，如果接收的话单少于TPR_COLLECTION_ITEM.FILE_CNT，则休眠，否则继续接收
     *
     */
    public static void main(String[] args) {
        if (Version.print(args, "DbIncept_code_2023-04-07 11:00")) {
            return;
        }

        for (String para : args) {
            if ("-f".equals(para.substring(0, 2))) {    //生产线
                stBillingLineId = para.substring(2);
            } else if ("-s".equals(para.substring(0, 2))) {   //单点 or 集群
                isCluster = true;
                if (para.length() > 2) {
                    cluster = para.substring(2);
                }
            } else if ("trial".equals(para)) {
                stModuleCode = "tDbIncept";
                stMode = para;
            } else if ("pre".equals(para)) {
                stModuleCode = "pDbIncept";
                stMode = para;
            } else if ("-d".equals(para)) {
                debug = 1;
                if (para.length() > 2) {
                    debug = Integer.parseInt(para.substring(2));
                }
            }
        }

        String moduleCode = System.getenv("MODULE_NAME");
        String billingLineId = System.getenv("BILLING_LINE_ID");
        log.info("billingLineId:" + billingLineId);

        if (moduleCode != null && !moduleCode.equals("")) {
            stModuleCode = moduleCode;
        }
        if (billingLineId != null && !billingLineId.equals("")) {
            stBillingLineId = billingLineId;
        }

        if (stBillingLineId == null) {
            help();
            return;
        }

        log.info("stBillingLineId:" + stBillingLineId);
        context = SpringApplication.run(DbInceptApplication.class, args);
        log.info("module_name:" + stModuleCode);

        if (!DbInceptApplication.startNormal()) {
            log.error("start " + stBillingLineId + " 运行失败");
            close();
        }
        log.info("初始化成功");
    }

}
