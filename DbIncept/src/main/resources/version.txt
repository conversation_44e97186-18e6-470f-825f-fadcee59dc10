--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2024-12-10 11:00
【修订日期】：2024-12-10
【修订人员】：zhangshf
【需求单号】：无
【SVN提交前版本】：CRM-devp-160
【实现功能】：
  1、试算接收 更新断点表增加trial模式
  2、去除要客定制化实时接收功能

【变更文件】：
修改：src/main/java/com/itco/dbincept/Thread/BatchManager.java
修改：src/main/java/com/itco/dbincept/Thread/Manager.java
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-04-07 11:00
【修订日期】：2023-04-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：55056
【实现功能】：
1、接收模块实现高可用功能的集群模式，通过配置文件DbIncept.properties添加cluster=11,12,21配置。
2、在DbIncept.sh启动参数中加入-s变成集群模式，没有加-s属于单点模式；
单点模式：所有的逻辑还是和之前一样，每个接收进程只负责自己billing_line_id的接收任务。
集群模式：接收程序会去轮询cluster配置的billing_line_id，哪个有待接收的任务，就处理哪个。
3、增加了，离线模式，开发人员本地调试的功能。
4、添加了详细日志，默认是不打印详细日志。
5、如果话单流转方式为"FILE"，则积压的话单是按照每个台主机单独计算。其他情况是集群共享积压话单。

【变更文件】：
修改：src/main/java/com/itco/dbincept/DbInceptApplication.java
修改：src/main/java/com/itco/dbincept/Thread/BatchManager.java
修改：src/main/java/com/itco/dbincept/Thread/BreakManager.java
修改：src/main/java/com/itco/dbincept/Thread/CollectManager.java
修改：src/main/java/com/itco/dbincept/Thread/impl/BaseManager.java
修改：src/main/java/com/itco/dbincept/Thread/Manager.java
修改：src/main/java/com/itco/dbincept/Thread/RollbackManager.java
修改：src/main/resources/application.properties
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  集群模式配置步骤：
  1、在tp_config_center表新增DbIncept.properties配置。
  2、在应用主机的config目录下新建文件DbIncept.properties配置文件，并加入cluster=11,12
  3、修改应用主机javabin目录下的DbIncept.sh进程启动脚本，启动命令加入 -s
  nohup java -Xms1024m -Xmx1024m -jar -Dfile.encoding=UTF-8 $JAR_NAME $LOG_ID $MODE -s > $JAR_LOG 2>&1 &
  4、执行upload.sh
--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-03-17 11:00
【修订日期】：2023-03-17
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54736
【实现功能】：
1、发送给调度的消息LOCAL_PATH路径不带绝对路径，只有配置表里面的相对路径。

【变更文件】：
修改：src/main/java/com/itco/dbincept/DbInceptApplication.java
修改：src/main/java/com/itco/dbincept/Thread/Manager.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-02-21 11:00
【修订日期】：2023-02-21
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54659
【实现功能】：
1、DbInceptBody、StandardBody、RollbackBody三个消息体添加TASK_NAME字段；
2、调度接收到的任务，保存任务名称到任务表里。
3、zookeeper连接失败，重新连接错了修改调整，涉及调度,DbIncept,Collection,统一任务框架。

【变更文件】：
修改：src/main/java/com/itco/dbincept/Thread/impl/BaseManager.java
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-02-14 11:00
【修订日期】：2023-02-14
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54590
【实现功能】：
1、zookeeper连接异常 。。。，不进行话单采集，每6秒进行一次判断调度是不是存在
2、调度、接收、主流程模块出现zookeeper连接异常时，输出告警日志到tg_alert_log表
3、zookeeper的session连续失效10次，触发重新初始化zookeeper接口，重新注册
4、zookeeper的CONNECTION_LOSS、CONNECTION_REFUSED的异常，等待自动重连上

【变更文件】：
修改：src/main/java/com/itco/dbincept/DbInceptApplication.java
修改：src/main/java/com/itco/dbincept/Thread/impl/BaseManager.java
修改：src/main/java/com/itco/dbincept/Thread/Manager.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-02-07 11:00
【修订日期】：2023-02-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54542
【实现功能】：
1、if (recordList.size() == collectionItem.getFile_cnt() || (iFor * collectionItem.getFile_cnt() + recordList.size()) == baseManager.getRecordList().size())
循环结束后，不需要在判断是否有未发送的话单。

【变更文件】：
修改：src/main/java/com/itco/dbincept/Thread/Manager.java

【修订要点】：
  1、无

【注意事项】：
  1、无
 --------------------------------------------------------------------------------------------------
【发布版本】：DbIncept_code_2023-01-17 11:00
【修订日期】：2023-01-17
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54358
【实现功能】：
接收模块，支持回退回收rollback模式。
1、tpr_collection表type字段rollback。
2、tpr_collection_item表mode字段rollback。

【变更文件】： 
修改：src/main/java/com/itco/dbincept/DbInceptApplication.java
修改：src/main/java/com/itco/dbincept/jdbc/DbUtilComm.java
修改：src/main/java/com/itco/dbincept/Thread/BatchManager.java
修改：src/main/java/com/itco/dbincept/Thread/CollectManager.java
修改：src/main/java/com/itco/dbincept/Thread/impl/BaseManager.java
修改：src/main/java/com/itco/dbincept/Thread/Manager.java
新增：src/main/java/com/itco/dbincept/entity/RollbackTask.java
新增：src/main/java/com/itco/dbincept/Thread/RollbackManager.java
新增：src/main/resources/version.txt

【修订要点】：
  1、select batch_id,task_id,to_char(insert_date,'yyyy-MM-dd HH:mm:ss') as insert_date,state,task_manual_cnt from data.recycle_rollback_task where state = 'D06' order by batch_id
  2、SELECT * from data.ticket_source_91 where batch_id=?

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------