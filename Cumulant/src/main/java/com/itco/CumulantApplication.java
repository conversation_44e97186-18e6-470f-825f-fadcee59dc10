package com.itco;

import com.itco.framework.Factory;
import com.itco.framework.Version;
import com.itco.framework.process.impl.SettleProcessManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import javax.annotation.PreDestroy;
import java.text.SimpleDateFormat;
import java.util.Date;

@SpringBootApplication
public class CumulantApplication {
    static Log log = LogFactory.getLog(CumulantApplication.class);
    static int debug = 0;
    static int elemet = 0;
    static String calcClassName = null;
    static String moduleCode = null;
    static String billingLineId = null;

    @Bean
    public boolean run() {
        log.info("Cumulant run start");

        // 消息接收和加载落地文件框架类实例化
        SettleProcessManager settleProcessManager = new SettleProcessManager();
        settleProcessManager.setDebug(debug);
        settleProcessManager.setIsElement(elemet);
        settleProcessManager.setCalcClassName(calcClassName);
        settleProcessManager.setModuleCode(moduleCode);
        settleProcessManager.setBillingLineId(billingLineId);

        if (!settleProcessManager.Init()) {
            log.info("settleProcessManager init false");
            Factory.setbWhileFlag(true);
            return false;
        }

        Thread recordThread = new Thread(settleProcessManager);
        recordThread.start();

        log.info("Cumulant run close");
        return true;
    }


    public static synchronized String getSystemDateStr() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String dateStr = df.format(new Date(System.currentTimeMillis()));// new Date()为获取当前系统时间，也可使用当前时间戳

        return dateStr;
    }

    @PreDestroy
    public void destroy() {
        Version.print();
        Version.destroy();
    }

    public static void main(String[] args) {
        if (Version.print(args, "Cumulant_code_2023-06-29 11:00")) {
            return;
        }

        String sModuleCode = new String("Cumulant");
        String sBillinglineId = null;

        for (String para : args) {
            if (para.startsWith("-f")) {
                sBillinglineId = new String(para.substring(2));
            } else if (para.startsWith("-d")) {
                debug = 1;
                if (para.length() > 2) {
                    debug = Integer.parseInt(para.substring(2));
                }
            } else if (para.startsWith("-e")) {
                elemet = 1;
            } else if (para.equals("trial")) {
                sModuleCode = "tCumulant";
            } else if (para.equals("pre")) {
                sModuleCode = "pCumulant";
            }
        }

        String envModuleCode = System.getenv("MODULE_NAME");
        String envBillingLineId = System.getenv("BILLING_LINE_ID");

        if (envModuleCode != null && !envModuleCode.equals("")) {
            sModuleCode = envModuleCode;
        }
        if (envBillingLineId != null && !envBillingLineId.equals("")) {
            sBillinglineId = envBillingLineId;
        }

        calcClassName = "com.itco.module.CalcRecordCumulant";
        moduleCode = sModuleCode;
        billingLineId = sBillinglineId;

        ConfigurableApplicationContext context = SpringApplication.run(CumulantApplication.class, args);
        Factory.setContext(context);
        if (Factory.isbWhileFlag()) {
            // 初始化失败的情况退出
            Factory.close();
        }
        log.info("初始化成功");
    }

}
