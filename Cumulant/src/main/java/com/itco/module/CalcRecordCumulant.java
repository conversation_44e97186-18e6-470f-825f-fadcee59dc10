package com.itco.module;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.framework.calcrecord.CalcRecordManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Map;
import java.util.Properties;

public class CalcRecordCumulant extends CalcRecordManager {
    static Log log = LogFactory.getLog(CalcRecordCumulant.class);

    /*整个模块初始化
     * 处理线程启动初始化操作，只有程序启动时执行一次。配置了多少个子线程就执行几次。
     * */
    @Override
    public boolean OnInit() {
        log.info("OnInit start ,startMode:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());
        String path = "Cumulant.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);

        log.info("OnInit close");
        return true;
    }


    @Override
    public boolean OnInitTrial() {
        log.info("OnInit start ,moduleType:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());
        String path = "tCumulant.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);

        log.info("OnInitTrial close");
        return true;
    }

    @Override
    public boolean OnInitPre() {
        log.info("OnInit start ,moduleType:" + getStartMode() + ",isbInitFlag()" + isbInitFlag());
        String path = "pCumulant.properties";
        Properties props = CalcRecordManager.getZkClientApi().getPropertiesFromZK(path);

        log.info("OnInitPre close");
        return true;
    }


    /*     入参：
     inRecord：输入话单
     outRecord：输出话单
     msg：处理异常时，填写异常信息
     返回值，是否处理成功*/
    public boolean OnTask(String inRecord) {
        String outRecord = inRecord;
        JSONObject record = null;
        try {
            //累积量处理
            record = JSON.parseObject(inRecord);

        } catch (Exception e) {
            String exceptionString = printStackTraceToString(e);
            log.error(exceptionString);
            log.error("OnTask()执行失败，执行退出前操作失败！" + e.toString());
        }

        setOutRecord(record == null ? inRecord : record.toJSONString());
        return true;
    }


    @Override
    public boolean OnOver(Map<String, List<List<KeyValue>>> dataOver) {
        return true;
    }

    /*线程初始化函数
     * 线程每次处理文件之前会执行此函数
     * */
    @Override
    public boolean OnChildInit() {
        return true;
    }

    @Override
    public void reset() {

    }

    /*线程结束函数
     * 线程处理完所有的记录之后，会处理此函数
     * */
    @Override
    public boolean OnChildOver() {
        return true;
    }

    @Override
    public Map<Integer, FunctionPerl> getFunctionPerl() {
        return null;
    }

    /*模块退出操作
     * 整个模块退出时，需要释放的空间这里操作。
     * */
    @Override
    public boolean OnExit() {
        return true;
    }
}
