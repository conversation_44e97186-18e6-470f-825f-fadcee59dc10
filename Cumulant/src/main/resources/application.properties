#log å¼å³
logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
#ä¸æå°spring boot å¯å¨çæ¥å
logging.level.org.springframework.boot.autoconfigure=WARN

#è¾åºæ¥å¿æä»¶
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger- %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.file.name=${LOG_HOME:~/javalog}/BlockSett.log
logging.file.max-size=50MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
logging.file.max-history=30
logging.file.total-size-cap=100MB

#è°è¯ä½¿ç¨å¼å³ workFlag:offline ç¦»çº¿æ¨¡å¼  onlineå¶ä»æåµï¼å¨çº¿è°åº¦æ¨¡å¼
workFlag=online
workDir=D:\\DCOOS_Git\\test\\Rating
workFileName=NORMAL_SERVICE_CONROL_60_6000

#åå¸çæ¬
version=Cumulant_deploy_2023-06-29 11:00