package com.itco.CalcFunction;


import com.itco.RuleFunction.RulePricingPlanTpss_591;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.Result;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.function.*;
import com.itco.framework.Factory;
import com.itco.rulefunction.ctgcache.TableService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.itco.commen.PubDef.*;

public class CalcFunctionLocal {
    static Log log = LogFactory.getLog(CalcFunctionLocal.class);

    static boolean bInitFlag = false;
    static Common common = null;
    static ZkClientApi zkClientApi = null;
    static Map<Integer, TprFunction> mTprFunction = new HashMap<>();
    static Map<Integer, List<TprFunctionCondition>> mTprFunctionCondition = new HashMap<>();
    static Map<Long, RefValue> mRefValue = new HashMap<>();
    static Map<Integer, TprResourceAttr> mTprAttr = new HashMap<>();
    static Map<String, TprResourceAttr> mTprAttrName = new HashMap<>();
    static Map<Integer, FunctionPerl> mPerl = new HashMap<>();

    static Map<Integer, CalcFunctionLocal> rulePricingPlanMap = new HashMap<>();
    static String rule_function_version = "rule-tpss_591_2022-12-30 11:00"; //因子库版本

    RulePricingPlanTpss_591 rulePricingPlanTpss591 = null;
    int iThreadNo = 0;
    int error_type = 0;
    String error_msg = null;

    SimpleDateFormat tFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat rFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    CalcFunctionLocal(int i) {
        iThreadNo = i;
        // 福建 TPSS
        rulePricingPlanTpss591 = new RulePricingPlanTpss_591();
        rulePricingPlanTpss591.setiThreadNo(i);
    }

    public static CalcFunctionLocal getInstance() {
        return getInstance(0);
    }

    public static CalcFunctionLocal getInstance(int i) {
        if (null == rulePricingPlanMap.get(i)) {
            CalcFunctionLocal calcFunctionLocal = new CalcFunctionLocal(i);
            rulePricingPlanMap.put(i, calcFunctionLocal);
        }

        return rulePricingPlanMap.get(i);
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        TableService.setZkClientApi(zkClientApi);
        RulePricingPlanTpss_591.setZkClientApi(zkClientApi);
        CalcFunctionLocal.zkClientApi = zkClientApi;
    }

    public static void setCommon(Common common) {
        TableService.setCommon(common);
        RulePricingPlanTpss_591.setCommon(common);
        CalcFunctionLocal.common = common;
    }

    public static void setbInitFlag(boolean bInitFlag) {
        CalcFunctionLocal.bInitFlag = bInitFlag;
    }

    public static boolean initCfg() {
        if (!bInitFlag) {
            common.setRule_function_version(rule_function_version);
            DbPool.setZkClientApi(zkClientApi);
            Connection connTmp = DbPool.getConn();
            if (null == connTmp) {
                return false;
            }
            //从zk中加载
            List<TprResourceAttr> attrLists = zkClientApi.getTableFromZK("tpr_resource_attr", TprResourceAttr.class);
            List<TprFunction> listFunction = zkClientApi.getTableFromZK("tpr_function", TprFunction.class);
            List<TprFunctionCondition> conditionList = zkClientApi.getTableFromZK("tpr_function_condition", TprFunctionCondition.class);
            List<RefValue> listRef = zkClientApi.getTableFromZK("ref_value", RefValue.class);

            //从数据库中加载
//          List<TprResourceAttr> attrLists = DBUtils.queryList(TprResourceAttr.class, connTmp, "select attr_id,lower(en_name) en_name,ch_name,substr(data_type,2) data_type,data_length,data_precision,array_size,comments from tpr_resource_attr");
//          List<TprFunction> listFunction = DBUtils.queryList(TprFunction.class, connTmp, "select FUNCTION_ID,EN_NAME,CH_NAME,FUNCTION_TYPE,RESULT_TYPE,PARAM_NUM,SRC_PATH,LIB_PATH,COMMENTS,MODULE_ID,DB_PKG_NAME,LATN_ID from tpr_function");
//          List<TprFunctionCondition> conditionList = DBUtils.queryList(TprFunctionCondition.class, connTmp, "select FUNCTION_ID,PARAM_ORDER,PARAM_TYPE,ATTR_ID,ATTR_NAME,COMMENTS from tpr_function_condition order by function_id,param_type,param_order");
//          List<RefValue> listRef = DBUtils.queryList(RefValue.class, connTmp, "select a.ref_value_id,a.ref_value_type,a.ref_value_name,a.value_type,a.pricing_ref_object_id,a.value_string,b.ref_object_name,b.property_type,b.property_define_id,b.extern_property_string rule_id from pricing_ref_value a,pricing_ref_object b where ref_value_type=3 and a.pricing_ref_object_id=b.pricing_ref_object_id and b.property_type=4");

            DbPool.close(connTmp);

            log.info("加载属性字段数量:" + attrLists.size());
            log.info("加载因子数量:" + listFunction.size());
            log.info("加载因子入参数量:" + conditionList.size());
            log.info("加载参考对象数量:" + listRef.size());

            mTprFunction = listFunction.stream().collect(Collectors.toMap(TprFunction::getFunction_id, a -> a, (k1, k2) -> k1));
            mTprFunctionCondition = conditionList.stream().collect(Collectors.groupingBy(TprFunctionCondition::getFunction_id));
            mTprAttr = attrLists.stream().collect(Collectors.toMap(TprResourceAttr::getAttr_id, a -> a, (k1, k2) -> k1));
            mTprAttrName = attrLists.stream().collect(Collectors.toMap(TprResourceAttr::getEn_name, a -> a, (k1, k2) -> k1));
            mRefValue = listRef.stream().collect(Collectors.toMap(RefValue::getRef_value_id, a -> a, (k1, k2) -> k1));

            bInitFlag = true;
        }
        return true;
    }

    // 预处理2的函数调用
    public Result calcFunction(String functionStr, Map<String, Object> record) {
        return calcFunction(functionStr, 0L, record);
    }

    // 预处理2的函数调用
    public Result calcFunction(String functionStr, Long lRefObjectId, Map<String, Object> record) {
        Map<String, Object> result = new HashMap<>();
        Result r = new Result(false);
        boolean b = calcFunction(functionStr, lRefObjectId, record, result);
        r.setBRet(b);
        if (!b) {
            r.setError_msg(error_msg + ";" + result.get("error_msg"));
        } else {
            r.put("VALUE", result.get("VALUE"));
            r.put("VALUE_TYPE", result.get("VALUE_TYPE"));
        }
        return r;
    }

    public boolean calcFunction(String functionStr, Long lRefObjectId, Map<String, Object> record, Map<String, Object> result) {
        boolean bRet = false;
        List<String> param = new ArrayList<>();
        //  获取函数标识
        int function_id = 0;
        if (functionStr == null) {
            return false;
        } else if (functionStr.indexOf("(") > 0) {
            function_id = Integer.parseInt(functionStr.substring(0, functionStr.indexOf("(")));
        } else {
            try {
                function_id = Integer.parseInt(functionStr);
            } catch (Exception e) {
                log.error("输入的函数不合法:" + functionStr);
                return false;
            }
        }

        //  获取函数属性
        TprFunction tprFunction = mTprFunction.get(function_id);
        if (tprFunction == null) {
            log.error("TPR_FUNCTION表没有:" + function_id);
            return false;
        }

        //  获取预处理函数参数
        if (functionStr.indexOf("(") > 0) {
            String str = functionStr.substring(functionStr.indexOf("(") + 1, functionStr.indexOf(")"));
            String[] tmp = str.split(",");

            for (int i = 0; i < tmp.length; i++) {
                if (tmp[i] == null || tmp[i].equals("")) {

                } else if (tmp[i].charAt(0) == '$' && tprFunction.getFunction_type().equals("9FB")) {
                    TprResourceAttr attr = mTprAttr.get(Integer.parseInt(tmp[i].substring(1)));
                    param.add(record.get(attr.getEn_name()).toString());
                } else if (tmp[i].charAt(0) == 'C') {
                    param.add(tmp[i].substring(1));
                } else {
                    param.add(tmp[i]);
                }
            }
        }

        long lStartTime = getMicroSecond();
        if (tprFunction.getFunction_type().equals("9FB")) {
            //通用因子
            try {
                bRet = calcFuntion_Common(tprFunction, param, result);
            } catch (Exception e) {
                error_msg = "调用通用因子：" + tprFunction.getFunction_id() + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e.getMessage();
                Factory.setTicketTypeAbn(record, ERROR_TYPE_GENERAL_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else if (tprFunction.getFunction_type().equals("9FA") || tprFunction.getFunction_type().equals("9FC")) {
            //业务因子
            try {
                bRet = calcFunction_Service(tprFunction, lRefObjectId, record, param, result);
            } catch (Exception e) {
                log.error(Factory.printStackTraceToString(e));
                error_msg = "调用业务因子：" + tprFunction.getFunction_id() + ",lRefObjectId:" + lRefObjectId + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e;
                Factory.setTicketTypeAbn(record, ERROR_TYPE_SERVICE_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else if (tprFunction.getFunction_type().equals("9FD")) {
            //自定义因子
            try {
                bRet = calcFunction_DbFunction(tprFunction, lRefObjectId, record, param, result);
            } catch (Exception e) {
                log.error(Factory.printStackTraceToString(e));
                error_msg = "调用自定义因子：" + tprFunction.getFunction_id() + ",lRefObjectId:" + lRefObjectId + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e;
                Factory.setTicketTypeAbn(record, ERROR_TYPE_CUSTOMIZE_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else {
            log.error("无法识别的函数类型:" + tprFunction.getFunction_type());
            return false;
        }

        long lDuration = getMicroSecond() - lStartTime;
        synchronized (CalcFunctionLocal.class) {
            FunctionPerl functionPerl = mPerl.get(tprFunction.getFunction_id());
            if (functionPerl == null) {
                functionPerl = new FunctionPerl(tprFunction.getFunction_id(), tprFunction.getEn_name(), tprFunction.getCh_name());
                mPerl.put(tprFunction.getFunction_id(), functionPerl);
            }

            if (!bRet) {
                functionPerl.setFailPlusCntPerl(1, lDuration);
            }
            functionPerl.setPlusCntPerl(1, lDuration);
        }

        return bRet;
    }

    // 新版本的通过参考值标识调用函数
    public Result calcRefValue(Long refValueId, Map<String, Object> record) {
        Map<String, Object> result = new HashMap<>();
        Result r = new Result(false);
        boolean b = calcRefValue(refValueId, record, result);
        r.setBRet(b);
        if (!b) {
            r.setError_msg(error_msg + ";" + result.get("error_msg"));
        } else {
            r.put("VALUE", result.get("VALUE"));
            r.put("VALUE_TYPE", result.get("VALUE_TYPE"));
        }
        return r;
    }

    // 新版本的通过参考值标识调用函数
    public boolean calcRefValue(Long refValueId, Map<String, Object> record, Map<String, Object> result) {
        boolean bRet = false;
        List<String> param = new ArrayList<>();
        RefValue refValue = mRefValue.get(refValueId);
        if (refValue == null) {
            log.error("REF_VALUE表没有:" + refValueId);
            return false;
        }

        TprFunction tprFunction = mTprFunction.get(Integer.parseInt(refValue.getRule_id()));
        if (tprFunction == null) {
            log.error("TPR_FUNCTION表没有:" + refValue.getRule_id());
            return false;
        }

        long lStartTime = getMicroSecond();
        Long lRefObjectId = refValue.getPricing_ref_object_id();
        if (tprFunction.getFunction_type().equals("9FB")) {
            //通用因子
            try {
                bRet = calcFuntion_Common(tprFunction, param, result);
            } catch (Exception e) {
                error_msg = "调用通用因子：" + tprFunction.getFunction_id() + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e.getMessage();
                Factory.setTicketTypeAbn(record, ERROR_TYPE_GENERAL_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else if (tprFunction.getFunction_type().equals("9FA") || tprFunction.getFunction_type().equals("9FC")) {
            //业务因子
            try {
                bRet = calcFunction_Service(tprFunction, lRefObjectId, record, param, result);
            } catch (Exception e) {
                error_msg = "调用业务因子：" + tprFunction.getFunction_id() + ",lRefObjectId:" + lRefObjectId + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e.getMessage();
                Factory.setTicketTypeAbn(record, ERROR_TYPE_SERVICE_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else if (tprFunction.getFunction_type().equals("9FD")) {
            //自定义因子
            try {
                bRet = calcFunction_DbFunction(tprFunction, lRefObjectId, record, param, result);
            } catch (Exception e) {
                e.printStackTrace();
                error_msg = "调用自定义因子：" + tprFunction.getFunction_id() + ",lRefObjectId:" + lRefObjectId + "," + tprFunction.getEn_name() + " 失败，异常信息：" + e.getMessage();
                Factory.setTicketTypeAbn(record, ERROR_TYPE_CUSTOMIZE_FUNCTION_FAILE, error_msg);
                log.error(error_msg);
                bRet = false;
            }
        } else {
            log.error("无法识别的函数类型:" + tprFunction.getFunction_type());
            return false;
        }

        long lendTime = getMicroSecond() - lStartTime;
        FunctionPerl functionPerl = mPerl.get(tprFunction.getFunction_id());
        if (functionPerl == null) {
            functionPerl = new FunctionPerl(tprFunction.getFunction_id(), tprFunction.getEn_name(), tprFunction.getCh_name());
            mPerl.put(tprFunction.getFunction_id(), functionPerl);
        }

        if (bRet) {
            functionPerl.setPlusCntPerl(1, lendTime);
        } else {
            functionPerl.setFailPlusCntPerl(1, lendTime);
        }
        return bRet;
    }

    // 预处理通用函数
    public boolean calcFuntion_Common(TprFunction tprFunction, List<String> param, Map<String, Object> result) {
        boolean bRet = false;
        switch (tprFunction.getFunction_id()) {
            case 40100:
                bRet = rulePricingPlanTpss591.GetFunctionResult(result);
                break;
            case 501000:
                bRet = rulePricingPlanTpss591.Rule_substr(param.get(0), Integer.parseInt(param.get(1)), Integer.parseInt(param.get(2)), result);
                break;
            case 501001:
                bRet = rulePricingPlanTpss591.Rule_strlen(param.get(0), result);
                break;
            case 501002:
                bRet = rulePricingPlanTpss591.Rule_makeUpper(param.get(0), result);
                break;
            case 501003:
                bRet = rulePricingPlanTpss591.Rule_isdigit(param.get(0), result);
                break;
            case 501010:
                bRet = rulePricingPlanTpss591.Rule_IntegerToString(Integer.parseInt(param.get(0)), result);
                break;
            case 501011:
                bRet = rulePricingPlanTpss591.Rule_StringToLong(param.get(0), result);
                break;
            case 501012:
                bRet = rulePricingPlanTpss591.Rule_CTimeToClong(param.get(0), result);
                break;
            case 501013:
                bRet = rulePricingPlanTpss591.Rule_Stringisnull(param.get(0), result);
                break;
            case 501015:
                bRet = rulePricingPlanTpss591.Rule_StringToInteger(param.get(0), result);
                break;
            default:
                String msg = "找不到函数ID:" + tprFunction.getFunction_id() + "," + tprFunction.getEn_name();
                result.put("error_msg", msg);
                break;
        }

        return bRet;
    }

    public boolean calcFunction_Service(TprFunction tprFunction, Long lPringRefObjectId, Map<String, Object> record, List<String> param, Map<String, Object> result) {
        boolean bRet = false;
        switch (tprFunction.getFunction_id()) {
            // 业务函数
            case 40212:
                bRet = rulePricingPlanTpss591.Rule_Preproc2_WriteDealCycle(record, lPringRefObjectId, param, result);
                break;
            case 40214:
                bRet = rulePricingPlanTpss591.Rule_Preproc2_WriteBillCycle(record, lPringRefObjectId, param, result);
                break;
            case 40215:
                bRet = rulePricingPlanTpss591.Rule_PreProc2_GetProd(record, lPringRefObjectId, param, result);
                break;
            case 40216:
                bRet = rulePricingPlanTpss591.Rule_PreProc2_GetProd_IsMixMeal(record, lPringRefObjectId, param, result);
                break;
            case 40217:
                bRet = rulePricingPlanTpss591.Rule_Preproc2_WriteBillCycle2(record, lPringRefObjectId, param, result);
                break;
            case 500004:
                bRet = rulePricingPlanTpss591.Rule_PreProc2_GetEventType(record, lPringRefObjectId, param, result);
                break;
            default:
                String msg = "找不到函数ID:" + tprFunction.getFunction_id() + "," + tprFunction.getEn_name();
                result.put("error_msg", msg);
                break;
        }
        return bRet;
    }

    public static List<String> analysisResult(String str) {
        List<String> list = new ArrayList<>();
        if (str == null || str.equals("")) {
            list.add(str);
            return list;
        }
        //System.out.println(str);

        int start = 0, count = 1;
        boolean flag = false;
        for (int j = 0; j < str.length(); j++) {
            if (str.charAt(j) == '"' && str.length() == j + 1 && count > 1) {
                list.add(str.substring(start + 1, start + count - 1));
                start += count;
                count = 0;
                flag = false;
            } else if (str.charAt(j) == '"' && count == 1) {
                flag = true;
            } else if (str.charAt(j) == ',') {
                if (flag && str.charAt(j - 1) == '"') {
                    list.add(str.substring(start + 1, start + count - 2));
                    start += count;
                    count = 0;
                    flag = false;
                } else if (!flag) {
                    if (count == 1) {
                        list.add(null);
                    } else {
                        list.add(str.substring(start, start + count - 1));
                    }
                    start += count;
                    count = 0;
                }
            }
            count++;
        }
        if (start < str.length()) {
            list.add(str.substring(start));
        } else if (str.charAt(str.length() - 1) == ',') {
            list.add(null);
        }

        return list;
    }

    boolean dealOutParam(int iOutCnt, String str, Map<String, Object> record, List<TprFunctionCondition> listCondition) {
        if (str != null && !str.equals("")) {
            List<String> outParam = new ArrayList<>();
            if (str.equals(",") || str.equals(",\"\"")) {
                outParam.add(null);
            } else {
                if (str.charAt(0) == ',') {
                    outParam = analysisResult(str.substring(1));
                } else {
                    outParam = analysisResult(str);
                }
            }

            if (iOutCnt != outParam.size()) {
                return false;
            }

            int i = 0;
            for (TprFunctionCondition condition : listCondition) {
                if (condition.getParam_type().equals("OUT")) {
                    TprResourceAttr attr = mTprAttrName.get(condition.getAttr_name().toLowerCase());
                    if (common.getIDebug() > 0) {
                        System.out.println("出参 " + i + " -> " + attr.getEn_name().toLowerCase() + " 原值:" + outParam.get(i));
                    }
                    Object value = null;
                    if (attr.getData_type().equals("TA")) {
                        if (outParam.get(i) == null || "".equals(outParam.get(i))) {
                            value = outParam.get(i);
                        } else {
                            value = Integer.parseInt(outParam.get(i));
                        }
                    } else if (attr.getData_type().equals("TB")) {
                        if (outParam.get(i) == null || "".equals(outParam.get(i))) {
                            value = outParam.get(i);
                        } else {
                            value = Long.parseLong(outParam.get(i));
                        }
                    } else if (attr.getData_type().equals("TC")) {
                        if (outParam.get(i) == null || "".equals(outParam.get(i))) {
                            value = outParam.get(i);
                        } else {
                            value = Double.parseDouble(outParam.get(i));
                        }
                    } else if (attr.getData_type().equals("TD")) {
                        value = outParam.get(i);
                    } else if (attr.getData_type().equals("TE")) {
                        if (outParam.get(i) == null && "".equals(outParam.get(i))) {
                            value = "2010-01-01 12:00:00";
                        } else {
                            value = outParam.get(i);
                        }
                    }
                    if (common.getIDebug() > 0) {
                        System.out.println("出参 " + i + " -> " + attr.getEn_name().toLowerCase() + " 填值:" + value);
                    }
                    record.put(attr.getEn_name().toLowerCase(), value);
                    i++;
                }
            }
        }
        return true;
    }


    public boolean calcFunction_DbFunction(TprFunction tprFunction, Long lPringRefObjectId, Map<String, Object> record, List<String> param, Map<String, Object> result) {
        String enName = tprFunction.getEn_name();
        List<TprFunctionCondition> listCondition = mTprFunctionCondition.get(tprFunction.getFunction_id());
        String sql = null;
        String paramStr = new String();
        PreparedStatement ps = null;
        ResultSet rs = null;
        int iOutCnt = 0;

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            for (int i = 0; i < listCondition.size(); i++) {
                if (listCondition.get(i).getParam_type().equals("IN")) {
                    paramStr += ",?";
                }
            }
            sql = "select " + enName + "(?" + paramStr + ")";
            if (common.getIDebug() > 0) {
                System.out.println("因子标识:" + tprFunction.getFunction_id() + ",调用:" + sql);
            }
            ps = connTmp.prepareStatement(sql);
            ps.setObject(1, lPringRefObjectId);
            if (common.getIDebug() > 0) {
                System.out.println("入参总数：" + listCondition.size());
            }
            for (int i = 0; i < listCondition.size(); i++) {
                TprResourceAttr attr = mTprAttrName.get(listCondition.get(i).getAttr_name().toLowerCase());
                if (attr == null) {
                    log.error("不存在的话单入参属性");
                    return false;
                }
                if (listCondition.get(i).getParam_type().equals("IN")) {
                    Object valueObject = record.get(attr.getEn_name());
                    if (common.getIDebug() > 0) {
                        System.out.println("第：" + (i + 1) + " 入参：" + attr.getEn_name() + " -> " + valueObject);
                    }
                    if (attr.getData_type().equals("TA")) {
                        Integer value = 0;
                        if (valueObject != null && !"".equals(valueObject)) {
                            value = Integer.parseInt(valueObject.toString());
                        }
                        ps.setObject(i + 2, value);
                    } else if (attr.getData_type().equals("TB")) {
                        Long value = 0L;
                        if (valueObject != null && !"".equals(valueObject)) {
                            value = Long.parseLong(valueObject.toString());
                        }
                        ps.setObject(i + 2, value);
                    } else if (attr.getData_type().equals("TC")) {
                        Double value = 0.0;
                        if (valueObject != null && !"".equals(valueObject)) {
                            value = Double.parseDouble(valueObject.toString());
                        }
                        ps.setDouble(i + 2, value);
                    } else if (attr.getData_type().equals("TD")) {
                        String value = "";
                        if (valueObject != null) {
                            value = valueObject.toString();
                        }
                        ps.setString(i + 2, value);
                    } else if (attr.getData_type().equals("TE")) {
                        //SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Timestamp value = Timestamp.valueOf("1970-01-01 00:00:00");
                        if (valueObject != null && !"".equals(valueObject)) {
                            try {
                                if (valueObject.toString().length() == 10) {
                                    SimpleDateFormat dfYmd = new SimpleDateFormat("yyyy-MM-dd");
                                    Date dateTmp = dfYmd.parse(valueObject.toString());
                                    value = new Timestamp(dateTmp.getTime());
                                } else {
                                    value = Timestamp.valueOf(valueObject.toString());
                                }
                            } catch (ParseException e) {
                                e.printStackTrace();
                                log.error("入参有时间格式，数值转换失败！第：" + (i + 1) + " 入参：" + attr.getEn_name() + " -> " + valueObject);
                                return false;
                            }
                        }
                        ps.setTimestamp(i + 2, value);
                    }
                } else {
                    if (common.getIDebug() > 0) {
                        System.out.println("第：" + (i + 1) + " 出参：" + attr.getEn_name());
                    }
                    iOutCnt++;
                }
            }
            rs = ps.executeQuery();
            String rStr = null;
            while (rs.next()) {
                rStr = rs.getString(1);
            }
            if (common.getIDebug() > 0) {
                System.out.println("调用结果," + rStr);
            }

            String allStr = rStr.substring(1, rStr.length() - 1);
            // 返回结果集处理
            ResultValue rValue = null;

            if (allStr.indexOf("(") > 0) {
                String resultStr = allStr.substring(allStr.indexOf("(") + 1, allStr.indexOf(")"));
                List<String> resultList = analysisResult(resultStr);
                rValue = ResultValue.getResultValue(resultList);
                if (rValue.isbRet()) {
                    result.put("VALUE_TYPE", rValue.getValue_type());
                    result.put("VALUE", rValue.getValue());
                }
                if (rValue.getError_msg() != null && (!"".equals(rValue.getError_msg()))) {
                    result.put("error_msg", rValue.getError_msg());
                }
            } else {
                String resultStr = allStr;
                List<String> resultList = analysisResult(resultStr);
                rValue = ResultValue.getResultValue(resultList);
                if (rValue.isbRet()) {
                    result.put("VALUE_TYPE", rValue.getValue_type());
                    result.put("VALUE", rValue.getValue());
                }
                if (rValue.getError_msg() != null && (!"".equals(rValue.getError_msg()))) {
                    result.put("error_msg", rValue.getError_msg());
                }
            }

            // 出参处理
            if (rValue.isbRet() && iOutCnt > 0) {
                if (allStr.indexOf(")") + 2 <= allStr.length()) {
                    String outParamStr = allStr.substring(allStr.indexOf(")") + 2);
                    if (!dealOutParam(iOutCnt, outParamStr, record, listCondition)) {
                        rValue.setbRet(false);
                        rValue.setError_msg("出参处理失败,iOutCnt:" + iOutCnt + "," + outParamStr);
                    }
                }
            }

            if (common.getIDebug() > 0) {
                System.out.println("解析结果," + rValue.toString());
            }
            return rValue.isbRet();
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("SQLException " + e);
            result.put("error_msg", e);
            return false;
        } finally {
            DbPool.close(connTmp, ps, rs);
        }

    }

    Long getMicroSecond() {
        return System.nanoTime() / 1000;
    }

    public static Map<Integer, FunctionPerl> getmPerl() {
        return mPerl;
    }

    public static void clearPerl() {
        mPerl.clear();
    }

    public static void close() {
        mTprFunction.clear();
        mTprFunctionCondition.clear();
        mTprAttr.clear();
        mTprAttrName.clear();
        mRefValue.clear();
        rulePricingPlanMap.clear();
        DbPool.close();
    }
}
