package com.itco.Util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {

    /**
     * 判断目标时间是否在某个时间区间范围内
     * @param targetTime 目标时间
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return boolean
     */
    public static boolean isTimeBetweenInterval(String targetTime, String beginTime, String endTime) {
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date startTime = df.parse(targetTime);
            Date startDate = df.parse(beginTime);
            Date endDate = df.parse(endTime);
            if (startTime.after(startDate) && startTime.before(endDate)) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }


    /**
     * 获取月份
     * @param monthTime 时间
     * @param deviation 偏移量
     * @param pattern 格式
     * @return String
     */
    public static String getMonth(String monthTime, int deviation, String pattern) {
        DateFormat dateFormat = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        if (monthTime != null) {
            Date date;
            try {
                date = dateFormat.parse(monthTime);
                calendar.setTime(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        calendar.add(Calendar.MONTH, deviation);
        return dateFormat.format(calendar.getTime());
    }

    /**
     * 获取日期
     * @param dateTime 时间
     * @param deviation 偏移量
     * @param pattern 格式
     * @return String
     */
    public static String getDate(String dateTime, int deviation, String pattern) {
        DateFormat dateFormat = new SimpleDateFormat(pattern);
        Calendar calendar = Calendar.getInstance();
        if (dateTime != null) {
            Date date;
            try {
                date = dateFormat.parse(dateTime);
                calendar.setTime(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        calendar.add(Calendar.DATE, deviation);
        return dateFormat.format(calendar.getTime());
    }

}
