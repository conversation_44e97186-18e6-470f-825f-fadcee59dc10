package com.itco.RuleFunction;

public class ResultValue {
    int iret;
    String error_msg;
    int value_type;
    String value;

    public int getIret() {
        return iret;
    }

    public void setIret(int iret) {
        this.iret = iret;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public int getValue_type() {
        return value_type;
    }

    public void setValue_type(int value_type) {
        this.value_type = value_type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "ResultValue{" +
                "iret=" + iret +
                ", error_msg='" + error_msg + '\'' +
                ", value_type=" + value_type +
                ", value='" + value + '\'' +
                '}';
    }
}
