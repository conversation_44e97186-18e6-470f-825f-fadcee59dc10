package com.itco.RuleFunction;


import com.alibaba.druid.util.StringUtils;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.TplErrorTypeRule;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.rulefunction.ctgcache.CShmOpt;
import com.itco.rulefunction.ctgcache.TableService;
import com.itco.rulefunction.database.CDbutils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RulePricingPlanTpss_591 {
    static Log log = LogFactory.getLog(RulePricingPlanTpss_591.class);
    static Common common = null;
    private final String VALUE = "VALUE";
    private final String VALUE_TYPE = "VALUE_TYPE";
    private final String ERROR_MSG = "ERROR_MSG";
    static Map<String, Integer> mapErrorTypeRule = new HashMap<>();
    static int iDebug = 0;
    int iThreadNo = 0;
    private final SimpleDateFormat tFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat rFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    public enum CALC_RESULT_VALUE {
        INT(1, "INT"),
        LONG(2, "LONG"),
        DOUBLE(3, "DOUBLE"),
        STRING(4, "STRING"),
        DATE(5, "DATE"),
        ARRAY_INT(6, "ARRAY_INT"),
        ARRAY_LNG(7, "ARRAY_LNG"),
        ARRAY_DOUBLE(8, "ARRAY_DOUBLE"),
        ARRAY_STRING(9, "ARRAY_STRING"),
        ARRAY_DATE(10, "ARRAY_DATE");

        int id;
        String value;

        CALC_RESULT_VALUE(int id, String value) {
            this.id = id;
            this.value = value;
        }
    }

    public void setiThreadNo(int iThreadNo) {
        this.iThreadNo = iThreadNo;
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        List<TplErrorTypeRule> tableList = zkClientApi.getTableFromZK("tpl_error_type_rule", TplErrorTypeRule.class);
        for (TplErrorTypeRule tplErrorTypeRule : tableList) {
            mapErrorTypeRule.put(String.valueOf(tplErrorTypeRule.getError_type()) + String.valueOf(tplErrorTypeRule.getLatn_id()),
                    tplErrorTypeRule.getTicket_type());
        }
        TableService.setZkClientApi(zkClientApi);
        CDbutils.setZkClientApi(zkClientApi);
    }

    public static void setCommon(Common common) {
        RulePricingPlanTpss_591.common = common;
        iDebug = common.getIDebug();
    }

    /*
     * 函数名称：话单类型赋值
     * 功能描述：40200
     * 逻辑：根据话单的 error_type latn_id 查询表 tpl_error_type_rule
     * 返回值: 取出表中的 ticket_type 填回话单
     * 作者：杨宇轩
     * 创建时间：2021年4月29日
     * */
    public boolean Rule_PreProc2_SetTicketType(Map<String, Object> record) {

        // 索引字段
        String iErrorTypeID = record.get("error_type").toString(); // 异常类型
        String iLatnID = record.get("latn_id").toString();  // 计费号码所属本地网

        //  查询结果字段
        int iTicketType; // 计费话单类型

        // 索引
        String queryStr = iErrorTypeID + iLatnID;

        // 从Map中取到对应索引的值,填入话单
        iTicketType = mapErrorTypeRule.get(queryStr);
        record.put("ticket_type", iTicketType);

        return true;
    }

    /*
     * 函数名称：外部规则调用结果
     * 功能描述：40100
     * 返回值： 0
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean GetFunctionResult(Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        result.put(VALUE, "0");
        return true;
    }


    /* 函数名称：字符串截取
     * 功能描述：501000
     * 返回值： 截取后的结果
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_substr(String str, int pos, int count, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.STRING);
        if (str == null || str.equals("")) {
            result.put(VALUE, "");
        } else {
            if (pos < 0) {
                result.put(VALUE, str.substring(str.length() + pos, count - pos));
            } else {
                int length = count + pos - 1;
                result.put(VALUE, str.substring(pos - 1, length > str.length() ? str.length() : length));
            }
        }
        return true;
    }

    /*
     * 函数名称：字符长度
     * 功能描述：501001
     * 返回值： 长度
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_strlen(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        if (str == null || str.equals("")) {
            result.put(VALUE, "0");
        } else {
            result.put(VALUE, str.length());
        }
        return true;
    }

    /*
     * 函数名称：字符转大写
     * 功能描述：501002
     * 返回值： 转换大写的值
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_makeUpper(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.STRING);
        if (str == null || str.equals("")) {
            result.put(VALUE, "");
        } else {
            result.put(VALUE, str.toUpperCase());
        }
        return true;
    }

    /*
     * 函数名称：字符转小写
     * 功能描述：40110
     * 返回值： 转换大写的值
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_makeLower(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.STRING);
        if (str == null || str.equals("")) {
            result.put(VALUE, "");
        } else {
            result.put(VALUE, str.toLowerCase());
        }
        return true;
    }

    /* 函数名称：是否全为数字
     * 功能描述：501003
     * 返回值： 0:否  1:是
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_isdigit(String str, Map<String, Object> result) {
        result.put(VALUE, "1");
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        if (str == null) {
            result.put(VALUE, "0");
        } else if ("".equals(str)) {
            result.put(VALUE, "1");
        } else {
            for (int i = 0; i < str.length(); i++) {
                if (!Character.isDigit(str.charAt(i))) {
                    result.put(VALUE, "0");
                    break;
                }
            }
        }
        return true;
    }

    /*
     * 函数名称：整型转换成字符串
     * 功能描述：501010
     * 返回值：
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_IntegerToString(int num, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.STRING);
        result.put(VALUE, String.valueOf(num));
        return true;
    }

    /*
     * 函数名称：字符串转换成长整型
     * 功能描述：501011
     * 返回值：
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_StringToLong(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.LONG);
        result.put(VALUE, Long.parseLong(str));
        return true;
    }

    /*
     * 函数名称：时间转换成长整型
     * 功能描述：501012
     * 返回值：
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_CTimeToClong(String str, Map<String, Object> result) {
        Long lDate = new Long(0);

        if (str == null || str.length() != 19) {
            lDate = 0L;
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
            SimpleDateFormat dfL = new SimpleDateFormat("yyyyMMddHHmmss");//设置日期格式
            try {
                Date date = df.parse(str);// new Date()为获取当前系统时间，也可使用当前时间戳
                lDate = Long.parseLong(dfL.format(date));
            } catch (ParseException e) {
                e.printStackTrace();
                return false;
            }
        }
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.LONG);
        result.put(VALUE, lDate);
        return true;
    }

    /*
     * 函数名称：字符串是否为空
     * 功能描述：501013
     * 返回值： 0:非空，1:空
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_Stringisnull(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        if (str == null || str.equals("")) {
            result.put(VALUE, "1");
        } else {
            result.put(VALUE, "0");
        }
        return true;
    }

    /*
     * 函数名称：字符串转整型
     * 功能描述：501015
     * 返回值： 整型
     * 作者：傅兴旺
     * 创建时间：2021年3月16日
     * */
    public boolean Rule_StringToInteger(String str, Map<String, Object> result) {
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        if (str == null || str.equals("")) {
            result.put(VALUE, "0");
        } else {
            result.put(VALUE, Integer.parseInt(str));
        }
        return true;
    }

    /*
     * 函数名称：字符型转换成十六进制
     * 功能描述：40515
     * 作者：luzhq
     * 创建时间：2021年5月10日
     * 逻辑:
     * 结果:
     */
    public boolean Rule_PreProc_StringToHex(String sParam, Map<String, Object> result) {
        log.info("Rule_PreProc_StringToHex sParam=" + sParam);
        String sOutParam = null;
        if (sParam == null || "".equals(sParam)) {
            sOutParam = "0000";
        } else {
            sOutParam = String.format("%04x", Long.parseLong(sParam)).toUpperCase();
        }
        log.info("Rule_PreProc_StringToHex sOutParam=" + sOutParam);

        result.put(VALUE, sOutParam);
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.STRING);
        return true;
    }

    /*
     * 函数名称：填写处理帐期和分区标识
     * 功能描述：40212
     * 作者：傅兴旺
     * 创建时间：2021年3月17日
     * */
    public boolean Rule_Preproc2_WriteDealCycle(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {

        Integer billingCycleId = Integer.parseInt(record.get("billing_cycle_id").toString());
        Integer serviceControlCycleId = Integer.parseInt(record.get("service_control_cycle_id").toString());
        if (String.valueOf(billingCycleId).length() < 6 ||
                String.valueOf(serviceControlCycleId).length() < 6) {
            return false;
        }

        System.out.println("billing_cycle_id:" + billingCycleId);
        System.out.println("service_control_cycle_id:" + serviceControlCycleId);
        System.out.println("billing_cycle_id - service_control_cycle_id=" + (billingCycleId - serviceControlCycleId));
        int iRet = billingCycleId - serviceControlCycleId;

        result.put(VALUE, String.valueOf(iRet));
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        return true;
    }

    /*
     * 函数名称：账期月填写
     * 功能描述：40214
     * 作者：傅兴旺
     * 创建时间：2021年3月17日
     *  历史账期:50E  =>  1100; 实时账期: 50A  =》1000; 周期账期:50D =>  1200; 未来账期:50C  => 1400;
     *  逻辑:通过话单finish_time查询billing_cycle 表，select billing_cycle_id,status_cd from billing_cycle where $finish_time between cycle_begin_date and cycle_end_date;
     *  查询出来的status_cd 1000直接使用该账期填写
     *  查询出来的status_cd 1400直接使用该账期填写
     *  查询出来的status_cd 1200、1100 判断是重算的用1200 查询出账期填写；不是重算的用1000查询出账期填写
     * */
    public boolean Rule_Preproc2_WriteBillCycle(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {
        CShmOpt opt_BILLINGCYCLE = new CShmOpt("BILLING_CYCLE", iThreadNo);
        CShmOpt opt_BILLINGCYCLE_STATE = new CShmOpt("BILLING_CYCLE", iThreadNo);
        String finish_time = record.get("finish_time").toString();
        String redo_flag = record.get("redo_flag").toString();

        if (finish_time == null || finish_time.equals("") || finish_time.length() < 14) {
            Factory.setTicketTypeAbn(record, "finish_time[" + finish_time + "]值不合法;");
            return false;
        }

        int billingCycleId = 0;
        int ifinishTime = Integer.parseInt(finish_time.substring(0, 6));
        String queryStr = "billing_cycle_id:" + ifinishTime;

        if (!opt_BILLINGCYCLE.QueryByIndex(queryStr)) {
            log.error("opt_BILLINGCYCLE.QueryByIndex(queryStr) faile");
            return false;
        }

        while (opt_BILLINGCYCLE.Next()) {
            Object oStatusCd = opt_BILLINGCYCLE.getValue().get("status_cd");
            if (oStatusCd.toString().equals("1000") || oStatusCd.toString().equals("1200") || oStatusCd.toString().equals("1400")) {
                billingCycleId = ifinishTime;
            } else if (oStatusCd.toString().equals("1100")) {
                String queryStatusCd = new String();
                //if ("11".equals(redo_flag) || "13".equals(redo_flag) || "20".equals(redo_flag) || "21".equals(redo_flag) || "22".equals(redo_flag)) {
                // 取消掉预开通 redo_flag=20
                if ("11".equals(redo_flag) || "13".equals(redo_flag) || "21".equals(redo_flag) || "22".equals(redo_flag)) {
                    queryStatusCd = "status_cd:1200";
                } else {
                    queryStatusCd = "status_cd:1000";
                }

                opt_BILLINGCYCLE_STATE.Reset();
                if (!opt_BILLINGCYCLE_STATE.QueryByIndex(queryStatusCd)) {
                    log.error("opt_BILLINGCYCLE_STATE.QueryByIndex(queryStatusCd) faile");
                    return false;
                }
                while (opt_BILLINGCYCLE_STATE.Next()) {
                    Object oBillingCycleId = opt_BILLINGCYCLE_STATE.getValue().get("billing_cycle_id");
                    billingCycleId = Integer.valueOf(oBillingCycleId.toString());
                }

            } else {
                Factory.setTicketTypeAbn(record, "查找不到匹配的账期,FINISH_TIME[" + ifinishTime + "]");
            }
        }

        if (billingCycleId > 0) {
            record.put("deal_cycle_id", billingCycleId);
            record.put("billing_cycle_id", billingCycleId);

            if ("TPSS".equals(CalcRecordManager.getCommon().getSSystem()) && "591".equals(CalcRecordManager.getCommon().getSLatnId())) {
                int batchIdTmp = Integer.parseInt(record.get("cdr_batch_id").toString());
                if ("20".equals(redo_flag)) {
                    // 预开通使用回收的方式跑话单，账期月使用账期表里面的，和回退回收区别开来

                } else if ("11".equals(redo_flag) || "13".equals(redo_flag) || "21".equals(redo_flag) || "22".equals(redo_flag) || batchIdTmp > 0) {
                    if (record.containsKey("service_control_cycle_id") && record.get("service_control_cycle_id") != null) {
                        record.put("billing_cycle_id", record.get("service_control_cycle_id"));
                    }
                }
            }

            result.put(VALUE, billingCycleId);
            result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        }
        return true;
    }

    /*  历史账期:50E  =>  1100; 实时账期: 50A  =》1000; 周期账期:50D =>  1200; 未来账期:50C  => 1400; 50H => 1500;
     * 函数名称：积分账期月填写
     * 功能描述：40217
     * */
    public boolean Rule_Preproc2_WriteBillCycle2(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {
        CShmOpt opt_BILLINGCYCLE = new CShmOpt("BILLING_CYCLE_POINT", iThreadNo);

        String finish_time = record.get("finish_time").toString();
        String redo_flag = record.get("redo_flag").toString();
        String latn_id = record.get("latn_id").toString();

        if (finish_time == null || finish_time.equals("") || finish_time.length() < 14) {
            Factory.setTicketTypeAbn(record, "finish_time[" + finish_time + "]值不合法;");
            return false;
        }

        int ifinishTime = Integer.parseInt(finish_time.substring(0, 6));


        if (iDebug == 1) {
            log.info("finish_time:" + finish_time);
            log.info("redo_flag:" + redo_flag);
            log.info("lath_id:" + latn_id);
        }

        String queryStr = "billing_cycle_type_id|latn_id:1|" + latn_id;
        if (!opt_BILLINGCYCLE.QueryByIndex(queryStr)) {
            log.error("opt_BILLINGCYCLE.QueryByIndex(queryStr) faile");
            return false;
        }
        if (opt_BILLINGCYCLE.Size() == 0) {
            log.info("查找不到匹配的账期,FINISH_TIME=" + finish_time);
            return false;
        }

        String sbilling = new String();
        String sstate = new String();
        int i = 0;
        int billingCycleId = 0;

        while (opt_BILLINGCYCLE.Next()) {
            String sbegin_time = String.valueOf(opt_BILLINGCYCLE.getValue().get("cycle_begin_date"));
            String send_time = String.valueOf(opt_BILLINGCYCLE.getValue().get("cycle_end_date"));
            int ibegin_time = Integer.parseInt(sbegin_time.replace("-", "").substring(0, 6));
            int iend_time = Integer.parseInt(send_time.replace("-", "").substring(0, 6));
            if (ifinishTime >= ibegin_time && ifinishTime < iend_time) {
                sbilling = String.valueOf(opt_BILLINGCYCLE.getValue().get("billing_cycle_id"));
                sstate = String.valueOf(opt_BILLINGCYCLE.getValue().get("state"));
                i++;
            }
        }

        if (i == 0) {
            log.error("查找不到匹配的账期月,FINISH_TIME=" + finish_time);
            return false;
        } else if (i > 1) {
            log.error("账期匹配到多条记录,FINISH_TIME=" + finish_time);
            return false;
        }

        if (sstate.equals("1000") || sstate.equals("1200") || sstate.equals("1400")) {
            record.put("billing_cycle_id", Integer.parseInt(sbilling));
            billingCycleId = Integer.parseInt(sbilling);
        } else if (sstate.equals("1100") || sstate.equals("1500")) {
            String queryState;
            if (redo_flag.equals("11") || redo_flag.equals("13") || redo_flag.equals("20") || redo_flag.equals("21") || redo_flag.equals("22")) {
                queryState = "billing_cycle_type_id|latn_id|state:1|" + latn_id + "|1200";

                opt_BILLINGCYCLE.Reset();
                if (!opt_BILLINGCYCLE.QueryByIndex(queryState)) {
                    log.error("opt_BILLINGCYCLE.QueryByIndex(queryState) faile");
                    return false;
                }
                if (opt_BILLINGCYCLE.Size() == 0) {
                    queryState = "billing_cycle_type_id|latn_id|state:1|" + latn_id + "|1000";
                    opt_BILLINGCYCLE.Reset();
                    if (!opt_BILLINGCYCLE.QueryByIndex(queryState)) {
                        log.error("opt_BILLINGCYCLE.QueryByIndex(queryState) faile");
                        return false;
                    }
                    if (opt_BILLINGCYCLE.Size() == 0) {
                        log.info("查找不到匹配的账期,FINISH_TIME=" + finish_time);
                    }
                }
            } else {
                queryState = "billing_cycle_type_id|latn_id|state:1|" + latn_id + "|1000";
                opt_BILLINGCYCLE.Reset();
                if (!opt_BILLINGCYCLE.QueryByIndex(queryState)) {
                    log.error("opt_BILLINGCYCLE.QueryByIndex(queryState) faile");
                    return false;
                }
                if (opt_BILLINGCYCLE.Size() == 0) {
                    log.info("查找不到匹配的账期,FINISH_TIME=" + finish_time);
                }
            }

            while (opt_BILLINGCYCLE.Next()) {
                Object oBillingCycleId = opt_BILLINGCYCLE.getValue().get("billing_cycle_id");
                sbilling = oBillingCycleId.toString();
                break;
            }
            if (ifinishTime <= Integer.parseInt(sbilling)) {
                billingCycleId = Integer.parseInt(sbilling);
                record.put("billing_cycle_id", billingCycleId);
            } else {
                int billingCycleIdTmp = Integer.parseInt(sbilling);
                int year = Integer.parseInt(sbilling.substring(0, 4));
                int month = Integer.parseInt(sbilling.substring(4, 6)) + 3;
                String str;
                year += month / 12;
                month = month % 12;
                if (month < 10) {
                    str = year + "0" + month;
                } else {
                    str = String.valueOf(year + "" + month);
                }
                int ibillingCycle = Integer.parseInt(str);
                if (iDebug == 1) {
                    log.info("超前三个月单,BILLING_CYCLE_ID=" + ibillingCycle);
                }
                if (ifinishTime > ibillingCycle) {
                    if (iDebug == 1) {
                        log.info("超前三个月单,BILLING_CYCLE_ID=" + sbilling);
                    }
                    billingCycleId = Integer.parseInt(sbilling);
                    record.put("billing_cycle_id", Integer.parseInt(sbilling));
                } else if (ifinishTime > billingCycleIdTmp && ifinishTime <= ibillingCycle) {
                    if (iDebug == 1) {
                        log.info("三个月内超前单,BILLING_CYCLE_ID=" + sbilling);
                    }
                    billingCycleId = Integer.parseInt(sbilling);
                    record.put("billing_cycle_id", Integer.parseInt(sbilling));
                }
            }
        }

        if (billingCycleId > 0) {
            result.put(VALUE, billingCycleId);
            result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        }

        return true;
    }

    /*
     * 函数名称：根据CRM类型查找对应的销售品或产品规格等信息
     * 功能描述：40215
     * 作者：傅兴旺
     * 创建时间：2021年3月17日
     * */
    public boolean Rule_PreProc2_GetProd(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {

        result.put(VALUE, "");
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        return true;
    }

    /*
     * 函数名称：融合-根据CRM类型查找对应的销售品或产品规格等信息
     * 功能描述：40216
     * 作者：傅兴旺
     * 创建时间：2021年3月17日
     * */
    public boolean Rule_PreProc2_GetProd_IsMixMeal(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {

        result.put(VALUE, "");
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        return true;
    }

    /*
     * 函数名称：获取事件匹配类型
     * 功能描述：500004
     * 作者：傅兴旺
     * 创建时间：2021年3月17日
     * */
    public boolean Rule_PreProc2_GetEventType(Map<String, Object> record, Long lPringRefObjectId, List<String> param, Map<String, Object> result) {


        result.put(VALUE, "");
        result.put(VALUE_TYPE, CALC_RESULT_VALUE.INT);
        return true;
    }
}
