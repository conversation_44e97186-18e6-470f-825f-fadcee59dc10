package com.itco;

import com.itco.CalcFunction.CalcFunctionLocal;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.Common;
import com.itco.entity.common.Result;
import com.itco.rulefunction.ctgcache.CShmOpt;
import com.itco.rulefunction.ctgcache.TableService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.sql.Connection;
import java.util.*;

@SpringBootApplication
public class RuleFunctionApplication {
    static Log log = LogFactory.getLog(RuleFunctionApplication.class);
    ZkClientApi zkClientApi = null;
    Connection conn = null;
    Common common = new Common(); //公共配置类

    boolean loadProperties() {
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        log.info(common.toString());
        return true;
    }

    //@Bean
    void startNew() {
        // 初始化zookeeper
        zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("tRuleFunc");
        if (!zkClientApi.init()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return;
        }
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());
        zkClientApi.register(true);

        if (!loadProperties()) {
            log.error("loadProperties() faile");
            return;
        }
        CalcFunctionLocal.setCommon(common);
        CalcFunctionLocal.setZkClientApi(zkClientApi);
        if (!CalcFunctionLocal.initCfg()) {
            log.error("CalcFunctionLocal.initCfg() 失败");
            return;
        }
        CalcFunctionLocal calcFunction = CalcFunctionLocal.getInstance();

        Map<String, Object> record = new HashMap<>();
        //Map<String, Object> result = new HashMap<>();
        Result result;

        System.out.println();
        record.clear();
        //构造话单字段
        record.put("finish_time", "20191001000000");
        record.put("redo_flag", "0");

        //函数调用
        calcFunction = CalcFunctionLocal.getInstance();
        // 业务因子调用
        result = calcFunction.calcFunction("40214", record);
        // 通用因子调用
        //calcFunction.calcFunction("40220($61)", record, result);

        for (Map.Entry<String, Object> map : record.entrySet()) {
            System.out.println(map.getKey() + "->" + map.getValue());
        }

        log.info("VALUE:" + result.get("VALUE"));
        log.info("VALUE_TYPE:" + result.get("VALUE_TYPE"));
    }


    static boolean test() {
        String functionStr = new String("501016()");
        List<String> param = new ArrayList<>();
        int function_id = 0;
        if (functionStr == null) {
            return false;
        } else if (functionStr.indexOf("(") > 0) {
            function_id = Integer.valueOf(functionStr.substring(0, functionStr.indexOf("(")));
            String str = functionStr.substring(functionStr.indexOf("(") + 1, functionStr.indexOf(")"));
            String[] tmp = str.split(",");

            for (int i = 0; i < tmp.length; i++) {
                System.out.println(i + ":" + tmp[i]);
                if (tmp[i] == null || tmp[i].equals("")) {

                } else if (tmp[i].charAt(0) == '$') {
                    param.add(tmp[i].substring(1));
                } else if (tmp[i].charAt(0) == 'C') {
                    param.add(tmp[i].substring(1));
                } else {
                    param.add(tmp[i]);
                }
            }
        } else {
            function_id = Integer.valueOf(functionStr);
        }

        System.out.println("function_id:" + function_id);
        for (String p : param) {
            System.out.println("p:" + p);
        }

        return true;
    }

    //@Bean
    void testTable() {
        zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("tRuleFunc");
        if (!zkClientApi.init()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return;
        }
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());
        zkClientApi.register(true);

        TableService.setZkClientApi(zkClientApi);
        TableService tableService = TableService.getInstance();

        CShmOpt cShmOpt = new CShmOpt("BILLING_CYCLE", 1);
        cShmOpt.QueryByIndex("billing_cycle_id|STATUS_CD:202001|1100");

        System.out.println("查询结果：");
        while (cShmOpt.Next()) {
            System.out.println(cShmOpt.getValue());
        }
    }


    public static void main(String[] args) {
        SpringApplication.run(RuleFunctionApplication.class, args);
    }

}
