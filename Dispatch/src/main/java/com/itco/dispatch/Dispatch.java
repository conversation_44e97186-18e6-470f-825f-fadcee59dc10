package com.itco.dispatch;

import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dispatch.entity.EntityMgr;
import com.itco.dispatch.entity.entitydef.ErrorType;
import com.itco.dispatch.thread.DatabaseManager;
import com.itco.dispatch.thread.MessageManager;
import com.itco.dispatch.thread.ProcessManager;
import com.itco.dispatch.thread.TaskRecordManager;
import com.itco.entity.common.Common;
import com.itco.entity.common.TgAlertLog;
import com.itco.entity.common.TpProcess;
import com.itco.framework.Factory;
import com.itco.framework.Version;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

@SpringBootApplication
public class Dispatch {
    static Log log = LogFactory.getLog(Dispatch.class);
    EntityMgr entityMgr = null;

    static ConfigurableApplicationContext context = null;
    static String moduleName;
    static volatile boolean eExit = false;
    static String billinglineId = null;
    static int debug = 0;


    String taskSmartMode = null;
    static ZkClientApi zkClientApi = null;
    Common common = new Common();

    static Dispatch dispatch = null;
    static String sModuleCode = "Dispatch";

    MessageManager messageManager = new MessageManager();
    DatabaseManager databaseManager = new DatabaseManager();

    public boolean init() {

        // 实例化zookeeper
        zkClientApi = new ZkClientApi();
        zkClientApi.setVersion(common.getVersion());
        zkClientApi.setBillingLineId(billinglineId);
        zkClientApi.setModuleCode(moduleName);
        zkClientApi.setDispatch(true); //调度模式

        if (!zkClientApi.init()) {
            log.info("zk初始化失败，无法启动");
            Factory.close();
            return false;
        }
        //进程加载基础配置
        if (!zkClientApi.loadInitTable()) {
            log.info("loadInitTable()失败，无法启动");
            Factory.close();
            return false;
        }

        if (!zkClientApi.register(false)) {
            log.info("进程注册失败，无法启动");
            Factory.close();
            return false;
        }

        if (!zkClientApi.readyWork()) {
            log.info("zkClientApi.readyWork() faile");
            Factory.close();
            return false;
        }

        //数据库数据初始化
        entityMgr = new EntityMgr();
        entityMgr.setZkClientApi(zkClientApi);
        entityMgr.setDatabaseManager(databaseManager);
        if (!entityMgr.init()) {
            log.error("数据库配置读取失败");
            Factory.close();
            return false;
        }

        if (!loadCommonProperties()) {
            log.error("loadCommonProperties() faile");
            Factory.close();
            return false;
        }

        log.info("application init end 初始化结束");
        return true;
    }

    public boolean loadCommonProperties() {
        if (!zkClientApi.loadCommonProperties(common)) {
            log.error("loadCommonProperties(common) faile");
            return false;
        }

        common.setIDebug(debug);
        log.info(common.toString());
        return true;
    }

    public boolean run() {
        log.info("application run start");

        // 1、消息管理线程
        messageManager.setZkClientApi(zkClientApi);
        messageManager.setCommon(common);
        if (!messageManager.init()) {
            log.info("messageManager.init()，初始化失败");
            Factory.setbWhileFlag(true);
            return false;
        }
        Thread messageThread = new Thread(messageManager);
        messageThread.start();

        //  2、进程管理线程初始化
        ProcessManager processManager = new ProcessManager();
        processManager.setMessageManager(messageManager);
        processManager.setEntityMgr(entityMgr);
        processManager.setZkClientApi(zkClientApi);
        processManager.setCommon(common);
        if (!processManager.init()) {
            log.info("processManager.init()，初始化失败");
            Factory.setbWhileFlag(true);
            return false;
        }
        Thread processThread = new Thread(processManager);
        processThread.start();

        //  3、任务管理线程初始化
        TaskRecordManager taskRecordManager = new TaskRecordManager();
        taskRecordManager.setMessageManager(messageManager);
        taskRecordManager.setProcessManager(processManager);
        taskRecordManager.setCommon(common);
        taskRecordManager.setEntityMgr(entityMgr);
        taskRecordManager.setZkClientApi(zkClientApi);
        if (taskSmartMode != null) {
            taskRecordManager.setM_SmartMode(taskSmartMode);
        }

        if (!taskRecordManager.init()) {
            log.info("taskRecordManager.init()，初始化失败");
            Factory.setbWhileFlag(true);
            return false;
        }

        //  任务管理线程启动、任务创建、分发，回收
        Thread taskRecordThread = new Thread(taskRecordManager);
        taskRecordThread.start();

        //  4、数据库操作线程启动
        databaseManager.setCommon(common);
        Thread databaseThread = new Thread(databaseManager);
        databaseThread.start();

        //  4、主从模式线程
        /* MasterSlaveManager masterSlaveManager=new MasterSlaveManager();
        masterSlaveManager.setMessageManager(messageManager);
        if (!masterSlaveManager.init()) {
            log.info("masterSlaveManager.init()，初始化失败");
            return;
        }
        //  启动主从模式
        Thread masterSlaveThread = new Thread(masterSlaveManager);
        masterSlaveThread.start();*/

        // 5、插入调度启动日志
        List<TpProcess> listTemp = new ArrayList<>();
        listTemp.add(zkClientApi.getTpProcess());
        databaseManager.createProcessLogToTable(listTemp);

        // 6、所有的管理类初始化完成，再开始启动监听队列
        messageManager.listenTopic();


        return true;
    }

    public static boolean start() {
        dispatch = new Dispatch();
        dispatch.setModuleName(sModuleCode);
        dispatch.setBillinglineId(billinglineId);

        if (!dispatch.init()) {
            log.info("dispatch.init() faile");
            return false;
        }

        if (!dispatch.run()) {
            log.info("dispatch.run() faile");
            return false;
        }

        if (Factory.isbWhileFlag()) {
            Factory.close();
        }
        return true;
    }

    public void setModuleName(String moduleName) {
        Dispatch.moduleName = moduleName;
    }

    public void setBillinglineId(String billinglineId) {
        Dispatch.billinglineId = billinglineId;
    }

    @PreDestroy
    public void destroy() {
        // 调度退出，更新进程表和插入退出到日志表
        TpProcess pro = zkClientApi.getTpProcess();
        if (pro != null) {
            // 1、修改调度进程状态为，TA
            pro.setSystem_process_id(-1);
            pro.setStatus(ProcessManager.PROCESS_STATE.TA.getState());
            pro.setExit_date(Factory.getSystemDateStr());
            databaseManager.saveDbProcessToTable(pro);

            // 2、生成调度退出日志
            List<TpProcess> listTemp = new ArrayList<>();
            listTemp.add(pro);
            databaseManager.createProcessLogToTable(listTemp);

            // 3、生成调度退出告警日志
            List<TgAlertLog> listTgAlertLog = new ArrayList<>();
            TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.DISPATCH_PROCESS_NORMAL_EXIT, pro.getProcess_id());
            tgAlertLog.setLog_desc("告警类型:" + ErrorType.DISPATCH_PROCESS_NORMAL_EXIT + ",调度进程：" + pro.getProcess_id() + "，退出。" + pro.toString());
            listTgAlertLog.add(tgAlertLog);
            databaseManager.createTgAlterLogToTable(listTgAlertLog);
        }

        Version.print();
        Version.destroy();
    }

    /*
     * 版本说明：
     * Dispatch_code_2022-01-09 17:00 -> 调度支持启动和退出进程表和进程日志表有记录。
     * Dispatch_code_2023-01-30 11:00 -> 调度新增进程退出，任务失败、任务超时的告警日志到tg_alert_log表。
     *
     */
    public static void main(String[] args) {
        if (Version.print(args, "Dispatch_code_2023-04-02 11:00")) {
            return;
        }

        billinglineId = "00";
        for (String para : args) {
            if (para.startsWith("-f")) {
                billinglineId = para.substring(2);
            } else if (para.startsWith("-d")) {
                debug = 1;
                if (para.length() > 2) {
                    debug = Integer.parseInt(para.substring(2));
                }
            }
        }
        context = SpringApplication.run(Dispatch.class, args);
        Factory.setContext(context);
        start();
        log.info("初始化成功");
    }
}
