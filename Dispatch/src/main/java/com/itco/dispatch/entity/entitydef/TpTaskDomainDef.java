package com.itco.dispatch.entity.entitydef;

public class TpTaskDomainDef {

    int order_id;
    String state;
    int smart_order_id;
    String body_type;
    String remark;

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getSmart_order_id() {
        return smart_order_id;
    }

    public void setSmart_order_id(int smart_order_id) {
        this.smart_order_id = smart_order_id;
    }

    public String getBody_type() {
        return body_type;
    }

    public void setBody_type(String body_type) {
        this.body_type = body_type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String toString() {
        return "TpTaskDomainDef{" +
                "order_id=" + order_id +
                ", state='" + state + '\'' +
                ", smart_order_id=" + smart_order_id +
                ", body_type='" + body_type + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
