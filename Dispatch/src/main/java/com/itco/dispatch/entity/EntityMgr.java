package com.itco.dispatch.entity;

import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dispatch.entity.entitydef.*;
import com.itco.dispatch.thread.DatabaseManager;
import com.itco.entity.common.TgAlertLog;
import com.itco.entity.common.TpModule;
import com.itco.entity.common.TpModuleVersion;
import com.itco.entity.common.TpProcess;
import com.itco.entity.process.MsgBody;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class EntityMgr {
    static Log log = LogFactory.getLog(EntityMgr.class);
    List<ClusterDef> mCluster = null;
    List<HostDef> mHost = null;
    Map<String, TpTaskRecord> mTpTaskRecord = new HashMap<>();
    Map<String, TpWorkpathDef> mTpWorkPath = new HashMap<>();
    List<TpTaskDomainDef> mTpTaskDomain = null;
    List<TpModule> mTpModuleList = new ArrayList<>(); //只用来存放生产线处理模块
    Map<String, TpModule> mapModule = new HashMap<>();
    // 进程实例Id和进程实例
    Map<String, TpProcess> mTpProcessMap = new HashMap<>();
    Map<String, MsgBody.ConfigSyncBody> mConfigTaskMap = new HashMap<>();
    List<TpTaskFlowDef> mTaskFlowList = new ArrayList<>();

    int m_TaskId;
    ZkClientApi zkClientApi = null;
    DatabaseManager databaseManager = null;

    public boolean init() {
        Long lStartTaskId = 0L;
        String startTaskId = System.getenv("START_TASK_ID");
        if (startTaskId != null) {
            lStartTaskId = Long.parseLong(startTaskId);
        }

        String sqlCluster = "select cluster_id,cluster_name,cluster_status,remark from dcoos_cluster_def";
        String sqlHost = "select host_id,action_type,change_type,cluster_id,host_ip,host_name,host_type,online_status,user_name,passwd,switch_type,target_host_id from dcoos_host_def";
        String sqlTaskRecord = "select task_id,cluster_id,task_name,file_name,mode,type,host_id,order_id,module_id,local_path,record_cnt,process_id,state,create_date,update_date,element_str,msg_body,curr_time,perl,redo_cnt from dcoos_tp_task_record where task_id > ? and state not in ('SSS') order by task_id";
        String sqlMaxTaskRecord = "select max(task_id) task_id from dcoos_tp_task_record";
        String sqlWorkPath = "select module_id,local_input_path,local_output_path,hdfs_path,hdfs_path_no_first from dcoos_tp_work_path order by module_id";
        String sqlDomain = "select order_id,state,smart_order_id,body_type,remark from dcoos_tp_task_domain";
        String sqlTpModule = "select module_id,module_name,parent_module_id,module_code,module_type,order_id,startup_priority,pro_path,start_command,stop_command,max_process_num from dcoos_tp_module order by module_id";
        String sqlTpProcess = "select process_id,module_id,host_id,status,type,billing_line_id,perl,module_code,system_process_id,process_name,update_date,host_name,host_ip from dcoos_tp_process order by process_id";
        String sqlConfigSync = "select config_sync_id,task_type,host_id,process_id,module_id,load_id,msg_comment,msg_type,state,error_msg,update_date from dcoos_config_sync_body where state is null order by config_sync_id";
        String sqlTaskFlow = "select flow_id,mode,module_id,process_module_id,order_id,state,remark from dcoos_tp_task_flow order by mode,order_id";

        // 初始化数据库
        DbPool.setZkClientApi(zkClientApi);

        // 获取数据库连接
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            mCluster = DBUtils.queryList(ClusterDef.class, connTmp, sqlCluster);
            mHost = DBUtils.queryList(HostDef.class, connTmp, sqlHost);
            setmTpTaskRecord(DBUtils.queryList(TpTaskRecord.class, connTmp, sqlTaskRecord, lStartTaskId));
            setmTpWorkPath(DBUtils.queryList(TpWorkpathDef.class, connTmp, sqlWorkPath));
            mTpTaskDomain = DBUtils.queryList(TpTaskDomainDef.class, connTmp, sqlDomain);
            setmTpModuleList(DBUtils.queryList(TpModule.class, connTmp, sqlTpModule));
            setmTpProcessMap(DBUtils.queryList(TpProcess.class, connTmp, sqlTpProcess));
            setmConfigTaskMap(DBUtils.queryList(MsgBody.ConfigSyncBody.class, connTmp, sqlConfigSync));
            setmTaskFlowList(DBUtils.queryList(TpTaskFlowDef.class, connTmp, sqlTaskFlow));
            List<TpTaskRecord> listTask = DBUtils.queryList(TpTaskRecord.class, connTmp, sqlMaxTaskRecord);
            if (listTask == null) {
                m_TaskId = 0;
            } else {
                m_TaskId = listTask.get(0).getTask_id();
            }
        } finally {
            DbPool.close(connTmp);
        }

        return true;
    }

    public int getOrderIdByModuleId(int module_id) {
        int order_id = 0;
        for (TpModule module : mTpModuleList) {
            if (module.getModule_id() == module_id) {
                order_id = module.getOrder_id();
                break;
            }
        }

        return order_id;
    }

    public TpModule getTpModuleByOrderId(int order_id) {
        TpModule module = null;
        for (TpModule moduleIter : mTpModuleList) {
            if (moduleIter.getOrder_id() == order_id) {
                module = moduleIter;
                break;
            }
        }

        return module;
    }

    public TpModule getTpModuleDefByModuleId(int module_id) {
        TpModule module = null;
        for (TpModule moduleIter : mTpModuleList) {
            if (moduleIter.getModule_id() == module_id) {
                module = moduleIter;
                break;
            }
        }

        return module;
    }

    // 获取任务状态
    public TpTaskDomainDef get_Domain(String state) {
        for (TpTaskDomainDef domain : mTpTaskDomain) {
            if (state.equals(domain.getState()))
                return domain;
        }
        return null;
    }

    public String getNextDomain(String state) {
        String nextDomain = null;
        int iPosition = 0;
        for (TpTaskDomainDef domain : mTpTaskDomain) {
            if (state.equals(domain.getState())) {
                iPosition = domain.getOrder_id() + 1;
                break;
            }
        }

        //如果下一个状态编号为10，则从新从1开始
        if (iPosition >= 10) {
            iPosition = 1;
        }

        if (iPosition > 0) {
            for (TpTaskDomainDef domain : mTpTaskDomain) {
                if (domain.getOrder_id() == iPosition) {
                    nextDomain = domain.getState();
                    break;
                }
            }
        }
        return nextDomain;
    }

    public String getFrontDomain(String state) {
        String frontDomain = null;
        int iPosition = -1;
        for (TpTaskDomainDef domain : mTpTaskDomain) {
            if (state.equals(domain.getState())) {
                iPosition = domain.getOrder_id() - 1;
                break;
            }
        }

        //如果上一个状态编号为0，则从新从9开始
        if (iPosition == 0) {
            iPosition = 9;
        }

        if (iPosition > 0) {
            for (TpTaskDomainDef domain : mTpTaskDomain) {
                if (domain.getOrder_id() == iPosition) {
                    frontDomain = domain.getState();
                    break;
                }
            }
        }

        return frontDomain;
    }

    ///////////////////////////////////外部进程和任务管理使用////////////////////////////////////////////////////
    public boolean createTpProcessLogToTable(TpProcess process) {
        TpProcess tmp = SerializationUtils.clone(process);
        databaseManager.offerG_createProcessLogQueue(tmp);
        return true;
    }

    public synchronized boolean saveDbProcessToTable(TpProcess process) {
        TpProcess tmp = SerializationUtils.clone(process);
        databaseManager.offerG_saveProcessQueue(tmp);
        return true;
    }

    public synchronized boolean saveTaskToTable(TpTaskRecord task) {
        TpTaskRecord tmp = SerializationUtils.clone(task);
        databaseManager.offerG_saveTaskQueue(tmp);
        return true;
    }

    public synchronized boolean createTaskToTable(TpTaskRecord task) {
        TpTaskRecord tmp = SerializationUtils.clone(task);
        databaseManager.offerG_createTaskQueue(tmp);
        return true;
    }

    public synchronized boolean createConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        databaseManager.offerG_createConfigSyncQueue(sync);
        return true;
    }

    public synchronized boolean saveConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        databaseManager.offerG_saveConfigSyncQueue(sync);
        return true;
    }

    public boolean createTpTaskRecordLogToTable(List<TpTaskRecordLog> list) {
        databaseManager.offerG_createTaskLogQueue(list);
        return true;
    }

    public boolean createTgAlterLogToTable(String msg) {
        databaseManager.offerG_createTgAlterLogQueue(msg);
        return true;
    }

    public boolean createTpmoduleVersionToTable(List<TpModuleVersion> tpModuleVersions) {
        for (TpModuleVersion tpModuleVersion : tpModuleVersions) {
            databaseManager.offerG_createModuleVersionQueue(tpModuleVersion);
        }
        return true;
    }

    //////////////////////////////////加工List转map////////////////////////////////////////////////////////////

    public Map<String, TpTaskRecord> getmTpTaskRecord() {
        return mTpTaskRecord;
    }

    public void setmTpTaskRecord(List<TpTaskRecord> mTpTaskRecord) {
        for (TpTaskRecord tpTaskRecord : mTpTaskRecord) {
            this.mTpTaskRecord.put(String.valueOf(tpTaskRecord.getTask_id()), tpTaskRecord);
        }
    }

    public Map<String, TpWorkpathDef> getmTpWorkPath() {
        return mTpWorkPath;
    }

    public void setmTpWorkPath(List<TpWorkpathDef> mTpWorkPath) {
        for (TpWorkpathDef tpWorkpathDef : mTpWorkPath) {
            this.mTpWorkPath.put(String.valueOf(tpWorkpathDef.getModule_id()), tpWorkpathDef);
        }
    }

    public Map<String, TpProcess> getmTpProcessMap() {
        return mTpProcessMap;
    }

    public void setmTpProcessMap(List<TpProcess> mTpProcessMap) {
        for (TpProcess tpProcess : mTpProcessMap) {
            this.mTpProcessMap.put(String.valueOf(tpProcess.getProcess_id()), tpProcess);
        }
    }

    public Map<String, MsgBody.ConfigSyncBody> getmConfigTaskMap() {
        return mConfigTaskMap;
    }

    public void setmConfigTaskMap(List<MsgBody.ConfigSyncBody> mConfigTaskList) {
        for (MsgBody.ConfigSyncBody configSyncBody : mConfigTaskList) {
            this.mConfigTaskMap.put(configSyncBody.CONFIG_SYNC_ID, configSyncBody);
        }
    }

    public void setmTpModuleList(List<TpModule> mTpModuleList) {
        for (TpModule tpModule : mTpModuleList) {
            mapModule.put(String.valueOf(tpModule.getModule_id()), tpModule);
        }

        for (TpModule tpModule : mTpModuleList) {
            if (tpModule.getOrder_id() > 0) {
                this.mTpModuleList.add(tpModule);
            }
        }
    }

    public Map<String, TpModule> getMapModule() {
        return mapModule;
    }

    public List<TpTaskFlowDef> getmTaskFlowList() {
        return mTaskFlowList;
    }

    public void setmTaskFlowList(List<TpTaskFlowDef> mTaskFlowList) {
        this.mTaskFlowList = mTaskFlowList;
    }

    //////////////////////////////////默认////////////////////////////////////////////////////////////

    public List<ClusterDef> getmCluster() {
        return mCluster;
    }

    public void setmCluster(List<ClusterDef> mCluster) {
        this.mCluster = mCluster;
    }

    public List<HostDef> getmHost() {
        return mHost;
    }

    public void setmHost(List<HostDef> mHost) {
        this.mHost = mHost;
    }

    public List<TpTaskDomainDef> getmTpTaskDomain() {
        return mTpTaskDomain;
    }

    public void setmTpTaskDomain(List<TpTaskDomainDef> mTpTaskDomain) {
        this.mTpTaskDomain = mTpTaskDomain;
    }

    public int getM_TaskId() {
        return m_TaskId;
    }

    public void setM_TaskId(int m_TaskId) {
        this.m_TaskId = m_TaskId;
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void setDatabaseManager(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }

    public List<TpModule> getmTpModuleList() {
        return mTpModuleList;
    }

    //////////////////////////////////默认////////////////////////////////////////////////////////////


    public void toShow() {
        log.info("CLUSTER:" + mCluster.toString());
        log.info("HOST:" + mHost.toString());
        log.info("TP_TASK_RECORD:" + mTpTaskRecord.toString());
    }
}
