package com.itco.dispatch.entity.entitydef;



public class HostDef  {

    private int cluster_id;

    private int host_id;

    private String host_name;

    private String host_ip;

    private String host_type;

    private String online_status;

    private int switch_type;

    private int action_type;

    private int target_host_id;

    private int change_type;

    public int getCluster_id() {
        return cluster_id;
    }

    public void setCluster_id(int cluster_id) {
        this.cluster_id = cluster_id;
    }

    public int getHost_id() {
        return host_id;
    }

    public void setHost_id(int host_id) {
        this.host_id = host_id;
    }

    public String getHost_name() {
        return host_name;
    }

    public void setHost_name(String host_name) {
        this.host_name = host_name;
    }

    public String getHost_ip() {
        return host_ip;
    }

    public void setHost_ip(String host_ip) {
        this.host_ip = host_ip;
    }

    public String getHost_type() {
        return host_type;
    }

    public void setHost_type(String host_type) {
        this.host_type = host_type;
    }

    public String getOnline_status() {
        return online_status;
    }

    public void setOnline_status(String online_status) {
        this.online_status = online_status;
    }

    public int getSwitch_type() {
        return switch_type;
    }

    public void setSwitch_type(int switch_type) {
        this.switch_type = switch_type;
    }

    public int getAction_type() {
        return action_type;
    }

    public void setAction_type(int action_type) {
        this.action_type = action_type;
    }

    public int getTarget_host_id() {
        return target_host_id;
    }

    public void setTarget_host_id(int target_host_id) {
        this.target_host_id = target_host_id;
    }

    public int getChange_type() {
        return change_type;
    }

    public void setChange_type(int change_type) {
        this.change_type = change_type;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public String toString() {
        return "HostDef{" +
                ", cluster_id='" + cluster_id + '\'' +
                ", host_id='" + host_id + '\'' +
                ", host_name='" + host_name + '\'' +
                ", host_ip='" + host_ip + '\'' +
                ", host_type='" + host_type + '\'' +
                ", online_status='" + online_status + '\'' +
                ", switch_type=" + switch_type +
                ", action_type=" + action_type +
                ", target_host_id=" + target_host_id +
                ", change_type=" + change_type +
                ", user_name='" + user_name + '\'' +
                ", passwd='" + passwd + '\'' +
                '}';
    }

    private String user_name;

    private String passwd;
}
