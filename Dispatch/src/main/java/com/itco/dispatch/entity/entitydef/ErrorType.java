package com.itco.dispatch.entity.entitydef;

public class ErrorType {
    // 调度
    public final static int DISPATCH_PROCESS_NORMAL_EXIT = 10100001;   //进程正常退出
    public final static int DISPATCH_PROCESS_ABN_EXIT = 10100002;       //进程异常退出
    public final static int DISPATCH_TASK_DEAL_ERROR = 10100003;       //任务处理异常
    public final static int DISPATCH_TASK_DEAL_TIMEOUT = 10100004;     //任务处理超时

    // 稽核日志
    public final static int DISPATCH_LOG_TASK_STATISTICAL = 10101001;   //任务统计日志

}
