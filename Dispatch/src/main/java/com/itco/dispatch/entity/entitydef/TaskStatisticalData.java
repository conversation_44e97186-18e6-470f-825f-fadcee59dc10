package com.itco.dispatch.entity.entitydef;

public class TaskStatisticalData {
    public String msg_type;      //消息类型
    // 任务统计
    public String type;         //业务类型
    public String startDate;    //开始时间
    public String endDate;      //结束时间
    public long lTaskTotal;     //总任务数
    public long lRecordTotal;   //总记录数
    public long lStartCnt;      //输入记录数
    public long lEndCnt;        //输出记录数
    public long lNormalCnt;     //正常单
    public long lPreCnt;        //预结单
    public long lAbnCnt;        //异常单
    public long lOtherCnt;      //不结算单
    public long lRepeatCnt;     //重单
    public long lErrorCnt;      //错单
    public long lPerl;          //平均性能

    public long lBackTaskCnt;   //积压任务数
    public long lBackRecordCnt; //积压任务数
    public long lFaileTaskCnt;  //失败任务数
    public long lFaileRecordCnt;//失败话单数

    // 进程统计
    public int iDbInceptCnt;    //接收进程数量
    public int iPreProc2Cnt;    //预处理进程数量
    public int iFilterCnt;      //排重进程数量
    public int iRatingCnt;      //批价进程数量
    public int iBlockSettCnt;   //结算进程数量
    public int iTicketIndbCnt;  //入库进程数量

    public String system;

    @Override
    public String toString() {
        return "TaskStatisticalData{" +
                "msg_type='" + msg_type + '\'' +
                "，type='" + type + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", lTaskTotal=" + lTaskTotal +
                ", lRecordTotal=" + lRecordTotal +
                ", lStartCnt=" + lStartCnt +
                ", lEndCnt=" + lEndCnt +
                ", lNormalCnt=" + lNormalCnt +
                ", lPreCnt=" + lPreCnt +
                ", lAbnCnt=" + lAbnCnt +
                ", lOtherCnt=" + lOtherCnt +
                ", lRepeatCnt=" + lRepeatCnt +
                ", lErrorCnt=" + lErrorCnt +
                ", lPerl=" + lPerl +
                ", lBackTaskCnt=" + lBackTaskCnt +
                ", lBackRecordCnt=" + lBackRecordCnt +
                ", lFaileTaskCnt=" + lFaileTaskCnt +
                ", lFaileRecordCnt=" + lFaileRecordCnt +
                ", iDbInceptCnt=" + iDbInceptCnt +
                ", iPreProc2Cnt=" + iPreProc2Cnt +
                ", iFilterCnt=" + iFilterCnt +
                ", iRatingCnt=" + iRatingCnt +
                ", iBlockSettCnt=" + iBlockSettCnt +
                ", iTicketIndbCnt=" + iTicketIndbCnt +
                ", system='" + system + '\'' +
                '}';
    }

    public String toMsg() {
        String str;
        if ("ISS".equals(system)) {
            str = String.format("业务类型:%s,起止时间:%s ~ %s,总任务数:%-5d,总记录数:%-8d,输入记录数:%-8d,输出记录数:%-8d,正常单:%-8d,预结单:%-8d,异常单:%-8d,不结算单:%-8d,重单:%-8d,错单:%-8d,平均性能:%-4d(条/秒)," +
                            "接收:%-2d个,预处理:%-2d个,排重:%-2d个,批价:%-2d个,结算:%-2d个,入库：%-2d个",
                    type, startDate, endDate.substring(11), lTaskTotal, lRecordTotal, lStartCnt, lEndCnt, lNormalCnt, lPreCnt, lAbnCnt, lOtherCnt, lRepeatCnt, lErrorCnt, lPerl, iDbInceptCnt, iPreProc2Cnt, iFilterCnt, iRatingCnt, iBlockSettCnt, iTicketIndbCnt);
        } else {
            str = String.format("业务类型:%s,起止时间:%s ~ %s,总任务数:%-5d,总记录数:%-8d,输入记录数:%-8d,输出记录数:%-8d,正常单:%-8d,预结单:%-8d,异常单:%-8d,不结算单:%-8d,错单:%-8d,平均性能:%-4d(条/秒)," +
                            "接收:%-2d个,预处理:%-2d个,批价:%-2d个,入库：%-2d个",
                    type, startDate, endDate.substring(11), lTaskTotal, lRecordTotal, lStartCnt, lEndCnt, lNormalCnt, lPreCnt, lAbnCnt, lOtherCnt, lErrorCnt, lPerl, iDbInceptCnt, iPreProc2Cnt, iRatingCnt, iTicketIndbCnt);
        }

        if (lBackTaskCnt > 0) {
            str += ", 积压任务数=" + String.format("%-6d", lBackTaskCnt) + ", 积压话单数=" + String.format("%-6d", lBackRecordCnt);
        }
        if (lFaileTaskCnt > 0) {
            str += ", 失败任务数=" + String.format("%-6d", lFaileTaskCnt) + ", 失败话单数=" + String.format("%-6d", lFaileRecordCnt);
        }

        return str;
    }

    public String toMsgOld() {
        String msg = "业务类型='" + type + '\'' +
                ", 起止时间='" + startDate + " ~ " + endDate.substring(11) + '\'' +
                ", 总任务数=" + String.format("%-5d", lTaskTotal) +
                ", 总记录数=" + String.format("%-8d", lRecordTotal) +
                ", 输入记录数=" + String.format("%-8d", lStartCnt) +
                ", 输出记录数=" + String.format("%-8d", lEndCnt) +
                ", 正常单=" + String.format("%-8d", lNormalCnt) +
                ", 预结单=" + String.format("%-8d", lPreCnt) +
                ", 异常单=" + String.format("%-8d", lAbnCnt) +
                ", 不结算单=" + String.format("%-8d", lOtherCnt);
        if ("ISS".equals(system)) {
            msg += ", 重单=" + String.format("%-8d", lRepeatCnt);
        }

        msg += ", 错单=" + String.format("%-8d", lErrorCnt) + ", 平均性能=" + String.format("%-4d", lPerl) + ", 接收=" + String.format("%-2d", iDbInceptCnt) + "个, 预处理=" + String.format("%-2d", iPreProc2Cnt);

        if ("ISS".equals(system)) {
            msg += "个, 排重=" + String.format("%2d", iFilterCnt) + "个, 批价=" + String.format("%2d", iRatingCnt) + "个, 结算=" + String.format("%2d", iBlockSettCnt) + "个, 入库=" + String.format("%2d", iTicketIndbCnt) + "个";
        } else {
            msg += "个, 批价=" + String.format("%2d", iRatingCnt) + "个, 入库=" + String.format("%2d", iTicketIndbCnt) + "个";
        }

        if (lBackTaskCnt > 0) {
            msg += ", 积压任务数=" + String.format("%-6d", lBackTaskCnt) + ", 积压话单数=" + String.format("%-6d", lBackRecordCnt);
        }
        if (lFaileTaskCnt > 0) {
            msg += ", 失败任务数=" + String.format("%-6d", lFaileTaskCnt) + ", 失败话单数=" + String.format("%-6d", lFaileRecordCnt);
        }
        return msg;
    }
}
