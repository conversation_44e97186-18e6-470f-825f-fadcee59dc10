package com.itco.dispatch.entity.entitydef;

public class TpTaskRecordLog {
    int task_id; //任务名称
    int batch_id;//批次号
    int module_id;//模块标识
    Long process_id;//进程标识
    int order_id;//流程标识
    int perl;//性能
    int start_cnt;//输入记录数
    int end_cnt;//输出记录数
    int error_cnt;//出错单
    int repeat_cnt;//重单
    int normal_cnt;//正常单
    int abn_cnt;//异常单
    int other_cnt;//不结算话单
    int pre_cnt;//预结单
    String start_time;//开始时间
    String end_time;//结束时间
    int host_id;//主机标识

    public int getTask_id() {
        return task_id;
    }

    public void setTask_id(int task_id) {
        this.task_id = task_id;
    }

    public int getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(int batch_id) {
        this.batch_id = batch_id;
    }

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public Long getProcess_id() {
        return process_id;
    }

    public void setProcess_id(Long process_id) {
        this.process_id = process_id;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public int getPerl() {
        return perl;
    }

    public void setPerl(int perl) {
        this.perl = perl;
    }

    public int getStart_cnt() {
        return start_cnt;
    }

    public void setStart_cnt(int start_cnt) {
        this.start_cnt = start_cnt;
    }

    public int getEnd_cnt() {
        return end_cnt;
    }

    public void setEnd_cnt(int end_cnt) {
        this.end_cnt = end_cnt;
    }

    public int getError_cnt() {
        return error_cnt;
    }

    public void setError_cnt(int error_cnt) {
        this.error_cnt = error_cnt;
    }

    public int getRepeat_cnt() {
        return repeat_cnt;
    }

    public void setRepeat_cnt(int repeat_cnt) {
        this.repeat_cnt = repeat_cnt;
    }

    public int getNormal_cnt() {
        return normal_cnt;
    }

    public void setNormal_cnt(int normal_cnt) {
        this.normal_cnt = normal_cnt;
    }

    public int getAbn_cnt() {
        return abn_cnt;
    }

    public void setAbn_cnt(int abn_cnt) {
        this.abn_cnt = abn_cnt;
    }

    public int getOther_cnt() {
        return other_cnt;
    }

    public void setOther_cnt(int other_cnt) {
        this.other_cnt = other_cnt;
    }

    public int getPre_cnt() {
        return pre_cnt;
    }

    public void setPre_cnt(int pre_cnt) {
        this.pre_cnt = pre_cnt;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public int getHost_id() {
        return host_id;
    }

    public void setHost_id(int host_id) {
        this.host_id = host_id;
    }


    @Override
    public String toString() {
        return "TpTaskRecordLog{" +
                "task_id=" + task_id +
                ", batch_id=" + batch_id +
                ", module_id=" + module_id +
                ", process_id=" + process_id +
                ", order_id=" + order_id +
                ", perl=" + perl +
                ", start_cnt=" + start_cnt +
                ", end_cnt=" + end_cnt +
                ", error_cnt=" + error_cnt +
                ", repeat_cnt=" + repeat_cnt +
                ", normal_cnt=" + normal_cnt +
                ", abn_cnt=" + abn_cnt +
                ", other_cnt=" + other_cnt +
                ", pre_cnt=" + pre_cnt +
                ", start_time='" + start_time + '\'' +
                ", end_time='" + end_time + '\'' +
                ", host_id=" + host_id +
                '}';
    }
}
