package com.itco.dispatch.entity.entitydef;

/**
 * @ClassName: TaskMessageEntity
 * @Description: 任务消息类
 * <AUTHOR>
 * @Date 2021/9/14
 * @Version 1.0
 */
public class TaskMessageEntity {
    String key;
    String msg;

    public TaskMessageEntity(String key, String msg){
        this.key=key;
        this.msg=msg;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "TaskMessageEntity{" +
                "key='" + key + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
