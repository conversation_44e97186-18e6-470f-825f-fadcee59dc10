package com.itco.dispatch.entity.entitydef;

public class TpTaskCrontabDef {

    int crontab_id;
    String crontab_code;
    String crontab_name;
    int cluster_id;
    int repeat_cnt;
    int repeat_secend;
    String miss_mode;
    int process_id;

    public int getCrontab_id() {
        return crontab_id;
    }

    public void setCrontab_id(int crontab_id) {
        this.crontab_id = crontab_id;
    }

    public String getCrontab_code() {
        return crontab_code;
    }

    public void setCrontab_code(String crontab_code) {
        this.crontab_code = crontab_code;
    }

    public String getCrontab_name() {
        return crontab_name;
    }

    public void setCrontab_name(String crontab_name) {
        this.crontab_name = crontab_name;
    }

    public int getCluster_id() {
        return cluster_id;
    }

    public void setCluster_id(int cluster_id) {
        this.cluster_id = cluster_id;
    }

    public int getRepeat_cnt() {
        return repeat_cnt;
    }

    public void setRepeat_cnt(int repeat_cnt) {
        this.repeat_cnt = repeat_cnt;
    }

    public int getRepeat_secend() {
        return repeat_secend;
    }

    public void setRepeat_secend(int repeat_secend) {
        this.repeat_secend = repeat_secend;
    }

    public String getMiss_mode() {
        return miss_mode;
    }

    public void setMiss_mode(String miss_mode) {
        this.miss_mode = miss_mode;
    }

    public int getProcess_id() {
        return process_id;
    }

    public void setProcess_id(int process_id) {
        this.process_id = process_id;
    }

    @Override
    public String toString() {
        return "TpTaskCrontabDef{" +
                "crontab_id=" + crontab_id +
                ", crontab_code='" + crontab_code + '\'' +
                ", crontab_name='" + crontab_name + '\'' +
                ", cluster_id=" + cluster_id +
                ", repeat_cnt=" + repeat_cnt +
                ", repeat_secend=" + repeat_secend +
                ", miss_mode='" + miss_mode + '\'' +
                ", process_id=" + process_id +
                '}';
    }
}
