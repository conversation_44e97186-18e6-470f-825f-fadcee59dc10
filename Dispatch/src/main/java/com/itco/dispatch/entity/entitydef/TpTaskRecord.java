package com.itco.dispatch.entity.entitydef;

import lombok.Data;

import java.io.Serializable;

@Data
public class TpTaskRecord implements Serializable {
    int task_id;  //任务标识
    int batch_id; //批次号
    int cluster_id; //集群标识
    int host_id;    //主机标识
    int order_id;   //流程标识
    int module_id;  //模块标识
    long process_id; //进程标识
    String mode;     //模块名称
    String type;     //进程业务类型
    String task_name; //任务名称
    String file_name; //文件名称
    String local_path;//文件路径
    int record_cnt;//话单数量
    String state; //话单状态
    String create_date;//创建时间
    String update_date;//更新时间
    String element_str;//任务要素串
    Long curr_time;//时间戳
    int perl;//处理性能
    String msg_body;//消息包
    int redo_cnt;//重做次数
    int warn_cnt;//告警次数


    public String toElement() {
        return "{" +
                "host_id:" + host_id +
                ",batch_id:" + batch_id +
                ",flow_id:" + order_id +
                ",module_id:" + module_id +
                ",process_id:" + process_id +
                ",record_cnt:" + record_cnt +
                ",state:" + state +
                ",perl:" + perl +
                ",type:" + type +
                ",update_date:" + update_date +
                ",redo_cnt:" + redo_cnt +
                "}";
    }

    @Override
    public String toString() {
        return "TpTaskRecordDef{" +
                "task_id=" + task_id +
                ", batch_id=" + batch_id +
                ", cluster_id=" + cluster_id +
                ", host_id=" + host_id +
                ", order_id=" + order_id +
                ", module_id=" + module_id +
                ", process_id=" + process_id +
                ", mode='" + mode + '\'' +
                ", type='" + type + '\'' +
                ", task_name='" + task_name + '\'' +
                ", local_path='" + local_path + '\'' +
                ", record_cnt=" + record_cnt +
                ", state='" + state + '\'' +
                ", create_date='" + create_date + '\'' +
                ", update_date='" + update_date + '\'' +
                ", perl=" + perl +
                ",redo_cnt:" + redo_cnt +
                '}';
    }
}