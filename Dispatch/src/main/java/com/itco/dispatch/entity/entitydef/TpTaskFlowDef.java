package com.itco.dispatch.entity.entitydef;

import java.util.Objects;

public class TpTaskFlowDef {
    int flow_id;
    String mode;
    int module_id;
    int process_module_id;
    int order_id;
    String state;
    String remark;

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public int getProcess_module_id() {
        return process_module_id;
    }

    public void setProcess_module_id(int process_module_id) {
        this.process_module_id = process_module_id;
    }

    public int getOrder_id() {
        return order_id;
    }

    public void setOrder_id(int order_id) {
        this.order_id = order_id;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TpTaskFlowDef{" +
                "flow_id=" + flow_id +
                ", mode='" + mode + '\'' +
                ", module_id=" + module_id +
                ", process_module_id=" + process_module_id +
                ", order_id=" + order_id +
                ", state='" + state + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TpTaskFlowDef that = (TpTaskFlowDef) o;
        return order_id == that.order_id;
    }

    @Override
    public int hashCode() {
        return Objects.hash(flow_id);
    }
}
