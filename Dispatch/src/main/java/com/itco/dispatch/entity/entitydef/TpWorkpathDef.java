package com.itco.dispatch.entity.entitydef;

public class TpWorkpathDef {

    int module_id;
    String local_input_path;
    String local_output_path;
    String hdfs_path;
    String hdfs_path_no_first;

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public String getLocal_input_path() {
        return local_input_path;
    }

    public void setLocal_input_path(String local_input_path) {
        this.local_input_path = local_input_path;
    }

    public String getLocal_output_path() {
        return local_output_path;
    }

    public void setLocal_output_path(String local_output_path) {
        this.local_output_path = local_output_path;
    }

    public String getHdfs_path() {
        return hdfs_path;
    }

    public void setHdfs_path(String hdfs_path) {
        this.hdfs_path = hdfs_path;
    }

    public String getHdfs_path_no_first() {
        return hdfs_path_no_first;
    }

    public void setHdfs_path_no_first(String hdfs_path_no_first) {
        this.hdfs_path_no_first = hdfs_path_no_first;
    }

    public String toString() {
        return "TpWorkpathDef{" +
                "module_id=" + module_id +
                ", local_input_path='" + local_input_path + '\'' +
                ", local_output_path='" + local_output_path + '\'' +
                ", hdfs_path='" + hdfs_path + '\'' +
                ", hdfs_path_no_first='" + hdfs_path_no_first + '\'' +
                '}';
    }
}
