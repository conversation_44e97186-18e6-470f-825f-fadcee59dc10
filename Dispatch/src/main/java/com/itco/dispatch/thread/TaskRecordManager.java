package com.itco.dispatch.thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dispatch.entity.EntityMgr;
import com.itco.dispatch.entity.entitydef.ErrorType;
import com.itco.dispatch.entity.entitydef.*;
import com.itco.entity.common.*;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class TaskRecordManager implements Runnable {
    static Log log = LogFactory.getLog(TaskRecordManager.class);
    MessageManager messageManager;
    ProcessManager processManager;

    // 任务状态队列
    LinkedHashMap<String, TpTaskRecord> recordLinkMap = new LinkedHashMap<>();
    LinkedHashMap<String, MsgBody.TaskBody> msgLinkMap = new LinkedHashMap<>();
    LinkedHashMap<String, List<TpTaskRecordLog>> recordLogLinkMap = new LinkedHashMap<>();
    Map<String, TaskStatisticalData> taskStaticMap = new HashMap<>();

    //业务处理第一个模块和最后一个模块信息
    Map<String, List<TpTaskFlowDef>> flowListMap = new HashMap<>();

    int m_iTaskId = 0;
    int lastFclTaskId = 0;

    Long lStartTimeMillis = 0L;
    String m_SmartMode = TASK_FLOW_MODE.SMART.mode;
    DispatchDef dispatchDef = new DispatchDef();

    EntityMgr entityMgr = null;
    ZkClientApi zkClientApi = null;
    Common common = null;

    final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public boolean init() {
        log.info("话单任务管理线程初始化 start");

        String propFileName = zkClientApi.getModuleCode() + ".properties";
        Properties dispProp = zkClientApi.getPropertiesFromZK(propFileName);

        if (dispProp.getProperty("RedoTaskErrorMsgKey") != null) {
            String tmp = dispProp.getProperty("RedoTaskErrorMsgKey");
            log.info("RedoTaskErrorMsgKey:" + tmp);
            dispatchDef.setRedoTaskErrorMsgKeyList(Arrays.asList(tmp.split(",")));
            for (String str : dispatchDef.getRedoTaskErrorMsgKeyList()) {
                log.info("str :" + str);
            }
        }

        if (dispProp.getProperty("redoCount") != null) {
            dispatchDef.setRedoCount(Integer.parseInt(dispProp.getProperty("redoCount")));
        }
        log.info("redoCount:" + dispatchDef.getRedoCount());

        if (dispProp.getProperty("out_tasklog_interval") != null) {
            dispatchDef.setOUT_TASKLOG_INTERVAL(Long.valueOf(dispProp.getProperty("out_tasklog_interval")));
        } else {
            if ("ISS".equals(common.getSSystem())) {
                dispatchDef.setOUT_TASKLOG_INTERVAL(3600 * 8L);
            }
        }

        // 模块工作目录配置
        Map<String, TpWorkpathDef> tpWorkpathDefMap = entityMgr.getmTpWorkPath();
        for (Map.Entry<String, TpWorkpathDef> transfer : tpWorkpathDefMap.entrySet()) {
            log.info("transfer:" + transfer.getValue().toString());
        }

        // 任务状态配置
        for (TpTaskDomainDef domain : entityMgr.getmTpTaskDomain()) {
            log.info("domain:" + domain.toString());
        }

        for (TpTaskFlowDef flow : entityMgr.getmTaskFlowList()) {
            List<TpTaskFlowDef> flowList = flowListMap.get(flow.getMode());
            if (flowList == null) {
                flowList = new ArrayList<>();
                flowList.add(flow);
                flowListMap.put(flow.getMode(), flowList);
            } else {
                flowList.add(flow);
            }
        }

        for (Map.Entry<String, List<TpTaskFlowDef>> itertor : flowListMap.entrySet()) {
            for (TpTaskFlowDef tpTaskFlowDef : itertor.getValue()) {
                log.info(itertor.getKey() + ":" + tpTaskFlowDef.toString());
            }
        }

        //log.info("传输模块：" + transferModule.toString());
        log.info("加载的未完成的任务数:" + entityMgr.getmTpTaskRecord().size());
        if (common.getIDebug() > 0) {
            for (Map.Entry<String, TpTaskRecord> map : entityMgr.getmTpTaskRecord().entrySet()) {
                log.info(map.toString());
            }
        }

        m_iTaskId = entityMgr.getM_TaskId() + 1;
        lastFclTaskId = m_iTaskId;

        // 任务数据库的任务列表
        Map<String, TpTaskRecord> taskMap = entityMgr.getmTpTaskRecord();
        for (Map.Entry<String, TpTaskRecord> task : taskMap.entrySet()) {
            if (task.getValue().getState().equals(RECORD_TASK_STATE.SSS.state) ) {
                continue;
            }

            recordLinkMap.put(task.getKey(), task.getValue());

            MsgBody.TaskBody body = JSONObject.parseObject(task.getValue().getMsg_body(), MsgBody.TaskBody.class);
            if (body != null) {
                msgLinkMap.put(task.getKey(), body);
            }
        }

        lStartTimeMillis = System.currentTimeMillis();
        log.info("话单任务管理线程初始化 close,m_iTaskId:" + m_iTaskId + ",m_SmartMode" + m_SmartMode);
        log.info(dispatchDef.toString());
        return true;
    }

    @Override
    public void run() {
        log.info("话单任务管理线程 启动");
        try {
            while (!Factory.isbWhileFlag()) {
                //////////////////////////////////消息处理////////////////////////////////////////////////////
                // 接收消息
                String taskMsg = messageManager.taskMsgReceInterface();
                if (taskMsg != null) {
                    //消息解析，任务创建，任务状态变更
                    if (!Message_Process_Interface(taskMsg)) {
                        log.info("消息处理失败，消息：" + taskMsg);
                        continue;
                    }
                }

                // 处理完成的任务，状态顺序流转，上传完成的要切换模块;任务分发
                if (!Task_State_Process_Interface()) {
                    log.info("任务状态处理失败,任务数：" + recordLinkMap.size());
                }

                // 根据进程空闲来分配任务，做到任务被处理顺序按照任务ID顺序
                if (!Process_State_Distribute_Task()) {
                    log.info("任务分发失败");
                }

                ////////////////////////////////任务监控///////////////////////////////////////////////////////
                // 任务监控，如果处理中任务的进程状态变为关闭，或者异常关闭，回收任务。
                if (!Process_State_Monitor_Task_Rollback()) {
                    log.info("对处理中的任务进行进程监控失败");
                }

                // 定时输出任务处理日志
                if (!OutTaskStaticLogToTable()) {
                    log.info("输出任务统计日志失败");
                }

                // 任务积压，如果话单处理模块的任务积压达到阀值处理，考虑启动新的处理节点

                Thread.sleep(10);
            }
        } catch (InterruptedException e) {
            log.error("InterruptedException:" + e.getMessage());
            Thread.currentThread().interrupt();
        }
        Factory.setbWhileFlag(true);
        log.info("话单任务管理线程 关闭");
    }

    boolean switchTaskNextFlow(TpTaskRecord task) {
        TpTaskFlowDef tmpFlow = new TpTaskFlowDef();
        tmpFlow.setOrder_id(task.getOrder_id());

        int indexOrderId = flowListMap.get(task.getMode()).indexOf(tmpFlow);
        if (indexOrderId == -1) {
            log.info("不存在的mode:" + task.getMode() + ",order_id:" + task.getOrder_id() + ",TASK_ID:" + task.getTask_id());
            return false;
        } else if (indexOrderId + 1 < flowListMap.get(task.getMode()).size()) {
            TpTaskFlowDef flow = flowListMap.get(task.getMode()).get(indexOrderId + 1);

            task.setOrder_id(flow.getOrder_id());
            task.setModule_id(flow.getModule_id());
            task.setState(flow.getState());
        } else {
            task.setState(RECORD_TASK_STATE.SSS.state);
        }

        if (task.getState().equals("DCL")) {
            //task.setProcess_id(0L);
        } else if (task.getState().equals("SCL")) {
            Long lTime = System.currentTimeMillis() - task.getCurr_time();
            int iPerl = (int) (task.getRecord_cnt() * 1000 / (lTime <= 0 ? 1 : lTime));
            task.setPerl(iPerl);
            task.setCurr_time(System.currentTimeMillis());

            TpProcess tpProcess = entityMgr.getmTpProcessMap().get(String.valueOf(task.getProcess_id()));
            if (tpProcess != null) {
                tpProcess.setPerl(iPerl);
            }
        } else if (task.getState().equals("SSS")) {
            task.setPerl(0);
            task.setModule_id(0);
            task.setProcess_id(0);
            task.setCurr_time(0L);

            List<TpTaskRecordLog> recordLogs = recordLogLinkMap.get(String.valueOf(task.getTask_id()));
            if (recordLogs != null) {
                entityMgr.createTpTaskRecordLogToTable(recordLogs);
            }

            // 进程任务处理情况统计
            if (dispatchDef.getOUT_TASKLOG_INTERVAL() > 0) {
                String type = "null";
                if (task.getType() != null) {
                    type = task.getType();
                }

                TaskStatisticalData taskStatisticalData = taskStaticMap.get(type);
                if (taskStatisticalData == null) {
                    taskStatisticalData = new TaskStatisticalData();
                    taskStatisticalData.system = common.getSSystem();
                    taskStatisticalData.type = type;
                    taskStatisticalData.msg_type = "info";

                    taskStaticMap.put(type, taskStatisticalData);
                }

                taskStatisticalData.lRecordTotal += task.getRecord_cnt();
                taskStatisticalData.lTaskTotal++;

                if (recordLogs != null) {
                    taskStatisticalData.lStartCnt += recordLogs.get(0).getStart_cnt();
                    taskStatisticalData.lEndCnt += recordLogs.get(recordLogs.size() - 1).getEnd_cnt();
                    taskStatisticalData.lNormalCnt += recordLogs.get(recordLogs.size() - 1).getNormal_cnt();
                    taskStatisticalData.lPreCnt += recordLogs.get(recordLogs.size() - 1).getPre_cnt();
                    taskStatisticalData.lAbnCnt += recordLogs.get(recordLogs.size() - 1).getAbn_cnt();
                    taskStatisticalData.lOtherCnt += recordLogs.get(recordLogs.size() - 1).getOther_cnt();
                    taskStatisticalData.lRepeatCnt += recordLogs.get(recordLogs.size() - 1).getRepeat_cnt();

                    for (TpTaskRecordLog taskLogTmp : recordLogs) {
                        taskStatisticalData.lErrorCnt += taskLogTmp.getError_cnt();
                    }
                }
            }

            log.info("switchTaskNextFlow() SSS ;task_id:" + task.getTask_id() + ",全流程完成");
        } else {
            task.setCurr_time(System.currentTimeMillis());
        }

        task.setUpdate_date(Factory.getSystemDateStr());
        task.setElement_str(task.getElement_str() + task.toElement());
        entityMgr.saveTaskToTable(task);

        return true;
    }

    boolean switchTaskFrontFlow(TpTaskRecord task) {
        TpTaskFlowDef tmpFlow = new TpTaskFlowDef();
        tmpFlow.setOrder_id(task.getOrder_id());

        int indexOrderId = flowListMap.get(task.getMode()).indexOf(tmpFlow);
        if (indexOrderId == -1) {
            log.info("不存在的mode:" + task.getMode() + ",order_id:" + task.getOrder_id() + ",TASK_ID:" + task.getTask_id());
            return false;
        } else {
            TpTaskFlowDef flow = flowListMap.get(task.getMode()).get(indexOrderId - 1);

            task.setOrder_id(flow.getOrder_id());
            task.setModule_id(flow.getModule_id());
            task.setState(flow.getState());
            task.setElement_str(task.getElement_str() + task.toElement());
        }

        task.setUpdate_date(Factory.getSystemDateStr());
        entityMgr.saveTaskToTable(task);
        return true;
    }

    /*
     * 采集上报的文件，创建话单任务和传输任务
     * 消息类型：采集处理完成*/
    public boolean createRecordTask(MsgBody.DbInceptBody body) {
        log.info("处理采集模块请求消息 body:" + body.toString());

        TpTaskRecord task = new TpTaskRecord();
        task.setTask_id(m_iTaskId++);
        task.setTask_name(body.TASK_NAME);
        task.setCluster_id(1);
        task.setFile_name(body.FILE_NAME);
        task.setHost_id(Integer.valueOf(body.HOST_ID));
        task.setLocal_path(body.LOCAL_PATH);
        task.setRecord_cnt(Integer.valueOf(body.RECORD_CNT));
        task.setProcess_id(Integer.valueOf(body.PROCESS_ID));
        if (body.PERL != null) {
            task.setPerl(Integer.valueOf(body.PERL));
        }
        task.setCreate_date(Factory.getSystemDateStr());
        task.setUpdate_date(Factory.getSystemDateStr());

        MsgBody.TaskBody taskBodyTmp = new MsgBody.TaskBody();
        taskBodyTmp.dbInceptBody = body;
        //task.setMsg_body("DbInceptBody" + JSONObject.toJSONString(body));
        task.setMsg_body(JSONObject.toJSONString(taskBodyTmp));
        task.setMode(body.FIRST);
        task.setType(body.TYPE);
        if (null != body.BATCH_ID) {
            task.setBatch_id(Integer.valueOf(body.BATCH_ID));
        } else {
            body.BATCH_ID = "0";
        }

        task.setCurr_time(System.currentTimeMillis());
        task.setRedo_cnt(0);

        if (flowListMap.get(task.getMode()) == null) {
            log.error("DCOOS_TP_TASK_FLOW未配置:" + task.getMode() + " 的处理流程。");
            task.setState("FCJ");
        } else {
            task.setOrder_id(flowListMap.get(task.getMode()).get(0).getOrder_id());
            task.setState(flowListMap.get(task.getMode()).get(0).getState());
        }

        task.setModule_id(Integer.valueOf(body.PROCESS_ID.substring(0, 4)));
        task.setProcess_id(Integer.valueOf(body.PROCESS_ID));
        task.setElement_str(task.toElement());

        Boolean ret = entityMgr.createTaskToTable(task);
        if (!ret) {
            log.info("创建任务失败," + task.toElement());
            return false;
        }
        log.info("任务创建成功:" + task.toElement());

        processManager.switchProcessState(body.PROCESS_ID, null, task.getPerl());

        //创建任务加入任务管理队列
        recordLinkMap.put(String.valueOf(task.getTask_id()), task);

        //创建任务对应的来往消息包存放类
        MsgBody.TaskBody taskBody = new MsgBody.TaskBody();
        taskBody.dbInceptBody = body;
        taskBody.standardBody = new ArrayList<>();
        msgLinkMap.put(String.valueOf(task.getTask_id()), taskBody);

        // 添加任务创建日志
        TpTaskRecordLog recordLog = new TpTaskRecordLog();
        recordLog.setTask_id(task.getTask_id());
        recordLog.setBatch_id(task.getBatch_id());
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        recordLog.setStart_time(dateformat.format(task.getCurr_time()));
        recordLog.setModule_id(task.getModule_id());
        recordLog.setProcess_id(task.getProcess_id());
        recordLog.setOrder_id(task.getOrder_id());
        recordLog.setStart_cnt(task.getRecord_cnt());
        recordLog.setEnd_cnt(task.getRecord_cnt());
        recordLog.setRepeat_cnt(0);
        recordLog.setError_cnt(0);
        recordLog.setNormal_cnt(task.getRecord_cnt());
        recordLog.setPre_cnt(0);
        recordLog.setAbn_cnt(0);
        recordLog.setOther_cnt(0);
        recordLog.setHost_id(Integer.parseInt(String.valueOf(task.getProcess_id()).substring(4, 6)));
        recordLog.setPerl(task.getPerl());
        recordLog.setEnd_time(dateformat.format(task.getCurr_time()));

        List<TpTaskRecordLog> item = recordLogLinkMap.get(String.valueOf(task.getTask_id()));
        if (item == null) {
            item = new ArrayList<>();
            item.add(recordLog);
            recordLogLinkMap.put(String.valueOf(task.getTask_id()), item);
        } else {
            item.add(recordLog);
        }
        return true;
    }


    /*处理接收获取可用任务数*/
    public boolean processDbInceptResponse(MsgBody.DbInceptBody body) {
        log.info("MSG_TYPE:" + body.MSG_TYPE + ",MODE:" + body.FIRST + ",TYPE:" + body.TYPE + " ,PROCESS_ID:" + body.PROCESS_ID);
        int iTaskCount = 0;
        for (Map.Entry<String, TpTaskRecord> task : recordLinkMap.entrySet()) {
            if (StringUtils.equals(body.FIRST, task.getValue().getMode()) && StringUtils.equals(body.TYPE, task.getValue().getType())) {
                if ((RECORD_TASK_STATE.OCL.state.equals(task.getValue().getState()) ||
                        RECORD_TASK_STATE.DCL.state.equals(task.getValue().getState()))) {
                    iTaskCount++;
                }
            }
        }

       /* int iDbIncept = 0;
        HashSet<Integer> tmpModuleSet = new HashSet<>();
        for (TpTaskFlowDef tpTaskFlowDef : flowListMap.get(body.MODE)) {
            if (tpTaskFlowDef.getModule_id() == 1011 || tpTaskFlowDef.getModule_id() == 2011 || tpTaskFlowDef.getModule_id() == 3011) {
                if (String.valueOf(tpTaskFlowDef.getModule_id()).equals(body.MODULE_ID)) {
                    iDbIncept++;
                }
            } else {
                tmpModuleSet.add(tpTaskFlowDef.getModule_id());
            }
        }

        boolean tmpFlag = true;
        StringBuffer tmpFileName = new StringBuffer();
        tmpFileName.append("部署方式:" + common.getDeployment() + ",业务类型:" + body.TYPE + "->");
        for (Integer tmpModuleId : tmpModuleSet) {
            int iProcessCount = 0;
            for (Map.Entry<String, TpProcess> process : entityMgr.getmTpProcessMap().entrySet()) {
                if (common.getDeployment().equals("CONTAINER")) {
                    if (tmpModuleId == process.getValue().getModule_id() && StringUtils.equals(body.TYPE, process.getValue().getType()) && (ProcessManager.PROCESS_STATE.TB.state.equals(process.getValue().getStatus()) || ProcessManager.PROCESS_STATE.TC.state.equals(process.getValue().getStatus()) || ProcessManager.PROCESS_STATE.TE.state.equals(process.getValue().getStatus()))) {
                        iProcessCount++;
                    }
                } else if (common.getDeployment().equals("VM")) {
                    if (tmpModuleId == process.getValue().getModule_id() && StringUtils.equals(body.TYPE, process.getValue().getType()) && (ProcessManager.PROCESS_STATE.TB.state.equals(process.getValue().getStatus()) || ProcessManager.PROCESS_STATE.TC.state.equals(process.getValue().getStatus()) || ProcessManager.PROCESS_STATE.TC.state.equals(process.getValue().getStatus()))) {
                        String tmpDbInceptHostId = body.PROCESS_ID.substring(3, 4);
                        String tmpProcessHostId = String.valueOf(process.getValue().getProcess_id()).substring(3, 4);
                        if (StringUtils.equals(tmpDbInceptHostId, tmpProcessHostId)) {
                            iProcessCount++;
                        }
                    }
                } else {
                    log.error("无法识别的部署方式:" + common.getDeployment());
                }
            }
            if (iProcessCount == 0) {
                tmpFlag = false;
            }
            tmpFileName.append("[" + "模块标识:" + tmpModuleId + ",数量:" + iProcessCount + ",结果:" + (iProcessCount > 0) + "]");
        }*/

        //log.info("iTaskCount:" + iTaskCount + ",tmpFlag:" + tmpFlag );
        log.info("iTaskCount:" + iTaskCount);
        body.MSG_TYPE = "RESPONSE";
        body.UPDATE_DATE = Factory.getSystemDateStr();
        body.RECORD_CNT = String.valueOf(iTaskCount);
        //body.FILE_NAME = tmpFileName.toString();
        body.PERL = String.valueOf(dispatchDef.getMAX_TASK_COUNT() - iTaskCount);

        sendDbInceptUseTaskCnt(body);
        return true;
    }


    /* 标准话单应答任务处理
     *消息类型：业务处理完成*/
    public boolean processRecordTaskResponse(MsgBody.StandardBody body) {
        log.info("处理业务模块应答消息 ," + body.toString());

        MsgBody.TaskBody taskBody = msgLinkMap.get(body.TASK_ID);
        if (taskBody != null) {
            taskBody.standardBody.add(body);
        }

        TpTaskRecord task = recordLinkMap.get(body.TASK_ID);
        if (task == null) {
            log.info("forwardRecordTask 不存在的TASK_ID：" + body.toString());
            return false;
        }
        if (!String.valueOf(task.getProcess_id()).equals(body.PROCESS_ID)) {
            log.error("无法识别的应答包，任务" + task.getTask_id() + " 的当前处理进程标识和应答包的进程标识不一致。" + task.getProcess_id() + ":" + body.PROCESS_ID);
            return false;
        }

        MsgBody.TaskBody taskBodyTmp = JSONObject.parseObject(task.getMsg_body(), MsgBody.TaskBody.class);
        if (taskBodyTmp.standardBody == null) {
            taskBodyTmp.standardBody = new ArrayList<>();
        }
        taskBodyTmp.standardBody.add(body);
        task.setMsg_body(JSONObject.toJSONString(taskBodyTmp));

        int perl = 0;
        if (body.STATE.equals("OK")) {
            TpTaskRecordLog recordLog = new TpTaskRecordLog();
            recordLog.setTask_id(task.getTask_id());
            recordLog.setBatch_id(task.getBatch_id());
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            recordLog.setStart_time(dateformat.format(task.getCurr_time()));
            recordLog.setModule_id(task.getModule_id());
            recordLog.setProcess_id(task.getProcess_id());
            recordLog.setOrder_id(task.getOrder_id());
            recordLog.setStart_cnt(task.getRecord_cnt());
            recordLog.setEnd_cnt(Integer.valueOf(body.RECORD_CNT));
            recordLog.setRepeat_cnt(Integer.valueOf(body.REPEAT_CNT));
            recordLog.setError_cnt(Integer.valueOf(body.ERROR_CNT));
            recordLog.setNormal_cnt(Integer.valueOf(body.NORMAL_CNT));
            recordLog.setPre_cnt(Integer.valueOf(body.PRE_CNT));
            recordLog.setAbn_cnt(Integer.valueOf(body.ABN_CNT));
            recordLog.setOther_cnt(Integer.valueOf(body.OTHER_CNT));
            recordLog.setHost_id(Integer.parseInt(String.valueOf(task.getProcess_id()).substring(4, 6)));

            task.setFile_name(body.FILE_NAME);
            task.setRecord_cnt(Integer.parseInt(body.RECORD_CNT));

            // 任务状态修改为下一个状态
            switchTaskNextFlow(task);
            perl = task.getPerl();
            recordLog.setPerl(task.getPerl());
            recordLog.setEnd_time(dateformat.format(task.getCurr_time()));

            List<TpTaskRecordLog> item = recordLogLinkMap.get(String.valueOf(task.getTask_id()));
            if (item == null) {
                item = new ArrayList<>();
                item.add(recordLog);
                recordLogLinkMap.put(String.valueOf(task.getTask_id()), item);
            } else {
                item.add(recordLog);
            }
        } else if (body.STATE.equals("ERROR")) {
            //业务模块任务处理失败
            if (body.ERROR_MSG != null && body.ERROR_MSG.indexOf("[Ljava.lang.StackTraceElement") > -1) {
                // 读取ctgCache话单文件失败，重新设置，处理。
                task.setState(RECORD_TASK_STATE.DCL.state);
                task.setOrder_id(task.getOrder_id() - 1); //状态回滚
                task.setUpdate_date(Factory.getSystemDateStr());

                task.setElement_str(task.getElement_str() + task.toElement());
            } else {
                // 业务模块任务处理异常
                task.setState(RECORD_TASK_STATE.FCL.state);
                task.setUpdate_date(Factory.getSystemDateStr());
                task.setElement_str(task.getElement_str() + task.toElement());

                // 输出告警日志到告警表
                TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.DISPATCH_TASK_DEAL_ERROR, (long) common.getProcess_id());
                tgAlertLog.setLog_desc("告警类型:" + ErrorType.DISPATCH_TASK_DEAL_ERROR + ",进程：" + task.getProcess_id() + ",处理任务ID：" + task.getTask_id() + " 失败。" + task.toString());

                // 插入进程退出的告警日志
                entityMgr.createTgAlterLogToTable(JSON.toJSONString(tgAlertLog));
            }
            entityMgr.saveTaskToTable(task);
        } else {
            log.error("无法识别的任务状态。state:" + body.STATE);
            return false;
        }

        TpProcess proTmp = entityMgr.getmTpProcessMap().get(body.PROCESS_ID);
        if (proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TC.state) ||
                proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TE.state)) {   // 进程状态修改为空闲
            processManager.switchProcessState(body.PROCESS_ID, ProcessManager.PROCESS_STATE.TB.state, perl);
        }

        return true;
    }


    boolean dealRollback(MsgBody.RollbackBody body) {
        TpTaskRecord taskTmp = recordLinkMap.get(body.TASK_ID);
        if (taskTmp == null) {
            log.error("没有找到重做的任务：" + body.TASK_ID);
            return false;
        } else if (!taskTmp.getState().substring(0, 1).equals("F")) {
            log.error("没有找到合法重做的任务：" + body.TASK_ID + ",state:" + taskTmp.getState());
            return false;
        }

        String rbModuleId = body.RB_MODULE_ID;
        if (rbModuleId == null || rbModuleId.equals("0")) {
            List<TpTaskFlowDef> flowList = flowListMap.get(taskTmp.getMode());
            if (flowList == null) {
                log.error("任务当前模块重做mode:" + taskTmp.getMode() + ",无法识别的任务流程");
                return false;
            }
            for (int i = 0; i < flowList.size(); i++) {
                if (flowList.get(i).getOrder_id() == taskTmp.getOrder_id()) {
                    taskTmp.setOrder_id(flowList.get(i - 1).getOrder_id());
                    taskTmp.setState(flowList.get(i - 1).getState());
                    taskTmp.setUpdate_date(Factory.getSystemDateStr());
                    taskTmp.setTask_name("任务当前模块重做");
                    taskTmp.setRedo_cnt(taskTmp.getRedo_cnt() + 1);
                    //保存到数据库
                    entityMgr.saveTaskToTable(taskTmp);
                    break;
                }
            }
        } else {
            boolean flag = false;
            List<TpTaskFlowDef> flowList = flowListMap.get("rollback");
            if (flowList == null) {
                log.error("任务当前模块重做mode:" + taskTmp.getMode() + ",无法识别的任务流程");
                return false;
            }

            for (int i = 0; i < flowList.size(); i++) {
                if (flowList.get(i).getModule_id() == Integer.valueOf(rbModuleId)) {
                    taskTmp.setOrder_id(flowList.get(i).getOrder_id());
                    taskTmp.setState(flowList.get(i).getState());
                    taskTmp.setModule_id(flowList.get(i).getModule_id());
                    taskTmp.setProcess_id(0);
                    taskTmp.setUpdate_date(Factory.getSystemDateStr());
                    taskTmp.setTask_name("任务重做MODULE_ID:" + rbModuleId);
                    taskTmp.setMode(flowList.get(i).getMode());
                    taskTmp.setRedo_cnt(taskTmp.getRedo_cnt() + 1);

                    //保存到数据库
                    entityMgr.saveTaskToTable(taskTmp);
                    flag = true;
                    break;
                }
            }

            if (!flag) {
                log.error("没有找到重做的任务的起始模块标识：TASK_ID:" + body.TASK_ID + ",MODULE_ID:" + body.RB_MODULE_ID);
            }
        }
        return true;
    }

    /*处理命令行客户端消息
     * */
    public boolean dealRollbackRequest(MsgBody.RollbackBody body) {
        String sParamValue = body.TASK_ID;
        if (sParamValue.indexOf(",") > -1) {
            log.info(sParamValue + ",指定多个任务重做");
            String[] split = sParamValue.split(",");
            for (String str : split) {
                body.TASK_ID = str;
                dealRollback(body);
            }
        } else if (sParamValue.indexOf("-") > -1) {
            log.info(sParamValue + ",指定范围内失败的任务重做");
            String[] split = sParamValue.split("-");
            Long beginTaskId = Long.parseLong(split[0]);
            Long endTaskId = Long.parseLong(split[1]);
            log.info("beginTaskId:" + beginTaskId + ",endTaskId:" + endTaskId);
            for (Map.Entry<String, TpTaskRecord> map : recordLinkMap.entrySet()) {
                if ("FCL".equals(map.getValue().getState())) {
                    Long currTaskId = Long.parseLong(map.getKey());
                    if (currTaskId >= beginTaskId && currTaskId <= endTaskId) {
                        body.TASK_ID = map.getKey();
                        dealRollback(body);
                        System.out.println("TASK_ID:" + body.TASK_ID + ",state:" + body.STATE + ",重做");
                    }
                }
            }
        } else if (sParamValue != null && StringUtils.isNumber(sParamValue)) {
            log.info(sParamValue + ",单个任务重做");
            dealRollback(body);
        }
        return true;
    }

    // 采集消息
    public MsgBody.DbInceptBody parseDbInceptBody(String json) {
        MsgBody.DbInceptBody body = JSONObject.parseObject(json, MsgBody.DbInceptBody.class);
        body.MODULE_ID = body.PROCESS_ID.substring(0, 4);
        if (body.BATCH_ID == null) {
            body.BATCH_ID = "0";
        }
        return body;
    }

    // 标准话单格式
    public MsgBody.StandardBody parseStandardBody(String json) {
        MsgBody.StandardBody body = JSONObject.parseObject(json, MsgBody.StandardBody.class);
        body.MODULE_ID = body.PROCESS_ID.substring(0, 4);
        return body;
    }

    public MsgBody.RollbackBody parseRollbackBody(String json) {
        MsgBody.RollbackBody body = JSONObject.parseObject(json, MsgBody.RollbackBody.class);
        return body;
    }

    // 消息处理接口
    boolean Message_Process_Interface(String msg) {
        // 解析json控制消息
        TpModule moduleInfo = getModuleByJson(msg);
        if (moduleInfo == null) {
            log.error("无法识别的消息发送者。");
            log.error(msg);
            return false;
        }
        if (MODULE_TYPE.MB.type.equals(moduleInfo.getModule_type())) {
            JSONObject j = JSONObject.parseObject(msg);
            if (MsgBody.TASK_TYPE.T_RECORD.getType().equals(j.get("TASK_TYPE"))) {
                //采集 创建任务
                if (!createRecordTask(parseDbInceptBody(msg))) {
                    log.error("创建任务失败:" + msg);
                    return false;
                }
            } else if (MsgBody.TASK_TYPE.T_DBINCEPT.getType().equals(j.get("TASK_TYPE"))) {
                //采集 处理
                if (!processDbInceptResponse(parseDbInceptBody(msg))) {
                    log.error("接收获取可用任务数失败:" + msg);
                    return false;
                }
            }
            return true;
        }

        // 消息类型
        String msgType = getMsgTypeByJSON(msg);
        if (msgType.equals("REQUEST")) { //请求消息
            String taskType = getTaskTypeByJSON(msg);
            if (MsgBody.TASK_TYPE.T_ROLLBACK.getType().equals(taskType)) {
                //任务重做
                if (!dealRollbackRequest(parseRollbackBody(msg))) {
                    log.error("命令行客户端请求处理失败");
                }
            }
        } else if (msgType.equals("RESPONSE")) { //应答消息
            if (moduleInfo.getModule_type().equals(MODULE_TYPE.MC.type) ||
                    moduleInfo.getModule_type().equals(MODULE_TYPE.TC.type) ||
                    moduleInfo.getModule_type().equals(MODULE_TYPE.PC.type)) {
                //标准话单 任务应答包处理
                if (!processRecordTaskResponse(parseStandardBody(msg))) {
                    log.error("标准话单任务应答处理失败:" + msg);
                }
            } else if (moduleInfo.getModule_type().equals(MODULE_TYPE.ME.type)) {
                //传输进程 应答包处理
                /*if (!processTransferTaskResponse(parseTransferBody(msg))) {
                    log.info("传输任务应答处理失败:" + msg);
                }*/
            }
        }

        return true;
    }

    List<TpProcess> getProcessListByModuleId(int module_id) {
        List<TpProcess> list = new ArrayList<>();

        for (Map.Entry<String, TpProcess> pro : entityMgr.getmTpProcessMap().entrySet()) {
            if (pro.getValue().getModule_id() == module_id) {
                list.add(pro.getValue());
            }
        }

        return list;
    }

    boolean sendDbInceptUseTaskCnt(MsgBody.DbInceptBody body) {
        TaskMessageEntity taskMessageEntity = new TaskMessageEntity(body.PROCESS_ID, JSONObject.toJSONString(body, SerializerFeature.WriteMapNullValue));
        messageManager.offerResponseJosnTask(taskMessageEntity);
        return true;
    }

    boolean sendDealTaskAfterSwitchState(String processId, TpTaskRecord task, TpTaskDomainDef taskState) {
        // 2、获取当前模块传输目录
        String moduleId = String.valueOf(task.getModule_id());
        TpWorkpathDef currentWorkpath = entityMgr.getmTpWorkPath().get(moduleId);

        //待业务模块处理
        JSONObject object = new JSONObject();
        object.put("TASK_ID", task.getTask_id());
        object.put("HOST_ID", task.getHost_id());
        object.put("PROCESS_ID", processId);
        object.put("FILE_NAME", task.getFile_name());

        if ("CTG_MQ".equals(common.getRecordTransferMode())) {
            TpProcessMQ tpProcessMQ = common.getProcessMQMap().get(processId);
            if (tpProcessMQ == null) {
                log.error("tpProcessMQ is null,process_id:" + processId);
                return false;
            }
            object.put("LOCAL_OUTPUT_PATH", tpProcessMQ.getRecord_producer_topic());
        } else if ("CTG_CACHE".equals(common.getRecordTransferMode())) {
            object.put("LOCAL_OUTPUT_PATH", task.getLocal_path());
        } else if ("HDFS".equals(common.getRecordTransferMode())) {
            object.put("LOCAL_OUTPUT_PATH", currentWorkpath.getHdfs_path());
        } else {
            object.put("LOCAL_OUTPUT_PATH", currentWorkpath.getLocal_output_path());
        }
        object.put("RECORD_CNT", task.getRecord_cnt());
        object.put("DEAL_TIME", "NULL");
        object.put("MSG_TYPE", "REQUEST");
        object.put("STATE", "NULL");
        object.put("CREATE_DATE", Factory.getSystemDateStr());
        object.put("UPDATE_DATE", "NULL");

        // 默认采集的下一流程的处理文件输入目录从消息包中区
        TpTaskFlowDef oneFlow = flowListMap.get(task.getMode()).get(1);
        TpTaskFlowDef fourFlow = flowListMap.get(task.getMode()).get(4);
        if ((oneFlow.getOrder_id() == task.getOrder_id() || fourFlow.getOrder_id() == task.getOrder_id()) &&
                (task.getModule_id() == 1012 || task.getModule_id() == 2012 || task.getModule_id() == 3012)) { //预处理
            MsgBody.TaskBody taskBody = msgLinkMap.get(String.valueOf(task.getTask_id()));
            MsgBody.DbInceptBody body = taskBody.dbInceptBody;
            object.put("LOCAL_INPUT_PATH", body.LOCAL_PATH);
            object.put("FILE_NAME", body.FILE_NAME);
        } else if (m_SmartMode.equals(TASK_FLOW_MODE.SMART.mode) &&
                (task.getModule_id() != 1012 && task.getModule_id() != 2012 && task.getModule_id() != 3012)) { //排重、批价、入库
            MsgBody.TaskBody taskBody = msgLinkMap.get(String.valueOf(task.getTask_id()));
            MsgBody.StandardBody body = taskBody.standardBody.get(taskBody.standardBody.size() - 1);
            object.put("LOCAL_INPUT_PATH", body.LOCAL_OUTPUT_PATH);

            for (int i = 0; i < taskBody.standardBody.size(); i++) {
                if (String.valueOf(task.getModule_id()).equals(taskBody.standardBody.get(i).MODULE_ID)) {
                    //处理过，重做的情况
                    object.put("LOCAL_INPUT_PATH", taskBody.standardBody.get(i).LOCAL_INPUT_PATH);
                    //object.put("LOCAL_INPUT_PATH", currentWorkpath.getLocal_input_path());
                    break;
                }
            }
        } else {
            object.put("LOCAL_INPUT_PATH", currentWorkpath.getLocal_input_path());
        }

        // 发送处理消息给客户端
        TaskMessageEntity taskMessageEntity = new TaskMessageEntity(processId, JSONObject.toJSONString(object, SerializerFeature.WriteMapNullValue));
        messageManager.offerResponseJosnTask(taskMessageEntity);
        return true;
    }

    //  任务状态监控处理接口,待上传、待处理、待下载状态，发送消息给处理模块，然后切换状态为处理中
    boolean Task_State_Process_Interface() {
        Iterator<Map.Entry<String, TpTaskRecord>> iter = recordLinkMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, TpTaskRecord> entry = iter.next();
            TpTaskRecord task = entry.getValue();

            // 1、获取任务切换状态
            TpTaskDomainDef taskState = entityMgr.get_Domain(task.getState());
            if (taskState == null) {
                log.info("非法的任务状态:" + task.toString());
                return false;
            }

            // 不需要发送消息的状态，跳过
            if (taskState.getState().equals(RECORD_TASK_STATE.SSS.state)) {
                //entityMgr.saveTaskToTable(task);
                iter.remove(); //回收任务
                msgLinkMap.remove(String.valueOf(task.getTask_id())); //回收任务对应消息包
                continue;
            } else if (taskState.getState().equals(RECORD_TASK_STATE.OCL.state)) {
                //任务处理中，监控处理进程是否有异常退出情况
                continue;
            } else if (taskState.getState().equals(RECORD_TASK_STATE.SCL.state)) {
                // 完成，切换到下一个状态
                switchTaskNextFlow(task);
            } else if (task.getRedo_cnt() < dispatchDef.getRedoCount() &&
                    RECORD_TASK_STATE.FCL.state.equals(taskState.getState()) &&
                    dispatchDef.getRedoTaskErrorMsgKeyList() != null) {
                log.info("自动进行任务回收判断，task_id:" + task.getTask_id());
                task.setRedo_cnt(task.getRedo_cnt() + 1);
                // 失败任务
                MsgBody.TaskBody taskBody = JSONObject.parseObject(task.getMsg_body(), MsgBody.TaskBody.class);
                MsgBody.StandardBody standardBody = taskBody.standardBody.get(taskBody.standardBody.size() - 1);
                for (String str : dispatchDef.getRedoTaskErrorMsgKeyList()) {
                    if (standardBody.ERROR_MSG != null && standardBody.ERROR_MSG.indexOf(str) > -1) {
                        task.setState(RECORD_TASK_STATE.DCL.state);
                        log.info("任务回收启动:" + task.toString());
                        break;
                    }
                }

                entityMgr.saveTaskToTable(task);
            }
        }
        return true;
    }

    // 根据模块ID和主机ID、任务类型获取空闲可处理的任务
    TpTaskRecord getWaitTaskByProcess(TpProcess tpProcess) {
        for (Map.Entry<String, TpTaskRecord> task : recordLinkMap.entrySet()) {
            if (task.getValue().getState().equals(RECORD_TASK_STATE.DCL.state)) {
                if (tpProcess.getType() == null) {
                    if (common.getRecordTransferMode().equals("CTG_MQ")) {
                        String proStr = String.valueOf(tpProcess.getProcess_id()).substring(4);
                        String taskStr = String.valueOf(task.getValue().getProcess_id()).substring(4);
                        if (task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType() == null && proStr.equals(taskStr)) {
                            return task.getValue();
                        }
                    } else {
                        if (task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType() == null) {
                            return task.getValue();
                        }
                    }
                } else {
                    if (task.getValue().getType() != null) {
                        if (task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType().equals(tpProcess.getType())) {
                            return task.getValue();
                        }
                    }
                }
            }
        }
        return null;
    }

    // 根据模块ID和主机ID、任务类型获取空闲可处理的任务
    TpTaskRecord getWaitTaskByProcessAndHost(TpProcess tpProcess) {
        for (Map.Entry<String, TpTaskRecord> task : recordLinkMap.entrySet()) {
            if (task.getValue().getState().equals(RECORD_TASK_STATE.DCL.state)) {
                if (tpProcess.getType() == null) {
                    if (common.getRecordTransferMode().equals("CTG_MQ")) {
                        String proStr = String.valueOf(tpProcess.getProcess_id()).substring(4);
                        String taskStr = String.valueOf(task.getValue().getProcess_id()).substring(4);
                        if (task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType() == null && proStr.equals(taskStr)) {
                            return task.getValue();
                        }
                    } else {
                        if (task.getValue().getHost_id() == tpProcess.getHost_id() && task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType() == null) {
                            return task.getValue();
                        }
                    }
                } else {
                    if (task.getValue().getType() != null) {
                        if (task.getValue().getHost_id() == tpProcess.getHost_id() && task.getValue().getModule_id() == tpProcess.getModule_id() && task.getValue().getType().equals(tpProcess.getType())) {
                            return task.getValue();
                        }
                    }
                }
            }
        }
        return null;
    }


    // 遍历空闲进程，分配任务
    boolean Process_State_Distribute_Task() {
        TpProcess tpProcessDispatch = entityMgr.getmTpProcessMap().get("10100000");
        if ("1".equals(tpProcessDispatch.getIsPause())) {
            log.info("调度挂起，跳过分配任务");
            return true;
        }

        Iterator<Map.Entry<String, TpProcess>> iter = entityMgr.getmTpProcessMap().entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, TpProcess> entry = iter.next();
            TpProcess pro = entry.getValue();

            if ("1".equals(pro.getIsPause())) {
                //log.info("进程挂起，跳过：" + pro.toString());
                continue;
            }

            if (pro.getStatus().equals(ProcessManager.PROCESS_STATE.TB.state)) {

                TpTaskRecord task = null;
                if ("CONTAINER".equals(common.getDeployment())) {
                    task = getWaitTaskByProcess(pro);
                } else if ("VM".equals(common.getDeployment())) {
                    task = getWaitTaskByProcessAndHost(pro);
                } else {
                    task = getWaitTaskByProcess(pro);
                }
                if (task == null) {
                    continue;
                }

                log.info("分配任务:" + task.getTask_id() + ",TASK_TYPE:" + task.getType() + " ,给进程:" + pro.getProcess_id() + ",PROCESS_TYPE:" + pro.getType() + ",模块:" + pro.getModule_code());

                TpTaskDomainDef taskState = entityMgr.get_Domain(task.getState());
                if (taskState.getState().equals(RECORD_TASK_STATE.DCL.state)) {
                    if (!sendDealTaskAfterSwitchState(String.valueOf(pro.getProcess_id()), task, taskState)) {
                        log.info("处理待处理任务状态失败，" + task.toString());
                    } else {
                        pro.setStatus(ProcessManager.PROCESS_STATE.TE.state);
                        //修改任务的进程状态
                        task.setProcess_id(pro.getProcess_id());
                        //消息发送之后，修改状态
                        switchTaskNextFlow(task);
                        //保存进程状态
                        processManager.switchProcessState(String.valueOf(pro.getProcess_id()), pro.getStatus(), -1);
                    }
                }
            }

        }

        return true;
    }

    boolean dealTaskTimeOut(TpTaskRecord task) {
        String moduleId = String.valueOf(task.getModule_id());
        TpWorkpathDef currentWorkpath = entityMgr.getmTpWorkPath().get(moduleId);

        String processId = String.valueOf(task.getProcess_id());
        Long lDealTime = (System.currentTimeMillis() - task.getCurr_time());
        if (lDealTime > (common.getLTimeOutSeconds() + 30000)) {
            MsgBody.StandardBody body = new MsgBody.StandardBody();
            body.TASK_TYPE = MsgBody.TASK_TYPE.T_RECORD_STATE.getType();
            body.TASK_ID = String.valueOf(task.getTask_id());

            body.HOST_ID = String.valueOf(task.getHost_id());
            body.PROCESS_ID = processId;
            body.FILE_NAME = task.getFile_name();

            if ("CTG_MQ".equals(common.getRecordTransferMode())) {
                TpProcessMQ tpProcessMQ = common.getProcessMQMap().get(processId);
                if (tpProcessMQ == null) {
                    log.error("tpProcessMQ is null,process_id:" + processId);
                    return false;
                }
                body.LOCAL_OUTPUT_PATH = tpProcessMQ.getRecord_producer_topic();
            } else if ("CTG_CACHE".equals(common.getRecordTransferMode())) {
                body.LOCAL_OUTPUT_PATH = task.getLocal_path();
            } else if ("HDFS".equals(common.getRecordTransferMode())) {
                body.LOCAL_OUTPUT_PATH = currentWorkpath.getHdfs_path();
            } else {
                body.LOCAL_OUTPUT_PATH = currentWorkpath.getLocal_output_path();
            }
            body.RECORD_CNT = String.valueOf(task.getRecord_cnt());
            body.DEAL_TIME = "NULL";
            body.MSG_TYPE = "REQUEST";
            body.STATE = "NULL";
            body.CREATE_DATE = Factory.getSystemDateStr();
            body.UPDATE_DATE = "NULL";

            // 默认采集的下一流程的处理文件输入目录从消息包中区
            TpTaskFlowDef oneFlow = flowListMap.get(task.getMode()).get(1);
            TpTaskFlowDef fourFlow = flowListMap.get(task.getMode()).get(4);
            if ((oneFlow.getOrder_id() == task.getOrder_id() || fourFlow.getOrder_id() == task.getOrder_id()) &&
                    (task.getModule_id() == 1012 || task.getModule_id() == 2012 || task.getModule_id() == 3012)) { //预处理
                MsgBody.TaskBody taskBody = msgLinkMap.get(String.valueOf(task.getTask_id()));
                MsgBody.DbInceptBody bodyTmp = taskBody.dbInceptBody;
                body.LOCAL_INPUT_PATH = bodyTmp.LOCAL_PATH;
                body.FILE_NAME = bodyTmp.FILE_NAME;
            } else if (m_SmartMode.equals(TASK_FLOW_MODE.SMART.mode) &&
                    (task.getModule_id() != 1012 && task.getModule_id() != 2012 && task.getModule_id() != 3012)) { //排重、批价、入库
                MsgBody.TaskBody taskBody = msgLinkMap.get(String.valueOf(task.getTask_id()));
                MsgBody.StandardBody bodyTmp = taskBody.standardBody.get(taskBody.standardBody.size() - 1);
                body.LOCAL_INPUT_PATH = bodyTmp.LOCAL_OUTPUT_PATH;

                for (int i = 0; i < taskBody.standardBody.size(); i++) {
                    if (String.valueOf(task.getModule_id()).equals(taskBody.standardBody.get(i).MODULE_ID)) {
                        //处理过，重做的情况
                        body.LOCAL_INPUT_PATH = currentWorkpath.getLocal_input_path();
                        //body.put("FILE_NAME", taskBody.standardBody.get(i - 1).FILE_NAME);
                        break;
                    }
                }
            } else {
                body.FILE_NAME = task.getFile_name();
            }

            // 发送处理消息给客户端
            log.info("任务:" + body.TASK_ID + ",进程:" + body.PROCESS_ID + " 处理超时，询问是否处理完成:");
            TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(task.getProcess_id()), JSONObject.toJSONString(body, SerializerFeature.WriteMapNullValue));
            messageManager.offerResponseJosnTask(taskMessageEntity);
        }

        return true;
    }

    // 任务状态为（处理中）的，监控进程是否有异常退出,异常退出的回收任务。
    public boolean Process_State_Monitor_Task_Rollback() {
        Iterator<Map.Entry<String, TpTaskRecord>> iter = recordLinkMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, TpTaskRecord> entry = iter.next();
            TpTaskRecord task = entry.getValue();

            // 1、获取任务切换状态
            TpTaskDomainDef taskState = entityMgr.get_Domain(task.getState());
            if (taskState == null) {
                log.info("非法的任务状态:" + task.toString());
                return false;
            }

            // 不需要发送消息的状态，跳过
            if (taskState.getState().equals(RECORD_TASK_STATE.SSS.state)) {
                continue;
            } else if (taskState.getState().equals(RECORD_TASK_STATE.DCL.state)) {
                continue;
            }

            if (taskState.getState().equals(RECORD_TASK_STATE.OCL.state)) {
                // 任务处理中，监控处理进程是否有异常退出情况
                TpProcess proTmp = entityMgr.getmTpProcessMap().get(String.valueOf(task.getProcess_id()));
                if (proTmp != null) {
                    if (proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TD.state)) { //进程有任务，异常超过50都是异常，回收状态
                        Date exitDat = getUtilDateByString(proTmp.getExit_date());
                        if (exitDat != null) {
                            Long tdStateTime = (new Date().getTime() - exitDat.getTime()) / 1000;

                            if (tdStateTime > common.getLTDProcessExitRollbackSeconds()) {
                                // 状态不为处理中，回收任务
                                switchTaskFrontFlow(task);

                                log.info("任务：" + task.getTask_id() + ",进程状态不为处理中，回收任务，状态切换回上一个状态,异常时长:" + tdStateTime);
                            }
                        }
                    } else if (proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TA.state)) {
                        // 状态不为处理中，回收任务
                        switchTaskFrontFlow(task);

                        log.info("任务：" + task.getTask_id() + ",进程状态不为处理中，回收任务，状态切换回上一个状态");
                    } else if (proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TC.state) || proTmp.getStatus().equals(ProcessManager.PROCESS_STATE.TE.state)) {
                        if (task.getWarn_cnt() == 0) {
                            Date updateDate = getUtilDateByString(task.getUpdate_date());
                            if (updateDate != null) {
                                Long tcStateTime = (new Date().getTime() - updateDate.getTime()) / 1000;

                                // 任务超时之后30秒，还是处理中的，输出告警日志
                                if (tcStateTime > (common.getLTimeOutSeconds() / 1000 + 30)) {
                                    // 处理超时
                                    TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.DISPATCH_TASK_DEAL_TIMEOUT, task.getProcess_id());
                                    tgAlertLog.setLog_desc("告警类型:" + ErrorType.DISPATCH_TASK_DEAL_TIMEOUT + ",进程 " + task.getProcess_id() + "处理任务:" + task.getTask_id() + ",超时，超时时长：" + tcStateTime + " 秒");

                                    entityMgr.createTgAlterLogToTable(JSON.toJSONString(tgAlertLog));
                                    task.setWarn_cnt(task.getWarn_cnt() + 1);
                                    log.error(tgAlertLog.getLog_desc());
                                }

                                //任务如果处理时长，超过配置的超时时长，发送消息确认任务是否收到正常处理
                                //dealTaskTimeOut(task); //不能重复发送
                            }
                        }
                    }
                } else {
                    log.error("任务：" + task.getTask_id() + ",找不到处理的进程:" + task.getProcess_id() + ";");
                    continue;
                }
            }
        }

        return true;
    }

    boolean OutTaskStaticLogToTable() {
        if (dispatchDef.getOUT_TASKLOG_INTERVAL() > 0) {
            Long currentTimeTmp = System.currentTimeMillis();
            if ((currentTimeTmp - lStartTimeMillis) > (dispatchDef.getOUT_TASKLOG_INTERVAL() * 1000)) {
                // 插入进程退出的告警日志
                for (Map.Entry<String, TaskStatisticalData> dataTmp : taskStaticMap.entrySet()) {
                    String type = "null".equals(dataTmp.getValue().type) ? null : dataTmp.getValue().type;

                    //  进程统计
                    for (Map.Entry<String, TpProcess> proTmp : entityMgr.getmTpProcessMap().entrySet()) {
                        if (ProcessManager.PROCESS_STATE.TB.getState().equals(proTmp.getValue().getStatus()) ||
                                ProcessManager.PROCESS_STATE.TC.getState().equals(proTmp.getValue().getStatus()) ||
                                ProcessManager.PROCESS_STATE.TE.getState().equals(proTmp.getValue().getStatus())) {
                            if (proTmp.getValue().getModule_id() == 1011 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iDbInceptCnt++;
                            } else if (proTmp.getValue().getModule_id() == 1012 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iPreProc2Cnt++;
                            } else if (proTmp.getValue().getModule_id() == 1013 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iFilterCnt++;
                            } else if (proTmp.getValue().getModule_id() == 1014 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iRatingCnt++;
                            } else if (proTmp.getValue().getModule_id() == 1030 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iBlockSettCnt++;
                            } else if (proTmp.getValue().getModule_id() == 1015 && StringUtils.equals(proTmp.getValue().getType(), type)) {
                                dataTmp.getValue().iTicketIndbCnt++;
                            }
                        }
                    }

                    // 任务统计 获取失败任务数和积压任务数
                    for (Map.Entry<String, TpTaskRecord> taskTmp : recordLinkMap.entrySet()) {
                        if (RECORD_TASK_STATE.FCL.state.equals(taskTmp.getValue().getState()) && StringUtils.equals(taskTmp.getValue().getType(), type)) {
                            if (lastFclTaskId < taskTmp.getValue().getTask_id()) {
                                dataTmp.getValue().lFaileTaskCnt++;
                                dataTmp.getValue().lFaileRecordCnt += taskTmp.getValue().getRecord_cnt();
                                lastFclTaskId = taskTmp.getValue().getTask_id();
                            }
                        } else if ((RECORD_TASK_STATE.DCL.state.equals(taskTmp.getValue().getState()) ||
                                RECORD_TASK_STATE.SCL.state.equals(taskTmp.getValue().getState()) ||
                                RECORD_TASK_STATE.OCL.state.equals(taskTmp.getValue().getState())) && StringUtils.equals(taskTmp.getValue().getType(), type)) {
                            dataTmp.getValue().lBackTaskCnt++;
                            dataTmp.getValue().lBackRecordCnt += taskTmp.getValue().getRecord_cnt();
                        }
                    }

                    dataTmp.getValue().startDate = sdf.format(new Date(lStartTimeMillis));
                    dataTmp.getValue().endDate = sdf.format(new Date(currentTimeTmp));
                    dataTmp.getValue().lPerl = dataTmp.getValue().lRecordTotal * 1000 / (currentTimeTmp - lStartTimeMillis);

                    TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.DISPATCH_LOG_TASK_STATISTICAL, Long.parseLong(zkClientApi.getProcessID()));
                    tgAlertLog.setLog_desc(dataTmp.getValue().toMsg());
                    //  into tg_alert_log
                    entityMgr.createTgAlterLogToTable(JSON.toJSONString(tgAlertLog));
                    //  into dcoos_tp_task_info;
                    entityMgr.createTgAlterLogToTable(JSON.toJSONString(dataTmp.getValue()));
                }

                lStartTimeMillis = currentTimeTmp;
                taskStaticMap.clear();
            }
        }

        return true;
    }

    public String getMsgTypeByJSON(String json) {
        //log.info("getTaskTypeByJSON:" + json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        String type = jsonObject.getString("MSG_TYPE");
        return type;
    }

    public String getTaskTypeByJSON(String json) {
        //log.info("getTaskTypeByJSON:" + json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        String type = jsonObject.getString("TASK_TYPE");
        return type;
    }

    public TpModule getModuleByJson(String json) {
        //log.info("getModuleByJson,json:" + json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        String processId = jsonObject.getString("PROCESS_ID");
        if (processId == null) {
            log.info("MQ消息无PROCESS_ID字段无法解析," + json);
            return null;
        }

        String moduleId = processId.substring(0, 4);
        TpModule module = entityMgr.getMapModule().get(moduleId);
        return module;
    }

    Date getUtilDateByString(String str) {
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void setMessageManager(MessageManager messageManager) {
        this.messageManager = messageManager;
    }

    public void setProcessManager(ProcessManager processManager) {
        this.processManager = processManager;
    }

    public String getM_SmartMode() {
        return m_SmartMode;
    }

    public void setM_SmartMode(String m_SmartMode) {
        this.m_SmartMode = m_SmartMode;
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public void setEntityMgr(EntityMgr entityMgr) {
        this.entityMgr = entityMgr;
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public enum MODULE_TYPE {
        MA(1, "MA", "调度模块"),
        MB(2, "MB", "采集模块"),
        MC(3, "MC", "话单处理模块"),
        MD(4, "MD", "常驻模块"),
        ME(5, "ME", "传输模块"),
        MF(6, "MF", "命令客户端"),
        TC(7, "TC", "试算话单处理"),
        PC(8, "PC", "预计费话单处理");

        int order_id;
        String type;
        String remark;

        MODULE_TYPE(int order_id, String type, String remark) {
            this.order_id = order_id;
            this.type = type;
            this.remark = remark;
        }

        public String getType() {
            return type;
        }
    }

    enum RECORD_TASK_STATE {
        /*DXZ(1, "DXZ", 0, "DOWN", "待下载（调度-> 传输）"),
        OXZ(2, "OXZ", 0, "T", "下载中（传输下载中） "),
        SXZ(3, "SXZ", 0, "T", "下载完成（传输->调度）"),*/
        DCL(4, "DCL", 1, "D", "待处理（调度-> 预处理）"),
        OCL(5, "OCL", 2, "D", "处理中（业务模块处理中）"),
        SCL(6, "SCL", 3, "D", "处理完成（预处理->调度）"),
        //        DSC(7, "DSC", 0, "UP", "待上传（调度-> 传输）"),
//        OSC(8, "OSC", 0, "T", "上传中（传输上传中）"),
//        SSC(9, "SSC", 0, "T", "上传完成（传输-> 调度）"),
        SSS(10, "SSS", 0, "T", "任务完成"),
        FXZ(11, "FXZ", 0, "T", "下载失败"),
        FCL(12, "FCL", 0, "T", "处理失败"),
        FSC(13, "FSC", 0, "T", "上传失败");

        int order_id;
        String state;
        int smart_order_id;
        String type;
        String remark;

        RECORD_TASK_STATE(int order_id, String state, int smart_order_id, String type, String remark) {
            this.order_id = order_id;
            this.state = state;
            this.smart_order_id = smart_order_id;
            this.type = type;
            this.remark = remark;
        }
    }

    public enum MESSAGE_TYPE {
        REQUEST(1, "REQUEST"),
        RESPONSE(2, "RESPONSE");
        int order_id;
        String type;

        MESSAGE_TYPE(int order_id, String type) {
            this.order_id = order_id;
            this.type = type;
        }
    }

    public enum MESSAGE_STATE {
        NULL(1, "NULL"),
        OK(2, "OK"),
        ERROR(3, "ERROR");
        int order_id;
        String state;

        MESSAGE_STATE(int order_id, String state) {
            this.order_id = order_id;
            this.state = state;
        }
    }

    public enum TASK_FLOW_MODE {
        NORMAL(1, "NORMAL"),
        SMART(2, "SMART");

        int order_id;
        String mode;

        public String getMode() {
            return mode;
        }

        TASK_FLOW_MODE(int order_id, String mode) {
            this.order_id = order_id;
            this.mode = mode;
        }
    }

}
