package com.itco.dispatch.thread;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.dispatch.entity.entitydef.ErrorType;
import com.itco.dispatch.entity.entitydef.TaskStatisticalData;
import com.itco.dispatch.entity.entitydef.TpTaskRecord;
import com.itco.dispatch.entity.entitydef.TpTaskRecordLog;
import com.itco.entity.common.Common;
import com.itco.entity.common.TgAlertLog;
import com.itco.entity.common.TpModuleVersion;
import com.itco.entity.common.TpProcess;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import lombok.SneakyThrows;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

public class DatabaseManager implements Runnable {
    static Log log = LogFactory.getLog(DatabaseManager.class);

    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    Common common = null;
    volatile LinkedBlockingQueue<TpProcess> g_createProcessLogQueue = new LinkedBlockingQueue<TpProcess>();
    volatile LinkedBlockingQueue<TpProcess> g_saveProcessQueue = new LinkedBlockingQueue<TpProcess>();
    volatile LinkedBlockingQueue<TpTaskRecord> g_saveTaskQueue = new LinkedBlockingQueue<TpTaskRecord>();
    volatile LinkedBlockingQueue<TpTaskRecord> g_createTaskQueue = new LinkedBlockingQueue<TpTaskRecord>();
    volatile LinkedBlockingQueue<MsgBody.ConfigSyncBody> g_createConfigSyncQueue = new LinkedBlockingQueue<MsgBody.ConfigSyncBody>();
    volatile LinkedBlockingQueue<MsgBody.ConfigSyncBody> g_saveConfigSyncQueue = new LinkedBlockingQueue<MsgBody.ConfigSyncBody>();
    volatile LinkedBlockingQueue<List<TpTaskRecordLog>> g_createTaskLogQueue = new LinkedBlockingQueue<List<TpTaskRecordLog>>();
    volatile LinkedBlockingQueue<String> g_createTgAlertLogQueue = new LinkedBlockingQueue<String>();
    volatile LinkedBlockingQueue<TpModuleVersion> g_createModuleVersionQueue = new LinkedBlockingQueue<TpModuleVersion>();

    public void setCommon(Common common) {
        this.common = common;
    }

    @SneakyThrows
    @Override
    public void run() {

        while (!Factory.isbWhileFlag()) {
            MsgBody.ConfigSyncBody poll = null;
            while ((poll = g_createConfigSyncQueue.poll()) != null) {
                log.debug("创建同步队列，createConfigSyncToTable->" + poll);
                createConfigSyncToTable(poll);
            }

            while ((poll = g_saveConfigSyncQueue.poll()) != null) {
                log.debug("处理同步队列，saveConfigSyncToTable->" + poll);
                saveConfigSyncToTable(poll);
            }

            // 优化了同步数据库表的方案，一次性读取队列的所有数据，在去批量更新。原先是一条一条更新，在更新数据库慢的情况下，只要还有数据就循环，导致一致更新任务状态，其他的sql语句堵塞。
            {
                List<TpTaskRecord> listTask = new ArrayList<>();
                TpTaskRecord task = null;
                while ((task = g_createTaskQueue.poll()) != null) {
                    log.debug("创建任务，createTaskToTable->" + task);
                    listTask.add(task);
                }
                createTaskToTable(listTask);
            }

            {
                Map<Integer, TpTaskRecord> mapTask = new HashMap<>();
                TpTaskRecord task = null;
                while ((task = g_saveTaskQueue.poll()) != null) {
                    log.debug("保存任务saveTaskToTable->" + task);
                    mapTask.put(task.getTask_id(), task);
                }
                saveTaskToTable(mapTask);
            }

            {
                List<TpTaskRecordLog> list = new ArrayList<>();
                List<TpTaskRecordLog> tmpList = null;
                while ((tmpList = g_createTaskLogQueue.poll()) != null) {
                    log.debug("插入任务日志，createTpTaskRecordLogToTable->" + tmpList.size());
                    list.addAll(tmpList);
                }
                createTpTaskRecordLogToTable(list);
            }

            {   // 更新进程状态表
                TpProcess tpProcessTmp = null;
                Map<Long, TpProcess> map = new HashMap<>();
                while ((tpProcessTmp = g_saveProcessQueue.poll()) != null) {
                    log.debug("修改进程状态，saveDbProcessToTable->" + tpProcessTmp);
                    map.put(tpProcessTmp.getProcess_id(), tpProcessTmp);
                }
                // 修改进程状态
                for (Map.Entry<Long, TpProcess> iter : map.entrySet()) {
                    saveDbProcessToTable(iter.getValue());
                }
            }

            {   // 写进程启停日志表
                TpProcess tpProcessTmp = null;
                List<TpProcess> listProcessTmp = new ArrayList<>();
                while ((tpProcessTmp = g_createProcessLogQueue.poll()) != null) {
                    log.debug("插入进程启停日志，createProcessLogToTable->" + tpProcessTmp.toString());
                    listProcessTmp.add(tpProcessTmp);

                    if (ProcessManager.PROCESS_STATE.TA.getState().equals(tpProcessTmp.getStatus())) {
                        //进程正常退出
                        TgAlertLog tmp = new TgAlertLog(ErrorType.DISPATCH_PROCESS_NORMAL_EXIT, (long) common.getProcess_id());
                        tmp.setLog_desc("告警类型:" + ErrorType.DISPATCH_PROCESS_NORMAL_EXIT + "," + tpProcessTmp.getProcess_name() + ",进程退出");
                        g_createTgAlertLogQueue.add(JSONObject.toJSONString(tmp));
                    } else if (ProcessManager.PROCESS_STATE.TD.getState().equals(tpProcessTmp.getStatus())) {
                        //  进程异常退出
                        TgAlertLog tmp = new TgAlertLog(ErrorType.DISPATCH_PROCESS_ABN_EXIT, (long) common.getProcess_id());
                        tmp.setLog_desc("告警类型:" + ErrorType.DISPATCH_PROCESS_ABN_EXIT + "," + tpProcessTmp.getProcess_name() + ",进程异常退出");
                        g_createTgAlertLogQueue.add(JSONObject.toJSONString(tmp));
                    }
                }
                if (listProcessTmp.size() > 0) {
                    createProcessLogToTable(listProcessTmp);
                }
            }

            {   // 插入告警日志
                String strTmp;
                List<TgAlertLog> listLogTmp = new ArrayList<>();
                List<TaskStatisticalData> listStatisTmp = new ArrayList<>();
                while ((strTmp = g_createTgAlertLogQueue.poll()) != null) {
                    JSONObject objectTmp = JSONObject.parseObject(strTmp);
                    if ("info".equals(objectTmp.get("msg_type"))) {
                        TaskStatisticalData taskStatisticalDataTmp = JSON.parseObject(strTmp, TaskStatisticalData.class);
                        listStatisTmp.add(taskStatisticalDataTmp);
                        //log.info("插入统计日志：" + taskStatisticalDataTmp.toString());
                    } else if ("alert".equals(objectTmp.get("msg_type"))) {
                        TgAlertLog tgAlertLogTmp = JSON.parseObject(strTmp, TgAlertLog.class);
                        listLogTmp.add(tgAlertLogTmp);
                        //log.info("插入告警日志，createTgAlterLogToTable->" + tgAlertLogTmp.toString());
                    }
                }

                if (listLogTmp.size() > 0) {
                    createTgAlterLogToTable(listLogTmp);
                }
                if (listStatisTmp.size() > 0) {
                    createTaskStaticalDateToTable(listStatisTmp);
                }
            }

            {
                // 插入模块版本表
                TpModuleVersion tpModuleVersionTmp = null;
                List<TpModuleVersion> listModuleVersionTmp = new ArrayList<>();
                while ((tpModuleVersionTmp = g_createModuleVersionQueue.poll()) != null) {
                    //log.info("插入模块版本，createModuleVersionToTable->" + tpModuleVersionTmp.toString());
                    listModuleVersionTmp.add(tpModuleVersionTmp);
                }
                if (listModuleVersionTmp.size() > 0) {
                    createModuleVersionToTable(listModuleVersionTmp);
                }
            }
            Thread.sleep(20);
        }
    }


    ///////////////////////////////////外部进程和任务管理使用////////////////////////////////////////////////////
    public synchronized boolean createProcessLogToTable(List<TpProcess> list) {
        if (list == null || list.size() == 0) {
            return true;
        }

        String insertSql = "insert into dcoos_tp_process_log(process_id,module_id,host_id,status,type,billing_line_id,perl,module_code,system_process_id,process_name,update_date,create_date,exit_date,host_name,host_ip,is_pause,version,insert_date) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        List<List<Object>> listss = new ArrayList<>();
        for (TpProcess process : list) {
            List<Object> tmp = new ArrayList<>();
            tmp.add(process.getProcess_id());
            tmp.add(process.getModule_id());
            tmp.add(process.getHost_id());
            tmp.add(process.getStatus());
            tmp.add(process.getType());
            tmp.add(process.getBilling_line_id());
            tmp.add(process.getPerl());
            tmp.add(process.getModule_code());
            tmp.add(process.getSystem_process_id());
            tmp.add(process.getProcess_name());
            tmp.add(process.getUpdate_date());
            tmp.add(process.getCreate_date());
            tmp.add(process.getExit_date());
            tmp.add(process.getHost_name());
            tmp.add(process.getHost_ip());
            tmp.add(process.getIsPause());
            tmp.add(process.getVersion());
            tmp.add(Factory.getSystemDateStr());
            listss.add(tmp);
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeInsertBatch(connTmp, insertSql, listss);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean createTgAlterLogToTable(List<TgAlertLog> list) {
        if (list == null || list.size() == 0) {
            return true;
        }

        String insertSql = "insert into tg_alert_log(code_id,pf_id,create_date,log_desc,state,state_date) values(?,?,?,?,?,?)";
        boolean ret = false;
        List<List<Object>> alertlogs = new ArrayList<>();
        for (TgAlertLog tgAlertLog : list) {
            List<Object> tmp = new ArrayList<>();
            tmp.add(tgAlertLog.getCode_id());
            tmp.add(tgAlertLog.getPf_id());

            try {
                Date createDate = df.parse(tgAlertLog.getCreate_date());
                Timestamp createTimestamp = new Timestamp(createDate.getTime());
                tmp.add(createTimestamp);
                tmp.add(tgAlertLog.getLog_desc());
                tmp.add(tgAlertLog.getState());
                Date stateDate = df.parse(tgAlertLog.getState_date());
                Timestamp stateTimestamp = new Timestamp(stateDate.getTime());
                tmp.add(stateTimestamp);

                alertlogs.add(tmp);
            } catch (ParseException e) {
                log.error(e);
            }
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeInsertBatch(connTmp, insertSql, alertlogs);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public boolean createTaskStaticalDateToTable(List<TaskStatisticalData> infos) {
        if (infos == null || infos.size() == 0) {
            return true;
        }

        String insertSql = "insert into dcoos_tp_task_info(type,start_date,end_date,task_total,record_total,start_cnt,end_cnt,normal_cnt,pre_cnt,abn_cnt,other_cnt,repeat_cnt,error_cnt,perl," +
                "back_task_cnt ,back_record_cnt,faile_task_cnt,faile_record_cnt,dbincept_cnt,preproc2_cnt,filter_cnt,rating_cnt,blocksett_cnt,ticketindb_cnt) " +
                "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        List<List<Object>> listss = new ArrayList<>();
        for (TaskStatisticalData info : infos) {
            List<Object> tmp = new ArrayList<>();
            tmp.add(info.type);
            tmp.add(info.startDate);
            tmp.add(info.endDate);
            tmp.add(info.lTaskTotal);
            tmp.add(info.lRecordTotal);
            tmp.add(info.lStartCnt);
            tmp.add(info.lEndCnt);
            tmp.add(info.lNormalCnt);
            tmp.add(info.lPreCnt);
            tmp.add(info.lAbnCnt);
            tmp.add(info.lOtherCnt);
            tmp.add(info.lRepeatCnt);
            tmp.add(info.lErrorCnt);
            tmp.add(info.lPerl);

            tmp.add(info.lBackTaskCnt);
            tmp.add(info.lBackRecordCnt);
            tmp.add(info.lFaileTaskCnt);
            tmp.add(info.lFaileRecordCnt);
            tmp.add(info.iDbInceptCnt);
            tmp.add(info.iPreProc2Cnt);
            tmp.add(info.iFilterCnt);
            tmp.add(info.iRatingCnt);
            tmp.add(info.iBlockSettCnt);
            tmp.add(info.iTicketIndbCnt);
            listss.add(tmp);
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeInsertBatch(connTmp, insertSql, listss);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean saveDbProcessToTable(TpProcess process) {
        String updateSql = "update dcoos_tp_process set status=?,system_process_id=?,update_date=?,create_date=?,exit_date=?,perl=?,host_name=?,host_ip=?,is_pause=?,version=? where process_id=?";
        boolean ret = false;

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdateThrowException(connTmp, updateSql, process.getStatus(), process.getSystem_process_id(), process.getUpdate_date(), process.getCreate_date(), process.getExit_date(), process.getPerl(), process.getHost_name(), process.getHost_ip(), process.getIsPause(), process.getVersion(), process.getProcess_id());
        } catch (SQLException e) {
            log.error("saveDbProcessToTable exception:" + e);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean saveTaskToTable(Map<Integer, TpTaskRecord> map) {
        String updateSql = "update dcoos_tp_task_record set task_name=?,file_name=?,order_id=?,module_id=?,process_id=?,state=?,update_date=?,element_str=?,msg_body=?,record_cnt=?,curr_time=?,perl=?,redo_cnt=? where task_id=?";
        boolean ret = false;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            for (Map.Entry<Integer, TpTaskRecord> iterator : map.entrySet()) {
                TpTaskRecord task = iterator.getValue();
                ret = DBUtils.exeUpdateThrowException(connTmp, updateSql, task.getTask_name(), task.getFile_name(), task.getOrder_id(), task.getModule_id(), task.getProcess_id(), task.getState(), task.getUpdate_date(), task.getElement_str(), task.getMsg_body(), task.getRecord_cnt(), task.getCurr_time(), task.getPerl(), task.getRedo_cnt(), task.getTask_id());
            }
        } catch (SQLException e) {
            log.error("saveTaskToTable exception:" + e);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean createTaskToTable(List<TpTaskRecord> list) {
        String insertSql = "insert into dcoos_tp_task_record(task_id,task_name,batch_id,cluster_id,file_name,mode,type,host_id,order_id,module_id,local_path,record_cnt,process_id,state,create_date,update_date,element_str,msg_body,curr_time,perl,redo_cnt) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        List<List<Object>> listss = new ArrayList<>();
        for (TpTaskRecord task : list) {
            List<Object> tmp = new ArrayList<>();
            tmp.add(task.getTask_id());
            tmp.add(task.getTask_name());
            tmp.add(task.getBatch_id());
            tmp.add(task.getCluster_id());
            tmp.add(task.getFile_name());
            tmp.add(task.getMode());
            tmp.add(task.getType());
            tmp.add(task.getHost_id());
            tmp.add(task.getOrder_id());
            tmp.add(task.getModule_id());
            tmp.add(task.getLocal_path());
            tmp.add(task.getRecord_cnt());
            tmp.add(task.getProcess_id());
            tmp.add(task.getState());
            tmp.add(task.getCreate_date());
            tmp.add(task.getUpdate_date());
            tmp.add(task.getElement_str());
            tmp.add(task.getMsg_body());
            tmp.add(task.getCurr_time());
            tmp.add(task.getPerl());
            tmp.add(task.getRedo_cnt());
            listss.add(tmp);
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeInsertBatch(connTmp, insertSql, listss);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean createConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        String insertSql = "insert into dcoos_config_sync_body(config_sync_id,task_type,host_id,process_id,module_id,load_id,msg_comment,msg_type,state,error_msg,update_date) values(?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdateThrowException(connTmp, insertSql, sync.CONFIG_SYNC_ID, sync.TASK_TYPE, sync.HOST_ID, sync.PROCESS_ID, sync.MODULE_ID, sync.LOAD_ID, sync.MSG_COMMENT, sync.MSG_TYPE, sync.STATE, sync.ERROR_MSG, sync.UPDATE_DATE);
        } catch (SQLException e) {
            log.error("createConfigSyncToTable exception:" + e);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public synchronized boolean saveConfigSyncToTable(MsgBody.ConfigSyncBody sync) {
        String updateSql = "update dcoos_config_sync_body set msg_type=?,state=?,error_msg=?,update_date=? where config_sync_id=?";
        boolean ret = false;
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeUpdateThrowException(connTmp, updateSql, sync.MSG_TYPE, sync.STATE, sync.ERROR_MSG, sync.UPDATE_DATE, sync.CONFIG_SYNC_ID);
        } catch (SQLException e) {
            log.error("saveConfigSyncToTable exception:" + e);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public boolean createTpTaskRecordLogToTable(List<TpTaskRecordLog> list) {
        if (list == null || list.size() == 0) {
            return true;
        }

        String insertSql = "insert into dcoos_tp_task_record_log(id,task_id,batch_id,module_id,process_id,order_id,perl,start_cnt,end_cnt,normal_cnt,repeat_cnt,error_cnt,pre_cnt,abn_cnt,other_cnt,start_time,end_time,host_id) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean ret = false;
        List<List<Object>> listss = new ArrayList<>();
        for (TpTaskRecordLog taskLog : list) {
            List<Object> tmp = new ArrayList<>();
            String id = taskLog.getTask_id() + "-" + taskLog.getModule_id();
            tmp.add(id);
            tmp.add(taskLog.getTask_id());
            tmp.add(taskLog.getBatch_id());
            tmp.add(taskLog.getModule_id());
            tmp.add(taskLog.getProcess_id());
            tmp.add(taskLog.getOrder_id());
            tmp.add(taskLog.getPerl());
            tmp.add(taskLog.getStart_cnt());
            tmp.add(taskLog.getEnd_cnt());
            tmp.add(taskLog.getNormal_cnt());
            tmp.add(taskLog.getRepeat_cnt());
            tmp.add(taskLog.getError_cnt());
            tmp.add(taskLog.getPre_cnt());
            tmp.add(taskLog.getAbn_cnt());
            tmp.add(taskLog.getOther_cnt());
            tmp.add(taskLog.getStart_time());
            tmp.add(taskLog.getEnd_time());
            tmp.add(taskLog.getHost_id());
            listss.add(tmp);
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            ret = DBUtils.exeInsertBatch(connTmp, insertSql, listss);
        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }

    public boolean createModuleVersionToTable(List<TpModuleVersion> list) {
        if (list == null || list.size() == 0) {
            return true;
        }

        String insertSql = "insert into disaster.dcoos_tp_module_version(module_id, version_number, revision_date, revision_personnel, demand_number, git_pre_version, functionality, code_file_changes, revision_highlights, notes, version_code_completion_time, version_online_release_time) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) on conflict (module_id, version_number) do update set revision_date = excluded.revision_date, revision_personnel = excluded.revision_personnel, demand_number = excluded.demand_number, git_pre_version = excluded.git_pre_version, functionality = excluded.functionality, code_file_changes = excluded.code_file_changes, revision_highlights = excluded.revision_highlights, notes = excluded.notes, version_code_completion_time = excluded.version_code_completion_time, version_online_release_time = excluded.version_online_release_time";
        Connection connTmp = null;
        boolean ret = false;
        try {
            connTmp = DbPool.getConn();
            for (TpModuleVersion moduleVersion : list) {
                ret = DBUtils.exeUpdate(connTmp, insertSql, moduleVersion.getModuleId(), moduleVersion.getVersionNumber(), moduleVersion.getRevisionDate(), moduleVersion.getRevisionPersonnel(), moduleVersion.getDemandNumber(), moduleVersion.getGitPreVersion(), moduleVersion.getFunctionality(), moduleVersion.getCodeFileChanges(), moduleVersion.getRevisionHighlights(), moduleVersion.getNotes(), moduleVersion.getVersionCodeCompletionTime(), moduleVersion.getVersionOnlineReleaseTime());
            }

        } finally {
            DbPool.close(connTmp);
        }

        return ret;
    }


    public void offerG_createProcessLogQueue(TpProcess tpProcess) {
        this.g_createProcessLogQueue.offer(tpProcess);
    }

    public void offerG_createTgAlterLogQueue(String msg) {
        this.g_createTgAlertLogQueue.offer(msg);
    }

    public void offerG_saveProcessQueue(TpProcess tpProcess) {
        this.g_saveProcessQueue.offer(tpProcess);
    }

    public void offerG_saveTaskQueue(TpTaskRecord tpTaskRecord) {
        this.g_saveTaskQueue.offer(tpTaskRecord);
    }

    public void offerG_createTaskQueue(TpTaskRecord tpTaskRecord) {
        this.g_createTaskQueue.offer(tpTaskRecord);
    }

    public void offerG_createConfigSyncQueue(MsgBody.ConfigSyncBody configSyncBody) {
        this.g_createConfigSyncQueue.offer(configSyncBody);
    }

    public void offerG_saveConfigSyncQueue(MsgBody.ConfigSyncBody configSyncBody) {
        this.g_saveConfigSyncQueue.offer(configSyncBody);
    }

    public void offerG_createTaskLogQueue(List<TpTaskRecordLog> list) {
        this.g_createTaskLogQueue.offer(list);
    }


    public void offerG_createModuleVersionQueue(TpModuleVersion tpModuleVersion) {
        this.g_createModuleVersionQueue.offer(tpModuleVersion);
    }
}
