package com.itco.dispatch.thread;

import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class MasterSlaveManager implements Runnable {
    Log log = LogFactory.getLog(MasterSlaveManager.class);

    MessageManager messageManager;
    int overTimeSec = 0;
    String mode = null;

    public boolean init() {
        overTimeSec = 30;
        mode = new String("master");


        return true;
    }

    @Override
    public void run() {
        try {
            while (!Factory.isbWhileFlag()) {

                // 监控进心跳进程
                String msg = messageManager.getHeartReceMsg();
                if (msg != null) {
                    log.info("messageManager.getHeartReceMsg():" + msg);
                }


            }
        } catch (Exception e) {
            log.info("exception:" + e.getMessage());
        }
    }

    public void setMessageManager(MessageManager messageManager) {
        this.messageManager = messageManager;
    }
}
