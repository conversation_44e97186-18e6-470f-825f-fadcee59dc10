package com.itco.dispatch.thread;

import com.itco.framework.Factory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class ClusterManager implements Runnable {
    Log log = LogFactory.getLog(ClusterManager.class);
    String name;

    @Override
    public void run() {
        log.info("集群管理线程 启动");
        while (!Factory.isbWhileFlag()) {


            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                Thread.currentThread().interrupt();
            }
        }

        log.info("集群管理线程 关闭");
        //Dispatch.close();
        Factory.close();
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
