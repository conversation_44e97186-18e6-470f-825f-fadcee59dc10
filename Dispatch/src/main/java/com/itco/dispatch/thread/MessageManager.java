package com.itco.dispatch.thread;

import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dispatch.entity.entitydef.TaskMessageEntity;
import com.itco.entity.common.Common;
import com.itco.framework.Factory;
import com.itco.framework.message.TaskMessage;
import com.itco.framework.message.TaskMessageFactory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.concurrent.LinkedBlockingQueue;

public class MessageManager implements Runnable {
    static Log log = LogFactory.getLog(MessageManager.class);
    Common common = null;

    TaskMessage taskMessage = null;
    ZkClientApi zkClientApi = null;
    int sendMsgFaileCnt = 0;

    LinkedBlockingQueue<TaskMessageEntity> g_ResponseJsonList = new LinkedBlockingQueue<TaskMessageEntity>();

    public boolean init() {
        if (!common.isWorkFlag()) {
            taskMessage = new TaskMessageFactory().getTaskMessageInstanceByMode(common.getTaskTransferMode());
            if (taskMessage == null) {
                log.error("任务消息管理类初始化失败，配置的消息通讯模式异常task_transfer_mode：" + common.getTaskTransferMode());
                Factory.setbWhileFlag(true);
                return false;
            }

            // 初始化
            taskMessage.setZkClientApi(zkClientApi);
            taskMessage.setCommon(common);
            taskMessage.setbDispatch(true);
            if (!taskMessage.init()) {
                Factory.setbWhileFlag(true);
                return false;
            }
        }

        return true;
    }

    public boolean listenTopic() {
        if (!taskMessage.startReceivedMessage()) {
            log.error("taskMessage.startReceivedMessage() faile");
            Factory.setbWhileFlag(true);
            return false;
        }
        return true;
    }

    //  获取应答任务包
    public TaskMessageEntity pollResponseJsonTask() {
        return g_ResponseJsonList.poll();
    }

    //  发送消息
    public void offerResponseJosnTask(TaskMessageEntity json) {
        if (!g_ResponseJsonList.offer(json)) {
            log.error("g_ResponseJsonList.offer(json) faile");
        }
    }

    // 消息发送接口
    synchronized boolean msgSendInterface(TaskMessageEntity taskMsg) {
        if (!taskMessage.sendMessage(taskMsg.getKey(), taskMsg.getMsg())) {
            log.error("taskMessage.sendMessage(" + taskMsg.getKey() + "," + taskMsg.getMsg() + ")) faile");
            return false;
        }
        log.info("发送任务消息成功:" + taskMsg.getKey() + "," + taskMsg.getMsg());
        return true;
    }

    //任务管理处理
    public String taskMsgReceInterface() {
        //接收模块,获取可用任务数量
        String msg = taskMessage.pollDbIncept();
        if (msg != null) {
            return msg;
        }

        //优先处理重做
        msg = taskMessage.pollRollbackRequest();
        if (msg != null) {
            return msg;
        }
        //处理话单任务
        msg = taskMessage.pollTaskRecord();
        if (msg != null) {
            return msg;
        }
        return null;
    }

    //进程管理处理
    public String processMsgReceInterface() {
        //1、优先处理同步任务
        String msg = taskMessage.pollConfigSyncResponse();
        if (msg != null) {
            return msg;
        }

        //2、是否有进程控制消息
        msg = taskMessage.pollSsProcessRequest();
        if (msg != null) {
            return msg;
        }

        // 处理进程状态上报
        msg = taskMessage.pollProcess();
        if (msg != null) {
            return msg;
        }

        // 处理日志等级开关
        msg = taskMessage.poolDebugRequest();
        if (msg != null) {
            return msg;
        }

        return null;
    }


    //主备管理处理
    public String getHeartReceMsg() {
        return taskMessage.pollHeart();
    }

    @Override
    public void run() {
        log.info("消息管理线程 启动");
        try {
            while (!Factory.isbWhileFlag()) {

                // 发送处理完成任务
                TaskMessageEntity responseJson = pollResponseJsonTask();
                if (responseJson != null) {
                    if (!msgSendInterface(responseJson)) {
                        if (sendMsgFaileCnt >= 2) {
                            //发送失败，重新放回队列中。
                            offerResponseJosnTask(responseJson);
                            sendMsgFaileCnt++;
                        } else {
                            //超过3次告警短信，程序不处理了。

                        }
                        Thread.sleep(1000);
                    }
                } else {
                    sendMsgFaileCnt = 0;
                    Thread.sleep(100);
                }
            }
        } catch (InterruptedException e) {
            log.error("InterruptedException:" + e.getMessage());
            Thread.currentThread().interrupt();
        }

        log.info("消息管理线程 关闭");
        Factory.setbWhileFlag(true);
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void close() {
        taskMessage.close();
    }

}
