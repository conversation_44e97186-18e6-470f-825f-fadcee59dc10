package com.itco.dispatch.thread;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itco.component.zookeeper.RegisterJsonValue;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.dispatch.entity.EntityMgr;
import com.itco.dispatch.entity.entitydef.TaskMessageEntity;
import com.itco.entity.common.*;
import com.itco.entity.process.MsgBody;
import com.itco.framework.Factory;
import com.itco.framework.log.LogAlert;
import com.itco.framework.log.LogLevelSpring;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;

import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_CONNECTION_LOSS;
import static com.itco.entity.common.ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED;

public class ProcessManager implements Runnable {
    static Log log = LogFactory.getLog(ProcessManager.class);
    MessageManager messageManager = null;

    // 配置同步任务
    Map<String, MsgBody.ConfigSyncBody> mConfigTaskMap = new HashMap<>();

    //zk客户端
    ZkClientApi zkClientApi = null;
    EntityMgr entityMgr = null;

    //定时监听配置生效目录的间隔时长:默认6秒中
    int m_iCycleSec = 0;

    int iZookeeperExpiredReconnectCnt = 0;
    int iZookeeperLossReconnectCnt = 0;
    int iZookeeperNoExistsDispatchCnt = 0;

    Common common = null;

    // 进程管理初始化，读取ZK基础信息
    public boolean init() {
        // 从ZK上读取已经启动的进程列表
        List<TpProcess> zkProMapList = zkClientApi.getProcessListFromZk();

        //进程初始化状态，同步数据库
        initProcessStat(zkProMapList);

        String propFileName = zkClientApi.getModuleCode() + ".properties";
        Properties DispProp = zkClientApi.getPropertiesFromZK(propFileName);

        String tmp = DispProp.getProperty("cycle.message.config");
        if (StringUtils.isNumber(tmp)) {
            m_iCycleSec = Integer.parseInt(tmp);
            zkClientApi.setiMonitorCycleSecs(m_iCycleSec);
        }

        log.info("init print mTpProcess close，m_iConfigSyncId：");
        return true;
    }

    public boolean initProcessStat(List<TpProcess> zkProcessList) {
        for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
            boolean flag = false;
            // 已经启动的进程
            for (TpProcess pro : zkProcessList) {
                if (itertor.getKey().equals(String.valueOf(pro.getProcess_id()))) {
                    itertor.getValue().setSystem_process_id(pro.getSystem_process_id());
                    itertor.getValue().setStatus(pro.getStatus());
                    itertor.getValue().setCreate_date(pro.getCreate_date());
                    itertor.getValue().setExit_date("");
                    itertor.getValue().setIsPause(pro.getIsPause());
                    itertor.getValue().setVersion(pro.getVersion());
                    itertor.getValue().setHost_name(pro.getHost_name());
                    itertor.getValue().setHost_ip(pro.getHost_ip());
                    if (itertor.getValue().getStatus().equals(PROCESS_STATE.TA.state) || itertor.getValue().getStatus().equals(PROCESS_STATE.TD.state)) {
                        itertor.getValue().setUpdate_date(pro.getUpdate_date());
                    }
                    entityMgr.saveDbProcessToTable(itertor.getValue());
                    flag = true;
                }
            }

            if (!flag) { //没有启动的进程
                itertor.getValue().setSystem_process_id(-1);
                if (itertor.getValue().getStatus().equals(PROCESS_STATE.TB.state) ||
                        itertor.getValue().getStatus().equals(PROCESS_STATE.TC.state) ||
                        itertor.getValue().getStatus().equals(PROCESS_STATE.TE.state) ||
                        itertor.getValue().getStatus().equals(PROCESS_STATE.TD.state)) {
                    itertor.getValue().setStatus(PROCESS_STATE.TA.state);
                }
                entityMgr.saveDbProcessToTable(itertor.getValue());
            }
        }

        return true;
    }

    // 当zk的/process目录下模块子节点有变化时，更新进程状态
    public boolean updateProccessStat(Map<String, List<TpProcess>> zkProListMap) {
        for (Map.Entry<String, List<TpProcess>> zkChangeProList : zkProListMap.entrySet()) {
            // 如果模块全部退出了，只有返回模块名称，空闲和运行的状态改为关闭
            if (zkChangeProList.getValue().size() == 0) {
                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if (zkChangeProList.getKey().equals(itertor.getValue().getModule_code())) {
                        if (itertor.getValue().getStatus().equals(PROCESS_STATE.TB.state)) {
                            itertor.getValue().setSystem_process_id(-1);
                            itertor.getValue().setStatus(PROCESS_STATE.TA.state);
                            itertor.getValue().setExit_date(Factory.getSystemDateStr());
                            entityMgr.createTpProcessLogToTable(itertor.getValue());
                            log.info(itertor.getValue().getModule_code() + "->" + itertor.getValue().getProcess_id() + " 进程注销成功，退出");
                        } else if (itertor.getValue().getStatus().equals(PROCESS_STATE.TC.state) ||
                                itertor.getValue().getStatus().equals(PROCESS_STATE.TE.state)) {
                            itertor.getValue().setSystem_process_id(-1);
                            itertor.getValue().setStatus(PROCESS_STATE.TD.state);
                            itertor.getValue().setExit_date(Factory.getSystemDateStr());
                            entityMgr.createTpProcessLogToTable(itertor.getValue());
                            log.info(itertor.getValue().getModule_code() + "->" + itertor.getValue().getProcess_id() + " 进程注销成功，异常退出");
                        }
                        itertor.getValue().setIsPause("0");
                        entityMgr.saveDbProcessToTable(itertor.getValue());
                    }
                }
                return true;
            } else {
                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if (itertor.getValue().getModule_code().equals(zkChangeProList.getKey())) {
                        boolean findFlag = false;
                        for (TpProcess zkProcess : zkChangeProList.getValue()) {
                            if (zkProcess.getProcess_id() == itertor.getValue().getProcess_id()) {
                                itertor.getValue().setSystem_process_id(zkProcess.getSystem_process_id());
                                itertor.getValue().setCreate_date(zkProcess.getCreate_date());
                                itertor.getValue().setExit_date("");
                                itertor.getValue().setHost_name(zkProcess.getHost_name());
                                itertor.getValue().setHost_ip(zkProcess.getHost_ip());
                                if (itertor.getValue().getStatus().equals(PROCESS_STATE.TA.state) || itertor.getValue().getStatus().equals(PROCESS_STATE.TD.state)) {
                                    itertor.getValue().setStatus(zkProcess.getStatus());
                                    itertor.getValue().setUpdate_date(zkProcess.getUpdate_date());
                                    itertor.getValue().setPerl(0);
                                    itertor.getValue().setIsPause(zkProcess.getIsPause());
                                    itertor.getValue().setVersion(zkProcess.getVersion());
                                    entityMgr.createTpProcessLogToTable(itertor.getValue());
                                    log.info(itertor.getValue().getModule_code() + "->" + itertor.getValue().getProcess_id() + " 进程注册成功");
                                }
                                findFlag = true;
                            }
                        }
                        if (!findFlag) {
                            if (itertor.getValue().getStatus().equals(PROCESS_STATE.TB.state)) {
                                itertor.getValue().setIsPause(null);
                                itertor.getValue().setSystem_process_id(-1);
                                itertor.getValue().setStatus(PROCESS_STATE.TA.state);
                                itertor.getValue().setExit_date(Factory.getSystemDateStr());
                                entityMgr.createTpProcessLogToTable(itertor.getValue());
                                log.info(itertor.getValue().getModule_code() + "->" + itertor.getValue().getProcess_id() + " 进程注销成功，退出");
                            } else if (itertor.getValue().getStatus().equals(PROCESS_STATE.TC.state) || itertor.getValue().getStatus().equals(PROCESS_STATE.TE.state)) {
                                itertor.getValue().setIsPause(null);
                                itertor.getValue().setSystem_process_id(-1);
                                itertor.getValue().setStatus(PROCESS_STATE.TD.state);
                                itertor.getValue().setExit_date(Factory.getSystemDateStr());
                                entityMgr.createTpProcessLogToTable(itertor.getValue());
                                log.info(itertor.getValue().getModule_code() + "->" + itertor.getValue().getProcess_id() + " 进程注销成功，异常退出");
                            }
                        }
                        entityMgr.saveDbProcessToTable(itertor.getValue());
                    }
                }
            }
        }

        return true;
    }

    // 根据zookeeper上/message/config的请求情况，进行配置同步通知
    boolean configSyncMessageRequest(List<String> list) {
        for (String json : list) {
            String process_id = JSON.parseObject(json).getString("PROCESS_ID");
            if (process_id == null) {
                continue;
            }

            TpProcess tpProcess = entityMgr.getmTpProcessMap().get(process_id);
            if (tpProcess == null) {
                continue;
            }
            if (tpProcess.getStatus().equals(PROCESS_STATE.TB.state) ||
                    tpProcess.getStatus().equals(PROCESS_STATE.TC.state) ||
                    tpProcess.getStatus().equals(PROCESS_STATE.TE.state)) {//进程在活

                TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(tpProcess.getProcess_id()), json);
                messageManager.offerResponseJosnTask(taskMessageEntity);

                MsgBody.ConfigSyncBody body = JSON.parseObject(json, MsgBody.ConfigSyncBody.class);
                entityMgr.createConfigSyncToTable(body);
                mConfigTaskMap.put(String.valueOf(body.CONFIG_SYNC_ID), body);
            }
        }
        return true;
    }

    boolean resetRegister(boolean flag, String msg) {
        //zkClientApi.close();
        if (flag) {
            boolean bRet = zkClientApi.init();
            if (bRet) {
                //重新注册，之间修改状态为0 ，非挂起
                if (!zkClientApi.register(false, "0")) {
                    log.error(msg + "，进程重新注册失败");
                    return false;
                } else {
                    log.info(msg + "，进程重新注册成功");
                    log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
                }
            }
        } else {
            //重新注册，之间修改状态为0 ，非挂起
            if (!zkClientApi.register(false, "0")) {
                log.error(msg + "，进程重新注册失败");
                return false;
            } else {
                log.info(msg + "，进程重新注册成功");
                log.info(zkClientApi.getProcessID() + " ,进程准备接收任务成功。");
            }
        }
        return true;
    }

    // 进程管理主函数
    @Override
    public void run() {
        int iCnt = 0;
        log.info("进程管理线程 启动");
        try {

            while (!Factory.isbWhileFlag()) {
                // 监控zk看是否有进程变化
                Map<String, List<String>> changeModuleList = zkClientApi.pollgChangeModuleList();
                if (changeModuleList != null) {
                    for (Map.Entry<String, List<String>> itertor : changeModuleList.entrySet()) {
                        //log.info("ProcessManager ，接收到子节点有变化，start");

                        // 获取变化所有子节点信息
                        Map<String, List<RegisterJsonValue>> changeMap = zkClientApi.getModuleChangeEvent(itertor.getKey(), itertor.getValue());
                        Map<String, List<TpProcess>> processMap = new HashMap<>();
                        for (Map.Entry<String, List<RegisterJsonValue>> change : changeMap.entrySet()) {
                            List<TpProcess> list = processMap.get(change.getKey());
                            List<TpModuleVersion> moduleVersions = new ArrayList();
                            if (list == null) {
                                list = new ArrayList<>();
                                for (RegisterJsonValue registerJsonValue : change.getValue()) {
                                    list.add(registerJsonValue.getTpProcess());
                                    moduleVersions.addAll(registerJsonValue.getModuleVersions());
                                }
                                processMap.put(change.getKey(), list);
                            } else {
                                for (RegisterJsonValue registerJsonValue : change.getValue()) {
                                    list.add(registerJsonValue.getTpProcess());
                                    moduleVersions.addAll(registerJsonValue.getModuleVersions());
                                }
                            }
                            entityMgr.createTpmoduleVersionToTable(moduleVersions);
                        }

                        // 更新local process进程状态
                        updateProccessStat(processMap);
                    }
                    //log.info("ProcessManager ，接收到子节点有变化，close");
                }

                //  判断zookeeper是否断开,断开重连
                int connectionLoss = zkClientApi.isConnectionLoss();
                if (connectionLoss < 0) {
                    if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_CONNECTION_LOSS) {
                        if (zkClientApi.getiConnectionLossCnt() == 1) {
                            TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                            LogAlert.alert(tgAlertLog);
                        }

                        if (zkClientApi.getiConnectionLossCnt() % 10 == 1 && iZookeeperLossReconnectCnt < 3) {
                            if (!resetRegister(true, "zookeeper失去连接")) {
                                iZookeeperLossReconnectCnt++;
                            } else {
                                iZookeeperExpiredReconnectCnt = 0;
                                iZookeeperLossReconnectCnt = 0;
                            }
                        }
                    } else if (zkClientApi.getErrorCode() == ZOOKEEPER_EXCEPTION_SESSION_EXPIRED) {
                        if (zkClientApi.getiSectionExpiredCnt() == 1) {
                            TgAlertLog tgAlertLog = new TgAlertLog(zkClientApi.getErrorCode(), Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + zkClientApi.getErrorCode() + "，zookeeper连接异常，" + zkClientApi.getErrorMsg());
                            LogAlert.alert(tgAlertLog);
                        }

                        if (zkClientApi.getiSectionExpiredCnt() % 10 == 1 && iZookeeperExpiredReconnectCnt < 3) {
                            // 连续10次超时，就重新连接
                            TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES, Long.parseLong(zkClientApi.getProcessID()));
                            tgAlertLog.setLog_desc("告警类型:" + ErrorType.ZOOKEEPER_EXCEPTION_SESSION_EXPIRED_10_TIMES + "，zookeeper的section连续10次失效");
                            LogAlert.alert(tgAlertLog);

                            if (!resetRegister(true, "zookeeper的section连续10次失效")) {
                                iZookeeperExpiredReconnectCnt++;
                            } else {
                                iZookeeperLossReconnectCnt = 0;
                                iZookeeperExpiredReconnectCnt = 0;
                            }

                        }
                    }
                } else {
                    if (connectionLoss == 0) {
                        log.error(zkClientApi.getProcessID() + ",zookeeper注册丢失");
                        TgAlertLog tgAlertLog = new TgAlertLog(ErrorType.BILL_PUBLIC_PROCESS_CONNECTION_LOSE, Long.parseLong(zkClientApi.getProcessID()));
                        tgAlertLog.setLog_desc("zookeeper注册丢失");
                        LogAlert.alert(tgAlertLog);

                        //重新注册，之间修改状态为0 ，非挂起
                        if (!resetRegister(false, "zookeeper注册丢失")) {
                            iZookeeperNoExistsDispatchCnt++;
                        } else {
                            iZookeeperNoExistsDispatchCnt = 0;
                        }
                    }
                }

                //  配置同步请求处理
                List<String> configSyncList = zkClientApi.getDataByMessageConfig();
                if (configSyncList != null && configSyncList.size() > 0) {
                    log.info("configSyncList.size():" + configSyncList.size());
                    //配置同步任务请求处理
                    if (!configSyncMessageRequest(configSyncList)) {
                        log.info("ProcessManager，接收到配置同步命令执行失败。");
                    }
                }

                // 同步应答、进程状态、日志等级控制、等处理
                String msg = null;
                do {
                    msg = messageManager.processMsgReceInterface();
                    if (msg != null) {
                        log.info("messageManager.processMsgReceInterface():" + msg);

                        JSONObject tmpBodyJson = JSON.parseObject(msg);
                        String taskType = tmpBodyJson.getString("TASK_TYPE");
                        String msgType = tmpBodyJson.getString("MSG_TYPE");
                        if ("RESPONSE".equals(msgType)) {
                            if (taskType.equals(MsgBody.TASK_TYPE.T_CONFIG.getType())) {
                                configSyncResponse(msg);
                            }
                        } else if ("REQUEST".equals(msgType) || msgType == null) {
                            if (taskType.equals(MsgBody.TASK_TYPE.T_PROCESS.getType())) {
                                processStateRequest(msg);
                            } else if (taskType.equals(MsgBody.TASK_TYPE.T_SS_PROCESS.getType())) {
                                processSsProcessRequest(msg);
                            } else if (taskType.equals(MsgBody.TASK_TYPE.T_DEBUG.getType())) {
                                processDebugRequest(msg);
                            } else {
                                log.error("无法识别的任务类型:" + taskType);
                            }
                        } else {
                            log.error("无法识别的消息类型:" + msgType);
                        }
                    }
                } while (msg != null);
            }

            Thread.sleep(10);
        } catch (InterruptedException e) {
            log.error("InterruptedException:" + e.getMessage());
            Thread.currentThread().interrupt();
        }
        //Dispatch.close();
        Factory.close();
        log.info("进程管理线程 关闭");
    }

    public boolean configSyncResponse(String msg) {
        JSONObject object = JSONObject.parseObject(msg);
        // 配置同步任务应答处理
        String configSyncId = object.getString("CONFIG_SYNC_ID");
        if (configSyncId == null) {
            log.info("不存在CONFIG_SYNC_ID:" + configSyncId + ",的配置同步请求ID");
            return false;
        }
        MsgBody.ConfigSyncBody body = JSON.parseObject(msg, MsgBody.ConfigSyncBody.class);
        entityMgr.saveConfigSyncToTable(body);

        mConfigTaskMap.remove(configSyncId);
        return true;
    }

    public boolean processStateRequest(String msg) {
        JSONObject object = JSONObject.parseObject(msg);
        // 监控非主流程进程状态变化
        switchProcessState(object.getString("PROCESS_ID"), object.getString("STATUS"), 0);

        return true;
    }

    public boolean processSsProcessRequest(String msg) {
        MsgBody.StartStopProcessBody body = JSON.parseObject(msg, MsgBody.StartStopProcessBody.class);

        if ("EXIT".equals(body.ACTION)) {
            if (body.MODULE_ID != null && !"".equals(body.MODULE_ID)) {
                int moduleId = Integer.parseInt(body.MODULE_ID);

                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if ((itertor.getValue().getModule_id() == moduleId || moduleId == 0) && itertor.getValue().getSystem_process_id() > 0) {
                        itertor.getValue().setIsPause("1");//发送退出命令，开启暂停发送任务
                        TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(itertor.getValue().getProcess_id()), msg);
                        messageManager.offerResponseJosnTask(taskMessageEntity);
                    }
                }
            } else if (body.PROCESS_ID != null && !"".equals(body.PROCESS_ID)) {
                TpProcess tpProcess = entityMgr.getmTpProcessMap().get(body.PROCESS_ID);
                if (tpProcess != null) {
                    tpProcess.setIsPause("1");//发送退出命令，开启暂停发送任务
                    TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(tpProcess.getProcess_id()), msg);
                    messageManager.offerResponseJosnTask(taskMessageEntity);
                }
            }
        } else if ("STOP".equals(body.ACTION)) {
            if (body.MODULE_ID != null && !"".equals(body.MODULE_ID)) {
                int moduleId = Integer.parseInt(body.MODULE_ID);

                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if ((itertor.getValue().getModule_id() == moduleId || moduleId == 0) && itertor.getValue().getSystem_process_id() > 0) {
                        itertor.getValue().setIsPause("1");
                        // 更新数据库状态
                        entityMgr.saveDbProcessToTable(itertor.getValue());
                    }
                }
            } else if (body.PROCESS_ID != null && !"".equals(body.PROCESS_ID)) {
                TpProcess tpProcess = entityMgr.getmTpProcessMap().get(body.PROCESS_ID);
                if (tpProcess != null) {
                    tpProcess.setIsPause("1");
                    // 更新数据库状态
                    entityMgr.saveDbProcessToTable(tpProcess);
                }
            }
        } else if ("START".equals(body.ACTION)) {
            if (body.MODULE_ID != null && !"".equals(body.MODULE_ID)) {
                int moduleId = Integer.parseInt(body.MODULE_ID);

                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if ((itertor.getValue().getModule_id() == moduleId || moduleId == 0) && itertor.getValue().getSystem_process_id() > 0) {
                        itertor.getValue().setIsPause("0");
                        if (!StringUtils.isEmpty(body.VERSION)) {
                            itertor.getValue().setVersion(body.VERSION);
                        }
                        // 更新数据库状态
                        entityMgr.saveDbProcessToTable(itertor.getValue());
                    }
                }
            } else if (body.PROCESS_ID != null && !"".equals(body.PROCESS_ID)) {
                TpProcess tpProcess = entityMgr.getmTpProcessMap().get(body.PROCESS_ID);
                if (tpProcess != null) {
                    tpProcess.setIsPause("0");
                    if (!StringUtils.isEmpty(body.VERSION)) {
                        tpProcess.setVersion(body.VERSION);
                    }
                    // 更新数据库状态
                    entityMgr.saveDbProcessToTable(tpProcess);
                }
            }
        }

        return true;
    }

    public boolean processDebugRequest(String msg) {
        MsgBody.DebugBody body = JSON.parseObject(msg, MsgBody.DebugBody.class);

        if (body.MODULE_ID != null && !"".equals(body.MODULE_ID)) {
            int moduleId = Integer.parseInt(body.MODULE_ID);
            if (moduleId == 1010 && (!"ELEMENT".equals(body.LEVEL))) {
                TpProcess tpProcess = entityMgr.getmTpProcessMap().get("10100000");
                log.info(tpProcess.toString());
                log.info(body.toString());
                tpProcess.setDebugLevel(body.LEVEL);//发送退出命令，开启暂停发送任务
                LogLevelSpring.debugProcess(common, body);
                System.out.println("当前进程信息：");

                SortedMap<String, TpProcess> sortMap = new TreeMap<String, TpProcess>(entityMgr.getmTpProcessMap());
                Set<Map.Entry<String, TpProcess>> entry1 = sortMap.entrySet();
                Iterator<Map.Entry<String, TpProcess>> it = entry1.iterator();
                while (it.hasNext()) {
                    Map.Entry<String, TpProcess> entry3 = it.next();
                    System.out.println(entry3.getKey() + "-->" + entry3.getValue());
                }
            } else {
                for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
                    if ((itertor.getValue().getModule_id() == moduleId) && itertor.getValue().getSystem_process_id() > 0) {
                        itertor.getValue().setDebugLevel(body.LEVEL);//发送退出命令，开启暂停发送任务
                        TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(itertor.getValue().getProcess_id()), msg);
                        messageManager.offerResponseJosnTask(taskMessageEntity);
                    }
                }
            }
        } else if (body.PROCESS_ID != null && !"".equals(body.PROCESS_ID)) {
            TpProcess tpProcess = entityMgr.getmTpProcessMap().get(body.PROCESS_ID);
            if (tpProcess != null) {
                if (tpProcess.getProcess_id() == 10100000 && (!"ELEMENT".equals(body.LEVEL))) {
                    tpProcess.setDebugLevel(body.LEVEL);//发送退出命令，开启暂停发送任务
                    LogLevelSpring.debugProcess(common, body);
                } else if (tpProcess.getSystem_process_id() > 0) {
                    tpProcess.setDebugLevel(body.LEVEL);//发送退出命令，开启暂停发送任务
                    TaskMessageEntity taskMessageEntity = new TaskMessageEntity(String.valueOf(tpProcess.getProcess_id()), msg);
                    messageManager.offerResponseJosnTask(taskMessageEntity);
                }
            }
        }

        return true;
    }

    public void show() {
        log.info("show start");
        for (Map.Entry<String, TpProcess> itertor : entityMgr.getmTpProcessMap().entrySet()) {
            log.info(itertor.getValue().getProcess_name() + "," + itertor.getValue().toString());
        }
        log.info("show close");
    }

    public boolean switchProcessState(String process_id, String state, int perl) {
        TpProcess pro = entityMgr.getmTpProcessMap().get(process_id);

        if (pro == null) {
            log.info("switchProcessState:" + process_id + ",state:" + state + ",进程实例号不存在");
            return false;
        }

        pro.setUpdate_date(Factory.getSystemDateStr());
        if (state != null) {
            pro.setStatus(state);
        }
        if (perl > 0) {
            pro.setPerl(perl);
        }

        entityMgr.saveDbProcessToTable(pro);
        return true;
    }

    public String getProcessId() {
        return zkClientApi.getProcessID();
    }

    public ZkClientApi getZkClientApi() {
        return zkClientApi;
    }

    public void setMessageManager(MessageManager messageManager) {
        this.messageManager = messageManager;
    }

    public void setZkClientApi(ZkClientApi zkClientApi) {
        this.zkClientApi = zkClientApi;
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public void setEntityMgr(EntityMgr entityMgr) {
        this.entityMgr = entityMgr;
    }


    public enum PROCESS_STATE {
        TA(1, "TA", "关闭"),
        TB(2, "TB", "空闲"),
        TC(3, "TC", "处理中"),
        TD(4, "TD", "异常关闭"),
        TE(5, "TE", "已分配任务");

        int order_id;
        String state;
        String remark;

        public String getState() {
            return state;
        }

        PROCESS_STATE(int order_id, String state, String remark) {
            this.order_id = order_id;
            this.state = state;
            this.remark = remark;
        }
    }
}
