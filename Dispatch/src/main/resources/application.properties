#log å¼å³
logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
#ä¸æå°spring boot å¯å¨çæ¥å
logging.level.org.springframework.boot.autoconfigure=ERROR

#è¾åºæ¥å¿æä»¶
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.file.name=${LOG_HOME:~/javalog}/Dispatch.log
logging.file.max-size=50MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
logging.file.max-history=30
logging.file.total-size-cap=100MB

#åå¸çæ¬
version=Dispatch_deploy_2023-04-02 11:00