--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2024-04-20 11:00
【修订日期】：2024-04-20
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：55057
【实现功能】：
1、进程注册添加生成disaster.dcoos_tp_module_version版本日志表数据。
实现原理，就是进程注册的时候，bill-public的代码会去读取version.txt文件最新的5条记录，和tp_process信息一起写入/process/DbIncept/DbIncept-10111111的目录中。
调度读取目录信息，并增量更新到disaster.dcoos_tp_module_version表中。

【变更文件】：
修改：bill-public/src/main/java/com/itco/BillApplication.java
修改：bill-public/src/main/java/com/itco/component/jdbc/DbPool.java
修改：bill-public/src/main/java/com/itco/component/jdbc/DBUtils.java
修改：bill-public/src/main/java/com/itco/component/zookeeper/ZkClientApi.java
修改：bill-public/src/main/java/com/itco/entity/common/Common.java
修改：bill-public/src/main/java/com/itco/entity/common/TpProcess.java
修改：bill-public/src/main/java/com/itco/framework/process/Process.java
修改：bill-public/src/main/java/com/itco/framework/Version.java
修改：Dispatch/src/main/java/com/itco/dispatch/entity/EntityMgr.java
修改：Dispatch/src/main/java/com/itco/dispatch/thread/DatabaseManager.java
修改：Dispatch/src/main/java/com/itco/dispatch/thread/ProcessManager.java
新增：bill-public/src/main/java/com/itco/component/zookeeper/MonitorWatcher.java
新增：bill-public/src/main/java/com/itco/component/zookeeper/RegisterJsonValue.java
新增：bill-public/src/main/java/com/itco/entity/common/TpModuleVersion.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-04-02 11:00
【修订日期】：2023-04-02
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：55057
【实现功能】：
1、单条循环更新改成，批量更新。

【变更文件】：
修改:src/main/java/com/itco/dispatch/thread/DatabaseManager.java

【修订要点】：
  1、优化了同步数据库表的方案，一次性读取队列的所有数据，在去批量更新。原先是一条一条更新，在更新数据库慢的情况下，只要队列还有数据就循环，导致一致更新任务状态，其他的sql语句堵塞。

【注意事项】：
  1、无
  --------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-03-10 11:00
【修订日期】：2023-03-10
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54934
【实现功能】：
1、新增dcoos_tp_task_info,每个小时的统计信息入这个表，方便统计，格式化展示。
2、error_type 重新编号，按照模块表示+4位序号，例如10100001
3、TgAlertLog 新增msg_type字段。

【变更文件】：
修改:src/main/java/com/itco/dispatch/entity/entitydef/ErrorType.java
修改:src/main/java/com/itco/dispatch/entity/EntityMgr.java
修改:src/main/java/com/itco/dispatch/thread/DatabaseManager.java
修改:src/main/java/com/itco/dispatch/thread/TaskRecordManager.java
修改:src/main/resources/version.txt
新增:src/main/java/com/itco/dispatch/entity/entitydef/TaskStatisticalData.java

【修订要点】：
  1、dcoos_tp_task_info建表语句
  CREATE TABLE config.dcoos_tp_task_info (
    type varchar(32),
    start_date varchar(32),
    end_date varchar(32),
    task_total int8,
    record_total int8,
    start_cnt int8,
    end_cnt int8,
    normal_cnt int8,
    pre_cnt int8,
    abn_cnt int8,
    other_cnt int8,
    repeat_cnt int8,
    error_cnt int8,
    perl int8,
    back_task_cnt int8,
    back_record_cnt int8,
    faile_task_cnt int8,
    faile_record_cnt int8,
    dbincept_cnt int4,
    preproc2_cnt int4,
    filter_cnt int4,
    rating_cnt int4,
    blocksett_cnt int4,
    ticketindb_cnt int4,
    remark varchar(128)
  )

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-03-02 11:00
【修订日期】：2023-03-02
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54687
【实现功能】：
1、默认每1小时，输出一次日志信息 ，包含：业务类型,开始时间,结束时间,总任务数,总记录数,输入记录数,输出记录数,正常单,预结单,异常单,不结算单,重单,错单,平均性能,
积压任务数,积压任务数,失败任务数,失败话单数,接收进程数量,预处理进程数量,排重进程数量,批价进程数量,结算进程数量,入库进程数量

【变更文件】：
修改：src/main/java/com/itco/dispatch/entity/entitydef/DispatchDef.java
修改：src/main/java/com/itco/dispatch/entity/entitydef/TaskStatisticalData.java
修改：src/main/java/com/itco/dispatch/thread/DatabaseManager.java
修改：src/main/java/com/itco/dispatch/thread/TaskRecordManager.java
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-02-21 11:00
【修订日期】：2023-02-21
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54687
【实现功能】：
1、DbInceptBody、StandardBody、RollbackBody三个消息体添加TASK_NAME字段；
2、调度接收到的任务，保存任务名称到任务表里。
3、zookeeper连接失败，重新连接错了修改调整，涉及调度,DbIncept,Collection,统一任务框架。

【变更文件】：
修改：src/main/java/com/itco/dispatch/thread/DatabaseManager.java
修改：src/main/java/com/itco/dispatch/thread/ProcessManager.java
修改：src/main/java/com/itco/dispatch/thread/TaskRecordManager.java
修改：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-02-14 11:00
【修订日期】：2023-02-14
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54545
【实现功能】：
1、调度、接收、主流程模块出现zookeeper连接异常时，输出告警日志到tg_alert_log表
1、默认每12个小时(Dispatch.properties的out_tasklog_interval项)，输出任务日志到tg_alert_log表
2、zookeeper的session连续失效10次，触发重新初始化zookeeper接口，重新注册
2、zookeeper的CONNECTION_LOSS、CONNECTION_REFUSED的异常，等待自动重连上
3、DbPool.init()新增接口，初始化使用
4、新增LogAlert输出告警日志

【变更文件】：
修改：src/main/java/com/itco/dispatch/entity/entitydef/ErrorType.java
修改：src/main/java/com/itco/dispatch/thread/ProcessManager.java
修改：src/main/java/com/itco/dispatch/thread/TaskRecordManager.java
新增：src/main/java/com/itco/dispatch/entity/entitydef/DispatchDef.java
新增：src/main/java/com/itco/dispatch/entity/entitydef/TaskStatisticalData.java


【修订要点】：
  1、select * from tg_alert_log where code_id=2001 order by log_id desc;查询任务处理情况。
  2、如果12小时内没有处理任何话单，则不输出任务处理情况日志。
  3、Dispatch.properties的out_tasklog_interval项配置为0，则是关闭次功能,单位秒。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-01-30 11:00
【修订日期】：2023-01-30
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54440
【实现功能】：
1、调度新增进程退出，任务处理失败、处理超时的告警日志到tg_alert_log表
2、bill-public新增告警类TgAlertLog和异常类型类ErrorType

【变更文件】：
修改：bill-public/src/main/java/com/itco/component/jdbc/DBUtils.java
修改：bill-public/src/main/java/com/itco/entity/common/Common.java
修改：bill-public/src/main/java/com/itco/framework/process/impl/SettleProcessManager.java
修改：Dispatch/src/main/java/com/itco/dispatch/Dispatch.java
修改：Dispatch/src/main/java/com/itco/dispatch/entity/entitydef/TpTaskRecord.java
修改：Dispatch/src/main/java/com/itco/dispatch/entity/EntityMgr.java
修改：Dispatch/src/main/java/com/itco/dispatch/thread/DatabaseManager.java
修改：Dispatch/src/main/java/com/itco/dispatch/thread/TaskRecordManager.java
新增：bill-public/src/main/java/com/itco/entity/common/ErrorType.java
新增：bill-public/src/main/java/com/itco/entity/common/TgAlertLog.java

【修订要点】：
  1、新增建表语句如下：
  CREATE TABLE "config"."tg_alert_log" (
      "log_id" SERIAL NOT NULL ,
      "code_id" int4,
      "pf_id" int8,
      "create_date" timestamp(6),
      "log_desc" varchar(4000) COLLATE "pg_catalog"."default",
      "state" varchar(3) COLLATE "pg_catalog"."default" DEFAULT '9SA'::character varying,
      "state_date" timestamp(6) DEFAULT LOCALTIMESTAMP
    )
    ;
   ALTER TABLE "config"."tg_alert_log" ADD CONSTRAINT "tg_alert_log_pkey" PRIMARY KEY ("log_id");

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：bill-public_2023-01-17 11:00
【修订日期】：2023-01-17
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54263
【实现功能】：
1、StandardBody消息包新增 int INVALID_CNT;//无效单。
2、处理完的话单文件，正常单，预结单，异常单，不计费话单，无效单，统计优化。
3、新增版本日志功能 java -jar Dispatch-0.0.1-SNAPSHOT.jar version -n5 ,可以查看最近的五个版本日志。

【变更文件】：
main/java/com/itco/entity/common/Common.java
main/java/com/itco/entity/process/MsgBody.java
main/java/com/itco/framework/calcrecord/CalcRecordManager.java
main/java/com/itco/framework/message/impl/TaskMsgCtgMq.java
main/java/com/itco/framework/process/impl/SettleProcessManager.java
main/java/com/itco/framework/Version.java

【修订要点】：
  1、新增版本日志。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：Dispatch_code_2023-01-09 17:00
【修订日期】：2023-01-09
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54342
【实现功能】：
调度退出之后，dcoos_tp_process表的状态会更新，dcoos_tp_process_log日志表有启动和退出日志。

【变更文件】： 
Dispatch/src/main/java/com/itco/dispatch/Dispatch.java
Dispatch/src/main/java/com/itco/dispatch/thread/ProcessManager.java
 
【修订要点】：
  1、调度支持启动和退出进程表和进程日志表有记录。

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------