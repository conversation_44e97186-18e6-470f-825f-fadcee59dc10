#logging:
#  pattern:
#    console: "%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n"
#    file: "%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n"
#    rolling-file-name: ${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
#  file:
#    name: ${LOG_HOME:~/javalog}/Dispatch.log
#    max-size: 50MB
#    max-history: 30
#    total-size-cap: 100MB
#  level:
#    root: WARN
#    com.itco: INFO
#    org.apache.zookeeper: WARN
#    org.springframework.boot.autoconfigure: ERROR