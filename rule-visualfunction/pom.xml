<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.itco</groupId>
        <artifactId>settle</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.itco</groupId>
    <artifactId>rule-visualfunction</artifactId>
    <version>5.0.1-SNAPSHOT</version>
    <name>rule-visualfunction</name>
    <description>VisualFunction project for Spring Boot</description>

    <dependencies>

        <dependency>
            <groupId>com.itco</groupId>
            <artifactId>bill-public</artifactId>
            <version>5.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus-dev</id>
            <url>http://***********:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-dev</id>
            <url>http://***********:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
