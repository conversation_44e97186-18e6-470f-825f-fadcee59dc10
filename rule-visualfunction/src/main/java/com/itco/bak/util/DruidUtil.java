package com.itco.bak.util;

import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.itco.component.jdbc.DecodePassword;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;
//import com.ffcs.security.Decrypt;

public class DruidUtil {

	private static DataSource dataSource;

	static {
		InputStream is = null;
		try {
			String filePathName ="/dataBase.properties";
			Resource resource = new ClassPathResource(filePathName);
			Properties properties = PropertiesLoaderUtils.loadProperties(resource);
			String host = properties.getProperty("host");
			String port = properties.getProperty("port");
			String dbName = properties.getProperty("dbname");

			properties.setProperty("driverClassName", "org.postgresql.Driver");
			properties.setProperty("url", "jdbc:postgresql://"+ host + ":" + port + "/" +dbName + "?useUnicode=true&characterEncoding=UTF-8&useSSL=true&serverTimezone=UTC&allowMultiQueries=true");

			properties.setProperty("password", DecodePassword.decryption(properties.getProperty("publicKey"), properties.getProperty("password"))); // 密码解密
			dataSource = DruidDataSourceFactory.createDataSource(properties);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public static Connection getConnection() throws SQLException {
		return dataSource.getConnection();
	}

	public static void close(Connection conn, PreparedStatement pstmt, ResultSet rs) {
		if (rs != null) {
			try {
				rs.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if (pstmt != null) {
			try {
				pstmt.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if (conn != null) {
			try {
				conn.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
	}

}
