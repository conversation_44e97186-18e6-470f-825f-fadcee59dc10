package com.itco.bak.util;/*
package com.itco.function.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

*/
/**
 * 上下文对象工具类
 * <AUTHOR>
 *//*

@Component
public class ApplicationContextUtil implements ApplicationContextAware {

    */
/**
     * 上下文对象实例
     *//*

    private static ApplicationContext applicationContext;

    */
/**
     * 设置ApplicationContext
     * @param applicationContext 上下文对象
     * @throws BeansException
     *//*

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtil.applicationContext = applicationContext;
    }

    */
/**
     * 获取ApplicationContext
     * @return ApplicationContext
     *//*

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    */
/**
     * 通过class获取Bean
     * @param clazz Bean的类
     * @param <T> Bean的类
     * @return <T> T
     *//*

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

}
*/
