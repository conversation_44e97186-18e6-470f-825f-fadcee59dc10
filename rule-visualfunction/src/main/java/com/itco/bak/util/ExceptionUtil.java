package com.itco.bak.util;

/**
 * 异常工具类
 * <AUTHOR>
 */
public class ExceptionUtil {

    /**
     * 打印异常
     * @param e 异常对象
     * @return String 异常信息
     */
    public static String printException(Exception e) {
        StringBuilder exceptionMsg = new StringBuilder("错误信息[" + e.getMessage() + "] 错误类型[" + e.getClass() + "] 详细信息");
        for (int i = 0; i < e.getStackTrace().length; i++) {
            exceptionMsg.append("[").append(e.getStackTrace()[i]).append("]");
        }
        return exceptionMsg.toString();
    }

}
