package com.itco.bak.dao.impl;/*
package com.itco.function.dao.impl;

import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.itco.function.dao.RedisDAO;
import org.springframework.stereotype.Repository;
import javax.annotation.Resource;

*/
/**
 * Redis接口实现类
 * <AUTHOR>
 *//*

@Repository
public class RedisDAOImpl implements RedisDAO {

    @Resource
    private VProxyJedis jedis;

    */
/**
     * 从Cache中查询数据
     * @param hashKey 哈希Key
     * @param field 字段
     * @return String 查询结果
     *//*

    @Override
    public String queryCacheData(String hashKey, String field) {
        return jedis.hget(hashKey, field);
    }

}
*/
