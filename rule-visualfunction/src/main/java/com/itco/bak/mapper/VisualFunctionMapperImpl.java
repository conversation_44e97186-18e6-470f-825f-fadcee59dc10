package com.itco.bak.mapper;

import com.itco.bak.entity.visual.*;
import com.itco.component.jdbc.DbPool;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class VisualFunctionMapperImpl implements VisualFunctionMapper {

    @Override
    public List<TprFunctionJava> getTprFunctionJava() {
        List<TprFunctionJava> tprFunctionJavaList = new ArrayList<>();
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT * FROM tpr_function_java";
            pstmt = DbPool.getConn().prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                TprFunctionJava tprFunctionJava = new TprFunctionJava();
                tprFunctionJava.setFunction_id(rs.getInt("function_id"));
                tprFunctionJava.setEn_name(rs.getString("en_name"));
                tprFunctionJava.setCh_name(rs.getString("ch_name"));
                tprFunctionJavaList.add(tprFunctionJava);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pstmt.close();
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tprFunctionJavaList;
    }

    @Override
    public List<TprTableJava> getTprTableJava() {
        List<TprTableJava> tprTableJavaList = new ArrayList<>();
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT * FROM tpr_table_java";
            pstmt = DbPool.getConn().prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                TprTableJava tprTableJava = new TprTableJava();
                tprTableJava.setTable_java_id(rs.getInt("table_java_id"));
                tprTableJava.setTpr_table_id(rs.getInt("tpr_table_id"));
                tprTableJava.setFunction_id(rs.getInt("function_id"));
                tprTableJavaList.add(tprTableJava);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pstmt.close();
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tprTableJavaList;
    }

    @Override
    public List<TprTableRuleJava> getTprTableRuleJava() {
        List<TprTableRuleJava> tprTableRuleJavaList = new ArrayList<>();
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT * FROM tpr_table_rule_java";
            pstmt = DbPool.getConn().prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                TprTableRuleJava tprTableRuleJava = new TprTableRuleJava();
                tprTableRuleJava.setRule_java_id(rs.getInt("rule_java_id"));
                tprTableRuleJava.setFunction_id(rs.getInt("function_id"));
                tprTableRuleJava.setTable_java_id(rs.getInt("table_java_id"));
                tprTableRuleJava.setRule_type(rs.getInt("rule_type"));
                tprTableRuleJava.setTable_attr_id(rs.getInt("table_attr_id"));
                tprTableRuleJava.setRelation(rs.getString("relation"));
                tprTableRuleJava.setResult_attr_value(rs.getString("result_attr_value"));
                tprTableRuleJavaList.add(tprTableRuleJava);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pstmt.close();
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tprTableRuleJavaList;
    }

    @Override
    public List<TprTableRuleRelationJava> getTprTableRuleRelationJava() {
        List<TprTableRuleRelationJava> tprTableRuleRelationJavaList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT * FROM tpr_table_rule_relation_java";
            pstmt = DbPool.getConn().prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                TprTableRuleRelationJava tprTableRuleRelationJava = new TprTableRuleRelationJava();
                tprTableRuleRelationJava.setRule_relation_java_id(rs.getInt("rule_relation_java_id"));
                tprTableRuleRelationJava.setFunction_id(rs.getInt("function_id"));
                tprTableRuleRelationJava.setTable_java_id(rs.getInt("table_java_id"));
                tprTableRuleRelationJava.setTable_attr_id(rs.getInt("table_attr_id"));
                tprTableRuleRelationJava.setRelation(rs.getString("relation"));
                tprTableRuleRelationJava.setResult_table_java_id(rs.getString("result_table_java_id"));
                tprTableRuleRelationJava.setResult_table_attr_id(rs.getInt("result_table_attr_id"));
                //tprTableRuleRelationJava.setPriority(rs.getInt("priority"));
                tprTableRuleRelationJavaList.add(tprTableRuleRelationJava);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pstmt.close();
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tprTableRuleRelationJavaList;
    }

    @Override
    public List<TprTableRuleResultJava> getTprTableRuleResultJava() {
        List<TprTableRuleResultJava> tprTableRuleResultJavaList = new ArrayList<>();
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT * FROM tpr_table_rule_result_java";
            pstmt = DbPool.getConn().prepareStatement(sql);
            rs = pstmt.executeQuery();
            while (rs.next()) {
                TprTableRuleResultJava tprTableRuleResultJava = new TprTableRuleResultJava();
                tprTableRuleResultJava.setRule_result_java_id(rs.getInt("rule_result_java_id"));
                tprTableRuleResultJava.setFunction_id(rs.getInt("function_id"));
                tprTableRuleResultJava.setTable_java_id(rs.getInt("table_java_id"));
                tprTableRuleResultJava.setResult_function_type(rs.getInt("result_function_type"));
                tprTableRuleResultJava.setTable_attr_id(rs.getInt("table_attr_id"));
                tprTableRuleResultJavaList.add(tprTableRuleResultJava);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                pstmt.close();
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return tprTableRuleResultJavaList;
    }

}
