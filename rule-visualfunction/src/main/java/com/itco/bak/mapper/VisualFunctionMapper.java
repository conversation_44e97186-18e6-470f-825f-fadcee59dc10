package com.itco.bak.mapper;

import com.itco.bak.entity.visual.*;

import java.util.List;

public interface VisualFunctionMapper {

    List<TprFunctionJava> getTprFunctionJava();

    List<TprTableJava> getTprTableJava();

    List<TprTableRuleJava> getTprTableRuleJava();

    List<TprTableRuleRelationJava> getTprTableRuleRelationJava();

    List<TprTableRuleResultJava> getTprTableRuleResultJava();

}
