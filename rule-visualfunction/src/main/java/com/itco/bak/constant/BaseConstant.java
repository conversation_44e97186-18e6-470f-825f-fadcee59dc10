package com.itco.bak.constant;

/**
 * 基础常量
 * <AUTHOR>
 */
public class BaseConstant {

    /**
     * 左括号
     */
    public static final String LEF_BRACKET = "(";

    /**
     * 结果值
     */
    public static final String VALUE = "value";

    /**
     * 结果值类型
     */
    public static final String VALUE_TYPE = "value_type";

    /**
     * 整形
     */
    public static final String INT = "INT";

    /**
     * 长整型
     */
    public static final String LONG = "LONG";

    /**
     * 双精度型
     */
    public static final String DOUBLE = "DOUBLE";

    /**
     * 字符型
     */
    public static final String STRING = "STRING";

    /**
     * 日期型
     */
    public static final String DATE = "DATE";

    /**
     * Map型
     */
    public static final String MAP = "MAP";

    /**
     * 整数数组
     */
    public static final String ARRAY_INT = "ARRAY_INT";

    /**
     * 长整型数组
     */
    public static final String ARRAY_LONG = "ARRAY_LONG";

    /**
     * 双精度型数组
     */
    public static final String ARRAY_DOUBLE = "ARRAY_DOUBLE";

    /**
     * 字符数组
     */
    public static final String ARRAY_STRING = "ARRAY_STRING";

    /**
     * 日期数组
     */
    public static final String ARRAY_DATE = "ARRAY_DATE";

}
