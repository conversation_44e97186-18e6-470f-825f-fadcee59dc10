package com.itco.bak.exception;

/**
 * 自定义异常
 * <AUTHOR>
 */
public class CustomException extends Exception {

    /**
     * 构造自定义异常
     * @param errorCode 错误信息
     */
    public CustomException(String errorCode) {
        super(errorCode);
    }

    /**
     * 构造自定义异常
     * @param errorCode 错误信息
     * @param ex 异常类
     */
    public CustomException(String errorCode, Throwable ex) {
        super(errorCode, ex);
    }

}
