package com.itco.bak.config;/*
package com.itco.function.config;

import com.ctg.itrdc.cache.vjedis.VProxyJedis;
import com.ctg.itrdc.cache.vjedis.jedis.HostAndPort;
import com.ctg.itrdc.cache.vjedis.jedis.JedisPoolConfig;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPool;
import com.ctg.itrdc.cache.vjedis.pool.CtgVJedisPoolConfig;
import com.itco.zookeeper.ZkClientApi;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

*/
/**
 * Redis配置
 * <AUTHOR>
 *//*

@Component
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Resource
    private ZkClientApi zkClientApi;

    */
/**
     * 初始化Redis
     * @return VProxyJedis
     *//*

    @Bean
    public VProxyJedis initRedis() {
        VProxyJedis jedis = null;
        try {
            // 获取CtgCache的配置
            Properties ctgCacheConfig = zkClientApi.getPropertiesFromZK("ctgCache.properties");

            // 解析配置IP和端口号
            List<HostAndPort> hostAndPortList = getHostAddress(ctgCacheConfig.getProperty("host"));

            // 线程池配置
            GenericObjectPoolConfig<?> poolConfig = new JedisPoolConfig();
            // 最大空闲连接数
            poolConfig.setMaxIdle(Integer.parseInt(ctgCacheConfig.getProperty("maxIdle")));
            // 最大连接数（空闲+使用中）
            poolConfig.setMaxTotal(Integer.parseInt(ctgCacheConfig.getProperty("maxTotal")));
            // 保持的最小空闲连接数
            poolConfig.setMinIdle(Integer.parseInt(ctgCacheConfig.getProperty("minIdle")));
            // 借出连接时最大的等待时间
            poolConfig.setMaxWaitMillis(Integer.parseInt(ctgCacheConfig.getProperty("maxWaitMillis")));

            CtgVJedisPoolConfig config = new CtgVJedisPoolConfig(hostAndPortList);
            // 分组对应的桶位
            config.setDatabase(ctgCacheConfig.getProperty("databasefilter"));
            // 用户#密码
            config.setPassword(ctgCacheConfig.getProperty("password"));
            // 线程池配置
            config.setPoolConfig(poolConfig);
            // 后台监控执行周期（毫秒）
            config.setPeriod(Integer.parseInt(ctgCacheConfig.getProperty("period")));
            // 后台监控ping命令超时时间（毫秒）
            config.setMonitorTimeout(Integer.parseInt(ctgCacheConfig.getProperty("monitorTimeout")));

            // 创建连接池
            CtgVJedisPool pool = new CtgVJedisPool(config);
            jedis = pool.getResource();
        } catch (Exception e) {
            logger.error("初始化Redis失败", e);
        }
        return jedis;
    }

    */
/**
     * 获取配置IP和端口号
     * @param hostAddress 主机地址
     * @return List<HostAndPort>
     *//*

    public List<HostAndPort> getHostAddress(String hostAddress) {
        List<HostAndPort> hostAndPortList = new ArrayList<>();
        String[] hostAddressArray = hostAddress.split("\\|");
        for (String address : hostAddressArray) {
            int index = address.indexOf(":");
            String ip = address.substring(0, index);
            String port = address.substring(index + 1);
            HostAndPort hostAndPort = new HostAndPort(ip, Integer.parseInt(port));
            hostAndPortList.add(hostAndPort);
        }
        return hostAndPortList;
    }

}
*/
