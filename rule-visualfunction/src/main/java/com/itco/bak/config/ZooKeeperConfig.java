package com.itco.bak.config;/*
package com.itco.function.config;

import com.itco.zookeeper.ZkClientApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

*/
/**
 * ZooKeeper配置
 * <AUTHOR>
 *//*

@Component
public class ZooKeeperConfig {

    private static final Logger logger = LoggerFactory.getLogger(ZooKeeperConfig.class);

    */
/**
     * 初始化ZooKeeper
     * @return ZkClientApi
     *//*

    @Bean
    public ZkClientApi initZooKeeper() {
        ZkClientApi zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("tRuleFunc");
        if (!zkClientApi.init()) {
            logger.info(zkClientApi.getModuleCode() + " zookeeper 初始化失败，process_id:" + zkClientApi.getProcessID());
        }
        logger.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());
        zkClientApi.register(true);
        return zkClientApi;
    }

}
*/
