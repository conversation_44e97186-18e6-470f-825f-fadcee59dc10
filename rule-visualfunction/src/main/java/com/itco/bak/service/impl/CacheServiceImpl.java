package com.itco.bak.service.impl;/*
package com.itco.function.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.itco.function.dao.RedisDAO;
import com.itco.function.entity.common.TprResourceAttr;
import com.itco.function.entity.common.TprTable;
import com.itco.function.entity.common.TprTableFormatItem;
import com.itco.function.entity.common.TprTableIndex;
import com.itco.function.exception.CustomException;
import com.itco.function.service.CacheService;
import com.itco.zookeeper.ZkClientApi;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

*/
/**
 * Cache接口实现
 * <AUTHOR>
 *//*

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CacheServiceImpl implements CacheService {

    */
/**
     * 依赖注入
     *//*

    private final ZkClientApi zkClientApi;
    private final RedisDAO redisDAO;

    */
/**
     * 定义全局变量
     *//*

    private Map<String, TprTable> tprTableNameMap;
    private Map<Integer, Map<Integer, List<TprTableIndex>>> tprTableIndexMap;
    private Map<Integer, TprResourceAttr> tprResourceAttrMap;
    private Map<Integer, List<TprTableFormatItem>> tprTableFormatItemMap;

    @PostConstruct
    public void init() {
        // 初始化资源Map
        List<TprTable> tprTableList = zkClientApi.getTableFromZK("tpr_table", TprTable.class);
        tprTableNameMap = new HashMap<>(16);
        for (TprTable tprTable : tprTableList) {
            tprTableNameMap.put(tprTable.getEn_name(), tprTable);
        }
        List<TprTableIndex> tprTableIndexList = zkClientApi.getTableFromZK("tpr_table_index", TprTableIndex.class);
        tprTableIndexMap = new HashMap<>(16);
        for (TprTableIndex tprTableIndex : tprTableIndexList) {
            int tableId = tprTableIndex.getTable_id();
            int groupId = tprTableIndex.getIndex_group_id();
            if (tprTableIndexMap.get(tableId) == null) {
                Map<Integer, List<TprTableIndex>> integerListMap = new HashMap<>(16);
                integerListMap.put(groupId, new ArrayList<TprTableIndex>() {{
                    add(tprTableIndex);
                }});
                tprTableIndexMap.put(tableId, integerListMap);
            } else {
                Map<Integer, List<TprTableIndex>> integerListMap = tprTableIndexMap.get(tableId);
                if (integerListMap.get(groupId) == null) {
                    integerListMap.put(groupId, new ArrayList<TprTableIndex>() {{
                        add(tprTableIndex);
                    }});
                } else {
                    integerListMap.get(groupId).add(tprTableIndex);
                }
            }
        }
        List<TprResourceAttr> tprResourceAttrList = zkClientApi.getTableFromZK("tpr_resource_attr", TprResourceAttr.class);
        tprResourceAttrMap = new HashMap<>(16);
        for (TprResourceAttr tprResourceAttr : tprResourceAttrList) {
            tprResourceAttrMap.put(tprResourceAttr.getAttr_id(), tprResourceAttr);
        }
        List<TprTableFormatItem> tprTableFormatItemList = zkClientApi.getTableFromZK("tpr_table_format_item", TprTableFormatItem.class);
        tprTableFormatItemMap = new HashMap<>(16);
        for (TprTableFormatItem tprTableFormatItem : tprTableFormatItemList) {
            int tableId = tprTableFormatItem.getTable_id();
            if (tprTableFormatItemMap.get(tableId) == null) {
                tprTableFormatItemMap.put(tableId, new ArrayList<TprTableFormatItem>() {{
                    add(tprTableFormatItem);
                }});
            } else {
                tprTableFormatItemMap.get(tableId).add(tprTableFormatItem);
            }
        }
    }

    */
/**
     * 从Cache中查询数据
     * @param tableName 查询表名
     * @param queryParamMap 查询条件
     * @return List<Map < String, Object>> 查询结果
     * @throws Exception
     *//*

    @Override
    public List<Map<String, Object>> queryCacheData(String tableName, Map<String, Object> queryParamMap) throws Exception {
        String billingCycle = "202001";
        List<Map<String, Object>> dataList = new ArrayList<>();
        TprTable tprTable = tprTableNameMap.get(tableName);
        if (tprTable == null) {
            throw new CustomException("tprTable为空");
        }
        if (queryParamMap.size() == 0) {
            throw new CustomException("查询条件不合法");
        }
        // 获取索引组ID
        int groupId = 0;
        Map<Integer, List<TprTableIndex>> tableIndexMap = tprTableIndexMap.get(tprTable.getTable_id());
        if (tableIndexMap == null) {
            throw new CustomException("未配置查询索引tpr_table_index.table_id:" + tprTable.getTable_id());
        }
        // 查询索引组编号
        for (Map.Entry<Integer, List<TprTableIndex>> tableIndex : tableIndexMap.entrySet()) {
            boolean containsAll = true;
            for (TprTableIndex index : tableIndex.getValue()) {
                String attrName = tprResourceAttrMap.get(index.getField_attr_id()).getEn_name();
                if (!queryParamMap.containsKey(attrName)) {
                    containsAll = false;
                    break;
                }
            }
            if (containsAll && tableIndex.getValue().size() == queryParamMap.size()) {
                groupId = tableIndex.getKey();
            }
        }
        List<String> valueList = new ArrayList<>();
        for (TprTableIndex tprTableIndex : tprTableIndexMap.get(tprTable.getTable_id()).get(groupId)) {
            String attrName = tprResourceAttrMap.get(tprTableIndex.getField_attr_id()).getEn_name();
            valueList.add(String.valueOf(queryParamMap.get(attrName)));
        }

        //  根据索引查询结果
        String hashIndexKey = tableName + "-" + billingCycle + "-group-" + groupId;

        System.out.println("hashIndexKey:" + hashIndexKey);

        String hashKey = tableName + "-" + billingCycle;

        String indexStr = redisDAO.queryCacheData(hashIndexKey, StringUtils.join(valueList, "|"));

        System.out.println("valueStr:" + StringUtils.join(valueList, "|"));

        System.out.println("indexStr:" + indexStr);

        if (StringUtils.isNotEmpty(indexStr)) {
            // 索引存放的主键位置 值存在多个，逗号隔开
            String[] indexArray = indexStr.split(",");
            for (String index : indexArray) {
                String record = redisDAO.queryCacheData(hashKey, index);
                if ("JSON".equals(tprTable.getRedis_format())) {
                    // 如果上载的数据为JSON格式，则将数据转为JSON格式后获取内层dataMap
                    dataList.add(JSONObject.parseObject(record).getInnerMap());
                } else {
                    // 如果上载的数据为STRING格式，则分割处理字符串获取dataMap
                    String[] filedArray = record.split("\\|");
                    List<TprTableFormatItem> tprTableFormatItemList = tprTableFormatItemMap.get(tprTable.getTable_id());
                    if (filedArray.length != tprTableFormatItemList.size()) {
                        throw new CustomException("444");
                    }
                    Map<String, Object> dataMap = new HashMap<>(16);
                    for (int i = 0; i < tprTableFormatItemList.size(); i++) {
                        TprTableFormatItem tprTableFormatItem = tprTableFormatItemList.get(i);
                        TprResourceAttr tprResourceAttr = tprResourceAttrMap.get(tprTableFormatItem.getAttr_id());
                        dataMap.put(tprResourceAttr.getEn_name(), filedArray[i]);
                    }
                    dataList.add(dataMap);
                }
            }
        } else {
            throw new Exception("indexStr:" + indexStr);
        }
        return dataList;
    }

}
*/
