package com.itco.bak.service;/*
package com.itco.function.service;

import java.util.List;
import java.util.Map;

*/
/**
 * Cache接口
 * <AUTHOR>
 *//*

public interface CacheService {

    */
/**
     * 从Cache中查询数据
     * @param tableName 查询表名
     * @param queryParamMap 查询条件
     * @return List<Map < String, Object>> 查询结果
     * @throws Exception
     *//*

    List<Map<String, Object>> queryCacheData(String tableName, Map<String, Object> queryParamMap) throws Exception;

}
*/
