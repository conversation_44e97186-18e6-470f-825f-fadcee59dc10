package com.itco.bak.entity.common;/*
package com.itco.function.bak.entity.common;

import lombok.Data;
import lombok.experimental.Accessors;

*/
/**
 * <AUTHOR>
 *//*

@Data
@Accessors(chain = true)
public class TprTable {

    private int table_id;

    private String en_name;

    private String ch_name;

    private String key_attr_name;

    private String redis_db;

    private String redis_format;

    private String redis_separator;

    private String type;

    private String upload_sql;

    private String file_path;

    private String file_foamrt;

    private String status_cd;

    private String create_staff;

    private String update_staff;

    private String create_date;

    private String update_date;

    private String status_date;

    private String upload_mode;

}
*/
