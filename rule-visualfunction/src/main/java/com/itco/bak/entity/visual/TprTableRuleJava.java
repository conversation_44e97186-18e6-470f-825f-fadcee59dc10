package com.itco.bak.entity.visual;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TprTableRuleJava {

    private int rule_java_id;

    private int function_id;

    private int table_java_id;

    private int rule_type;

    private int table_attr_id;

    private String relation;

    private String result_attr_value;

    private int priority;

}
