package com.itco.bak.entity.visual;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TprFunctionJava {

    private int function_id;

    private String en_name;

    private String ch_name;

    private String function_type;

    private String result_type;

    private int param_num;

    private String src_path;

    private String lib_path;

    private String comments;

    private int module_id;

    private String db_pkg_name;

    private int latn_id;

}
