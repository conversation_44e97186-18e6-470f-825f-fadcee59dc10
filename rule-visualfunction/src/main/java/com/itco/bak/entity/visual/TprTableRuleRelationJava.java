package com.itco.bak.entity.visual;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TprTableRuleRelationJava {

    private int rule_relation_java_id;

    private int function_id;

    private int table_java_id;

    private int table_attr_id;

    private String relation;

    private String result_table_java_id;

    private int result_table_attr_id;

    private int priority;

}
