package com.itco.CalcFunction;

import com.itco.bak.entity.visual.*;
import com.itco.bak.exception.CustomException;
import com.itco.bak.mapper.VisualFunctionMapperImpl;
import com.itco.bak.util.ExceptionUtil;
import com.itco.component.jdbc.DbPool;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.table.TprTable;
import com.itco.rulefunction.ctgcache.CShmOpt;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.itco.bak.constant.BaseConstant.LONG;
import static com.itco.bak.constant.BaseConstant.STRING;

public class VisualFunction {
    static Log log = LogFactory.getLog(VisualFunction.class);
    private final String VALUE = "VALUE";
    private final String VALUE_TYPE = "VALUE_TYPE";
    private final String ERROR_MSG = "ERROR_MSG";

    //缓存因子表
    static Map<Integer, TprFunctionJava> tprFunctionJavaMap;
    // 逻辑函数表信息
    static List<TprTableJava> tprTableJavaList;
    // 逻辑函数表条件信息
    static List<TprTableRuleJava> tprTableRuleJavaList;
    // 逻辑函数关联条件信息
    static List<TprTableRuleRelationJava> tprTableRuleRelationJavaList;
    // 逻辑函数结果信息
    static List<TprTableRuleResultJava> tprTableRuleResultJavaList;
    // 线程编号
    int iThreadNo = 0;

    public enum CALC_RESULT_VALUE {
        INT(1, "INT"),
        LONG(2, "LONG"),
        DOUBLE(3, "DOUBLE"),
        STRING(4, "STRING"),
        DATE(5, "DATE"),
        ARRAY_INT(6, "ARRAY_INT"),
        ARRAY_LNG(7, "ARRAY_LNG"),
        ARRAY_DOUBLE(8, "ARRAY_DOUBLE"),
        ARRAY_STRING(9, "ARRAY_STRING"),
        ARRAY_DATE(10, "ARRAY_DATE");

        int id;
        String value;

        CALC_RESULT_VALUE(int id, String value) {
            this.id = id;
            this.value = value;
        }
    }

    public void setiThreadNo(int iThreadNo) {
        this.iThreadNo = iThreadNo;
    }

    public static boolean initCfg() {
        DbPool.setZkClientApi(CalcVisualFunctionLocal.getZkClientApi());
        if (null == DbPool.getConn()) {
            return false;
        }
        VisualFunctionMapperImpl visualFunctionMapper = new VisualFunctionMapperImpl();
        List<TprFunctionJava> tprFunctionJavaList = visualFunctionMapper.getTprFunctionJava();
        if (tprFunctionJavaList != null) {
            tprFunctionJavaMap = tprFunctionJavaList.stream().collect(Collectors.toMap(TprFunctionJava::getFunction_id, a -> a, (k1, k2) -> k1));
        }
        tprTableJavaList = visualFunctionMapper.getTprTableJava();
        tprTableRuleJavaList = visualFunctionMapper.getTprTableRuleJava();
        tprTableRuleRelationJavaList = visualFunctionMapper.getTprTableRuleRelationJava();
        tprTableRuleResultJavaList = visualFunctionMapper.getTprTableRuleResultJava();
        DbPool.close();
        return true;
    }

    public boolean invoke(int functionId, Long lRefObjectID, Map<String, Object> record, Map<String, Object> result) {
        boolean bRet = true;
        long lStartTime = System.currentTimeMillis();

        boolean invokeFlag = true;
        String executeMsg = "";
        try {
            List<String> pricingRefObjectList = new ArrayList<>();
            if (lRefObjectID > 0) {
                CShmOpt opt = new CShmOpt("PRICING_REF_ID_VALUE", iThreadNo);
                String queryStr = "pricing_ref_object_id:" + lRefObjectID;
                if (!opt.QueryByIndex(queryStr)) {
                    System.out.println("cShmOpt.QueryByIndex(queryStr) failed");
                    return false;
                }
                while (opt.Next()) {
                    pricingRefObjectList.add(opt.getValue().get("value_string").toString());
                }
            }

            List<List<Map<String, Object>>> allDataList = new ArrayList<>();
            List<TprTableJava> tprTableJavaList = getTprTableJavaList(functionId);
            for (TprTableJava tableJava : tprTableJavaList) {
                // 上载的数据表名
                String tprTableName = getTprTable(tableJava.getTpr_table_id()).getEn_name();
                // 非 = 条件集合
                List<TprTableRuleJava> unEqualTableRuleJavaList = new ArrayList<>();
                List<TprTableRuleJava> tprTableRuleJavaList = getTprTableRuleJavaList(functionId, tableJava.getTable_java_id());
                Map<String, Object> queryParamMap = new HashMap<>(16);
                for (TprTableRuleJava tableRuleJava : tprTableRuleJavaList) {
                    String relation = tableRuleJava.getRelation();
                    String attr = getTprResourceAttr(tableRuleJava.getTable_attr_id()).getEn_name();
                    Object right = null;
                    if (tableRuleJava.getRule_type() == 1) {
                        // 1:常量，则 tableRuleJava.getResult_attr_value() 就是常量的值
                        right = tableRuleJava.getResult_attr_value();
                        if ("=".equals(relation)) {
                            queryParamMap.put(attr, right);
                        } else {
                            unEqualTableRuleJavaList.add(tableRuleJava);
                        }
                    } else if (tableRuleJava.getRule_type() == 2) {
                        // 2:话单结构体，则 tableRuleJava.getResult_attr_value() 就是属性id
                        TprResourceAttr tprResourceAttr = getTprResourceAttr(Integer.parseInt(tableRuleJava.getResult_attr_value()));
                        right = record.get(tprResourceAttr.getEn_name());
                        if ("=".equals(relation)) {
                            queryParamMap.put(attr, right);
                        } else {
                            tableRuleJava.setResult_attr_value(String.valueOf(right));
                            unEqualTableRuleJavaList.add(tableRuleJava);
                        }
                    } else if (tableRuleJava.getRule_type() == 3) {
                        // 参考对象对象标识
                        log.info("pricingRefObjectList:" + pricingRefObjectList.toString());
                        right = pricingRefObjectList.stream().collect(Collectors.joining(","));
                        tableRuleJava.setResult_attr_value(String.valueOf(right));
                        unEqualTableRuleJavaList.add(tableRuleJava);
                    }
                }
                // 查询缓存，获取数据，此时数据只匹配了 = 的条件
                System.out.println("tprTableName:" + tprTableName);
                System.out.println("queryParamMap:" + queryParamMap);
                List<String> keyList = new ArrayList<>(queryParamMap.keySet());
                List<Object> valueList = new ArrayList<>(queryParamMap.values());
                CShmOpt opt = new CShmOpt(tprTableName, iThreadNo);
                String queryStr = StringUtils.join(keyList, "|") + ":" + StringUtils.join(valueList, "|");
                log.info("queryStr:" + queryStr);
                if (!opt.QueryByIndex(queryStr)) {
                    System.out.println("cShmOpt.QueryByIndex(queryStr) failed");
                    return false;
                }
                List<Map<String, Object>> originDataList = new ArrayList<>();
                while (opt.Next()) {
                    originDataList.add(opt.getValue());
                }
                originDataList.forEach(System.out::println);
                System.out.println("------------------------------------------------------------------------------------------");
                // 二次处理数据，匹配非 = 的查询条件
                List<Map<String, Object>> processedDataList = processDataList(originDataList, unEqualTableRuleJavaList);
                processedDataList.forEach(System.out::println);
                allDataList.add(processedDataList);
            }

            // 获取处理后的最终数据集合
            List<Map<String, Object>> resultDataList;
            if (allDataList.size() > 1) {
                // 有多个List（目前最多2），则获取逻辑函数关联条件配置信息
                List<TprTableRuleRelationJava> tableRuleRelationJavaList = getTprTableRuleRelationJavaList(functionId);
                // 根据关联条件关联两个List并得到关联后的结果
                resultDataList = relateDoubleList(allDataList.get(0), allDataList.get(1), tableRuleRelationJavaList);
            } else {
                resultDataList = allDataList.get(0);
            }

            // 获取逻辑函数结果配置信息
            TprTableRuleResultJava tableRuleResultJava = getTprTableRuleResultJava(functionId);
            String attr = getTprResourceAttr(tableRuleResultJava.getTable_attr_id()).getEn_name();
            int resultFunctionType = tableRuleResultJava.getResult_function_type();
            Object value = null;
            Object valueType = null;
            switch (resultFunctionType) {
                case 1:
                    // count(X)
                    // filter筛选出需要的结果
                    value = resultDataList.stream().filter(data -> data.get(attr) != null).count();
                    valueType = LONG;
                    break;
                case 2:
                    // sum(X)
                    value = resultDataList.stream().mapToDouble(data -> Double.parseDouble(valueConvert(data.get(attr)))).sum();
                    valueType = LONG;
                    break;
                case 3:
                    // count(distinct(X))
                    // filter筛选出需要的结果，并distinct去重
                    value = resultDataList.stream().filter(data -> data.get(attr) != null).distinct().count();
                    valueType = LONG;
                    break;
                case 4:
                    // X
                    if (resultDataList.size() > 0) {
                        value = resultDataList.get(0).get(attr);
                    } else {
                        value = "";
                    }
                    valueType = STRING;
                    break;
                default:
                    break;
            }
            result.put(VALUE, value);
            result.put(VALUE_TYPE, valueType);
        } catch (CustomException e) {
            invokeFlag = false;
            executeMsg = e.getMessage();
            bRet = false;
        } catch (Exception e) {
            invokeFlag = false;
            executeMsg = "运行异常：" + ExceptionUtil.printException(e);
            bRet = false;
        }
        result.put("invokeFlag", invokeFlag);
        result.put("executeMsg", executeMsg);

        return bRet;
    }

/**************************************************** 逻辑函数 ****************************************************/
    /**
     * 处理缓存查询到的结果，筛选出符合条件的数据（判断非 = 的条件）
     *
     * @param dataList                 缓存查询结果数据集合
     * @param unEqualTableRuleJavaList 非 = 判断条件集合
     * @return List<Map < String, Object>> 符合条件的数据集合
     */
    public List<Map<String, Object>> processDataList(List<Map<String, Object>> dataList, List<TprTableRuleJava> unEqualTableRuleJavaList) throws Exception {
        List<Map<String, Object>> processedDataList = new ArrayList<>();
        for (Map<String, Object> data : dataList) {
            // 是否满足条件
            boolean qualified = true;
            for (TprTableRuleJava tableRuleJava : unEqualTableRuleJavaList) {
                String relation = tableRuleJava.getRelation();
                String left = String.valueOf(data.get(getTprResourceAttr(tableRuleJava.getTable_attr_id()).getEn_name()));
                String right = tableRuleJava.getResult_attr_value();

                BigDecimal leftValue;
                BigDecimal rightValue;
                // 判断左右值关系
                switch (relation) {
                    case "<>":
                        if (left.compareTo(right) == 0) {
                            qualified = false;
                        }
                        break;
                    case ">":
                        leftValue = new BigDecimal(left);
                        rightValue = new BigDecimal(right);
                        if (leftValue.compareTo(rightValue) < 0 || leftValue.compareTo(rightValue) == 0) {
                            qualified = false;
                        }
                        break;
                    case "<":
                        leftValue = new BigDecimal(left);
                        rightValue = new BigDecimal(right);
                        if (leftValue.compareTo(rightValue) >= 0) {
                            qualified = false;
                        }
                        break;
                    case ">=":
                        leftValue = new BigDecimal(left);
                        rightValue = new BigDecimal(right);
                        if (leftValue.compareTo(rightValue) < 0) {
                            qualified = false;
                        }
                        break;
                    case "<=":
                        leftValue = new BigDecimal(left);
                        rightValue = new BigDecimal(right);
                        if (leftValue.compareTo(rightValue) > 0) {
                            qualified = false;
                        }
                        break;
                    case "in":
                        String[] rightArrayIn = right.split(",");
                        List<String> rightListIn = new ArrayList<>();
                        Collections.addAll(rightListIn, rightArrayIn);
                        System.out.println("rightListIn" + rightListIn.toString());
                        if (!rightListIn.contains(left)) {
                            qualified = false;
                        }
                        break;
                    case "not in":
                        String[] rightArrayNotIn = right.split(",");
                        List<String> rightListNotIn = new ArrayList<>();
                        Collections.addAll(rightListNotIn, rightArrayNotIn);
                        if (rightListNotIn.contains(left)) {
                            qualified = false;
                        }
                        break;
                    default:
                        break;
                }
            }
            System.out.println("data:" + data.toString());
            if (qualified) {
                processedDataList.add(data);
            }
        }
        return processedDataList;
    }

    /**
     * 关联A、B表的缓存数据
     *
     * @param dataListA                    A表数据
     * @param dataListB                    B表数据
     * @param tprTableRuleRelationJavaList 关联条件集合
     * @return List<Map < String, Object>> 关联后的数据
     */
    public List<Map<String, Object>> relateDoubleList(List<Map<String, Object>> dataListA, List<Map<String, Object>> dataListB, List<TprTableRuleRelationJava> tprTableRuleRelationJavaList) throws Exception {
        List<Map<String, Object>> dataListC = new ArrayList<>();
        for (Map<String, Object> dataA : dataListA) {
            for (Map<String, Object> dataB : dataListB) {
                // 是否满足条件
                boolean qualified = true;
                for (TprTableRuleRelationJava tableRuleRelationJava : tprTableRuleRelationJavaList) {
                    // 利用dataA的某个字段值 = dataB的某个字段值进行关联
                    if (dataA.get(getTprResourceAttr(tableRuleRelationJava.getTable_attr_id()).getEn_name()) != dataB.get(getTprResourceAttr(tableRuleRelationJava.getResult_table_attr_id()).getEn_name())) {
                        qualified = false;
                        break;
                    }
                }
                if (qualified) {
                    dataB.putAll(dataA);
                    dataListC.add(dataB);
                }
            }
        }
        return dataListC;
    }

    /**************************************************** 功能函数 ****************************************************/

    /**
     *  根据函数ID获取函数名称
     * @param functionId
     * @return TprFunctionJava
     */
    public TprFunctionJava getTprFunctionJava(int functionId) {
        return tprFunctionJavaMap.get(functionId);
    }

    /**
     * 根据表ID获取表信息
     *
     * @param tableId 表ID
     * @return TprTable
     */
    private TprTable getTprTable(int tableId) throws Exception {
        Optional<TprTable> option = CalcVisualFunctionLocal.tprTableList.stream().filter(data -> data.getTable_id() == tableId).findFirst();
        if (option.isPresent()) {
            return option.get();
        } else {
            throw new CustomException("TPR_TABLE 表不存在ID为" + tableId + "的表");
        }
    }

    /**
     * 根据属性ID获取属性信息
     *
     * @param attrId 属性ID
     * @return TprResourceAttr
     */
    private TprResourceAttr getTprResourceAttr(int attrId) throws Exception {
        Optional<TprResourceAttr> option = CalcVisualFunctionLocal.tprResourceAttrList.stream().filter(data -> data.getAttr_id() == attrId).findFirst();
        if (option.isPresent()) {
            return option.get();
        } else {
            throw new CustomException("TPR_RESOURCE_ATTR 表不存在ID为" + attrId + "的属性");
        }
    }


    /**
     * 根据函数ID获取函数所涉及的数据表信息
     *
     * @param functionId 函数ID
     * @return List<TprTableJava>
     */
    private List<TprTableJava> getTprTableJavaList(int functionId) {
        return tprTableJavaList.stream().filter(data -> data.getFunction_id() == functionId).collect(Collectors.toList());
    }

    /**
     * 根据函数ID、逻辑函数表ID 获取逻辑函数表所涉及的表（查询）条件信息
     *
     * @param functionId 函数ID
     * @return List<TprTableJava>
     */
    private List<TprTableRuleJava> getTprTableRuleJavaList(int functionId, int tableJavaId) {
        return tprTableRuleJavaList.stream().filter(data -> data.getFunction_id() == functionId && data.getTable_java_id() == tableJavaId).collect(Collectors.toList());
    }

    /**
     * 根据函数ID获取函数所涉及的数据表关联关系信息
     *
     * @param functionId 函数ID
     * @return List<TprTableRuleRelationJava>
     */
    private List<TprTableRuleRelationJava> getTprTableRuleRelationJavaList(int functionId) {
        return tprTableRuleRelationJavaList.stream().filter(data -> data.getFunction_id() == functionId).collect(Collectors.toList());
    }

    /**
     * 根据函数ID获取函数结果信息
     *
     * @param functionId 函数ID
     * @return TprTableRuleResultJava
     */
    private TprTableRuleResultJava getTprTableRuleResultJava(int functionId) throws Exception {
        Optional<TprTableRuleResultJava> option = tprTableRuleResultJavaList.stream().filter(data -> data.getFunction_id() == functionId).findFirst();
        if (option.isPresent()) {
            return option.get();
        } else {
            throw new CustomException("TPR_TABLE_RULE_RESULT_JAVA 表不存在函数ID为" + functionId + "的结果");
        }
    }

    /**
     * 值转换
     * 如果值为null，返回"0"，否则返回字符型结果
     *
     * @param value 待转换值
     * @return String
     */
    private String valueConvert(Object value) {
        return value == null ? "0" : value.toString();
    }


}
