package com.itco.CalcFunction;

import com.itco.bak.entity.visual.TprFunctionJava;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.TprResourceAttr;
import com.itco.entity.function.FunctionPerl;
import com.itco.entity.function.RefValue;
import com.itco.entity.function.TprFunction;
import com.itco.entity.function.TprFunctionCondition;
import com.itco.entity.table.TprTable;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CalcVisualFunctionLocal {
    static Log log = LogFactory.getLog(CalcVisualFunctionLocal.class);

    static ZkClientApi zkClientApi = null;
    static boolean bInitFlag = false;

    static Map<Integer, TprFunction> mTprFunction = new HashMap<>();
    static Map<Integer, List<TprFunctionCondition>> mTprFunctionCondition = new HashMap<>();
    static Map<Long, RefValue> mRefValue = new HashMap<>();
    static Map<Integer, TprResourceAttr> mTprAttr = new HashMap<>();
    static Map<String, TprResourceAttr> mTprAttrName = new HashMap<>();
    static Map<Integer, FunctionPerl> mPerl = new HashMap<>();
    public static List<TprTable> tprTableList;
    public static List<TprResourceAttr> tprResourceAttrList;

    static Map<Integer, CalcVisualFunctionLocal> rulePricingPlanMap = new HashMap<>();

    int iThreadNo = 0;
    VisualFunction visualFunction = null;

    CalcVisualFunctionLocal(int i) {
        visualFunction = new VisualFunction();
        visualFunction.setiThreadNo(iThreadNo);
    }

    public static CalcVisualFunctionLocal getInstance() {
        return getInstance(0);
    }

    public static CalcVisualFunctionLocal getInstance(int i) {
        if (null == rulePricingPlanMap.get(i)) {
            CalcVisualFunctionLocal calcFunctionLocal = new CalcVisualFunctionLocal(i);
            rulePricingPlanMap.put(i, calcFunctionLocal);
        }
        return rulePricingPlanMap.get(i);
    }

    public static void setZkClientApi(ZkClientApi zkClientApi) {
        CalcVisualFunctionLocal.zkClientApi = zkClientApi;
    }

    public static ZkClientApi getZkClientApi() {
        return zkClientApi;
    }

    public static void setbInitFlag(boolean bInitFlag) {
        CalcVisualFunctionLocal.bInitFlag = bInitFlag;
    }

    public static boolean initCfg() {
        if (!bInitFlag) {
            tprTableList = zkClientApi.getTableFromZK("tpr_table", TprTable.class);
            tprResourceAttrList = zkClientApi.getTableFromZK("tpr_resource_attr", TprResourceAttr.class);

            //加载缓存因子使用的配置规则
            if (!VisualFunction.initCfg()) {
                log.error("VisualFunction.initCfg() faile");
                return false;
            }
            log.info("VisualFunction.initCfg() 成功");
            bInitFlag = true;
        }
        return true;
    }

    public boolean calcFunction(int functionId, Long lRefObjectID, Map<String, Object> record, Map<String, Object> result) {
        boolean bRet = false;
        TprFunctionJava tprFunctionJava = visualFunction.getTprFunctionJava(functionId);
        if (tprFunctionJava == null) {
            log.error("不存在的缓存因子：" + functionId);
            return false;
        }

        long lStartTime = getMicroSecond();
        bRet = visualFunction.invoke(functionId, lRefObjectID, record, result);

        long lDuration = System.currentTimeMillis() - lStartTime;
        FunctionPerl functionPerl = mPerl.get(functionId);
        if (functionPerl == null) {
            functionPerl = new FunctionPerl(functionId, tprFunctionJava.getEn_name(), tprFunctionJava.getCh_name());
            mPerl.put(functionId, functionPerl);
        }
        if (bRet) {
            functionPerl.setPlusCntPerl(1, lDuration);
        } else {
            functionPerl.setFailPlusCntPerl(1, lDuration);
        }
        return bRet;
    }

    Long getMicroSecond() {
        return System.nanoTime() / 1000;
    }

    public static Map<Integer, FunctionPerl> getmPerl() {
        return mPerl;
    }

    public static void clearPerl() {
        mPerl.clear();
    }

    public static void close(){
        mTprFunction.clear();
        mTprFunctionCondition.clear();
        mTprAttr.clear();
        mTprAttrName.clear();
        mRefValue.clear();
        rulePricingPlanMap.clear();
    }
}
