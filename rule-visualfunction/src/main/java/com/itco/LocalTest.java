/*
package com.itco;

import com.itco.CalcFunction.CalcVisualFunctionLocal;
import com.itco.component.zookeeper.ZkClientApi;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

//*
// * 本地测试
// * <AUTHOR>

@Component
public class LocalTest implements CommandLineRunner {

    static Log log = LogFactory.getLog(LocalTest.class);
    ZkClientApi zkClientApi = null;
    Connection conn = null;
    
    @Override
    public void run(String... args) throws Exception {
        // 初始化zookeeper
        zkClientApi = new ZkClientApi();
        zkClientApi.setBillingLineId("11");
        zkClientApi.setModuleCode("tRuleFunc");
        if (!zkClientApi.init()) {
            log.info(zkClientApi.getModuleCode() + " zookeeper初始化失败,PROCESS_ID:" + zkClientApi.getProcessID());
            return;
        }
        log.info("zookeeper 初始化完成，process_id:" + zkClientApi.getProcessID());
        zkClientApi.register(true);

        CalcVisualFunctionLocal.setZkClientApi(zkClientApi);

        Map<String, Object> record = new HashMap<>();
        Map<String, Object> result = new HashMap<>();

        System.out.println();
        record.clear();
        //构造话单字段
//        record.put("billing_mode_id", "202106");
//        record.put("bill_prod_inst_id", "983369");

        //函数调用
        CalcVisualFunctionLocal calcFunction = CalcVisualFunctionLocal.getInstance();
        // 业务因子调用
        calcFunction.calcFunction(169, 50149628L, record, result);

        for (Map.Entry<String, Object> map : record.entrySet()) {
            System.out.println(map.getKey() + "->" + map.getValue());
        }

        log.info(result);

        log.info("VALUE:" + result.get("VALUE"));
        log.info("VALUE_TYPE:" + result.get("VALUE_TYPE"));

    }

}
*/
