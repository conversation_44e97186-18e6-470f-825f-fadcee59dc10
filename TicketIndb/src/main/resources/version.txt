--------------------------------------------------------------------------------------------------
【发布版本】：TicketIndb_code_2023-05-06 11:00
【修订日期】：2023-05-06
【修订人员】：zhangshf
【需求单号】：61000
【SVN提交前版本】：55280
【实现功能】：
1、补充不支付原因填写，通过解析批价模块的要素串element_str字段、error_type字段进行不支付原因填写。

【变更文件】：
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndb.java
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndbTable.java
修改：src/main/java/com/itco/ticketindb/entity/EntityMgr.java
修改：src/main/java/com/itco/ticketindb/entity/entityDef/TpNoPayment.java

【修订要点】：
  1、配置文件增加两个字段：
    （一）is_no_payment 是否开启不支付原因填写功能，false：关闭；  true：开启； 默认false
    （二）no_payment_error_type 指定话单的异常类型error_type进行不支付原因填写，每个异常类型使用逗号分割，例 10046,10047,10048;  默认10046和10048
  2、需导入不支付原因表 tp_no_payment 该表字段说明：
     ticket_type：话单类型，0：正常单；1：异常单；2：other单；3：预结单。 表示哪些话单能进行该记录的不支付原因填写，用英文逗号分隔开; 如 0,1,2,3
     ref_value_str: 需要匹配的要素串#后面的内容，其中中间如果配置为%则代表通配符，表示任意值;  如  1011,%,false
     error_type: 异常类型
     no_payment： 不支付原因
  3、填写逻辑
    （一）话单里的话单类型ticket_type为0 或 3， 或 异常类型error_type为配置文件里配置的no_payment_error_type的其中之一，则取出该话单要素串中#后的字串，通过表中的ticket_type + ref_value_str进行匹配，若匹配到，则给话单里的no_payment填写不支付原因；
    （二）当一条件不满足时，则通过话单里的异常类型error_type匹配表中的error_type，若一致，则填写不支付原因。
  4、建表语句：
  CREATE TABLE "config"."tp_no_payment_new" (
    "ticket_type" varchar(64) COLLATE "pg_catalog"."default",
    "ref_value_str" varchar(64) COLLATE "pg_catalog"."default",
    "error_type" int8,
    "no_payment" varchar(100) COLLATE "pg_catalog"."default",
    "seq_id" int4 NOT NULL
  )
  ;
  COMMENT ON COLUMN "config"."tp_no_payment_new"."ticket_type" IS '话单类型，0：正常单；1：异常单；2：other单；3：预结单';
  COMMENT ON COLUMN "config"."tp_no_payment_new"."ref_value_str" IS '要素串';
  COMMENT ON COLUMN "config"."tp_no_payment_new"."error_type" IS '异常类型';
  COMMENT ON COLUMN "config"."tp_no_payment_new"."no_payment" IS '不支付原因';
  COMMENT ON COLUMN "config"."tp_no_payment_new"."seq_id" IS '主键唯一标识';

    INSERT INTO "config"."tp_no_payment_new" VALUES ('0,1,2,3', '1103,%,false', NULL, 'zsf_测试不支付原因！！', 2);
    INSERT INTO "config"."tp_no_payment_new" VALUES ('0', '0', 211051, '找到多个终端编码', 17);

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：TicketIndb_code_2023-03-28 11:00
【修订日期】：2023-03-28
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54933
【实现功能】：
1、原先是用表名来确定文件名，所以一个话单6000会拆成9个文件，影响合账的效率；要求改成按照事件类型来确定文件名，减少文件个数。

【变更文件】：
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndbTable.java

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
【发布版本】：TicketIndb_code_2023-03-02 11:00
【修订日期】：2023-03-02
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54647
【实现功能】：
1、入库的输出话单以前是按照拆单数量，输出的outRecord,如果有预结单的情况，会导致输出记录数不准确。改造成拆单的情况，输出也是一条。

【变更文件】：
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndb.java
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndbTable.java

【修订要点】：
  1、无

【注意事项】：
  1、无
 --------------------------------------------------------------------------------------------------
【发布版本】：TicketIndb_code_2023-02-07 11:00
【修订日期】：2023-02-07
【修订人员】：fuxingwang
【需求单号】：无
【SVN提交前版本】：54542
【实现功能】：
1、ticket_type、latn_id、event_type_id话单中不存在这些字段或者值为空，进入错单
2、支持时间类型为yyyyMMdd和yyyy-MM-dd的格式入库。

【变更文件】：
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndb.java
修改：src/main/java/com/itco/ticketindb/module/CalcRecordTicketIndbTable.java
修改：src/main/java/com/itco/ticketindb/TicketIndb.java
新增：src/main/resources/version.txt

【修订要点】：
  1、无

【注意事项】：
  1、无
--------------------------------------------------------------------------------------------------
