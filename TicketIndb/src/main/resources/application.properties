#log å¼å³
logging.level.root=WARN
logging.level.com.itco=INFO
logging.level.org.apache.zookeeper=WARN
logging.level.com.ctg.itrdc.cache=ERROR
#ä¸æå°spring boot å¯å¨çæ¥å
logging.level.org.springframework.boot.autoconfigure=ERROR 

#è¾åºæ¥å¿æä»¶
logging.pattern.console=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.pattern.file=%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger-> %msg%n
logging.file.name=${LOG_HOME:~/javalog}/TicketIndb.log
logging.file.max-size=50MB
logging.pattern.rolling-file-name=${logging.file.name}.%d{yyyy-MM-dd}.%i.gz
logging.file.max-history=30
logging.file.total-size-cap=100MB

#è°è¯ä½¿ç¨å¼å³ workFlag:offline onlineãç¦»çº¿æ¨¡å¼  å¶ä»æåµï¼å¨çº¿è°åº¦æ¨¡å¼
workFlag=online
workDir=D:\\1_test\\Rating
workFileName=N_REAL_20220720162506_2000_6292.1014

#åå¸çæ¬
version=TicketIndb_deploy_2023-01-30 11:00