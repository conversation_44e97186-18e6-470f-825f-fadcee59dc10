package com.itco.ticketindb.entity.entityDef;


public class TplIndbTable {

    int table_id;
    int event_type_id;
    int latn_id;
    int ticket_type;
    int if_deduct;
    int billing_cycle_id;
    String table_name;
    int table_format_id;
    String comments;

    public int getTable_id() {
        return table_id;
    }

    public void setTable_id(int table_id) {
        this.table_id = table_id;
    }

    public int getEvent_type_id() {
        return event_type_id;
    }

    public void setEvent_type_id(int event_type_id) {
        this.event_type_id = event_type_id;
    }

    public int getLatn_id() {
        return latn_id;
    }

    public void setLatn_id(int latn_id) {
        this.latn_id = latn_id;
    }

    public int getTicket_type() {
        return ticket_type;
    }

    public void setTicket_type(int ticket_type) {
        this.ticket_type = ticket_type;
    }

    public int getIf_deduct() {
        return if_deduct;
    }

    public void setIf_deduct(int if_deduct) {
        this.if_deduct = if_deduct;
    }

    public int getBilling_cycle_id() {
        return billing_cycle_id;
    }

    public void setBilling_cycle_id(int billing_cycle_id) {
        this.billing_cycle_id = billing_cycle_id;
    }

    public String getTable_name() {
        return table_name;
    }

    public void setTable_name(String table_name) {
        this.table_name = table_name;
    }

    public int getTable_format_id() {
        return table_format_id;
    }

    public void setTable_format_id(int table_format_id) {
        this.table_format_id = table_format_id;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }


    @Override
    public String toString() {
        return "TplIndbTable{" +
                "table_id=" + table_id +
                ", event_type_id=" + event_type_id +
                ", latn_id=" + latn_id +
                ", ticket_type=" + ticket_type +
                ", billing_cycle_id=" + billing_cycle_id +
                ", table_name='" + table_name + '\'' +
                ", table_format_id=" + table_format_id +
                ", comments='" + comments + '\'' +
                '}';
    }
}
