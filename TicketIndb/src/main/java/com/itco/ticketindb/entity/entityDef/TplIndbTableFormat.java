package com.itco.ticketindb.entity.entityDef;


public class TplIndbTableFormat {

    int table_format_id;

    String table_format_name;

    String comments;

    public int getTable_format_id() {
        return table_format_id;
    }

    public void setTable_format_id(int table_format_id) {
        this.table_format_id = table_format_id;
    }

    public String getTable_format_name() {
        return table_format_name;
    }

    public void setTable_format_name(String table_format_name) {
        this.table_format_name = table_format_name;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "TplIndbTableFormat{" +
                "table_format_id=" + table_format_id +
                ", table_format_name='" + table_format_name + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
