package com.itco.ticketindb.entity;

import com.itco.ticketindb.entity.entityDef.*;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EntityMgr {
    List<TprResourceAttr> tprResourceAttrList = null;
    List<TplIndbTable> tplIndbTableList = null;
    List<TplIndbTableFormat> tplIndbTableFormatList = null;
    List<TplIndbTableFormatItem> tplIndbTableFormatItemList = null;

    Map<Integer, TprResourceAttr> tprResourceAttrMap = null;
    Map<Integer, List<TplIndbTableFormatItem>> tplIndbTableFormatItemMap = null;
    Map<Integer, BreakPoint> breakPointMap = null;
    Map<String, TplIndbTable> tplIndbTableMap = new HashMap<>();
    Map<String, TpNoPayment> tpNoPaymentElementMap = null;
    Map<Long, TpNoPayment> tpNoPaymentErrorTypeMap = null;

    public List<TprResourceAttr> getTprResourceAttrList() {
        return tprResourceAttrList;
    }

    public void setTprResourceAttrList(List<TprResourceAttr> tprResourceAttrList) {
        this.tprResourceAttrList = tprResourceAttrList;
        tprResourceAttrMap = new HashMap<>();
        for (TprResourceAttr tprResourceAttr : tprResourceAttrList) {
            tprResourceAttrMap.put(tprResourceAttr.getAttr_id(), tprResourceAttr);
        }
    }

    public Map<Integer, TprResourceAttr> getTprResourceAttrMap() {
        return tprResourceAttrMap;
    }

    public void setTprResourceAttrMap(Map<Integer, TprResourceAttr> tprResourceAttrMap) {
        this.tprResourceAttrMap = tprResourceAttrMap;
    }


    public List<TplIndbTable> getTplIndbTableList() {
        return tplIndbTableList;
    }

    public void setTplIndbTableList(List<TplIndbTable> list) {
        tplIndbTableList = list;
        for (TplIndbTable tplIndbTable : list) {
            String tplKey = tplIndbTable.getEvent_type_id() + "|" + tplIndbTable.getLatn_id() + "|" + tplIndbTable.getTicket_type();
            tplIndbTableMap.put(tplKey, tplIndbTable);
        }
    }

    public Map<String, TplIndbTable> getTplIndbTableMap() {
        return tplIndbTableMap;
    }

    public List<TplIndbTableFormat> getTplIndbTableFormatList() {
        return tplIndbTableFormatList;
    }

    public void setTplIndbTableFormatList(List<TplIndbTableFormat> tplIndbTableFormatList) {
        this.tplIndbTableFormatList = tplIndbTableFormatList;
    }

    public List<TplIndbTableFormatItem> getTplIndbTableFormatItemList() {
        return tplIndbTableFormatItemList;
    }

    public void setTplIndbTableFormatItemList(List<TplIndbTableFormatItem> tplIndbTableFormatItemList) {
        this.tplIndbTableFormatItemList = tplIndbTableFormatItemList;
        tplIndbTableFormatItemMap = new HashMap<>();
        for (TplIndbTableFormatItem item : tplIndbTableFormatItemList) {

            List<TplIndbTableFormatItem> list = tplIndbTableFormatItemMap.get(item.getTable_format_id());

            if (list == null) {
                List<TplIndbTableFormatItem> tmp = new ArrayList<>();
                tmp.add(item);
                tplIndbTableFormatItemMap.put(item.getTable_format_id(), tmp);
            } else {
                list.add(item);
            }
        }
    }

    public Map<Integer, List<TplIndbTableFormatItem>> getTplIndbTableFormatItemMap() {
        return tplIndbTableFormatItemMap;
    }

    public void setTplIndbTableFormatItemMap(Map<Integer, List<TplIndbTableFormatItem>> tplIndbTableFormatItemMap) {
        this.tplIndbTableFormatItemMap = tplIndbTableFormatItemMap;
    }

    public Map<Integer, BreakPoint> getBreakPointMap() {
        return breakPointMap;
    }

    public void setBreakPointMap(List<BreakPoint> breakPointList) {
        breakPointMap = new HashMap<>();
        for (BreakPoint breakPoint : breakPointList) {
            breakPointMap.put(breakPoint.getPoint_id(), breakPoint);
        }
    }

    // 上载不支付原因码表(根据ref_value_str)
    public void setTpNoPaymentElementMap(List<TpNoPayment> tpNoPaymentList) {
        tpNoPaymentElementMap = new HashMap<>();
        for(TpNoPayment tpNoPayment : tpNoPaymentList) {
            if (!StringUtils.isNotEmpty(tpNoPayment.getRef_value_str())) {
                continue;
            }
            // ticket_type可能为 0,1,2,3
            for (String ticketType : tpNoPayment.getTicket_type().split(",")) {
                // 以ticket_type + ef_value_str为键
                String key = ticketType + "#" + tpNoPayment.getRef_value_str();
                tpNoPaymentElementMap.put(key,tpNoPayment);
            }
        }
    }

    public Map<String, TpNoPayment> getTpNoPaymentElementMap() {
        return tpNoPaymentElementMap;
    }

    // 上载不支付原因码表(根据error_type)
    public void setTpNoPaymentErrorTypeMap(List<TpNoPayment> tpNoPaymentList) {
        tpNoPaymentErrorTypeMap = new HashMap<>();
        for(TpNoPayment tpNoPayment : tpNoPaymentList) {
            Long tpKey = tpNoPayment.getError_type();
            if (tpKey != null && tpKey != 0){
                // 以error_type作为键
                tpNoPaymentErrorTypeMap.put(tpKey, tpNoPayment);
            }
        }
    }

    public Map<Long, TpNoPayment> getTpNoPaymentErrorTypeMap() {
        return tpNoPaymentErrorTypeMap;
    }


    public void show() {
        for (TprResourceAttr tprResourceAttr : tprResourceAttrList) {
            System.out.println(tprResourceAttr.toString());
        }
        for (TplIndbTable tplIndbTable : tplIndbTableList) {
            System.out.println("tpl_indb_table_list:" + tplIndbTable.toString());
        }
        for (TplIndbTableFormat tplIndbTableFormat : tplIndbTableFormatList) {
            System.out.println(tplIndbTableFormat.toString());
        }
        for (TplIndbTableFormatItem tplIndbTableFormatItem : tplIndbTableFormatItemList) {
            System.out.println(tplIndbTableFormatItem.toString());
        }
    }

    // 通过事件类型和话单类型获取入库格式iD
    public TplIndbTable getTableFormatItemByEventTypeid(int event_type_id, int latn_id, int ticket_type, int if_deduct) {
        int tmpTicketType = ticket_type;
        // 非立即结算的 是ticket_type 3
        if (if_deduct == 0 && ticket_type == 0) {
            tmpTicketType = 3;
        }

        for (TplIndbTable tplIndbTable : tplIndbTableList) {
            if (event_type_id == tplIndbTable.getEvent_type_id() &&
                    tmpTicketType == tplIndbTable.getTicket_type() &&
                    (latn_id == tplIndbTable.getLatn_id() || tplIndbTable.getLatn_id() == 0)) {
                return tplIndbTable;
            }
        }

        return null;
    }

    // 通过入库格式Id话单类型获取入库字段列表
    public List<TplIndbTableFormatItem> getTableFormatItemByTableFormatId(int table_format_id) {
        return tplIndbTableFormatItemMap.get(table_format_id);
    }

}
