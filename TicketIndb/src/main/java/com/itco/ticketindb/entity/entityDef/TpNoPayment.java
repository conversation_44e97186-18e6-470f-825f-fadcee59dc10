package com.itco.ticketindb.entity.entityDef;

public class TpNoPayment {

    /**
     * 计费话单类型
     */
    private String ticket_type;


    /**
     * 参考值定义的标识
     */
    private String ref_value_str;

    /**
     * 异常类型
     */
    private Long error_type;

    /**
     * 不支付原因
     */
    private String no_payment;

    public String getTicket_type() {
        return ticket_type;
    }

    public void setTicket_type(String ticket_type) {
        this.ticket_type = ticket_type;
    }

    public String getRef_value_str() {
        return ref_value_str;
    }

    public void setRef_value_str(String ref_value_str) {
        this.ref_value_str = ref_value_str;
    }

    public Long getError_type() {
        return error_type;
    }

    public void setError_type(Long error_type) {
        this.error_type = error_type;
    }

    public String getNo_payment() {
        return no_payment;
    }

    public void setNo_payment(String no_payment) {
        this.no_payment = no_payment;
    }
}
