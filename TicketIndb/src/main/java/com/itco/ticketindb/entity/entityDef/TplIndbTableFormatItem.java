package com.itco.ticketindb.entity.entityDef;


public class TplIndbTableFormatItem {
    int table_format_id;

    int indb_field_id;

    int source_attr_id;

    String default_value;

    String comments;

    public int getTable_format_id() {
        return table_format_id;
    }

    public void setTable_format_id(int table_format_id) {
        this.table_format_id = table_format_id;
    }

    public int getIndb_field_id() {
        return indb_field_id;
    }

    public void setIndb_field_id(int indb_field_id) {
        this.indb_field_id = indb_field_id;
    }

    public int getSource_attr_id() {
        return source_attr_id;
    }

    public void setSource_attr_id(int source_attr_id) {
        this.source_attr_id = source_attr_id;
    }

    public String getDefault_value() {
        return default_value;
    }

    public void setDefault_value(String default_value) {
        this.default_value = default_value;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "TplIndbTableFormatItem{" +
                "table_format_id=" + table_format_id +
                ", indb_field_id=" + indb_field_id +
                ", source_attr_id=" + source_attr_id +
                ", default_value='" + default_value + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
