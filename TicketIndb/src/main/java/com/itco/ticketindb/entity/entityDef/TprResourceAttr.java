package com.itco.ticketindb.entity.entityDef;

public class TprResourceAttr {
    int attr_id;
    String en_name;
    String ch_name;
    String data_type;
    String comments;

    public int getAttr_id() {
        return attr_id;
    }

    public void setAttr_id(int attr_id) {
        this.attr_id = attr_id;
    }

    public String getEn_name() {
        return en_name;
    }

    public void setEn_name(String en_name) {
        this.en_name = en_name;
    }

    public String getCh_name() {
        return ch_name;
    }

    public void setCh_name(String ch_name) {
        this.ch_name = ch_name;
    }

    public String getData_type() {
        return data_type;
    }

    public void setData_type(String data_type) {
        this.data_type = data_type;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public String toString() {
        return "TprResourceAttr{" +
                "attr_id=" + attr_id +
                ", en_name='" + en_name + '\'' +
                ", ch_name='" + ch_name + '\'' +
                ", data_type='" + data_type + '\'' +
                ", comments='" + comments + '\'' +
                '}';
    }
}
