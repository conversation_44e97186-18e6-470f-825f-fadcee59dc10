//package com.itco.ticketindb.Hbase;
//
//
//import com.itco.filter.hdfs.HDFSUtil;
//import org.apache.commons.logging.Log;
//import org.apache.commons.logging.LogFactory;
//import org.apache.hadoop.conf.Configuration;
//import org.apache.hadoop.fs.Path;
//import org.apache.hadoop.hbase.util.Bytes;
//import org.apache.hadoop.security.UserGroupInformation;
//
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Properties;
//import java.util.concurrent.Future;
//
//
///**
// * HBase工具类
// *
// * <AUTHOR>
// */
//public class HBaseClientApi {
//
//    private static HBaseClientApi hBaseClientApi;
//    private HDFSUtil hdfsUtil;
//    //private Common common;
//    private Configuration conf;
//    private Connection connection;
//    private Admin admin;
//    private Log log = LogFactory.getLog(HBaseClientApi.class);
//    //    String path = "HBase.properties";
////    String commonPath = "Common.properties";
//    Properties props = null;
//
//    public static HBaseClientApi newInstance() {
//        if (hBaseClientApi == null) {
//            hBaseClientApi = new HBaseClientApi();
//        }
//        return hBaseClientApi;
//    }
//
//    public boolean init() {
//        boolean res = true;
//        if (conf == null) {
//            Configuration conf = new Configuration();
//            if (true) {
//                hdfsUtil = new HDFSUtil();
//                if (null == hdfsUtil.getHdfs()) {
//                    log.error("fileSystem 初始化失败");
//                    return false;
//                }
//
//                String confDir = "/config";
//                String[] resouces = {"core-site.xml", "hbase-site.xml"};
//                for (String resouce : resouces) {
//                    java.nio.file.Path path = Paths.get(confDir, resouce);
//                    if (Files.exists(path)) {
//                        conf.addResource(new Path(path.toString()));
//                        log.info("配置文件目录:" + confDir + "加载配置文件：" + resouce + " [加载成功]\n");
//                    } else {
//                        log.info("配置文件目录:" + confDir + "加载配置文件：" + resouce + " [加载失败：文件不存在]\n");
//                    }
//                }
//                UserGroupInformation.setConfiguration(conf);
//                try {
//                    System.setProperty("java.security.krb5.conf", conf.get("java.security.krb5.conf"));
//                    System.setProperty("sun.security.krb5.debug", conf.get("sun.security.krb5.debug"));
//                    UserGroupInformation.loginUserFromKeytab(conf.get("custom.kerberos.user"), conf.get("custom.kerberos.keytab"));
//                    connection = ConnectionFactory.createConnection(conf);
//                    admin = connection.getAdmin();
//                } catch (IOException e) {
//                    log.error(e.getMessage());
//                    res = false;
//                } finally {
//                    return res;
//                }
//            } else {
//                System.setProperty("hadoop.home.dir", "D:\\DevlopFiles\\hadoop-common-2.2.0-bin");
//                conf.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
//                conf.set("hbase.zookeeper.quorum", "**************");
//                conf.set("hbase.zookeeper.property.clientPort", "2182");
//                try {
//                    connection = ConnectionFactory.createConnection(conf);
//                    admin = connection.getAdmin();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//
//
//            //设置hbase服务器ip
////            props = zkClientApi.getPropertiesFromZK(path);
////
////            conf.set("zookeeper.znode.parent", "/hbase-secure");
////            conf.set("hadoop.security.authentication", "Kerberos");
////            conf.set("keytab.file", keytabPath);
////            conf.set("kerberos.principal", "<EMAIL>");
////
////            conf.set("hbase.zookeeper.quorum", host);
////            conf.set("hbase.zookeeper.property.clientPort", port);
////            log.info("HBase host and port" + host + "," + port);
////            log.info("HBase period and cacheSize" + period + "," + cacheSize);
////            conf.set("hbase.client.scanner.timeout.period", period);
////            conf.set("hfile.block.cache.size", cacheSize);
//
//
//        }
//        return true;
//    }
//
//
//    /**
//     * 创建命名空间
//     *
//     * @param spaceName
//     */
//    public void createNameSpace(String spaceName) {
//        NamespaceDescriptor namespaceDescriptor = NamespaceDescriptor.create(spaceName).build();
//        try {
//            admin.createNamespace(namespaceDescriptor);
//            log.info("创建命名空间" + spaceName + "成功！");
//        } catch (NamespaceExistException ex) {
//            log.info("命名空间已存在！");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 创建表
//     *
//     * @param tableName
//     * @param cfs
//     */
//    public void createTableAsync(String tableName, String... cfs) {
//        if (isTableExist(tableName)) {
//            log.info("表" + tableName + "已经存在！");
//            return;
//        }
//        if (cfs == null || cfs.length == 0) {
//            log.info("列族不能为空！");
//            return;
//        }
//        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
//        List<ColumnFamilyDescriptor> familyDescriptors = new ArrayList<>();
//        for (String cf : cfs) {
//            ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(cf)).build();
//            familyDescriptors.add(columnFamilyDescriptor);
//        }
//        TableDescriptor tableDescriptor = tableDescriptorBuilder.setColumnFamilies(familyDescriptors).build();
//
//        try {
//            Future<Void> tableAsync = admin.createTableAsync(tableDescriptor);
//            log.info("创建表" + tableName + "成功！");
//        } catch (Exception e) {
//            log.info("创建失败！ 表名：" + tableName);
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 创建表
//     *
//     * @param tableName
//     * @param cfs
//     */
//    public void createTableAsyncSplit(String tableName, List<String> splits, String... cfs) {
//        byte[][] splitBytes = new byte[splits.size()][10];
//        for (int i = 0; i < splits.size(); i++) {
//            splitBytes[i] = Bytes.toBytes(splits.get(i));
//        }
//
//        if (isTableExist(tableName)) {
//            log.info("表" + tableName + "已经存在！");
//            return;
//        }
//        if (cfs == null || cfs.length == 0) {
//            log.info("列族不能为空！");
//            return;
//        }
//        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
//        List<ColumnFamilyDescriptor> familyDescriptors = new ArrayList<>();
//        for (String cf : cfs) {
//            ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(cf)).build();
//            familyDescriptors.add(columnFamilyDescriptor);
//        }
//        TableDescriptor tableDescriptor = tableDescriptorBuilder.setColumnFamilies(familyDescriptors).build();
//
//        try {
//            Future<Void> tableAsync = admin.createTableAsync(tableDescriptor, splitBytes);
//            log.info("创建表" + tableName + "成功！预分区：" + splits.toString());
//        } catch (Exception e) {
//            log.info("创建失败！ 表名：" + tableName + ",预分区：" + splits.toString());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 创建表
//     *
//     * @param tableName
//     * @param cfs
//     */
//    public void createTable(String tableName, String... cfs) {
//        if (isTableExist(tableName)) {
//            log.info("表" + tableName + "已经存在！");
//            return;
//        }
//        if (cfs == null || cfs.length == 0) {
//            log.info("列族不能为空！");
//            return;
//        }
//        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
//        List<ColumnFamilyDescriptor> familyDescriptors = new ArrayList<>();
//        for (String cf : cfs) {
//            ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(cf)).build();
//            familyDescriptors.add(columnFamilyDescriptor);
//        }
//        TableDescriptor tableDescriptor = tableDescriptorBuilder.setColumnFamilies(familyDescriptors).build();
//
//        try {
//            admin.createTable(tableDescriptor);
//            log.info("创建表" + tableName + "成功！");
//        } catch (Exception e) {
//            log.info("创建失败！ 表名：" + tableName);
//            e.printStackTrace();
//        }
//    }
//
//
//    public void printTabledesc(String tableName) {
//        try {
//            Table table = connection.getTable(TableName.valueOf(tableName));
//            TableDescriptor descriptor = table.getDescriptor();
//            log.info(tableName + ":表描述：");
//            log.info("toString");
//            String s = descriptor.toString();
//            log.info("toStringCustomizedValues");
//            String s1 = descriptor.toStringCustomizedValues();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * 判断表是否存在
//     *
//     * @param tableName
//     * @return
//     */
//    public boolean isTableExist(String tableName) {
//        try {
//            return admin.tableExists(TableName.valueOf(tableName));
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return false;
//    }
//
//    /**
//     * 列出所有表
//     */
//    public void listTables() {
//        try {
//            TableName[] tableNames = admin.listTableNames();
//            log.info("打印所有命名空间表名：");
//            for (TableName tableName : tableNames) {
//                log.info(tableName);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 列出指定命名空间里的表
//     *
//     * @param spaceName
//     */
//    public TableName[] getTablesByNameSpace(String spaceName) {
//        if (spaceName == null || spaceName.length() == 0) {
//            log.info("请输入正确的命名空间！");
//            return null;
//        }
//        try {
//            TableName[] tableNames = admin.listTableNamesByNamespace(spaceName);
//            return tableNames;
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 列出指定命名空间里的表
//     *
//     * @param spaceName
//     */
//    public void listTablesByNameSpace(String spaceName) {
//        if (spaceName == null || spaceName.length() == 0) {
//            log.info("请输入正确的命名空间！");
//            return;
//        }
//        try {
//            TableName[] tableNames = admin.listTableNamesByNamespace(spaceName);
//            log.info("打印" + spaceName + "命名空间下表名：");
//            for (TableName tableName : tableNames) {
//                log.info(tableName);
//            }
//            log.info("一共：" + tableNames.length + "张表。");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 删除指定row
//     *
//     * @param tableName
//     */
//    public void deleteRow(String tableName, byte[] row) {
//        if (tableName == null || tableName.length() == 0) {
//            log.info("请正确输入表名！");
//            return;
//        }
//        Table table = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//            Delete del = new Delete(row);
//            table.delete(del);
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//        }
//    }
//
//    /**
//     * 扫描表
//     *
//     * @param tableName
//     * @param startRow  起始位置
//     * @param stopRow   结束位置
//     */
//    public ResultScanner scanTable(String tableName, String startRow, String stopRow) {
//        if (tableName == null || tableName.length() == 0) {
//            log.info("请正确输入表名！");
//            return null;
//        }
//        Table table = null;
//        ResultScanner resultScanner = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//
//            Scan scan = new Scan();
//            // 左闭右开
//            if (startRow != null && stopRow != null) {
//                scan.withStartRow(Bytes.toBytes(startRow));
//                scan.withStopRow(Bytes.toBytes(stopRow));
//            }
//            resultScanner = table.getScanner(scan);
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//            return resultScanner;
//        }
//    }
//
//    /**
//     * 扫描表
//     *
//     * @param tableName
//     */
//    public ResultScanner scanTable(String tableName) {
//        return scanTable(tableName, null, null);
//    }
//
//    /**
//     * 扫描表
//     *
//     * @param tableName
//     */
//    public ResultScanner scanTable(String tableName, FilterBase filter) {
//        Scan scan = new Scan();
//        scan.setFilter(filter);
//        return scanTable(tableName, scan);
//    }
//
//    /**
//     * 扫描表
//     *
//     * @param tableName
//     * @param family
//     */
//    public ResultScanner getFamilyData(String tableName, String family) {
//        FamilyFilter ff = new FamilyFilter(CompareFilter.CompareOp.EQUAL,
//                new BinaryComparator(Bytes.toBytes(family)));
//        return scanTable(tableName, ff);
//    }
//
//    /**
//     * 按列值扫描表
//     *
//     * @param tableName
//     * @param family
//     * @param column
//     * @param value
//     * @param compareOp
//     * @return
//     */
//    public ResultScanner getDataByColumnValue(String tableName, String family, String column,
//                                              String value, CompareFilter.CompareOp compareOp) {
//        SingleColumnValueFilter scvf = new SingleColumnValueFilter(Bytes.toBytes(family), Bytes.toBytes(column),
//                compareOp, value.getBytes());
//        scvf.setFilterIfMissing(true);
//        return scanTable(tableName, scvf);
//    }
//
//    /**
//     * 按列族扫描表
//     *
//     * @param tableName
//     * @param family
//     * @param compareOp
//     * @return
//     */
//    public ResultScanner getDataByFamilyName(String tableName, String family,
//                                             CompareFilter.CompareOp compareOp) {
//        FamilyFilter ff = new FamilyFilter(compareOp, new BinaryComparator(Bytes.toBytes(family)));
//        return scanTable(tableName, ff);
//    }
//
//
//    /**
//     * 列名扫描表
//     *
//     * @param tableName
//     * @param qualifier
//     * @param compareOp
//     * @return
//     */
//    public ResultScanner getQualifierData(String tableName, String qualifier,
//                                          CompareFilter.CompareOp compareOp) {
//        QualifierFilter qf = new QualifierFilter(compareOp, new BinaryComparator(Bytes.toBytes(qualifier)));
//        return scanTable(tableName, qf);
//    }
//
//    /**
//     * 按列名前缀扫描表
//     *
//     * @param tableName
//     * @param columnPrefix
//     */
//    public ResultScanner getDataByColumnPrefix(String tableName, String columnPrefix) {
//        ColumnPrefixFilter cf = new ColumnPrefixFilter(Bytes.toBytes(columnPrefix));
//        return scanTable(tableName, cf);
//    }
//
//    /**
//     * 按多列名前缀扫描表
//     *
//     * @param tableName
//     * @param prefixes
//     */
//    public ResultScanner getDataByMultipleColumnPrefix(String tableName, byte[][] prefixes) {
//        MultipleColumnPrefixFilter mf = new MultipleColumnPrefixFilter(prefixes);
//        return scanTable(tableName, mf);
//    }
//
//    /**
//     * 仅返回每一行中的第一个cell的值
//     *
//     * @param tableName
//     */
//    public ResultScanner getFirstCellDatas(String tableName) {
//        FirstKeyOnlyFilter ff = new FirstKeyOnlyFilter();
//        return scanTable(tableName, ff);
//    }
//
//    /**
//     * 打印扫描结果
//     *
//     * @param results
//     */
//    public int printScanner(ResultScanner results) {
//        int i = 0;
//        log.info("RowKey\tTimeStamp\tcolumnFamilyName\tcolumnQualifierName");
//        for (Result rs : results) {
//            i++;
//            showCell(rs);
//        }
//        return i;
//    }
//
//    /**
//     * 扫描表
//     *
//     * @param tableName
//     * @param scan
//     */
//    public ResultScanner scanTable(String tableName, Scan scan) {
//        if (tableName == null || tableName.length() == 0) {
//            log.info("请正确输入表名！");
//            return null;
//        }
//        Table table = null;
//        ResultScanner resultScanner = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//            resultScanner = table.getScanner(scan);
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//            return resultScanner;
//        }
//    }
//
//    /**
//     * 获取某列数据
//     *
//     * @param tableName
//     * @param columnName
//     * @return
//     */
//    public ResultScanner getColumnData(String tableName, String columnName) {
//        QualifierFilter filter = new QualifierFilter(
//                CompareFilter.CompareOp.EQUAL, new BinaryComparator(Bytes.toBytes(columnName)));
//        return scanTable(tableName, filter);
//    }
//
//    /**
//     * 格式化输出
//     *
//     * @param result
//     */
//    public void showCell(Result result) {
//        Cell[] cells = result.rawCells();
//        for (Cell cell : cells) {
//            System.out.print(Bytes.toString(CellUtil.cloneRow(cell)) + "\t");
//            System.out.print(cell.getTimestamp() + "\t");
//            String columnFamilyName = Bytes.toString(CellUtil.cloneFamily(cell));
//            String columnQualifierName = Bytes.toString(CellUtil.cloneQualifier(cell));
//            String value = Bytes.toString(CellUtil.cloneValue(cell));
//            System.out.println(columnFamilyName + ":" + columnQualifierName + "\t\t\t" + value);
//        }
//    }
//
//    /**
//     * 关闭连接
//     */
//    public void closeConn() {
//        try {
//            if (null != admin) {
//                admin.close();
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    /**
//     * 关闭表
//     *
//     * @param table
//     */
//    public void closeTable(Table table) {
//        if (table != null) {
//            try {
//                table.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    /**
//     * 检查并插入信息
//     *
//     * @param tableName
//     * @param rowKey
//     * @param cf
//     * @param value
//     */
//    public boolean checkAndPutTableData(String tableName, String rowKey, String cf, String value) {
//        Table table = null;
//        boolean b = false;
//        try {
//
//            table = connection.getTable(TableName.valueOf(tableName));
//
////            log.info("获取表成功：" + table.getName());
//            Put p = new Put(Bytes.toBytes(rowKey));
////            log.info("获取put成功:" + p.getRow().toString());
//            p.addColumn(Bytes.toBytes(cf), Bytes.toBytes(""), Bytes.toBytes(value));
////            log.info("checkAndPut之前, value:" + value);
//            b = table.checkAndPut(Bytes.toBytes(rowKey), Bytes.toBytes(cf), Bytes.toBytes(""), null, p);
////            b = table.checkAndMutate(Bytes.toBytes(rowKey), Bytes.toBytes(cf)).qualifier(Bytes.toBytes(""))
////                    .ifNotExists().thenPut(p);
//
////            log.info("checkAndPut结果:" + b);
////            if (b)
////                log.info("插入数据成功！");
////            else
////                log.info("重复数据！");
//        } catch (Exception e) {
//            log.error("tablename: " + tableName + "columnfamily:" + cf + ":rowkey:" + rowKey + "value:" + value);
//            e.printStackTrace();
//        } finally {
////            log.info("插入结果:" + b);
//            closeTable(table);
//            return b;
//        }
//    }
//
//    /**
//     * 插入信息
//     *
//     * @param tableName
//     * @param rowKey
//     * @param cf
//     * @param value
//     */
//    public void putTableData(String tableName, String rowKey, String cf, String value) {
//        Table table = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//            Put p = new Put(Bytes.toBytes(rowKey));
//            p.addColumn(Bytes.toBytes(cf), null, Bytes.toBytes(value));
//            table.put(p);
//            log.info("插入数据成功！");
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//        }
//    }
//
//    /**
//     * 插入信息
//     *
//     * @param tableName
//     * @param rowKey
//     * @param cf
//     * @param column
//     * @param value
//     */
//    public void putTableData(String tableName, String rowKey, String cf, String column, String value) {
//        Table table = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//            Put p = new Put(Bytes.toBytes(rowKey));
//            p.addColumn(Bytes.toBytes(cf), Bytes.toBytes(column), Bytes.toBytes(value));
//            table.put(p);
//            log.info("插入数据成功！");
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//        }
//    }
//
//
//    /**
//     * 获得指定row
//     *
//     * @param tableName
//     * @param rowKey
//     * @param colFamily
//     * @param col
//     */
//    public Result getRow(String tableName, String rowKey, String colFamily, String col) {
//        Table table = null;
//        Result result = null;
//        try {
//            table = connection.getTable(TableName.valueOf(tableName));
//            Get g = new Get(Bytes.toBytes(rowKey));
//            // 获取指定列族数据
//            if (col == null && colFamily != null) {
//                g.addFamily(Bytes.toBytes(colFamily));
//            } else if (col != null && colFamily != null) {
//                // 获取指定列数据
//                g.addColumn(Bytes.toBytes(colFamily), Bytes.toBytes(col));
//            }
//            result = table.get(g);
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            closeTable(table);
//            return result;
//        }
//    }
//
//    /**
//     * 删除表
//     *
//     * @param tableName
//     */
//    public void deleteTable(String tableName) {
//        try {
//            if (!isTableExist(tableName)) {
//                return;
//            }
//            if (admin.isTableEnabled(TableName.valueOf(tableName))) {
//                admin.disableTable(TableName.valueOf(tableName));
//            }
//            admin.deleteTable(TableName.valueOf(tableName));
//            log.info("表：" + tableName + "删除成功！");
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.info("表：" + tableName + "删除失败！");
//        }
//    }
//
//    public void test(String tablename) {
//        try {
////            log.info("--------------------------------------------------");
////            createNameSpace("zhjs_data");
///*            log.info("--------------------------------------------------");
//            createTable("zhjs_data:stu", "info", "score");
//            log.info("--------------------------------------------------");
//            listTables();
//            log.info("--------------------------------------------------");
//            listTablesByNameSpace("zhjs_data");
//            log.info("--------------------------------------------------");
//            putTableData("zhjs_data:stu", "20000", "info", "name", "迈克");
//            putTableData("zhjs_data:stu", "20001", "info", "name", "jane");
//            putTableData("zhjs_data:stu", "20002", "info", "name", "tom");
//            log.info("--------------------------------------------------");
//            putTableData("zhjs_data:stu", "20002", "score", "math", "98");
//            log.info("--------------------------------------------------");
//            putTableData("zhjs_data:stu", "20002", "info", "age", "22");
//            log.info("--------------------------------------------------");*/
//            log.info("--------------------------------------------------");
//            ResultScanner results = scanTable(tablename, null, null);
//            printScanner(results);
//            log.info("--------------------------------------------------");
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            closeConn();
//        }
//    }
//
//   /* public Common getCommon() {
//        return common;
//    }
//
//    public void setCommon(Common common) {
//        this.common = common;
//    }*/
//}
