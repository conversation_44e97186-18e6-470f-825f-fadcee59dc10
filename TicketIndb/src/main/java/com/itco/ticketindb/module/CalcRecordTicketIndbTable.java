package com.itco.ticketindb.module;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.itco.component.jdbc.DBUtils;
import com.itco.component.jdbc.DbPool;
import com.itco.component.zookeeper.ZkClientApi;
import com.itco.entity.common.KeyValue;
import com.itco.entity.function.FunctionPerl;
import com.itco.framework.Factory;
import com.itco.framework.calcrecord.CalcRecordManager;
import com.itco.framework.record.impl.settle.TransferSettle;
import com.itco.framework.record.impl.settle.impl.TransferFile;
import com.itco.rulefunction.ctgcache.TableService;
import com.itco.ticketindb.entity.EntityMgr;
import com.itco.ticketindb.entity.entityDef.*;
import com.itco.ticketindb.hdfs.TransferHdfs;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


public class CalcRecordTicketIndbTable extends CalcRecordManager {
    static Log log = LogFactory.getLog(CalcRecordTicketIndbTable.class);
    static EntityMgr entityMgr = new EntityMgr();

    String propPath = "TicketIndb.properties";
    int host_id = 0;
    int billing_line_id = 0;
    int module_id = 0;
    String moduleType = new String("MC");
    String etiFieldName = new String("source_event_type_id");

    static Map<String, String> tableSqlMap = new HashMap<>();

    //static Map<Integer, BreakPoint> breakPointMap = new HashMap<>();
    Map<String, BreakPointTable> breakPointTableMap = new HashMap<>();
    static Map<String, BreakPointTable> maxBreakPointTableMap = new HashMap<>();
    static Map<String, TpNoPayment> tpNoPaymentElementMap = new HashMap<>();
    static Map<Long, TpNoPayment> tpNoPaymentErrorTypeMap = new HashMap<>();


    SimpleDateFormat sdf_14 = new SimpleDateFormat("yyyyMMddHHmmss");
    SimpleDateFormat sdf_19 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    int m_rRecordSize = 0;
    String m_rRecordName = "a_charge";

    ZkClientApi zkClientApi = null;
    //JEDISUtil jedisUtil = new JEDISUtil();
    //HBaseClientApi hBaseClientApi = null;

    TransferSettle transfer = null;
    String hdfsPath = "/data/work/TicketIndb";

    boolean isDateBase = true;
    boolean isHbase = false;
    boolean isHdfs = false;
    boolean isNoPayment = false;
    String noPaymentErrorType = null;
    String isMode = "FILE";
    String updatePointMode = "point_redis_lock_zk";
    String familyColomn = "info";

    void printLog(String... params) {
        if (CalcRecordManager.getCommon().getIDebug() == 1) {
            for (int i = 0; i < params.length; i++) {
                log.info(params[i]);
            }
        }
    }

    void printError(String... params) {
        for (int i = 0; i < params.length; i++) {
            log.error(params[i]);
        }
    }

    public boolean InitReadIndbTableRule() {
        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();

            if (moduleType.equals("MC")) {  //正式流程
                List<TplIndbTable> tplIndbTableList = DBUtils.queryList(TplIndbTable.class, connTmp, "select * from tpl_indb_table order by event_type_id,latn_id desc,ticket_type");
                entityMgr.setTplIndbTableList(tplIndbTableList);
            } else if (moduleType.equals("TC")) {// 试算流程
                List<TplIndbTable> tplIndbTableList = DBUtils.queryList(TplIndbTable.class, connTmp, "select * from tpl_indb_table_tc order by event_type_id,latn_id desc,ticket_type");
                entityMgr.setTplIndbTableList(tplIndbTableList);
            } else if (moduleType.equals("PC")) {// 预出账流程
                List<TplIndbTable> tplIndbTableList = DBUtils.queryList(TplIndbTable.class, connTmp, "select * from tpl_indb_table_pc order by event_type_id,latn_id desc,ticket_type");
                entityMgr.setTplIndbTableList(tplIndbTableList);
            }

            entityMgr.setTprResourceAttrList(DBUtils.queryList(TprResourceAttr.class, connTmp, " select attr_id,lower(en_name) en_name,ch_name,substr(data_type,2) data_type,comments from tpr_resource_attr"));
            entityMgr.setTplIndbTableFormatList(DBUtils.queryList(TplIndbTableFormat.class, connTmp, "select table_format_id,table_format_name,comments from tpl_indb_table_format"));
            entityMgr.setTplIndbTableFormatItemList(DBUtils.queryList(TplIndbTableFormatItem.class, connTmp, "select table_format_id,indb_field_id,source_attr_id,default_value,comments from tpl_indb_table_format_item order by table_format_id,source_attr_id"));
            entityMgr.setBreakPointMap(DBUtils.queryList(BreakPoint.class, connTmp, "select point_id,module_id,host_id,billing_line_id,point_type,point_value,create_date from break_point"));

            // 上载不支付原因表
            if (isNoPayment) {
                entityMgr.setTpNoPaymentElementMap(DBUtils.queryList(TpNoPayment.class, connTmp, "select ticket_type, ref_value_str, error_type, no_payment from tp_no_payment_new"));
                entityMgr.setTpNoPaymentErrorTypeMap(DBUtils.queryList(TpNoPayment.class, connTmp, "select ticket_type, ref_value_str, error_type, no_payment from tp_no_payment_new"));
            }
        } finally {
            DbPool.close(connTmp);
        }

        if (!InitIndbSql()) {
            return false;
        }
        return true;
    }

    public boolean InitIndbSql() {
        List<TplIndbTable> tableList = entityMgr.getTplIndbTableList();
        Map<Integer, List<TplIndbTableFormatItem>> formatItemMap = entityMgr.getTplIndbTableFormatItemMap();
        Map<Integer, TprResourceAttr> attrMap = entityMgr.getTprResourceAttrMap();

        for (TplIndbTable table : tableList) {
            if (common.getIDebug() == 1) {
                log.info(table.toString());
            }
            if (tableSqlMap.get(String.valueOf(table.getTable_format_id())) != null) {
                continue;
            }

            List<TplIndbTableFormatItem> formatItemList = formatItemMap.get(table.getTable_format_id());
            if (formatItemList == null) {
                log.error("未查找到入库字段格式：" + table.getTable_format_id());
                return false;
            }

            String insert = new String("(");
            String value = new String(" (");

            for (int i = 0; i < formatItemList.size(); i++) {
                TprResourceAttr attr = attrMap.get(formatItemList.get(i).getIndb_field_id());
                if (attr == null) {
                    log.error("未找到配置入库字段属性信息：" + formatItemList.get(i));
                    return false;
                }
                insert += attr.getEn_name();
                if (i + 1 < formatItemList.size()) {
                    insert += ",";
                    value += "?,";
                } else {
                    insert += ")";
                    value += "?)";
                }
            }
            String sql = "insert into XXXXYYYY" + insert + " values " + value;

            tableSqlMap.put(String.valueOf(table.getTable_format_id()), sql);
            if (common.getIDebug() == 1) {
                log.info("table_format_id->" + table.getTable_format_id() + ":" + sql);
            }
        }
        log.info("host_id:" + host_id + ",billing_line_id:" + billing_line_id + ",process_id:" + getProcess_id());

        module_id = Integer.valueOf(getProcess_id().substring(0, 4));
        host_id = Integer.valueOf(getProcess_id().substring(4, 6));
        billing_line_id = Integer.valueOf(getProcess_id().substring(6, 8));

        List<BreakPointTable> breakPointTableList = null;
        if (updatePointMode.equals(POINT_VALUE_LOCK.ZK_ZK.mode) ||
                updatePointMode.equals(POINT_VALUE_LOCK.ZK_REDIS.mode)) {
            breakPointTableList = zkClientApi.getTableFromZK("break_point_table", BreakPointTable.class);
        } else if (updatePointMode.equals(POINT_VALUE_LOCK.REDIS_ZK.mode) ||
                updatePointMode.equals(POINT_VALUE_LOCK.REDIS_REDIS.mode)) {
            breakPointTableList = TableService.getInstance(getThreadNum()).getTableFromRedis("break_point_table", "break_point_table", BreakPointTable.class);
        } else {
            log.error("updatePointMode:" + updatePointMode + ",配置不合法");
            return false;
        }

        for (BreakPointTable breakPointTable : breakPointTableList) {
            String tplKey = breakPointTable.getEvent_type_id() + "|" + breakPointTable.getLatn_id() + "|" + breakPointTable.getTicket_type();
            print(tplKey + "," + breakPointTable.toString());
        }
        if (breakPointTableList == null || breakPointTableList.size() == 0) {
            log.error("未配置断点表:break_point_table，启动失败");
            return false;
        }

        log.info("host_id:" + host_id + ",billing_line_id:" + billing_line_id + ",process_id:" + getProcess_id());
        return true;
    }

    /*初始化*/
    @Override
    public boolean OnInit() {
        log.info("OnInit start ,getiMaxProcessCnt():" + getiMaxProcessCnt());
        zkClientApi = CalcRecordManager.getZkClientApi();

        Properties props = zkClientApi.getPropertiesFromZK(propPath);
        String etiFieldNameTmp = props.getProperty("eti_field_name");
        if (etiFieldNameTmp != null && (!etiFieldNameTmp.equals(""))) {
            etiFieldName = etiFieldNameTmp;
        }

        String isDatabaseTmp = props.getProperty("is_database");
        if (isDatabaseTmp != null && (!isDatabaseTmp.equals(""))) {
            if (isDatabaseTmp.equals("true")) {
                isDateBase = true;
            } else if (isDatabaseTmp.equals("false")) {
                isDateBase = false;
            }
        }

        String isHbaseTmp = props.getProperty("is_Hbase");
        if (isHbaseTmp != null && (!isHbaseTmp.equals(""))) {
            if (isHbaseTmp.equals("true")) {
                isHbase = true;
            } else if (isHbaseTmp.equals("false")) {
                isHbase = false;
            }
        }

        String isHdfsTmp = props.getProperty("is_Hdfs");
        if (isHdfsTmp != null && (!isHdfsTmp.equals(""))) {
            if (isHdfsTmp.equals("true")) {
                isHdfs = true;
                String pathTmp = props.getProperty("hdfs_path");
                if (pathTmp != null && (!"".equals(pathTmp))) {
                    hdfsPath = pathTmp;
                }
            } else if (isHdfsTmp.equals("false")) {
                isHdfs = false;
            }
        }

        if (props.getProperty("is_mode") != null) {
            isMode = props.getProperty("is_mode");
        }

        if (null != props.getProperty("update_point_mode")) {
            updatePointMode = props.getProperty("update_point_mode");
        }

        if (null != props.getProperty("is_no_payment")) {
            if ("true".equals(props.getProperty("is_no_payment"))) {
                isNoPayment = true;
            }
        }


        String noPaymentErrorTypeStr = props.getProperty("no_payment_error_type");
        if (noPaymentErrorTypeStr != null && (!noPaymentErrorTypeStr.equals(""))) {
            noPaymentErrorType = noPaymentErrorTypeStr;
        } else {
            noPaymentErrorType = "10046,10048";
        }

        // 初始化数据库
        DbPool.setAutoCommit(false);
        DbPool.setZkClientApi(zkClientApi);
        if (!DbPool.init()) {
            log.error("DbPool.init() faile,数据库连接失败！");
            return false;
        }
        log.info("is_database:" + isDateBase + ",isHbase:" + isHbase + ",isHdfs:" + isHdfs);


       /* if (isHbase) {
            hBaseClientApi = new HBaseClientApi();
            if (!hBaseClientApi.init()) {
                log.error("hBaseClientApi.init() 初始化失败");
                return false;
            }
            log.info("hBaseClientApi.init() 初始化成功");
        }*/

        if (isHdfs) {
            if (isMode.equals("FILE")) {
                transfer = new TransferFile();
            } else if (isMode.equals("HDFS")) {
                transfer = new TransferHdfs();
            } else {
                log.error("无法识别的:" + isMode);
                return false;
            }

            transfer.setZkClientApi(zkClientApi);
            transfer.setCommon(common);
            if (!transfer.init()) {
                log.error("transfer.init(p) 失败");
                return false;
            }
        }

        if (!isbInitFlag()) {
            TableService.setZkClientApi(zkClientApi);
            TableService.setCommon(common);
            if (!TableService.initCfg()) {
                log.error("TableService.initCfgAst() 失败");
                return false;
            }
            log.info("缓存接口初始化成功");

            if (!InitReadIndbTableRule()) {
                log.error("InitReadIndbTableRule 初始化失败");
                return false;
            }
            log.info("加载入库规则成功");
        }

        if (!isDateBase && !isHbase && !isHdfs) {
            log.error("即不入数据库、也不入Hbase、也补入Hdfs，配置错误");
            return false;
        }

        log.info("OnInit close");
        return true;
    }

    @Override
    public boolean OnInitTrial() {
        moduleType = "TC";
        propPath = "tTicketIndb.properties";
        return OnInit();
    }

    @Override
    public boolean OnInitPre() {
        moduleType = "PC";
        propPath = "pTicketIndb.properties";
        return OnInit();
    }


    @Override
    public boolean OnChildInit() {
        dataMap.clear();
        return true;
    }

    @Override
    public void reset() {
        maxBreakPointTableMap.clear();
    }

    /**
     * 从zoookeeeper获取断点值
     *
     * @return
     */
    boolean updatePointByZkLockZookeeper() {
        String lockPath = "/process/Lock/TicketIndb/";

        // 获取锁，加锁
        String lockStr = null;
        try {
            lockStr = zkClientApi.lock(lockPath);

            breakPointTableMap.clear();
            Map<String, BreakPointTable> breakTempMap = new HashMap<>();
            // 获取zookeeper上的最新断点值
            List<BreakPointTable> breakPointTableList = zkClientApi.getTableFromZK("break_point_table", BreakPointTable.class);
            for (BreakPointTable breakPointTable : breakPointTableList) {
                String tplKey = breakPointTable.getEvent_type_id() + "|" + breakPointTable.getLatn_id() + "|" + breakPointTable.getTicket_type();
                breakTempMap.put(tplKey, breakPointTable);
                if (common.getIDebug() > 0) {
                    System.out.println("tplKey:" + tplKey + "->" + breakPointTable.getPoint_value());
                }
            }

            // 话单断点值 to 断点表更新
            for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
                String tplKey = itertor.getKey();
                BreakPointTable tableTmp1 = breakTempMap.get(tplKey);
                if (tableTmp1 == null) {
                    log.error("tplKey:" + tplKey + ",未配置break_point_table断点表");
                    return false;
                }
                tableTmp1.setLast_point_value(tableTmp1.getPoint_value());
                tableTmp1.setPoint_value(tableTmp1.getPoint_value() + itertor.getValue().size());
                tableTmp1.setCreate_date(Factory.getSystemDateStr());

                breakPointTableMap.put(tplKey, tableTmp1);
                maxBreakPointTableMap.put(tplKey, tableTmp1);
            }

            // 断点表 to zookeeper
            List<BreakPointTable> collect = breakTempMap.values().stream().collect(Collectors.toList());
            zkClientApi.setTableToZk("break_point_table", collect);

        } finally {
            //释放锁
            zkClientApi.unLock(lockStr);
        }

        return true;
    }


    /**
     * 从zoookeeeper获取断点值
     *
     * @return
     */
    boolean updatePointByRedisLockZookeeper() {
        boolean bRet = true;
        String lockPath = "/process/Lock/TicketIndb/";
        // 获取锁，加锁
        String lockStr = null;
        try {
            lockStr = zkClientApi.lock(lockPath);

            breakPointTableMap.clear();
            Map<String, BreakPointTable> breakTempMap = new HashMap<>();
            // 获取zookeeper上的最新断点值
            List<BreakPointTable> breakPointTableList = TableService.getInstance(getThreadNum()).getTableFromRedis("break_point_table", "break_point_table", BreakPointTable.class);
            if (breakPointTableList == null) {
                log.error("从ctgCache中读取break_point_table断点表失败，请确认是否已经上载10338表。");
                return false;
            }

            for (BreakPointTable breakPointTable : breakPointTableList) {
                String tplKey = breakPointTable.getEvent_type_id() + "|" + breakPointTable.getLatn_id() + "|" + breakPointTable.getTicket_type();
                breakTempMap.put(tplKey, breakPointTable);
            }

            // 话单断点值 to 断点表更新
            for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
                String tplKey = itertor.getKey();
                BreakPointTable tableTmp1 = breakTempMap.get(tplKey);
                if (tableTmp1 == null) {
                    log.error("tplKey:" + tplKey + ",未配置break_point_table断点表");
                    return false;
                }
                tableTmp1.setLast_point_value(tableTmp1.getPoint_value());
                tableTmp1.setPoint_value(tableTmp1.getPoint_value() + itertor.getValue().size());
                tableTmp1.setCreate_date(Factory.getSystemDateStr());

                breakPointTableMap.put(tplKey, tableTmp1);
                maxBreakPointTableMap.put(tplKey, tableTmp1);
            }

            // 断点表 to zookeeper
            List<BreakPointTable> collect = breakTempMap.values().stream().collect(Collectors.toList());
            bRet = TableService.getInstance(getThreadNum()).setTableToRedis("break_point_table", "break_point_table", collect);

        } finally {
            //释放锁
            if (lockStr != null) {
                zkClientApi.unLock(lockStr);
            }
        }

        return bRet;
    }

    boolean updatePointByZkLockRedis() {
        // 获取锁，加锁
        TableService.getInstance(getThreadNum()).lock("TicketIndb", getProcess_id() + "-" + getThreadNum());

        try {
            breakPointTableMap.clear();
            Map<String, BreakPointTable> breakTempMap = new HashMap<>();
            // 获取zookeeper上的最新断点值
            List<BreakPointTable> breakPointTableList = zkClientApi.getTableFromZK("break_point_table", BreakPointTable.class);
            for (BreakPointTable breakPointTable : breakPointTableList) {
                String tplKey = breakPointTable.getEvent_type_id() + "|" + breakPointTable.getLatn_id() + "|" + breakPointTable.getTicket_type();
                breakTempMap.put(tplKey, breakPointTable);
                //System.out.println("tplKey:" + tplKey);
            }

            // 话单断点值 to 断点表更新
            for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
                String tplKey = itertor.getKey();
                BreakPointTable tableTmp1 = breakTempMap.get(tplKey);
                if (tableTmp1 == null) {
                    log.error("tplKey:" + tplKey + ",未配置break_point_table断点表");
                    return false;
                }
                tableTmp1.setLast_point_value(tableTmp1.getPoint_value());
                tableTmp1.setPoint_value(tableTmp1.getPoint_value() + itertor.getValue().size());
                tableTmp1.setCreate_date(Factory.getSystemDateStr());

                breakPointTableMap.put(tplKey, tableTmp1);
                maxBreakPointTableMap.put(tplKey, tableTmp1);
            }

            // 断点表 to zookeeper
            List<BreakPointTable> collect = breakTempMap.values().stream().collect(Collectors.toList());
            zkClientApi.setTableToZk("break_point_table", collect);

        } finally {
            //释放锁
            TableService.getInstance(getThreadNum()).unLock("TicketIndb", getProcess_id() + "-" + getThreadNum());
        }
        return true;
    }

    boolean updatePointByRedisLockRedis() {
        // 获取锁，加锁
        TableService.getInstance(getThreadNum()).lock("TicketIndb", getProcess_id() + "-" + getThreadNum());

        try {
            breakPointTableMap.clear();
            Map<String, BreakPointTable> breakTempMap = new HashMap<>();
            // 获取zookeeper上的最新断点值
            List<BreakPointTable> breakPointTableList = TableService.getInstance(getThreadNum()).getTableFromRedis("break_point_table", "break_point_table", BreakPointTable.class);
            for (BreakPointTable breakPointTable : breakPointTableList) {
                String tplKey = breakPointTable.getEvent_type_id() + "|" + breakPointTable.getLatn_id() + "|" + breakPointTable.getTicket_type();
                breakTempMap.put(tplKey, breakPointTable);
            }

            // 话单断点值 to 断点表更新
            for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
                String tplKey = itertor.getKey();
                BreakPointTable tableTmp1 = breakTempMap.get(tplKey);
                if (tableTmp1 == null) {
                    log.error("tplKey:" + tplKey + ",未配置break_point_table断点表");
                    return false;
                }
                tableTmp1.setLast_point_value(tableTmp1.getPoint_value());
                tableTmp1.setPoint_value(tableTmp1.getPoint_value() + itertor.getValue().size());
                tableTmp1.setCreate_date(Factory.getSystemDateStr());

                breakPointTableMap.put(tplKey, tableTmp1);
                maxBreakPointTableMap.put(tplKey, tableTmp1);
            }

            // 断点表 to zookeeper
            List<BreakPointTable> collect = breakTempMap.values().stream().collect(Collectors.toList());
            TableService.getInstance(getThreadNum()).setTableToRedis("break_point_table", "break_point_table", collect);
        } finally {
            //释放锁
            TableService.getInstance(getThreadNum()).unLock("TicketIndb", getProcess_id() + "-" + getThreadNum());
        }

        return true;
    }

    boolean Record2Database(Connection conn) throws SQLException {
        for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
            String tplKey = itertor.getKey();
            if (common.getIDebug() == 1) {
                log.info("tplKey:" + tplKey + ",itertor.getValue():" + itertor.getValue().size());
            }

            TplIndbTable tplIndbTable = entityMgr.getTplIndbTableMap().get(itertor.getKey());

            String SourceSql = tableSqlMap.get(String.valueOf(tplIndbTable.getTable_format_id()));
            String sql = SourceSql.replace("XXXXYYYY", tplIndbTable.getTable_name());

            printLog(sql);
            PreparedStatement ps = conn.prepareStatement(sql);
            int jCnt = 0;

            for (List<KeyValue> objectList : itertor.getValue()) {
                int i = 1;
                for (KeyValue keyValue : objectList) {
                    print(keyValue.toString());
                    ps.setObject(i++, keyValue.value);
                }
                ps.addBatch();

                jCnt++;
                if (jCnt > 1000) {
                    ps.executeBatch();
                    conn.commit();
                    jCnt = 0;
                }
                print("");
            }

            if (jCnt > 0) {
                ps.executeBatch();
                conn.commit();
            }
            ps.close();
        }

        return true;
    }

    boolean Record2Hbase() {
        for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
            String tplKey = itertor.getKey();
            log.info("tplKey:" + tplKey + ",itertor.getValue():" + itertor.getValue().size());

            TplIndbTable tplIndbTable = entityMgr.getTplIndbTableMap().get(tplKey);
            List<TplIndbTableFormatItem> itemList = entityMgr.getTableFormatItemByTableFormatId(tplIndbTable.getTable_format_id());

           /* if (!hBaseClientApi.isTableExist(table.getTable_name())) {
                hBaseClientApi.createTable(table.getTable_name(), familyColomn);
            }*/

            for (List<KeyValue> objectList : itertor.getValue()) {
                Map<String, String> rec = new HashMap<>();
                int i = 0;
                String rowKey = new String();
                for (TplIndbTableFormatItem item : itemList) {
                    TprResourceAttr attr = entityMgr.getTprResourceAttrMap().get(item.getIndb_field_id());
                    if (attr.getEn_name().equals("ticket_id")) {
                        rowKey = objectList.get(i).toString();
                    }

                    rec.put(attr.getEn_name().toLowerCase(), objectList.get(i).toString());
                    i++;
                }

                log.info("rowkey:" + rowKey + ",family" + familyColomn);
                log.info("rec:" + rec.toString());
                //hBaseClientApi.putTableData(table.getTable_name(), rowKey, familyColomn, rec);
            }
        }
        return true;
    }

    boolean Record2Hdfs(Map<String, List<List<KeyValue>>> dataOver) {
        String datePrx = Factory.getSystemDateStr("yyyyMMddHHmmss");
        Map<String, List<String>> fileMap = new HashMap<>();
        for (Map.Entry<String, List<List<KeyValue>>> itertor : dataOver.entrySet()) {
            String tplKey = itertor.getKey();
            if (common.getIDebug() == 1) {
                log.info("tplKey:" + tplKey + ",itertor.getValue():" + itertor.getValue().size());
            }

            TplIndbTable tplIndbTable = entityMgr.getTplIndbTableMap().get(itertor.getKey());
            List<String> recordsTmp = new ArrayList<>();

            for (List<KeyValue> objectList : itertor.getValue()) {
                String str = new String();
                for (KeyValue keyValue : objectList) {
                    print(keyValue.toString());
                    if (str.length() == 0) {
                        str = keyValue.value.toString();
                    } else {
                        str += "," + keyValue.value.toString();
                    }
                }
                recordsTmp.add(str);
            }
            /*String tableName = tplIndbTable.getTable_name();
            if (tplIndbTable.getTable_name().indexOf(".") > 0) {
                tableName = tplIndbTable.getTable_name().substring(tplIndbTable.getTable_name().lastIndexOf(".") + 1);
            }*/

			// modify by zhouguizhu 2023年3月28日  原先是用表名来确定文件名，所以一个话单6000会拆成9个文件，影响合账的效率；要求改成按照事件类型来确定文件名，减少文件个数。
            String path = hdfsPath + "/" + tplIndbTable.getEvent_type_id() + "/" + tplIndbTable.getTicket_type();
            String file = tplIndbTable.getEvent_type_id() + "_" + tplIndbTable.getTicket_type() + "_" + getProcess_id() + "_" + datePrx;
            String pf = path + "/" + file;

            List<String> tmpList = fileMap.get(pf);
            if (tmpList == null) {
                tmpList = new ArrayList<>();
                fileMap.put(pf, tmpList);
            }
            tmpList.addAll(recordsTmp);
        }

        for (Map.Entry<String, List<String>> mapTmp : fileMap.entrySet()) {
            String tmpPath = mapTmp.getKey().substring(0, mapTmp.getKey().lastIndexOf("/"));
            if (!transfer.mkdirByNoExist(tmpPath, true)) {
                log.error("transfer.mkdirByNoExist() false");
                return false;
            }
            if (common.getIDebug() == 1) {
                log.info(mapTmp.getKey() + "->" + mapTmp.getValue().size());
            }
            if (mapTmp.getValue().size() > 0) {
                transfer.outputRecord(mapTmp.getKey(), mapTmp.getValue());
            }
        }
        fileMap.clear();
        return true;
    }

    // 每个线程独立
    @Override
    public boolean OnChildOver() {
        log.info("OnChildOver() start");

        boolean bRet = false;
        //获取断点值 ,跟新断点值到 redis
        if (updatePointMode.equals(POINT_VALUE_LOCK.ZK_ZK.mode)) {
            bRet = updatePointByZkLockZookeeper();
        } else if (updatePointMode.equals(POINT_VALUE_LOCK.REDIS_ZK.mode)) {
            bRet = updatePointByRedisLockZookeeper();
        } else if (updatePointMode.equals(POINT_VALUE_LOCK.ZK_REDIS.mode)) {
            bRet = updatePointByZkLockRedis();
        } else if (updatePointMode.equals(POINT_VALUE_LOCK.REDIS_REDIS.mode)) {
            bRet = updatePointByRedisLockRedis();
        } else {
            log.error("updatePointMode:" + updatePointMode + ",配置不合法");
            return false;
        }

        if (!bRet) {
            log.error("updatePointMode:" + updatePointMode + ",执行uploadPoint() 失败");
            return false;
        }

        // ticket_id,更新到每条话单中
        for (Map.Entry<String, List<List<KeyValue>>> itertor : dataMap.entrySet()) {
            String tplKey = itertor.getKey();
            BreakPointTable breakPointTable = breakPointTableMap.get(tplKey);
            Long ticketId = breakPointTable.getLast_point_value();

            if (common.getIDebug() == 1) {
                log.info("tplKey:" + tplKey + ",ticketId:" + ticketId);
            }

            for (List<KeyValue> objectList : itertor.getValue()) {
                for (KeyValue keyValue : objectList) {
                    if ("ticket_id".equals(keyValue.key)) {
                        keyValue.value = ticketId++;
                    }
                }
            }
        }

        if (isDateBase) {
            log.info("OnChildOver() database start");
            Connection connTmp = null;
            try {
                // 话单入库
                connTmp = DbPool.getConn();
                bRet = Record2Database(connTmp);
            } catch (SQLException e) {
                e.printStackTrace();
                setErrorMsg(e.getMessage());

                if (e.getMessage().indexOf("com.mysql.cj.jdbc.exceptions.CommunicationsException") > 0 ||
                        e.getMessage().indexOf("wait_timeout") > 0 ||
                        e.getMessage().equals("Can't call commit when autocommit=true") ||
                        e.getMessage().equals("This connection has been closed.")) {
                    log.info("重新获取数据库连接实例：");
                    try {
                        bRet = Record2Database(connTmp);
                    } catch (SQLException ex) {
                        log.info("OnChildOver 2 exception:" + e.getMessage());
                    }
                } else if (e.getMessage().indexOf("ERROR: current transaction is aborted, commands ignored until end of transaction block") > -1) {
                    log.info("connTmp.rollback() 694");
                    try {
                        connTmp.rollback();
                    } catch (SQLException ex) {
                        ex.printStackTrace();
                    }
                } else {
                    log.info("connTmp.rollback() 701");
                    try {
                        connTmp.rollback();
                    } catch (SQLException ex) {
                        ex.printStackTrace();
                    }
                }
                return false;
            } finally {
                DbPool.close(connTmp);
            }
            log.info("OnChildOver() database close");
        }

        if (isHbase) {
            log.info("OnChildOver() Record2Hbase start");
            // 话单入hbase
            bRet = Record2Hbase();
            log.info("OnChildOver() Record2Hbase close");
        }

        log.info("OnChildOver() close");
        return bRet;
    }

    boolean UpdateBreakPointTable(Connection conn) throws SQLException {
        String update_sql = "update break_point_table set point_value=?,create_date=? where point_id=?";
        for (Map.Entry<String, BreakPointTable> itertor : maxBreakPointTableMap.entrySet()) {

            itertor.getValue().setCreate_date(Factory.getSystemDateStr());
            boolean bRet = DBUtils.exeUpdateThrowException(conn, update_sql, itertor.getValue().getPoint_value(),
                    itertor.getValue().getCreate_date(),
                    itertor.getValue().getPoint_id());
            if (bRet) {
                conn.commit();
            } else {
                return false;
            }
        }
        return true;
    }

    // 所有的线程执行完成后
    @Override
    public boolean OnOver(Map<String, List<List<KeyValue>>> dataOver) {
        // 更新断点值
        boolean bRet = false;

        // 写文件到hdfs
        if (isHdfs) {
            log.info("OnOver() Record2Hdfs(dataOver) start：dataOver.size():" + dataOver.size());
            bRet = Record2Hdfs(dataOver);
            log.info("OnOver() Record2Hdfs(dataOver) close");
            if (!bRet) {
                log.info("OnOver() Record2Hdfs(dataOver) false");
                return false;
            }
        }

        Connection connTmp = null;
        try {
            connTmp = DbPool.getConn();
            return UpdateBreakPointTable(connTmp);
        } catch (SQLException e) {
            log.info("OnOver 1 exception:" + e.getMessage());
            if (e.getMessage().indexOf("wait_timeout") > 0 ||
                    e.getMessage().equals("Can't call commit when autocommit=true") ||
                    e.getMessage().equals("This connection has been closed.")) {
                log.info("重新获取数据库连接实例：");
                try {
                    return UpdateBreakPointTable(connTmp);
                } catch (SQLException ex) {
                    log.info("OnOver 2 exception:" + e.getMessage());
                    return false;
                }
            } else if (e.getMessage().indexOf("ERROR: current transaction is aborted, commands ignored until end of transaction block") > -1) {
                log.info("DbPool.getConn(getThreadNum()).rollback()");
                try {
                    connTmp.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                    return false;
                }
            }
        } finally {
            DbPool.close(connTmp);
        }
        return bRet;
    }


    /*     入参：
     inRecord：输入话单
     outRecord：输出话单
     msg：处理异常时，填写异常信息
     返回值，是否处理成功*/
    public boolean OnTask(String inRecord) {
        //log.info("OnTask inRecord :" + inRecord);
        Integer rEventTypeId = null;
        Integer rTicketType = null;
        Integer rLatnId = null;

        try {
            JSONObject object = JSON.parseObject(inRecord);
            if (!object.containsKey(etiFieldName)) {
                printError("话单字段不存在：" + etiFieldName);
                setOutErrorRecord(inRecord);
                return false;
            }
            if (!object.containsKey("ticket_type")) {
                printError("话单字段不存在：ticket_type");
                setOutErrorRecord(inRecord);
                return false;
            }
            if (!object.containsKey("latn_id")) {
                printError("话单字段不存在：latn_id");
                setOutErrorRecord(inRecord);
                return false;
            }

            rEventTypeId = object.getInteger(etiFieldName);
            rTicketType = object.getInteger("ticket_type");
            rLatnId = object.getInteger("latn_id");

            if (rEventTypeId == null || rTicketType == null || rLatnId == null) {
                printError("话单值异常：event_type_id:" + rEventTypeId + " ,ticket_type:" + rTicketType + " ,latn_id:" + rLatnId + "的值为空。");
                setOutErrorRecord(inRecord);
                return false;
            }

            if (rTicketType == 0) {
                m_rRecordSize = ((JSONArray) object.get(m_rRecordName)).size();
                if (m_rRecordSize == 0) {
                    m_rRecordSize = 1;
                }
            } else if (rTicketType == 1) {
                m_rRecordSize = 1;

            } else if (rTicketType == 2) {
                m_rRecordSize = 1;
            }

            // 不支付原因填写
            if (isNoPayment) {
                if (rTicketType == 0) {
                    for (int i = 0; i < m_rRecordSize; i++) {
                        // 处理ticket_type 0 3
                        if (((JSONArray) object.get("a_if_deduct_old_owe")).getInteger(i) == 0) {
                            //正常单  0
                            SetNoPaymentReason(object, 0);

                        } else if (((JSONArray) object.get("a_if_deduct_old_owe")).getInteger(i) == 1) {
                            //预计单
                            if (object.get("re_settle_flag") == "1") {
                                // 补充不支付原因字段 no_payment   1
                                SetNoPaymentReason(object, 1);
                            } else {
                                //预结单  3
                                SetNoPaymentReason(object, 3);
                            }
                        }
                    }
                } else if (rTicketType == 1) {
                    // 补充不支付原因字段 no_payment   1
                    SetNoPaymentReason(object, 1);
                } else if (rTicketType == 2) {
                    // 补充不支付原因字段 no_payment   2
                    SetNoPaymentReason(object, 2);
                }
            }

            for (int i = 0; i < m_rRecordSize; i++) {
                // new list存放一条话单的空间。
                List<KeyValue> listAddRecord = new ArrayList<>();
                int ifDeduct = 0;
                if (rTicketType == 0) {
                    if (object.containsKey("a_if_deduct_old_owe")) {
                        if (((JSONArray) object.get("a_if_deduct_old_owe")).size() >= (i + 1)) {
                            ifDeduct = ((JSONArray) object.get("a_if_deduct_old_owe")).getInteger(i);
                        }
                    }
                }

                TplIndbTable tplIndbTable = entityMgr.getTableFormatItemByEventTypeid(rEventTypeId, rLatnId, rTicketType, ifDeduct);
                if (tplIndbTable == null) {
                    printError("没有找到入库格式(tpl_indb_table)" + etiFieldName + ":" + rEventTypeId + ",ticket_type:" + rTicketType + ",if_deduct:" + ifDeduct);
                    setErrorMsg("没有找到入库格式(tpl_indb_table)" + etiFieldName + ":" + rEventTypeId + ",ticket_type:" + rTicketType + ",if_deduct:" + ifDeduct);
                    setOutErrorRecord(inRecord);
                    return false;
                }

                String tplKey = tplIndbTable.getEvent_type_id() + "|" + tplIndbTable.getLatn_id() + "|" + tplIndbTable.getTicket_type();
                printLog("入库格式：" + tplIndbTable.toString());
                // 找到存放话单的地方
                List<List<KeyValue>> dataListList = dataMap.get(tplKey);
                if (dataListList == null) {
                    dataListList = new ArrayList<>();
                    dataMap.put(tplKey, dataListList);
                }
                // 查找话单格式字段集
                List<TplIndbTableFormatItem> itemList = entityMgr.getTableFormatItemByTableFormatId(tplIndbTable.getTable_format_id());
                Map<Integer, TprResourceAttr> attrMap = entityMgr.getTprResourceAttrMap();

                if (tplIndbTable.getTicket_type() == 0) {
                    if (common.getSSystem().equals("TPSS")) {
                        ((JSONArray) object.get("a_ticket_id")).set(i, 1);//佣金非统一版本
                    } else {
                        object.put("ticket_id", 1);
                    }
                } else if (tplIndbTable.getTicket_type() == 1) {
                    object.put("ticket_id", 1);
                } else if (tplIndbTable.getTicket_type() == 2) {
                    object.put("ticket_id", 1);
                } else if (tplIndbTable.getTicket_type() == 3) {
                    if (common.getSSystem().equals("TPSS")) {
                        ((JSONArray) object.get("a_ticket_id")).set(i, 1);//佣金非统一版本
                    } else {
                        object.put("ticket_id", 1);
                    }
                }

                for (int j = 0; j < itemList.size(); j++) {
                    TprResourceAttr sourceAttr = attrMap.get(itemList.get(j).getSource_attr_id());
                    TprResourceAttr indbAttr = attrMap.get(itemList.get(j).getIndb_field_id());
                    if (!object.containsKey(sourceAttr.getEn_name())) {
                        printError("配置的入库字段在话单内不存在，source:" + sourceAttr.getEn_name() + ",indb:" + indbAttr.getEn_name());
                        setErrorMsg("配置的入库字段在话单内不存在，source:" + sourceAttr.getEn_name() + ",indb:" + indbAttr.getEn_name());
                        setOutErrorRecord(inRecord);
                        return false;
                    }
                    print(i + "->" + j + ",sourceAttr:" + sourceAttr.toString() + ",indbAttr" + indbAttr.toString());

                    KeyValue keyValue = new KeyValue();
                    keyValue.key = indbAttr.getEn_name();
                    if (sourceAttr.getData_type().equals(CommonEnum.DataType.TA.getName())) {
                        if (object.getString(sourceAttr.getEn_name()) == null || "".equals(object.getString(sourceAttr.getEn_name()))) {
                            keyValue.value = null;
                        } else {
                            keyValue.value = Integer.valueOf(object.getString(sourceAttr.getEn_name()));
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TB.getName())) {
                        if (object.getString(sourceAttr.getEn_name()) == null || "".equals(object.getString(sourceAttr.getEn_name()))) {
                            keyValue.value = null;
                        } else {
                            keyValue.value = Long.valueOf(object.getString(sourceAttr.getEn_name()));
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TC.getName())) {
                        if (object.getString(sourceAttr.getEn_name()) == null || "".equals(object.getString(sourceAttr.getEn_name()))) {
                            keyValue.value = null;
                        } else {
                            keyValue.value = Double.valueOf(object.getString(sourceAttr.getEn_name()));
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TD.getName())) {
                        keyValue.value = object.getString(sourceAttr.getEn_name());
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TE.getName())) {
                        if (object.get(sourceAttr.getEn_name()) != null && !object.get(sourceAttr.getEn_name()).equals("")) {
                            SimpleDateFormat df = null;
                            java.util.Date d = null;
                            if (object.getString(sourceAttr.getEn_name()).length() == 14) {
                                df = new SimpleDateFormat("yyyyMMddHHmmss");
                            } else if (object.getString(sourceAttr.getEn_name()).length() == 19) {
                                df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            } else if (object.getString(sourceAttr.getEn_name()).length() == 8) {
                                df = new SimpleDateFormat("yyyyMMdd");
                            } else if (object.getString(sourceAttr.getEn_name()).length() == 10) {
                                df = new SimpleDateFormat("yyyy-MM-dd");
                            } else {
                                setErrorMsg("日期格式值异常:" + sourceAttr.getEn_name() + "->" + object.getString(sourceAttr.getEn_name()));
                                printError("日期格式值异常:" + sourceAttr.getEn_name() + "->" + object.getString(sourceAttr.getEn_name()));
                            }

                            try {
                                d = df.parse(object.getString(sourceAttr.getEn_name()));
                                java.sql.Timestamp date = new java.sql.Timestamp(d.getTime());
                                keyValue.value = date;
                            } catch (Exception e) {
                                e.printStackTrace();
                                keyValue.value = null;
                            }
                        } else {
                            keyValue.value = null;
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TF.name)) {
                        if (((JSONArray) object.get(sourceAttr.getEn_name())).size() == 0) {
                            keyValue.value = 0;
                        } else {
                            String sTmp = ((JSONArray) object.get(sourceAttr.getEn_name())).getString(i);
                            keyValue.value = Integer.valueOf(sTmp);
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TG.name)) {
                        if (((JSONArray) object.get(sourceAttr.getEn_name())).size() == 0) {
                            keyValue.value = 0;
                        } else {
                            String sTmp = ((JSONArray) object.get(sourceAttr.getEn_name())).getString(i);
                            keyValue.value = Long.valueOf(sTmp);
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TH.name)) {
                        if (((JSONArray) object.get(sourceAttr.getEn_name())).size() == 0) {
                            keyValue.value = 0;
                        } else {
                            String sTmp = ((JSONArray) object.get(sourceAttr.getEn_name())).getString(i);
                            keyValue.value = Double.valueOf(sTmp);
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TI.name)) {
                        if (((JSONArray) object.get(sourceAttr.getEn_name())).size() == 0) {
                            keyValue.value = "";
                        } else {
                            String sTmp = ((JSONArray) object.get(sourceAttr.getEn_name())).getString(i);
                            keyValue.value = sTmp;
                        }
                    } else if (sourceAttr.getData_type().equals(CommonEnum.DataType.TJ.name)) {
                        if (((JSONArray) object.get(sourceAttr.getEn_name())).size() == 0) {
                            keyValue.value = "";
                        } else {
                            String sTmp = ((JSONArray) object.get(sourceAttr.getEn_name())).getString(i);
                            keyValue.value = sTmp;

                            if (((JSONArray) object.get(sourceAttr.getEn_name())).get(i) != null) {
                                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                java.util.Date d = null;
                                try {
                                    d = df.parse(((JSONArray) object.get(sourceAttr.getEn_name())).getString(i));
                                    java.sql.Date date = new java.sql.Date(d.getTime());
                                    keyValue.value = date;
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    keyValue.value = null;
                                }
                            } else {
                                keyValue.value = null;
                            }
                        }
                    } else {
                        printError("无法识别的转换规则，" + sourceAttr.toString());
                        setErrorMsg("无法识别的转换规则，" + sourceAttr.toString());
                    }

                    listAddRecord.add(keyValue);
                }
                dataListList.add(listAddRecord);
            }
            setOutRecord(JSONObject.toJSONString(object, SerializerFeature.WriteMapNullValue));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("On Task() exception:" + e.toString());
            setErrorMsg("On Task() exception:" + e.toString());
            setOutErrorRecord(inRecord);
            return false;
        }

        return true;
    }


    @Override
    public Map<Integer, FunctionPerl> getFunctionPerl() {
        return null;
    }

    @Override
    public boolean OnExit() {
        if (isHbase) {
            //hBaseClientApi.closeConn();
        }

        return true;
    }


    void SetNoPaymentReason(JSONObject record, Integer ticketType) {
        Integer errorType = record.getInteger("error_type");
        String elementStr = record.getString("element_str");

        StringBuilder noPayment = new StringBuilder();
        List<String> refValueStrList;
        String keyPre = ticketType + "#";
        // 正常单、预结单、指定error_type：通过话单类型 + 参考值串匹配不支付原因
        if (ticketType == 0 || ticketType == 3 || getErrorTypeFlag(errorType)) {
            if (!elementStr.contains("#")) {
                record.put("no_payment", noPayment.toString());
                return;
            }
            String[] str = elementStr.split("#");
            refValueStrList = Arrays.stream(str, 1, str.length)
                    // 截取出要素串#后的子串 并去除 %或& 后的内容
                    .map(s1 -> s1.replaceFirst("([&%]).*", ""))
                    .filter(s2 -> (ticketType == 0 || ticketType == 3) ? !s2.endsWith("false") : !s2.endsWith("true"))
                    .collect(Collectors.toList());

            for (String reValueStr : refValueStrList) {
                TpNoPayment tpNoPayment = tpNoPaymentElementMap.get(keyPre + reValueStr);
                if (tpNoPayment == null) {
                    // 如果找不到，则需通过通配符%再找一次
                    reValueStr = reValueStr.replaceFirst("(?<=,)[^,]+(?=,)", "%");
                    tpNoPayment = tpNoPaymentElementMap.get(keyPre + reValueStr);
                }
                if (tpNoPayment != null) {
                    noPayment.append(tpNoPayment.getNo_payment()).append(";");
                }
            }
        }
        // 根据异常类型查询不结算原因
        else {
            Long mapKey = errorType.longValue();
            TpNoPayment tpNoPayment = tpNoPaymentErrorTypeMap.get(mapKey);
            if (tpNoPayment != null) {
                noPayment.append(tpNoPayment.getNo_payment());
            }
        }
        record.put("no_payment", noPayment.toString());
    }

    /**
     * 查找话单中的errorType是否存在于配置文件中的errorType
     *
     * @param errorType
     * @return
     */
    private boolean getErrorTypeFlag(Integer errorType) {
        if (errorType == null) {
            return false;
        }
        return Arrays.stream(noPaymentErrorType.split(","))
                .map(String::trim)
                .mapToInt(Integer::parseInt)
                .anyMatch(type -> type == errorType);

    }

    public enum ATTR_TYPE {
        A(1, "1", "整数"),
        B(2, "2", "浮点数"),
        C(3, "3", "长整型"),
        D(4, "4", "字符串"),
        E(5, "5", "布尔型"),
        F(6, "6", "日期型"),
        TB(7, "7", "字符串数组"),
        TA(8, "8", "整型数组"),
        TC(9, "9", "长整型数组");

        int No;
        String type;
        String remark;

        ATTR_TYPE(int no, String type, String remark) {
            No = no;
            this.type = type;
            this.remark = remark;
        }
    }

    public enum POINT_VALUE_LOCK {
        ZK_ZK(1, "point_zk_lock_zk", "断点表zk，锁zk"),
        REDIS_ZK(2, "point_redis_lock_zk", "断点表redis，锁zk"),
        ZK_REDIS(3, "point_zk_lock_redis", "断点表zk，锁redis"),
        REDIS_REDIS(4, "point_redis_lock_redis", "断点表redis，锁redis");

        int No;
        String mode;
        String remark;

        POINT_VALUE_LOCK(int no, String mode, String remark) {
            this.No = no;
            this.mode = mode;
            this.remark = remark;
        }
    }
}
