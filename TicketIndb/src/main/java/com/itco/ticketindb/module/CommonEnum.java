package com.itco.ticketindb.module;

public class CommonEnum {
    public enum DataType{
        TA(1,"TA"),
        TB(2,"TB"),
        TC(3,"TC"),
        TD(4,"TD"),
        TE(5,"TE"),
        TF(6,"TF"),
        TG(7,"TG"),
        TH(8,"TH"),
        TI(9,"TI"),
        TJ(10,"TJ");
        int id;
        String name;

        DataType(int id, String name) {
            this.id = id;
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

}
