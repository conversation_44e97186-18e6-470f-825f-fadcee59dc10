/**
 * Copyright (c) 1987-2010 Fujian Fujitsu Communication Software Co.,
 * Ltd. All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of
 * Fujian Fujitsu Communication Software Co., Ltd.
 * ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with
 * the terms of the license agreement you entered into with FFCS.
 * <p>
 * <PERSON><PERSON> MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF
 * THE SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
 * TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. FFCS SHALL NOT BE LIABLE FOR
 * ANY DAMAGES SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR
 * DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.itco.ticketindb.hdfs;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.hdfs.DistributedFileSystem;
import org.apache.hadoop.hdfs.protocol.DatanodeInfo;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;


/***
 *
 *@Author: <EMAIL>
 *@CreateTime: 2019-04-01 16:55
 *@Description: hadoop文件系统工具类
 *
 **/
public class HDFSUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HDFSUtil.class);
    private static final String LINE_END = "\r\n";
    private static final int READ_BYTE_LEN = 4096;
    protected static Configuration conf = null;
    protected static FileSystem hdfs;
    private Comparator<? super LocatedFileStatus> fileCompare;
    private boolean isFileOrderByTime;
    private static final String DIR_ITEM_LIMIT = ".*The directory item limit of .* is exceeded.*";
    private static final Pattern itemPattern = Pattern.compile(DIR_ITEM_LIMIT);
    private static final int HDFS_ERR_CODE = 813207001;
    private static final int REANME_ERR_CODE = 813207002;
    private static final int ITEM_LIMIT_ERROR_CODE = 813204003;
    //public static List<HdfsFilesInfo> paths=new ArrayList<>();
    static final ConcurrentHashMap<String, FileSystem> OTHER_FS_MAP = new ConcurrentHashMap<String, FileSystem>();
    //private boolean isReloadHdfs = false;//是否需要重新刷新hdfs（kerberos刷新后要重新取）
    private static long kerberosTime;//kerberos票据每次刷新的时间
    private static int checkDate = 60;//该参数是kerberos防止认证过期,多久时间重新认证一次,单位分钟
    private static boolean isKerberos = false;

    static String confDir = "/config";

    public FileSystem getHdfs() {
        if (isKerberos) {
            if (System.currentTimeMillis() - kerberosTime >= 1000 * 60 * Integer.valueOf(checkDate)) {
                synchronized (HDFSUtil.class) {
                    if (System.currentTimeMillis() - kerberosTime > 1000 * 60 * Integer.valueOf(checkDate)) {//再判断一次防止刚刷过又刷
                        try {
                            LOGGER.info("重新刷新kerberos票据");
                            kerberosTime = System.currentTimeMillis();
                            UserGroupInformation.getLoginUser().checkTGTAndReloginFromKeytab();
                            LOGGER.info("重新刷新kerberos票据成功");
                        } catch (IOException e) {
                            LOGGER.info("重新刷新kerberos票据失败,{}", e.getMessage());
                            e.printStackTrace();
                        }
                        //isReloadHdfs = true;
                    }
                }
            }
        }
        return hdfs;
    }

    public HDFSUtil() {
        new HDFSUtil("");
    }

    public HDFSUtil(String path) {
        if (!"".equals(path)) {
            confDir = path;
        }
        try {
            if (conf == null) {
                initConf();
            }
            if ("ctgdfs".equals(conf.get("dfs.type"))) {//如果是集团小文件系统要增加配置
                Path newFile = new Path(conf.get("fs.defaultPath"));
                hdfs = newFile.getFileSystem(conf);
            } else {//原生hdfs
                String user = conf.get("dfs.user");
                if (user != null && !"".equals(user)) {
                    //hdfs = FileSystem.get(FileSystem.getDefaultUri(conf),conf,user);
                    System.setProperty("HADOOP_USER_NAME", conf.get("dfs.user"));
                }
                hdfs = FileSystem.get(conf);
            }
            LOGGER.debug("获取hadoop连接对象成功");
        } catch (IOException e) {
            e.printStackTrace();
            LOGGER.error("HDFS读取配置文件获取连接操作异常！,{}", e.getMessage());
            //throw new BaseException("HDFS读取配置文件获取连接操作异常！");
        }
        fileCompare = new Comparator<LocatedFileStatus>() {
            @Override
            public int compare(LocatedFileStatus o1, LocatedFileStatus o2) {
                int rs = 0;
                long l1 = o1.getModificationTime();
                long l2 = o2.getModificationTime();
                if (l1 > l2) {
                    rs = 1;
                } else if (l1 < l2) {
                    rs = -1;
                }
                return rs;
            }
        };
    }

    /**
     * 初始化HDFS的文件系统FileSystem
     */
    public synchronized static void initConf() {
        if (conf != null) {
            return;
        }
        conf = new Configuration();

        String[] resouces = {"hadoop-default.xml", "hadoop-site.xml", "core-default.xml", "core-site.xml", "hdfs-default.xml", "hdfs-site.xml"};
        if (!StringUtils.isEmpty(confDir)) {//从指定的配置文件目录读取
            StringBuilder msg = new StringBuilder();
            msg.append("配置文件目录:").append(confDir).append("\n");
            for (String resouce : resouces) {
                java.nio.file.Path path = Paths.get(confDir, resouce);
                if (Files.exists(path)) {
                    conf.addResource(new Path(path.toString()));
                    msg.append("加载配置文件：").append(resouce).append(" [加载成功]\n");
                } else {
                    msg.append("加载配置文件：").append(resouce).append(" [加载失败：文件不存在]\n");
                }
            }

            if ("ctgdfs".equals(conf.get("dfs.type"))) {//如果是集团小文件系统要增加配置
                //String[] resouces_dfs = {"dfs-site.xml", "ctdfs-site.xml", "ctdfs-base-site.xml", "ctdfs-metrics-site.xml", "ctdfs-rest-site.xml", "ctdfs-ni-site.xml","ftp-conf"+File.separator+"dfs-over-ftp.properties","ftp-conf"+File.separator+"users.properties"};
                String[] resouces_dfs = {"dfs-site.xml", "ctdfs-site.xml", "ctdfs-base-site.xml", "ctdfs-metrics-site.xml", "ctdfs-rest-site.xml", "ctdfs-ni-site.xml", "ctdfs-hbase-site.xml", "ctdfs-hdp-site.xml", "dfs-hbase-site.xml", "hbase-site.xml", "mapred-site.xml", "ctdfs-external.xml"};
                if (confDir.endsWith("/") || confDir.endsWith("\\")) {
                    confDir = confDir.substring(0, confDir.length() - 1);
                }
                String conf_ctgdfs = confDir + "/ctgdfs";
                for (String resouce : resouces_dfs) {
                    java.nio.file.Path path = Paths.get(conf_ctgdfs, resouce);
                    if (Files.exists(path)) {
                        conf.addResource(new Path(path.toString()));
                        msg.append("加载配置文件：").append(resouce).append(" [加载成功]\n");
                    } else {
                        msg.append("加载配置文件：").append(resouce).append(" [加载失败：文件不存在]\n");
                    }
                }
            }
            LOGGER.info(msg.toString());
        } else {
            LOGGER.error("请配置启动参数conf.path");
        }

        //Kerberos认证
        if (Boolean.valueOf(conf.get("hadoop.security.authorization")) && "kerberos".equals(conf.get("hadoop.security.authentication").toLowerCase())) {
            validKerberos();
        }
    }

    private static void validKerberos() {
        LOGGER.info("开始kerberos认证");
        isKerberos = true;
        checkDate = StringUtils.isNotBlank(conf.get("kerberos.checkDate")) ? Integer.parseInt(conf.get("kerberos.checkDate")) : 60;
        kerberosTime = System.currentTimeMillis();
        System.setProperty("java.security.krb5.conf", conf.get("java.security.krb5.conf"));
        System.setProperty("sun.security.krb5.debug", conf.get("sun.security.krb5.debug"));
        /*System.setProperty("java.security.krb5.realm",conf.get("java.security.krb5.realm"));
        System.setProperty("java.security.krb5.kdc", conf.get("java.security.krb5.kdc"));*/
        UserGroupInformation.setConfiguration(conf);
        try {
            UserGroupInformation.loginUserFromKeytab(conf.get("custom.kerberos.user"), conf.get("custom.kerberos.keytab"));
            LOGGER.info("kerberos认证成功");
        } catch (IOException e) {
            e.printStackTrace();
            LOGGER.error("hadoop kerberos Auth fail! ", e.getMessage());
        }
    }

    public InputStream openHdfsFile(String url) throws IOException {
        return getHdfs().open(new Path(url));
    }

    /**
     * 获取HDFS集群上所有节点名称信息
     */
    public void getDateNodeHost() {
        DistributedFileSystem fs = (DistributedFileSystem) hdfs;
        DatanodeInfo[] dataNodeStats;
        try {
            dataNodeStats = fs.getDataNodeStats();
            for (int i = 0; i < dataNodeStats.length; i++) {
                System.out.println("DataNode_" + i + "_Name:" + dataNodeStats[i].getHostName());
            }
        } catch (IOException e) {
            LOGGER.error("HDFS获取集群节点信息失败！", e);
            //throw new BaseException("HDFS获取集群节点信息失败！");
        }
    }

    /**
     * 将本地文件上传到HDFS服务器上
     *
     * @param source 源文件路径
     * @param dest   目标文件路径
     */
    public boolean uploadLocalFile2HDFS(String source, String dest) {
        boolean flag = false;
        Path src = new Path(source);
        try {
            Path dst = new Path(dest);
            if (getHdfs().exists(dst)) { // 如果目标文件已存在，则先删除再上传
                LOGGER.debug("目标路径下已存在同名文件", dest, ",准备执行删除操作.");
                deleteFileOrDir(dest);
            }
            getHdfs().copyFromLocalFile(src, dst);

            // 设置上传者本身有删除权限
            getHdfs().setPermission(dst, FsPermission.valueOf("-rwxrwxr-x"));
            flag = true;
        } catch (Exception e) {
            if (itemPattern.matcher(e.getMessage()).matches()) {
                LOGGER.error("所在目录的文件数超过上限.", e);
                //throw new BaseException(dest, "所在目录的文件数超过上限.");
            }
            LOGGER.error("HDFS上传文件失败.", e);
            //throw new BaseException("HDFS上传文件失败.");
        }
        return flag;
    }

    /**
     * @param strPath
     * @return 获取hadoop文件的长度  单位B
     * @throws IOException
     * <AUTHOR>
     * @CreateTime: 2019年5月30日
     * @Description:
     */
    public long getFileLength(String strPath) throws IOException {
        Path filePath = new Path(strPath);
        long length = getHdfs().getContentSummary(filePath).getLength();
        return length;
    }

    /**
     * 创建HDFS文件
     *
     * @param path 文件路径
     * @param bos  输出流，由外面传入，写入到文件中
     */
    public void createNewFile(String path, ByteArrayOutputStream bos) {
        FSDataOutputStream os = null;
        try {
            os = getHdfs().create(new Path(path));
            os.write(bos.toByteArray());
        } catch (IOException e) {
            if (itemPattern.matcher(e.getMessage()).matches()) {
                LOGGER.error("所在目录{}的文件数超过上限.", path, e);
                //throw new BaseException(path, "所在目录的文件数超过上限.");
            }
            LOGGER.error("HDFS创建文件失败！", e);
            //throw new BaseException("HDFS创建文件失败！");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    LOGGER.error("流关闭失败！", e);
                    //throw new BaseException("流关闭失败！");
                }
            }
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    LOGGER.error("流关闭失败！", e);
                    //throw new BaseException("流关闭失败！");
                }
            }
        }
    }

    /**
     * @param newPath
     * @param fileBeanList
     * @return
     * <AUTHOR>
     * @CreateTime: 2019年5月14日
     * @Description: 将 List<HdfsFilesInfo>中的List<String>写入到hadoop文件中
     */
    public boolean writeFileToHadoop(Path newPath, List<HdfsFilesInfo> fileBeanList) {
        boolean result = true;
        FSDataOutputStream fsOut = null;
        try {
            //fsOut = DTSplittingInit.fs.create(newPath);
            fsOut = getHdfs().create(newPath);
            for (int i = 0; i < fileBeanList.size(); i++) {
                List<String> lineVaules = fileBeanList.get(i).getLineVaule();
                for (int j = 0; j < lineVaules.size(); j++) {
                    //fsOut.write(lineVaules.get(j).getBytes("UTF-8"));
                    fsOut.writeBytes(lineVaules.get(j));
                    fsOut.writeBytes("\n");
                    //log.warn("{}{}{}","尾号"+key,Thread.currentThread().getName(),lineVaules.get(j));
                }
            }
        } catch (IOException e) {
            LOGGER.error("HDFS创建文件失败！", e);
            result = false;
            return result;
        } finally {
            if (fsOut != null) {
                try {
                    fsOut.flush();
                    fsOut.close();
                } catch (IOException e) {
                    LOGGER.error("流关闭失败！", e);
                    result = false;
                    return result;
                }
            }
        }
        return result;
    }

    /**
     * 修改HDFS文件内容(较少用)
     *
     * @param path       源文件
     * @param appendPath 包含追加内容的文件
     */
    public void modifyFile(String path, String appendPath) {
        try (
                FileSystem fs = FileSystem.get(URI.create(path), conf);
                InputStream in = new BufferedInputStream(new FileInputStream(appendPath));
                OutputStream out = fs.append(new Path(path));
        ) {
            // 追加流
            org.apache.hadoop.io.IOUtils.copyBytes(in, out, 4096, true);
        } catch (FileNotFoundException e) {
            LOGGER.error("文件'" + appendPath + "'不存在！", e);
            //throw new BaseException("文件'" + appendPath + "'不存在！");
        } catch (IOException e) {
            LOGGER.error("修改文件失败！", e);
            //throw new BaseException("修改文件失败！");
        }

    }

    /**
     * 读取指定长度的HDFS文件内容
     *
     * @param dir      读取的文件路径
     * @param position 读取的起始下标
     * @param length   读取的内容长度
     */
    public byte[] readHDFSFile(String dir, long position, int length) {
        if (length > 1024) {
            return null;
        }
        byte[] buffer = null;
        Path path = new Path(dir);
        try {
            if (getHdfs().exists(path)) {
                FSDataInputStream is = getHdfs().open(path);
                buffer = new byte[length];
                is.readFully(position, buffer);
                is.close();
            }
        } catch (IOException e) {
            LOGGER.error("读取文件失败！", e);
            //throw new BaseException("读取文件失败！");
        }
        return buffer;
    }

    /**
     * @param dir
     * @return 获取hdfs输入流 读取文件
     * @throws IOException
     * <AUTHOR>
     * @CreateTime: 2019年5月30日
     * @Description:
     */
    public FSDataInputStream getHDFSFileInputStream(String dir) throws IOException {
        Path path = new Path(dir);
        return getHdfs().open(path);
    }

    /**
     * 新建目录
     *
     * @param dir 目录名
     */
    public boolean makeDir(String dir) {
        boolean flag = false;
        try {
            Path path = new Path(dir);
            if (getHdfs().exists(path)) {
                flag = true; // 如果路径存在，直接返回true
            } else {
                flag = getHdfs().mkdirs(new Path(dir));
                // hdfs.setOwner(path, username, groupname);
                // hdfs.setPermission(path, FsPermission.valueOf("777"));
            }
        } catch (IOException e) {
            LOGGER.error("创建目录失败！,{}", dir, e);
            //throw new BaseException("创建目录失败！");
        }
        return flag;
    }

    /**
     * 文件重命名
     *
     * @param orgDir 源文件名
     * @param newDir 新文件名
     * @return true:修改成功 false:修改失败
     * 若新文件名的文件路径不存在，则重命名失败;若在目标文件夹下存在同名文件，则将其文件名加上时间戳
     */
    public boolean renameFileOrDir(String orgDir, String newDir) {
        boolean flag = false;
        try {
            Path dst = new Path(newDir);
            if (this.existFile(orgDir)) { // 源文件存在
                if (this.existFile(newDir)) {
                    LOGGER.error("HDFS目标文件：" + newDir + "已存在!");
                    //throw new BaseException("HDFS目标文件：" + newDir + "已存在!");
                } else {
                    flag = getHdfs().rename(new Path(orgDir), dst);
                    if (!flag) {
                        LOGGER.error("HDFS文件" + orgDir + "重命名失败,目标文件:" + newDir);
                        //throw new BaseException("HDFS文件" + orgDir + "重命名失败!");
                    }
                }
            } else { // 源文件不存在
                if (this.existFile(newDir)) {
                    flag = true;
                } else {
                    LOGGER.error("HDFS源文件：" + orgDir + "不存在!");
                    //throw new BaseException("HDFS源文件：" + orgDir + "不存在!!");
                }
            }
        } catch (IOException e) {
            if (itemPattern.matcher(e.getMessage()).matches()) {
                LOGGER.error(newDir + "所在目录的文件数超过上限.", e);
                //throw new BaseException(newDir, "所在目录的文件数超过上限.");
            }
            LOGGER.error("HDFS文件重命名失败!", e);
            //throw new BaseException("HDFS文件重命名失败!");
        }
        return flag;
    }

    /**
     * @param path
     * @param copyOriginalFile
     * @return 将原始文件移动到另一个目录下
     */
    public boolean mvfileToAnother(String path, String name, String copyOriginalFile) {
        boolean result = true;
        String stringNewPath = "";
        try {
            if (path.endsWith("/") || path.endsWith("\\")) {
                path = path.substring(0, path.length() - 1);
            }
            if (copyOriginalFile.endsWith("/") || copyOriginalFile.endsWith("\\")) {
                copyOriginalFile = copyOriginalFile.substring(0, copyOriginalFile.length() - 1);
            }
            //stringNewPath = copyOriginalFile + path;
            stringNewPath = copyOriginalFile;
            if (!this.existFile(stringNewPath)) {
                this.makeDir(stringNewPath);
            }
            this.renameFileOrDir(path + "/" + name, stringNewPath + "/" + name);
        } catch (RuntimeException e) {
            result = false;
            LOGGER.error("移动文件错误,{},{},{}", path + "/" + name, stringNewPath + "/" + name, e.getMessage());
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 删除目录或文件
     *
     * @param dir 目录名
     */
    public boolean deleteFileOrDir(String dir) {
        boolean flag = false;
        try {
            flag = getHdfs().delete(new Path(dir), true);
            if (!flag) {
                //throw new BaseException("删除目录或文件失败，请检查'" + dir + "'目录或文件是否存在!");
            }
        } catch (IOException e) {
            LOGGER.error("HDFS删除目录或文件失败!", e);
            //throw new BaseException("HDFS删除目录或文件失败!");
        }
        return flag;
    }

    /**
     * @return void 返回类型
     * @Title: listAll
     * @Description: 列出目录下所有文件
     */
    public FileStatus[] listAll(String dir) {
        FileStatus[] stats = null;
        try {
            stats = getHdfs().listStatus(new Path(dir));
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!");
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return stats;
    }

    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下所有的全路径文件
     */
    public List<HDFSFileInfo> listAllPath(String dir) {
        if (isFileOrderByTime) {
            return listAllPathOrderByModificationTime(dir);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                HDFSFileInfo hdfsFileInfo = new HDFSFileInfo();
                LocatedFileStatus fileStatus = iFileStatus.next();
                hdfsFileInfo.setDirectory(fileStatus.isDirectory());
                hdfsFileInfo.setFile(fileStatus.isFile());
                hdfsFileInfo.setFileName(fileStatus.getPath().toString());
                hdfsFileInfo.setSize(fileStatus.getLen());
                hdfsFileInfo.setModificationDate(fileStatus.getModificationTime());
                pathes.add(hdfsFileInfo);
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下指定个数的全路径文件
     */
    public List<HDFSFileInfo> listAllPath(String dir, int limit) {
        if (isFileOrderByTime) {
            return listAllPathOrderByModificationTime(dir, limit);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        int count = 0;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                if (fileStatus.isDirectory()) {
                    continue;
                }
                HDFSFileInfo hdfsFileInfo = new HDFSFileInfo();
                hdfsFileInfo.setDirectory(fileStatus.isDirectory());
                hdfsFileInfo.setFile(fileStatus.isFile());
                hdfsFileInfo.setFileName(fileStatus.getPath().toString());
                hdfsFileInfo.setSize(fileStatus.getLen());
                hdfsFileInfo.setModificationDate(fileStatus.getModificationTime());
                pathes.add(hdfsFileInfo);
                ++count;
                if (count >= limit) {
                    break;
                }
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下指定个数的全路径文件
     */
    public List<HDFSFileInfo> listAllFile(String dir, int limit) {
        if (isFileOrderByTime) {
            return listAllPathOrderByModificationTime(dir, limit);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        int count = 0;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                HDFSFileInfo hdfsFileInfo = new HDFSFileInfo();
                LocatedFileStatus fileStatus = iFileStatus.next();
                hdfsFileInfo.setDirectory(fileStatus.isDirectory());
                hdfsFileInfo.setFile(fileStatus.isFile());
                hdfsFileInfo.setFileName(fileStatus.getPath().toString());
                hdfsFileInfo.setSize(fileStatus.getLen());
                hdfsFileInfo.setModificationDate(fileStatus.getModificationTime());
                pathes.add(hdfsFileInfo);
                if (fileStatus.isDirectory()) {
                    continue;
                }
                ++count;
                if (count >= limit) {
                    break;
                }
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }


    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下所有的全路径文件
     */
    public List<HDFSFileInfo> listAllPath(String dir, boolean isAll) {
        if (!isAll) {
            return listAllPath(dir);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        try {
            iFileStatus = getHdfs().listFiles(new Path(dir), isAll);
            while (iFileStatus.hasNext()) {
                HDFSFileInfo hdfsFileInfo = new HDFSFileInfo();
                LocatedFileStatus fileStatus = iFileStatus.next();
                hdfsFileInfo.setDirectory(fileStatus.isDirectory());
                hdfsFileInfo.setFile(fileStatus.isFile());
                hdfsFileInfo.setFileName(fileStatus.getPath().toString());
                hdfsFileInfo.setSize(fileStatus.getLen());
                hdfsFileInfo.setModificationDate(fileStatus.getModificationTime());
                hdfsFileInfo.setPath(fileStatus.getPath());
                pathes.add(hdfsFileInfo);
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下指定个数的全路径文件
     */
    public List<HDFSFileInfo> listAllPath(String dir, boolean isAll, int limit) {
        if (!isAll) {
            return listAllPath(dir, limit);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        int count = 0;
        try {
            iFileStatus = getHdfs().listFiles(new Path(dir), isAll);
            while (iFileStatus.hasNext()) {
                HDFSFileInfo hdfsFileInfo = new HDFSFileInfo();
                LocatedFileStatus fileStatus = iFileStatus.next();
                hdfsFileInfo.setDirectory(fileStatus.isDirectory());
                hdfsFileInfo.setFile(fileStatus.isFile());
                hdfsFileInfo.setFileName(fileStatus.getPath().toString());
                hdfsFileInfo.setSize(fileStatus.getLen());
                hdfsFileInfo.setModificationDate(fileStatus.getModificationTime());
                pathes.add(hdfsFileInfo);
                if (fileStatus.isDirectory()) {
                    continue;
                }
                ++count;
                if (count >= limit) {
                    break;
                }
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!");
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    /**
     * @return List
     * @Title: listAllPath
     * @Description: 返回目录下所有的全路径文件
     */
    public List<String> listAllPathString(String dir) {
        if (isFileOrderByTime) {
            return listAllPathStringOrderByModTime(dir);
        }
        List<String> pathes = new ArrayList<String>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                pathes.add(fileStatus.getPath().toString());
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    /**
     * @param path
     * <AUTHOR>
     * @CreateTime: 2019年2月12日
     * @Description: 获取文件夹下的所有文件(多级目录)
     */
    public List<HdfsFilesInfo> listAllPaths(String path) {
        List<HdfsFilesInfo> paths = new ArrayList<>();
        try {
            FileStatus[] timeNames = getHdfs().listStatus(new Path(path));
            for (int i = 0; i < timeNames.length; i++) {
                String name = timeNames[i].getPath().getName();
                if (timeNames[i].isDirectory()) {
                    String timeNamePath = path + File.separator + timeNames[i].getPath().getName();
                    List<HdfsFilesInfo> chlist = listAllPaths(timeNamePath);
                    if (chlist != null && chlist.size() > 0) {
                        paths.addAll(chlist);
                    }
                } else {
                    HdfsFilesInfo fileMode = new HdfsFilesInfo();
                    fileMode.setFileName(name);
                    fileMode.setFilePath(path + File.separator + name);
                    fileMode.setFileMtime(timeNames[i].getModificationTime());
                    fileMode.setOrg_file_size(timeNames[i].getLen());
//					String[] spitName = name.split("_");
//					fileMode.setFileTime(spitName[2]+spitName[3]+spitName[4]);
                    paths.add(fileMode);
                }
            }
            return paths;
        } catch (IllegalArgumentException | IOException e) {
            LOGGER.error("获取文件列表失败", e);
            //throw new BaseException("HDFS获取文件列表失败！" + e.getMessage());
        }
        return null;
    }


    /**
     * @param path
     * <AUTHOR>
     * @CreateTime: 2019年2月12日
     * @Description: 获取文件夹下的所有文件(多级目录)
     */
    public List<HdfsFilesInfo> listAllPaths(String path, PathFilter filter) {
        List<HdfsFilesInfo> paths = new ArrayList<>();
        try {
            FileStatus[] timeNames = getHdfs().listStatus(new Path(path), filter);
            for (int i = 0; i < timeNames.length; i++) {
                String name = timeNames[i].getPath().getName();
                if (timeNames[i].isDirectory()) {
                    String timeNamePath = path + File.separator + timeNames[i].getPath().getName();
                    List<HdfsFilesInfo> chlist = listAllPaths(timeNamePath, filter);
                    if (chlist != null && chlist.size() > 0) {
                        paths.addAll(chlist);
                    }
                } else {
                    HdfsFilesInfo fileMode = new HdfsFilesInfo();
                    fileMode.setFileName(name);
                    fileMode.setFilePath(path + File.separator + name);
                    fileMode.setFileMtime(timeNames[i].getModificationTime());
                    fileMode.setOrg_file_size(timeNames[i].getLen());
//					String[] spitName = name.split("_");
//					fileMode.setFileTime(spitName[2]+spitName[3]+spitName[4]);
                    paths.add(fileMode);
                }
            }
            return paths;
        } catch (IllegalArgumentException | IOException e) {
            LOGGER.error("获取文件列表失败", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return null;
    }


    /**
     * 按修改时间排序返回目录下文件列表.
     *
     * @param dir
     * @return 2015年12月31日 chenyongjun
     */
    private List<String> listAllPathStringOrderByModTime(String dir) {
        List<String> pathes = new ArrayList<String>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        List<LocatedFileStatus> fileStatusList = new ArrayList<LocatedFileStatus>();
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                fileStatusList.add(fileStatus);
            }
            Collections.sort(fileStatusList, fileCompare);
            for (LocatedFileStatus locatedFileStatus : fileStatusList) {
                pathes.add(locatedFileStatus.getPath().toString());
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return pathes;
    }

    public void setFileOrderByTimeFlag(boolean isFileOrderByTime) {
        this.isFileOrderByTime = isFileOrderByTime;
    }

    /**
     * 按修改时间排序返回目录下文件列表.
     *
     * @param dir
     * @return 2015年12月25日 zhengxiaoling
     */
    private List<HDFSFileInfo> listAllPathOrderByModificationTime(String dir) {
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        List<LocatedFileStatus> fileStatusList = new ArrayList<LocatedFileStatus>();
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                fileStatusList.add(fileStatus);
            }
            Collections.sort(fileStatusList, fileCompare);
            for (LocatedFileStatus locatedFileStatus : fileStatusList) {
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                fileInfo.setDirectory(locatedFileStatus.isDirectory());
                fileInfo.setFile(locatedFileStatus.isFile());
                fileInfo.setFileName(locatedFileStatus.getPath().toString());
                fileInfo.setSize(locatedFileStatus.getLen());
                fileInfo.setModificationDate(locatedFileStatus.getModificationTime());
                pathes.add(fileInfo);
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!");
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return pathes;
    }

    /**
     * 按修改时间排序返回目录下指定个数的文件列表.
     *
     * @param dir
     * @param limit
     * @return 2015年12月25日 ZHANGH
     */
    private List<HDFSFileInfo> listAllPathOrderByModificationTime(String dir, int limit) {
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        List<LocatedFileStatus> fileStatusList = new ArrayList<LocatedFileStatus>();
        int count = 0;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext()) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                fileStatusList.add(fileStatus);
            }
            Collections.sort(fileStatusList, fileCompare);
            for (LocatedFileStatus locatedFileStatus : fileStatusList) {
                if (locatedFileStatus.isDirectory()) {
                    continue;
                }
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                fileInfo.setDirectory(locatedFileStatus.isDirectory());
                fileInfo.setFile(locatedFileStatus.isFile());
                fileInfo.setFileName(locatedFileStatus.getPath().toString());
                fileInfo.setSize(locatedFileStatus.getLen());
                fileInfo.setModificationDate(locatedFileStatus.getModificationTime());
                pathes.add(fileInfo);
                ++count;
                if (count >= limit) {
                    break;
                }
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return pathes;
    }

    /**
     * 按修改时间排序返回目录下文件列表.
     *
     * @param dir
     * @param count
     * @return 2015年12月25日 zhengxiaoling
     */
    private List<HDFSFileInfo> listFilePathOrderByModificationTime(String dir, int count) {
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        List<LocatedFileStatus> fileStatusList = new ArrayList<LocatedFileStatus>();
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext() && count > 0) {
                LocatedFileStatus fileStatus = iFileStatus.next();
                fileStatusList.add(fileStatus);
            }
            Collections.sort(fileStatusList, fileCompare);
            int num = 0;
            for (LocatedFileStatus locatedFileStatus : fileStatusList) {
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                fileInfo.setDirectory(locatedFileStatus.isDirectory());
                fileInfo.setFile(locatedFileStatus.isFile());
                fileInfo.setFileName(locatedFileStatus.getPath().toString());
                fileInfo.setSize(locatedFileStatus.getLen());
                fileInfo.setModificationDate(locatedFileStatus.getModificationTime());
                pathes.add(fileInfo);
                num++;
                if (num == count) {
                    break;
                }
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!", e);
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return pathes;
    }

    /**
     * @return
     * @Title: listFileOneByOne
     * @Description: 迭代列出指定目录下文件
     */
    public RemoteIterator<LocatedFileStatus> getFileStatusIterator(String dir) {
        // RemoteIterator<FileStatus> iFileStatus = null;
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        try {
            // iFileStatus = hdfs.listStatusIterator(new Path(dir));
            iFileStatus = getHdfs().listFiles(new Path(dir), true);
            // iFileStatus = hdfs.listLocatedStatus(new Path(dir));
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!");
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }
        return iFileStatus;
    }

    /**
     * @return List
     * @Title: listAll
     * @Description: 列出目录下数量为count个的文件路径
     */
    public List<HDFSFileInfo> listFilePath(String dir, int count) {
        if (isFileOrderByTime) {
            return listFilePathOrderByModificationTime(dir, count);
        }
        List<HDFSFileInfo> pathes = new ArrayList<HDFSFileInfo>();
        RemoteIterator<LocatedFileStatus> iFileStatus = null;
        try {
            iFileStatus = getHdfs().listLocatedStatus(new Path(dir));
            while (iFileStatus.hasNext() && count > 0) {
                LocatedFileStatus locatedFileStatus = iFileStatus.next();
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                fileInfo.setDirectory(locatedFileStatus.isDirectory());
                fileInfo.setFile(locatedFileStatus.isFile());
                fileInfo.setFileName(locatedFileStatus.getPath().toString());
                fileInfo.setSize(locatedFileStatus.getLen());
                fileInfo.setModificationDate(locatedFileStatus.getModificationTime());
                pathes.add(fileInfo);
                count--;
            }
        } catch (FileNotFoundException e) {
            LOGGER.error("目录'" + dir + "'不存在!");
            //throw new BaseException("目录'" + dir + "'不存在!");
        } catch (IOException e) {
            LOGGER.error("HDFS获取文件列表失败！", e);
            //throw new BaseException("HDFS获取文件列表失败！");
        }

        return pathes;
    }

    public InputStream getHdfsInputStream(String path) throws IOException {
        // FileSystem fs = FileSystem.get(URI.create(hdfsPath), conf);
        return getHdfs().open(new Path(path));
    }

    public FSDataOutputStream getOutputStreamByCreate(String path) throws IOException {
        return (FSDataOutputStream) getHdfs().create(new Path(path));
    }

    public FSDataOutputStream getOutputStreamByAppend(String path) throws IOException {
        return (FSDataOutputStream) getHdfs().append(new Path(path));
    }

    /**
     * 打开文件读取流
     *
     * @param dir
     * @param charset 编码类型
     */
    public HdfsReader openReadStream(String dir, String charset) {
        HdfsReader hdfsReader = null;
        Path path = new Path(dir);
        try {
            if (getHdfs().exists(path)) {
                FSDataInputStream is = getHdfs().open(path);
                hdfsReader = new HdfsReader();
                hdfsReader.setIs(is, dir, charset);
            } else {
                LOGGER.error("HDFS文件：" + dir + "不存在!");
                //throw new BaseException("HDFS文件：" + dir + "不存在!");
            }
        } catch (IOException e) {
            LOGGER.error("HDFS开启文件读取流异常!", e);
            //throw new BaseException("HDFS开启文件读取流异常!");
        }
        return hdfsReader;
    }

    public void closeFileSystem() {
        try {
            if (getHdfs() != null) {
                getHdfs().close();
            }
        } catch (IOException e) {
            LOGGER.error("HDFS文件系统关闭操作异常！", e);
            //throw new BaseException("HDFS文件系统关闭操作异常！");
        }
    }

    /**
     * 获取URL文件夹下的文件列表 .
     *
     * @param url
     * @return
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public Path[] dirList(String url) throws IOException {
        FileStatus[] fs = getHdfs().listStatus(new Path(url));
        Path[] listPath = FileUtil.stat2Paths(fs);
        return listPath;
    }

    public Path[] dirList(String url, FileSystem otherFS) throws IOException {
        FileStatus[] fs = otherFS.listStatus(new Path(url));
        Path[] listPath = FileUtil.stat2Paths(fs);
        return listPath;
    }

    /**
     * 根据HDFSPATH写入CONTENT内容 .
     *
     * @param hdfsPath
     * @param content
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public void writeFile(String hdfsPath, String content) throws IOException {
        Path path = new Path(hdfsPath);
        FSDataOutputStream fsOut = getHdfs().create(path);
        fsOut.write(content.getBytes());
        fsOut.close();
    }

    /**
     * deleteOnExit .
     *
     * @param hdfsPath
     * @return
     * @throws IOException
     * <AUTHOR> 2015年11月16日 zhengwei
     */
    public boolean deleteOnExit(String hdfsPath) throws IOException {
        return getHdfs().deleteOnExit(new Path(hdfsPath));
    }

    /**
     * 是否存在目录或文件 .
     *
     * @param hdfsPath
     * @return
     * @throws IOException
     * <AUTHOR> 2015年11月12日 zhengwei
     */
    public boolean existFile(String hdfsPath) throws IOException {
        Path path = new Path(hdfsPath);
        return getHdfs().exists(path);
    }

    /**
     * 根据HDFS文件路径读取文件文本内容 .
     *
     * @param hdfsPath
     * @return
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public String readFile(Path hdfsPath) throws IOException {
        FSDataInputStream in = getHdfs().open(hdfsPath);
        return IOUtils.toString(in);
    }

    /**
     * 上传多个文件到HDFS目录下 .
     *
     * @param hdfsDirPath
     * @param fileList
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public void uploadFiles(String hdfsDirPath, List<File> fileList) throws IOException {
        if ("ctgdfs".equals(conf.get("dfs.type"))) {//如果是集团小文件系统要增加配置
            uploadFiles2Ctdfs(hdfsDirPath, fileList);
        } else {
            for (File file : fileList) {
                Path filePath = new Path(file.getPath());
                Path hdfsPath = new Path(hdfsDirPath + "/" + file.getName());
                getHdfs().copyFromLocalFile(filePath, hdfsPath);
            }
        }
    }

    /**
     * 上传本地文件到HDFS中 .
     *
     * @param hdfsPath
     * @param localPath
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public void uploadFile(String hdfsPath, String localPath) throws IOException {
        if ("ctgdfs".equals(conf.get("dfs.type"))) {//如果是集团小文件系统要增加配置
            uploadFile2Ctdfs(hdfsPath, localPath);
        } else {
            Path pathSrc = new Path(localPath);
            Path pathDst = new Path(hdfsPath);
            LOGGER.debug("拷贝本地文件【" + pathSrc + "】到hdfs【" + hdfsPath + "】");
            getHdfs().copyFromLocalFile(pathSrc, pathDst);
        }
    }

    /**
     * 上传本地文件到小文件系统 .
     *
     * @param hdfsPath
     * @param localPath
     * @throws IOException
     */
    public void uploadFile2Ctdfs(String hdfsPath, String localPath) throws IOException {
        /*makeDir(hdfsPath);//不存在则创建目录
        String fileName = localPath.substring(localPath.lastIndexOf("/")+1,localPath.length());
        if(hdfsPath.endsWith("/")){
            hdfsPath = hdfsPath + fileName;
        }else{
            hdfsPath = hdfsPath + "/" + fileName;
        }
        Path pathDst = new Path(hdfsPath);
        LOGGER.info("拷贝本地文件【" + localPath + "】到hdfs【" + hdfsPath + "】");
        DFSDataOutputStream myout = (DFSDataOutputStream) getHdfs().create(pathDst);
        InputStream in = new FileInputStream(new File(localPath));
        byte[] ioBuffer = new byte[1024];
        int byteread = 0;
        while ((byteread = in.read(ioBuffer)) != -1) {
            myout.write(ioBuffer, 0, byteread);
        }
        in.close();
        myout.close();*/
    }

    /**
     * 上传多个文件到小文件系统
     *
     * @param hdfsDirPath
     * @param fileList
     * @throws IOException
     */
    public void uploadFiles2Ctdfs(String hdfsDirPath, List<File> fileList) throws IOException {
        if (hdfsDirPath.endsWith("/") || hdfsDirPath.endsWith("\\")) {
            hdfsDirPath = hdfsDirPath.substring(0, hdfsDirPath.length() - 1);
        }
        for (File file : fileList) {
            uploadFile2Ctdfs(hdfsDirPath + "/" + file.getName(), file.getPath());
        }
    }

    /**
     * 上传本地文件到HDFS中 .
     *
     * @param hdfsPath
     * @param localPath
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public void uploadFile(String hdfsPath, String localPath, boolean isDel) throws IOException {
        Path pathSrc = new Path(localPath);
        Path pathDst = new Path(hdfsPath);
        LOGGER.debug("拷贝本地文件【" + pathSrc + "】到hdfs【" + hdfsPath + "】");
        getHdfs().copyFromLocalFile(isDel, pathSrc, pathDst);
    }

    /**
     * 上传本地文件到HDFS中 .
     *
     * @param hdfsPath
     * @param localFile
     * @throws IOException
     * <AUTHOR> 2015年10月1日 zhengwei
     */
    public void uploadFile(String hdfsPath, File localFile) throws IOException {
        String localPath = localFile.getPath();
        uploadFile(hdfsPath, localPath);
    }

    /**
     * HDFS下载文件到本地 .
     *
     * @param hdfsPath
     * @param file
     * @throws IOException
     * <AUTHOR> 2015年10月2日 zhengwei
     */
    public void downloadFile(String hdfsPath, File file) throws IOException {
        file.getParentFile().mkdirs();
        FileSystem fs = FileSystem.get(URI.create(hdfsPath), conf);
        InputStream is = fs.open(new Path(hdfsPath));
        FileOutputStream fos = new FileOutputStream(file);
        IOUtils.copy(is, fos);
        fos.close();
        is.close();
    }

    /**
     * HDFS下载文件到本地 .
     *
     * @param hdfsPath
     * @param filePath
     * @throws IOException
     * <AUTHOR> 2015年10月2日 zhengwei
     */
    public void downloadFile(String hdfsPath, String filePath) throws IOException {
        downloadFile(hdfsPath, new File(filePath));
    }

    /**
     * HDFS下载文件到本地 .
     *
     * @param hdfsPath
     * @param file
     * @throws IOException
     * <AUTHOR> 2015年10月2日 zhengwei
     */
    public void downloadFile(Path hdfsPath, File file) throws IOException {
        downloadFile(hdfsPath, file.getPath());
    }

    /**
     * HDFS下载文件到本地 .
     *
     * @param hdfsPath
     * @param filePath
     * @throws IOException
     * <AUTHOR> 2015年10月2日 zhengwei
     */
    public void downloadFile(Path hdfsPath, String filePath) throws IOException {
        downloadFile(hdfsPath.toString(), new File(filePath));
    }

    /**
     * 拷贝路径 .
     *
     * @param srcHdfsPath
     * @param dstHdfsPath
     * @throws IOException
     * <AUTHOR> 2015年11月12日 zhengwei
     */
    public void copyPath(String srcHdfsPath, String dstHdfsPath) throws IOException {
        Path pathsrc = new Path(srcHdfsPath);
        Path pathdst = new Path(dstHdfsPath);
        FileUtil.copy(hdfs, pathsrc, hdfs, pathdst, false, conf);
    }

    /**
     * 拷贝文件 .
     *
     * @param srcHdfsPath
     * @param dstHdfsPath
     * @throws IllegalArgumentException
     * @throws IOException
     * <AUTHOR> 2015年12月15日 zhengwei
     */
    public void copyFile(String srcHdfsPath, String dstHdfsPath) throws IllegalArgumentException, IOException {
        FileUtil.copy(hdfs, dirList(srcHdfsPath), hdfs, new Path(dstHdfsPath), false, false, conf);
    }

    public void copyToLocal(boolean delSrc, Path srcHdfsPath, Path dstLocalPath) {
        try {
            getHdfs().copyToLocalFile(delSrc, srcHdfsPath, dstLocalPath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void copyToLocal(boolean delSrc, Path srcHdfsPath, Path dstLocalPath, boolean local) throws IOException {
        getHdfs().copyToLocalFile(delSrc, srcHdfsPath, dstLocalPath, local);
    }

    /**
     * 判断文件是否存在（从源集群检测目标集群）
     *
     * @param hdfsPath
     * @param destConfigPath
     * @return
     * @throws IOException 2018年7月27日ZHANGH
     */
    public boolean existFileFromOtherFS(String hdfsPath, String destConfigPath) throws IOException {
        FileSystem desthdfs = getOherFSConnect(destConfigPath);
        Path path = new Path(hdfsPath);
        return desthdfs.exists(path);
    }

    /**
     * 跨集群copy文件（从源集群copy到目标集群）
     * .
     *
     * @param srcHdfsPath
     * @param dstHdfsPath
     * @param destConfigPath 目标集群配置文件的路径,需要core-site.xml和hdfs-site.xml
     * @throws IllegalArgumentException
     * @throws IOException
     * <AUTHOR>
     * @创建日期：2018年7月24日
     */
    public void copyFileToOtherFS(String srcHdfsPath, String dstHdfsPath, String destConfigPath) throws IllegalArgumentException, IOException {
        FileSystem desthdfs = getOherFSConnect(destConfigPath);

        FileUtil.copy(desthdfs, dirList(srcHdfsPath, desthdfs), hdfs, new Path(dstHdfsPath), false, false, getHdfs().getConf());
    }

    public HDFSFileInfo getOtherFSFileInfo(String file, String destConfigPath) throws MalformedURLException, IOException {
        FileSystem desthdfs = getOherFSConnect(destConfigPath);
        Path path = new Path(file);
        try {
            if (desthdfs.exists(path)) {
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                FileStatus[] fileStatusList = desthdfs.listStatus(path);
                if (fileStatusList == null || fileStatusList.length == 0) {
                    return null;
                }
                FileStatus fileStatus = fileStatusList[0];
                fileInfo.setDirectory(fileStatus.isDirectory());
                fileInfo.setFile(fileInfo.isFile());
                fileInfo.setFileName(file);
                fileInfo.setSize(fileStatus.getLen());
                fileInfo.setModificationDate(fileStatus.getModificationTime());
                return fileInfo;
            } else {
                //throw new BaseException("文件不存在:" + file);
            }
        } catch (Exception e) {
            //throw new BaseException(e);
        }
        return null;
    }

    public HdfsReader openReadStreamFromOtherFS(String dir, String charset, String destConfigPath) throws MalformedURLException, IOException {
        FileSystem desthdfs = getOherFSConnect(destConfigPath);
        HdfsReader hdfsReader = null;
        Path path = new Path(dir);
        try {
            if (desthdfs.exists(path)) {
                FSDataInputStream is = desthdfs.open(path);
                hdfsReader = new HdfsReader();
                hdfsReader.setIs(is, dir, charset);
            } else {
                LOGGER.error("HDFS文件：" + dir + "不存在!");
                //throw new BaseException("HDFS文件：" + dir + "不存在!");
            }
        } catch (IOException e) {
            LOGGER.error("HDFS开启文件读取流异常!", e);
            //throw new BaseException("HDFS开启文件读取流异常!");
        }
        return hdfsReader;
    }

    private FileSystem getOherFSConnect(String destConfigPath)
            throws MalformedURLException, IOException {
        FileSystem desthdfs;
        if (!StringUtils.isBlank(destConfigPath) && OTHER_FS_MAP.containsKey(destConfigPath)) {
            desthdfs = OTHER_FS_MAP.get(destConfigPath);
        } else {
            Configuration destConf = new Configuration(false);
            File path = new File(destConfigPath);
            if (null == path || !path.exists()) {
                //throw new BaseException("目标集群配置文件目录不存在：" + destConfigPath);
            }
            File[] files = path.listFiles();
            if (null == files || files.length < 2) {
                //throw new BaseException("目标集群配置文件不完整,必须core-site.xml和hdfs-site.xml");
            }
            for (File f : files) {
                destConf.addResource(new URL("file://" + f.getAbsolutePath()));
            }
            destConf.set("fs.hdfs.impl.disable.cache", "true");
            desthdfs = FileSystem.get(destConf);

            if (!OTHER_FS_MAP.containsKey(destConfigPath)) {
                OTHER_FS_MAP.put(destConfigPath, desthdfs);
            }
        }
        return desthdfs;
    }

    /**
     * 替换文件 .
     *
     * @param srcHdfsPath
     * @param dstHdfsPath
     * @throws IOException
     * @throws IllegalArgumentException
     * <AUTHOR> 2015年12月15日 zhengwei
     */
    public void replaceFile(String srcHdfsPath, String dstHdfsPath) throws IllegalArgumentException, IOException {
        FileUtil.copy(hdfs, dirList(srcHdfsPath), hdfs, new Path(dstHdfsPath), false, true, conf);
    }

    public HDFSFileInfo getFileInfo(String file) {
        Path path = new Path(file);
        try {
            if (getHdfs().exists(path)) {
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                FileStatus fileStatus = getHdfs().getFileStatus(path);
                if (fileStatus == null) {
                    return null;
                }
                //FileStatus fileStatus = fileStatusList[0];
                fileInfo.setDirectory(fileStatus.isDirectory());
                fileInfo.setFile(fileStatus.isFile());
                fileInfo.setFileName(file);
                fileInfo.setSize(fileStatus.getLen());
                fileInfo.setModificationDate(fileStatus.getModificationTime());
                return fileInfo;
            } else {
                //throw new BaseException("文件不存在:" + file);
            }
        } catch (Exception e) {
            //throw new BaseException(e);
        }
        return null;
    }

    /**
     * 获取hdfs文件或目录信息
     * .
     *
     * @param file
     * @return
     */
    public HDFSFileInfo getHdfsFileInfo(String file) {
        Path path = new Path(file);
        try {
            if (getHdfs().exists(path)) {
                HDFSFileInfo fileInfo = new HDFSFileInfo();
                FileStatus fileStatus = getHdfs().getFileStatus(path);
                if (fileStatus == null) {
                    return null;
                }
                fileInfo.setDirectory(fileStatus.isDirectory());
                fileInfo.setFile(fileInfo.isFile());
                fileInfo.setFileName(file);
                fileInfo.setSize(fileStatus.getLen());
                fileInfo.setModificationDate(fileStatus.getModificationTime());
                return fileInfo;
            } else {
                //throw new BaseException("文件不存在:" + file);
            }
        } catch (Exception e) {
            //throw new BaseException(e);
        }
        return null;
    }

    /**
     * .
     *
     * <AUTHOR>
     * @version Revision 1.0.0
     * @版权：福富软件 版权所有 (c) 2015
     * @see:
     * @创建日期：2016年4月9日
     * @功能说明：
     */
    public static class ReadInfo {
        private String context;
        private byte[] bytes;

        private long pos;
        private long start;
        private int len;
        private String file;
        private boolean isEnd;
        private boolean isClose;

        public boolean isEnd() {
            return isEnd;
        }

        public void setEnd(boolean isEnd) {
            this.isEnd = isEnd;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }

        public long getPos() {
            return pos;
        }

        public byte[] getBytes() {
            return bytes;
        }

        public void setBytes(byte[] bytes, int len) {
            this.bytes = bytes;
            context = new String(bytes, 0, len);
        }

        public void setPos(long pos) {
            this.pos = pos;
        }

        public long getStart() {
            return start;
        }

        public void setStart(long start) {
            this.start = start;
        }

        public int getLen() {
            return len;
        }

        public void setLen(int len) {
            this.len = len;
        }

        public String getFile() {
            return file;
        }

        public void setFile(String file) {
            this.file = file;
        }

        public boolean addReadInfo(ReadInfo nextReadInfo) {
            if (nextReadInfo == null) {
                return true;
            }
            long nextStart = nextReadInfo.getStart();
            if (nextStart - this.start != this.len) {
                // 不是连续的数据不能加;
                return false;
            }
            int newLen = this.len + nextReadInfo.getLen();
            byte[] newBytes = arraycat(bytes, nextReadInfo.getBytes());
            this.bytes = newBytes;
            this.context = new String(newBytes);
            this.len = newLen;
            return true;
        }

        public ReadInfo pollLineInfo() {
            if (context == null || bytes == null) {
                return null;
            }
            int index = context.indexOf(LINE_END);
            if (index == -1) {
                return null;
            }
            String lineStr = context.substring(0, index);
            int byteLen = lineStr.getBytes().length;
            ReadInfo lineReadInfo = new ReadInfo();
            lineReadInfo.setLen(byteLen);
            lineReadInfo.setContext(lineStr);
            lineReadInfo.setStart(this.start);

            this.start = start + byteLen + LINE_END.length();
            len = len - byteLen - LINE_END.length();
            byte[] lastBytes = new byte[len];
            int byteIndex = byteLen + LINE_END.length();
            for (int i = 0; i < len; i++) {
                lastBytes[i] = bytes[byteIndex];
                byteIndex++;
            }
            bytes = lastBytes;
            context = new String(bytes);
            return lineReadInfo;
        }

        private byte[] arraycat(byte[] b1, byte[] b2) {
            if (b1 == null) {
                if (b2 == null) {
                    return null;
                } else {
                    return b2;
                }
            }
            if (b2 == null) {
                return b1;
            }
            int len1 = b1.length;
            int len2 = b2.length;
            byte[] newBytes = new byte[len1 + len2];
            for (int i = 0; i < len1; i++) {
                newBytes[i] = b1[i];
            }
            int i2 = 0;
            for (int i = len1; i < newBytes.length; i++) {
                newBytes[i] = b2[i2];
                i2++;
            }
            return newBytes;
        }

        public boolean isClose() {
            return isClose;
        }

        public void setClose(boolean isClose) {
            this.isClose = isClose;
        }
    }

    public static class HDFSFileInfo {
        private String fileName;
        private long size;
        private Date modificationDate;
        private boolean isDirectory;
        private boolean isFile;
        private Path path;

        public boolean isDirectory() {
            return isDirectory;
        }

        public void setDirectory(boolean isDirectory) {
            this.isDirectory = isDirectory;
        }

        public void setModificationDate(Date modificationDate) {
            this.modificationDate = modificationDate;
        }

        public String toString() {
            return fileName;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public long getSize() {
            return size;
        }

        public void setSize(long size) {
            this.size = size;
        }

        public Date getModificationDate() {
            return modificationDate;
        }

        public void setModificationDate(long ms) {
            this.modificationDate = new Date(ms);
        }

        public boolean isFile() {
            return isFile;
        }

        public void setFile(boolean isFile) {
            this.isFile = isFile;
        }

        /**
         * @return path属性
         */
        public Path getPath() {
            return path;
        }

        /**
         * @param path 设置path属性
         */
        public void setPath(Path path) {
            this.path = path;
        }

    }

    /**
     * <AUTHOR>
     * @CreateTime: 2019年5月13日
     * @Description: 保存hadoop路径信息(多级目录)，提供排序方式以及保存hadoop文件信息
     */
    public static class HdfsFilesInfo {
        private String filePath;  //文件路径
        private String fileName;  //文件名称
        private String fileTime;  //名称中的时间
        private long fileMtime; //文件的修改时间
        private String fileModifyTime;//原始文件修改时间（原始文件开始时间） yyyyMMddHHmmss
        private List<String> lineVaule;
        private String sign;
        private long org_file_size;

        public long getOrg_file_size() {
            return org_file_size;
        }

        public void setOrg_file_size(long org_file_size) {
            this.org_file_size = org_file_size;
        }

        public String getFileModifyTime() {
            return fileModifyTime;
        }

        public void setFileModifyTime(String fileModifyTime) {
            this.fileModifyTime = fileModifyTime;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public List<String> getLineVaule() {
            return lineVaule;
        }

        public void setLineVaule(List<String> lineVaule) {
            this.lineVaule = lineVaule;
        }

        public String getFileTime() {
            return fileTime;
        }

        public void setFileTime(String fileTime) {
            this.fileTime = fileTime;
        }

        public long getFileMtime() {
            return fileMtime;
        }

        public void setFileMtime(long fileMtime) {
            this.fileMtime = fileMtime;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }

        @Override
        public String toString() {
            return "FileBean [filePath=" + filePath + ", fileName=" + fileName + ", fileTime=" + fileTime + ", fileMtime="
                    + fileMtime + ", lineVaule=" + lineVaule + "]";
        }
    }


    public boolean isFile(String pathStr) {
        boolean flag = false;
        try {
            Path path = new Path(pathStr);
            flag = getHdfs().isFile(path);
        } catch (IllegalArgumentException e) {
            LOGGER.error("HDFS解析文件路径异常：" + pathStr, e);
            //throw new BaseException("HDFS解析文件路径异常：", pathStr);
        } catch (IOException e) {
            LOGGER.error("HDFS调用isFile()方法异常：" + pathStr, e);
            //throw new BaseException("HDFS调用isFile()方法异常：", pathStr);
        }
        return flag;
    }

    public boolean isDirectory(String pathStr) {
        boolean flag = false;
        try {
            Path path = new Path(pathStr);
            flag = getHdfs().isDirectory(path);
        } catch (IllegalArgumentException e) {
            LOGGER.error("HDFS文件系统关闭操作异常！", e);
            //throw new BaseException("HDFS文件系统关闭操作异常！");
        } catch (IOException e) {
            LOGGER.error("HDFS调用isDirectory()方法异常：" + pathStr, e);
            //throw new BaseException("HDFS调用isDirectory()方法异常：", pathStr);
        }
        return flag;
    }

    public static class HdfsReader {
        private FSDataInputStream is;
        private BufferedReader hdfsReader;
        private ReadInfo curReadInfo;
        private ReadInfo bufReadInfo;
        private String fileName;

        public void setIs(FSDataInputStream is, String dir, String charset) {
            this.is = is;
            if (charset == null) {
                charset = "UTF-8";
            }

            try {
                hdfsReader = new BufferedReader(new InputStreamReader(is, charset));
            } catch (UnsupportedEncodingException e) {
                LOGGER.error("不支持的编码类型：" + charset + "！", e);
                //throw new BaseException("不支持的编码类型：" + charset + "！");
            }
            curReadInfo = new ReadInfo();
            curReadInfo.setFile(dir);
            bufReadInfo = new ReadInfo();
            bufReadInfo.setFile(dir);
            this.fileName = dir;
        }

        public String getFileName() {
            return this.fileName;
        }

        /**
         * 读取文件中一定字节的数据.
         * <p>
         * 需要先调用openReadStream 完成后需要调用closeReadStream
         *
         * @param start
         * @param len
         * @return 2016年4月9日 zhengxiaoling
         */
        public ReadInfo read(long start, int len) {
            if (is == null) {
                //throw new BaseException("文件未打开");
            }
            ReadInfo readInfo = new ReadInfo();
            byte[] buffer = new byte[len];
            int offset = 0;
            int rs = 0;
            try {
                rs = is.read(start, buffer, offset, len);
                if (rs == -1) {
                    return null;
                }
                readInfo.setStart(start);
                readInfo.setLen(rs);
                curReadInfo.setPos(start + rs);
                readInfo.setBytes(buffer, rs);
                if (rs != len) {
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("需要读取的位置" + start + ",长度[" + len + "],实际读取的长度[" + rs + "],读取的结果[" + readInfo.getContext() + "]");
                    }
                    curReadInfo.setEnd(true);
                    readInfo.setEnd(true);
                }
            } catch (IOException e) {
                LOGGER.error("读取文件失败！", ",文件[" + curReadInfo.getFile() + "],开始位置[" + start + "],准备读取长度[" + len + "]", e);
                //throw new BaseException("读取文件失败！");
            }
            return readInfo;
        }

        /**
         * 按行读取.
         *
         * @return 2016年4月9日 zhengxiaoling
         */
        public ReadInfo readLine() {
            if (is == null) {
                //throw new BaseException("文件未打开");
            }
            if (curReadInfo.isEnd() && bufReadInfo == null) {
                return null;
            }
            ReadInfo lineInfo = null;
            ReadInfo readInfo = null;
            while (true) {
                lineInfo = bufReadInfo.pollLineInfo();
                if (lineInfo != null) {
                    break;
                }
                if (curReadInfo.isEnd()) {
                    lineInfo = bufReadInfo;
                    bufReadInfo = null;
                    break;
                }
                readInfo = read(curReadInfo.getPos(), READ_BYTE_LEN);
                bufReadInfo.addReadInfo(readInfo);
            }
            return lineInfo;
        }

        /**
         * 读取HDFS文件
         *
         * @return
         */
        public String readFileByLine() throws IOException {
            String str = null;
            try {
                if (hdfsReader != null) {
                    str = hdfsReader.readLine();
                }
            } catch (IOException e) {
                LOGGER.error("读取文件失败！", e);
                //throw new BaseException("读取文件失败！");
            }
            return str;
        }

        public void close() {
            try {
                if (hdfsReader != null) {
                    hdfsReader.close();
                    hdfsReader = null;
                }
                if (is != null) {
                    is.close();
                    curReadInfo.setClose(true);
                }
            } catch (IOException e) {
                LOGGER.error("流关闭操作异常！", e);
                //throw new BaseException("流关闭操作异常！");
            }
        }

        public InputStream getInputStream() {
            return is;
        }
    }

    /**
     * @param fileBeanList
     * @return fileBeanList
     * <AUTHOR>
     * @CreateTime: 2019年2月25日
     * @Description: 将文件排序
     */
    public List<HdfsFilesInfo> sortFileBeanList(List<HdfsFilesInfo> fileBeanList, final String sortMode) {
        Collections.sort(fileBeanList, new Comparator<HdfsFilesInfo>() {
            @Override
            public int compare(HdfsFilesInfo o1, HdfsFilesInfo o2) {
                int n = 0;
                switch (sortMode) {
                    case "fileName":
                        /*int n1 = extractNumber(o1.getFileName());
                        int n2 = extractNumber(o2.getFileName());
                        n = n2 - n1;*/
                        n = o1.getFileName().compareTo(o2.getFileName());
                        break;
                    case "fileTime":
                        if (Long.parseLong(o1.getFileTime()) > Long.parseLong(o2.getFileTime())) {
                            n = 1;
                        } else if (Long.parseLong(o1.getFileTime()) < Long.parseLong(o2.getFileTime())) {
                            n = -1;
                        } else {
                            n = 0;
                        }
                        break;
                    case "fileMtime":
                        if (o1.getFileMtime() > o2.getFileMtime()) {
                            n = 1;
                        } else if (o1.getFileMtime() < o2.getFileMtime()) {
                            n = -1;
                        } else {
                            n = 0;
                        }
                        break;
                    default:
                        //log.debug("排序类型为空或者不存在(fileName|fileTime|fileMtime),启用默认排序fileMtime");
                        if (o1.getFileMtime() > o2.getFileMtime()) {
                            n = 1;
                        } else if (o1.getFileMtime() < o2.getFileMtime()) {
                            n = -1;
                        } else {
                            n = 0;
                        }
                        break;
                }
                return n;

            }

            private int extractNumber(String fileName) {
                int i;
                try {
                    String number = fileName.replaceAll("[^\\d]", "");
                    i = Integer.parseInt(number);
                } catch (Exception e) {
                    i = 0;
                }
                return i;
            }

        });

        return fileBeanList;
    }

    public static void main(String[] args) throws IOException {
        HDFSUtil hh = new HDFSUtil();
        //hh.createNewFile(path, bos);
        long b = hh.getFileLength("/data");
        System.out.println(b);

        List<HdfsFilesInfo> aa = hh.listAllPaths("/data");
        for (int i = 0; i < aa.size(); i++) {
            System.out.println(aa.get(i).toString());
        }
    }
}
