package com.itco.ticketindb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.json.JSONException;
import org.json.JSONTokener;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest
class TicketIndbTests {

    void test1() {
        String a = "{\"A\":\"1000\",\"C\":[\"10\",\"20\"]}";

        JSONObject jsonObject = JSON.parseObject(a);

        Object objectC = jsonObject.get("A");
        Object objectD = jsonObject.getJSONArray("C");
        System.out.println("objectC:" + objectC);
        System.out.println("objectD:" + objectD);


        try {

            System.out.println("objectC:" + objectC.toString());
            Object resObj = new JSONTokener(jsonObject.getString("A")).nextValue();
            if (resObj instanceof JSONObject) {
                System.out.println(objectC.toString() + ",对象是JSONObject:" + resObj);
            }

            if (resObj instanceof JSONArray) {
                System.out.println(objectC.toString() + ",对象是JSONArray:" + resObj);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }


        System.out.println("objectD:" + ((JSONArray) objectD).size());
        if (((JSONArray) objectD).size() > 1) {
            for (int i = 0; i < ((JSONArray) objectD).size(); i++) {
                System.out.println("objectD:" + i + "->" + ((JSONArray) objectD).get(i));

            }
        }
    }

    void test2() {
        Map<String, String> map = new HashMap<String, String>();
        map.put("name", "bob");
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(map));

        System.out.println(jsonObject);

        List<Map> list = new ArrayList<Map>();
        Map<String, String> map1 = new HashMap<String, String>();
        Map<String, String> map2 = new HashMap<String, String>();
        Map<String, String> map3 = new HashMap<String, String>();
        map1.put("name", "刘备");
        list.add(map1);
        map2.put("name", "关羽");
        list.add(map2);
        map3.put("name", "张飞");
        list.add(map3);
        JSONArray jsonArray = new JSONArray(Collections.singletonList(list));

        System.out.println(jsonArray);


        //Object resObj = new JSONTokener(jsonObject.toString()).nextValue();
        Object resObj = null;
        try {
            resObj = new JSONTokener(jsonArray.toString()).nextValue();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (resObj instanceof JSONObject) {
            System.out.println("对象是JSONObject: " + resObj);
        }

        if (resObj instanceof JSONArray) {
            System.out.println("对象是JSONArray: " + resObj);
        }
    }

    @Test
    void contextLoads() {
    }

}
