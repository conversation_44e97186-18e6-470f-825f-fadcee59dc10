#æ¯å¦åå¸å¼è¿ç¨æ¡æ¶æ¥ç®¡
process.take.flag=true
#zkæä½ç¨æ·
process.zk.param.userName=subt
#zk sha1 base64å¯ç 
process.zk.param.shaBase64Passwd=vJ7T2nw/d1/FBcLLkvcU+/p2UI0=
#zkæä½ç¨æ·å¯ç 
process.zk.param.password=D+eGFMuqz1LMJN4T9IHiuX/BTi58IcelCufAYf1jembmZcZ6lXK9JnztHiepIKV11tRXl/ICHJJ5ZNYlcbk3bQ==
#å å¯å¬é¥
process.zk.param.publicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALyqDwwkbvD0b/f5hvQ9hBv9L4WXbXMRNGG2Mz/EOSAj26F4s7T/A1cgqOSlCNAGULdTfVns+7XUH2MESPJns08CAwEAAQ==
#æå¤§ä¼ç æ¶é´
process.zk.param.baseSleepTimeMs=1000
#æå¤§éè¿æ¬¡æ°
process.zk.param.maxRetries=3
process.zk.param.sessionTimeoutMs=3000
process.zk.param.connectionTimeoutMs=3000
#zookeeperéç½®ä¸»ç®å½
configHome=/config
#è¿ç¨æ³¨åæ ¹ç®å½
processHome=/process
#è¿ç¨æ³¨åè¾å©ç®å½
processAssistHome=/process/Assist
#è¿ç¨éç®å½
processLockHome=/process/Lock
        

#æ¨¡åæä»¶
configTpModule=/config/table/tp_module
#è¿ç¨æä»¶
configTpProcess=/config/table/tp_process
#éç½®åæ­¥ä¸»ç®å½
message.config=/message/config
#è¿ç¨å¯åä¸»ç®å½
message.process=/message/process
#ä½¿ç¨ZKæ¶åä»»å¡æ¶æ¯è·¯å¾
message.task=/message/task

#ç¦å»º TPSS
#process.zk.param.ip=***********:32181
#uploadHomeConfig=/opt/optps/config

# digit  
#process.zk.param.ip=***************:32183,***************:32183,***************:32183,***************:32183,***************:32183
process.zk.param.ip=***************:32181
uploadHomeConfig=/config
